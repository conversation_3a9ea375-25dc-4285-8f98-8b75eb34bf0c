name: Build Godot
description: Build Godot with the provided options.

inputs:
  target:
    description: Build target (editor, template_release, template_debug).
    default: editor
  tests:
    description: Unit tests.
    default: false
    required: false
  platform:
    description: Target platform.
    required: false
  sconsflags:
    description: Additional SCons flags.
    default: ""
    required: false
  scons-cache:
    description: The SCons cache path.
    default: ${{ github.workspace }}/.scons_cache/

runs:
  using: composite
  steps:
    - name: SCons Build
      shell: sh
      env:
        SCONSFLAGS: ${{ inputs.sconsflags }}
      run: |
        echo "Building with flags:" platform=${{ inputs.platform }} target=${{ inputs.target }} tests=${{ inputs.tests }} ${{ env.SCONSFLAGS }} "cache_path=${{ inputs.scons-cache }}" redirect_build_objects=no

        if [ "${{ inputs.target }}" != "editor" ]; then
          # Ensure we don't include editor code in export template builds.
          rm -rf editor
        fi

        if [ "${{ github.event.number }}" != "" ]; then
          # Set build identifier with pull request number if available. This is displayed throughout the editor.
          export BUILD_NAME="gh-${{ github.event.number }}"
        else
          export BUILD_NAME="gh"
        fi

        scons platform=${{ inputs.platform }} target=${{ inputs.target }} tests=${{ inputs.tests }} ${{ env.SCONSFLAGS }} "cache_path=${{ inputs.scons-cache }}" redirect_build_objects=no
        ls -l bin/
