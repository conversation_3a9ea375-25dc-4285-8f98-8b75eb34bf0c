name: 🐧 Linux Builds
on:
  workflow_call:

# Global Settings
env:
  GODOT_CPP_BRANCH: 4.4
  SCONSFLAGS: verbose=yes warnings=extra werror=yes module_text_server_fb_enabled=yes strict_checks=yes "accesskit_sdk_path=${{ github.workspace }}/accesskit-c-0.15.1/"
  DOTNET_NOLOGO: true
  DOTNET_CLI_TELEMETRY_OPTOUT: true
  TSAN_OPTIONS: suppressions=${{ github.workspace }}/misc/error_suppressions/tsan.txt
  UBSAN_OPTIONS: suppressions=${{ github.workspace }}/misc/error_suppressions/ubsan.txt

jobs:
  build-linux:
    # Stay one LTS before latest to increase portability of Linux artifacts.
    runs-on: ubuntu-22.04
    name: ${{ matrix.name }}
    timeout-minutes: 120
    strategy:
      fail-fast: false
      matrix:
        include:
          - name: Editor w/ Mono (target=editor)
            cache-name: linux-editor-mono
            target: editor
            sconsflags: module_mono_enabled=yes
            bin: ./bin/redot.linuxbsd.editor.x86_64.mono
            build-mono: true
            tests: false # Disabled due freeze caused by mix Mono build and CI
            doc-test: true
            proj-conv: true
            api-compat: true
            artifact: true
            # Validate godot-cpp compatibility on one arbitrary editor build.
            godot-cpp: true
            # Validate redot-cpp compatibility on one arbitrary editor build.
            redot-cpp: true

          - name: Editor with doubles and GCC sanitizers (target=editor, tests=yes, dev_build=yes, scu_build=yes, precision=double, use_asan=yes, use_ubsan=yes, linker=mold)
            cache-name: linux-editor-double-sanitizers
            target: editor
            # Debug symbols disabled as they're huge on this build and we hit the 14 GB limit for runners.
            sconsflags: dev_build=yes scu_build=yes debug_symbols=no precision=double use_asan=yes use_ubsan=yes linker=mold
            bin: ./bin/redot.linuxbsd.editor.dev.double.x86_64.san
            build-mono: false
            tests: true
            proj-test: true
            # Skip 2GiB artifact speeding up action.
            artifact: false

          - name: Editor with clang sanitizers (target=editor, tests=yes, dev_build=yes, use_asan=yes, use_ubsan=yes, use_llvm=yes, linker=lld)
            cache-name: linux-editor-llvm-sanitizers
            target: editor
            sconsflags: dev_build=yes use_asan=yes use_ubsan=yes use_llvm=yes linker=lld
            bin: ./bin/redot.linuxbsd.editor.dev.x86_64.llvm.san
            build-mono: false
            tests: true
            # Skip 2GiB artifact speeding up action.
            artifact: false
            # Test our oldest supported SCons/Python versions on one arbitrary editor build.
            legacy-scons: true

          - name: Editor with ThreadSanitizer (target=editor, tests=yes, dev_build=yes, use_tsan=yes, use_llvm=yes, linker=lld)
            cache-name: linux-editor-thread-sanitizer
            target: editor
            tests: true
            sconsflags: dev_build=yes use_tsan=yes use_llvm=yes linker=lld
            bin: ./bin/redot.linuxbsd.editor.dev.x86_64.llvm.san
            build-mono: false
            # Skip 2GiB artifact speeding up action.
            artifact: false

          - name: Template w/ Mono, release (target=template_release, tests=yes)
            cache-name: linux-template-mono
            target: template_release
            sconsflags: module_mono_enabled=yes
            bin: ./bin/redot.linuxbsd.template_release.x86_64.mono
            build-mono: false
            tests: true
            artifact: true

          - name: Template w/ Mono, debug (target=template_debug, tests=yes)
            cache-name: linux-template-mono-debug
            target: template_debug
            sconsflags: module_mono_enabled=yes
            bin: ./bin/redot.linuxbsd.template_debug.x86_64.mono
            build-mono: false
            tests: true
            artifact: true

          - name: Minimal template (target=template_release, tests=yes, everything disabled)
            cache-name: linux-template-minimal
            target: template_release
            sconsflags: modules_enabled_by_default=no disable_3d=yes disable_advanced_gui=yes disable_physics_2d=yes disable_physics_3d=yes deprecated=no minizip=no
            bin: ./bin/redot.linuxbsd.template_release.x86_64
            tests: true
            artifact: true

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Linux dependencies for tests
        if: matrix.proj-test
        run: |
          sudo rm -f /etc/apt/sources.list.d/microsoft-prod.list
          sudo apt-get install mesa-vulkan-drivers

      # TODO: Figure out somehow how to embed this one.
      - name: wayland-scanner dependency
        run: |
          sudo apt-get install libwayland-bin

      - name: Free disk space on runner
        run: |
          echo "Disk usage before:" && df -h
          sudo rm -rf /usr/local/lib/android
          echo "Disk usage after:" && df -h

      - name: Restore Godot build cache
        uses: ./.github/actions/godot-cache-restore
        with:
          cache-name: ${{ matrix.cache-name }}
        continue-on-error: true

      - name: Setup Python and SCons
        if: "!matrix.legacy-scons"
        uses: ./.github/actions/godot-deps

      - name: Setup Python and SCons (legacy versions)
        if: matrix.legacy-scons
        uses: ./.github/actions/godot-deps
        with:
          # Sync with Ensure*Version in SConstruct.
          python-version: 3.8
          scons-version: 4.0

      - name: Force remove preinstalled .NET SDKs
        if: matrix.build-mono
        run: |
          sudo rm -rf /usr/share/dotnet/sdk/*

      - name: Setup older .NET SDK as baseline
        if: matrix.build-mono
        uses: actions/setup-dotnet@v4
        with:
          # Targeting the oldest version we want to support to ensure it still builds.
          dotnet-version: 8.0.100

      - name: Download pre-built AccessKit
        uses: dsaltares/fetch-gh-release-asset@1.1.2
        with:
          repo: AccessKit/accesskit-c
          version: tags/0.15.1
          file: accesskit-c-0.15.1.zip
          target: accesskit-c-0.15.1/accesskit_c.zip

      - name: Extract pre-built AccessKit
        run: unzip -o accesskit-c-0.15.1/accesskit_c.zip

      - name: Install mold linker
        if: matrix.proj-test
        uses: rui314/setup-mold@v1

      - name: Compilation
        uses: ./.github/actions/godot-build
        with:
          sconsflags: ${{ env.SCONSFLAGS }} ${{ matrix.sconsflags }}
          platform: linuxbsd
          target: ${{ matrix.target }}
          tests: ${{ matrix.tests }}

      - name: Compilation (godot-cpp)
        uses: ./.github/actions/godot-cpp-build
        if: matrix.godot-cpp
        with:
          bin: ${{ matrix.bin }}
          scons-flags: target=template_debug dev_build=yes verbose=yes
          godot-cpp-branch: ${{ env.GODOT_CPP_BRANCH }}

      - name: Compilation (redot-cpp)
        uses: ./.github/actions/redot-cpp-build
        if: matrix.redot-cpp
        with:
          bin: ${{ matrix.bin }}
          scons-flags: target=template_debug dev_build=yes verbose=yes
          redot-cpp-branch: ${{ env.GODOT_CPP_BRANCH }}

      - name: Save Godot build cache
        uses: ./.github/actions/godot-cache-save
        with:
          cache-name: ${{ matrix.cache-name }}
        continue-on-error: true

      - name: Generate C# glue
        if: matrix.build-mono
        run: |
          ${{ matrix.bin }} --headless --generate-mono-glue ./modules/mono/glue

      - name: Build .NET solutions
        if: matrix.build-mono
        run: |
          dotnet --info
          ./modules/mono/build_scripts/build_assemblies.py --godot-output-dir=./bin --godot-platform=linuxbsd --werror

      - name: Prepare artifact
        if: matrix.artifact
        run: |
          strip bin/redot.*
          chmod +x bin/redot.*

      - name: Upload artifact
        uses: ./.github/actions/upload-artifact
        if: matrix.artifact
        with:
          name: ${{ matrix.cache-name }}

      - name: Unit tests
        if: matrix.tests
        run: |
          ${{ matrix.bin }} --version
          ${{ matrix.bin }} --help
          ${{ matrix.bin }} --headless --test --force-colors

      - name: .NET source generators tests
        if: matrix.build-mono
        run: |
          dotnet test modules/mono/editor/Godot.NET.Sdk/Godot.SourceGenerators.Tests

      # Check class reference
      - name: Check for class reference updates
        if: matrix.doc-test
        run: |
          echo "Running --doctool to see if this changes the public API without updating the documentation."
          echo -e "If a diff is shown, it means that your code/doc changes are incomplete and you should update the class reference with --doctool.\n\n"
          ${{ matrix.bin }} --doctool --headless 2>&1 > /dev/null || true
          git diff --color --exit-code && ! git ls-files --others --exclude-standard | sed -e 's/^/New doc file missing in PR: /' | grep 'xml$'

      # Check API backwards compatibility
      - name: Check for GDExtension compatibility
        if: matrix.api-compat
        run: |
          ./misc/scripts/validate_extension_api.sh "${{ matrix.bin }}"

      # Download and run the test project
      - name: Test Godot project
        uses: ./.github/actions/godot-project-test
        if: matrix.proj-test
        with:
          bin: ${{ matrix.bin }}

      # Test the project converter
      - name: Test project converter
        uses: ./.github/actions/godot-converter-test
        if: matrix.proj-conv
        with:
          bin: ${{ matrix.bin }}
