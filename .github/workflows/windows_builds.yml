name: 🏁 Windows Builds
on:
  workflow_call:

# Global Settings
# SCONS_CACHE for windows must be set in the build environment
env:
  SCONSFLAGS: verbose=yes warnings=extra werror=yes module_text_server_fb_enabled=yes d3d12=yes strict_checks=yes "angle_libs=${{ github.workspace }}/" "accesskit_sdk_path=${{ github.workspace }}/accesskit-c-0.15.1/"
  SCONS_CACHE_MSVC_CONFIG: true
  PYTHONIOENCODING: utf8

jobs:
  build-windows:
    # Windows 10 with latest image
    runs-on: windows-latest
    name: ${{ matrix.name }}
    timeout-minutes: 120
    strategy:
      fail-fast: false
      matrix:
        include:
          - name: Editor (target=editor, tests=yes)
            cache-name: windows-editor
            target: editor
            tests: true
            # Skip debug symbols, they're way too big with MSVC.
            sconsflags: debug_symbols=no vsproj=yes vsproj_gen_only=no windows_subsystem=console
            bin: ./bin/redot.windows.editor.x86_64.exe
            compiler: msvc

          - name: Editor w/ clang-cl (target=editor, tests=yes, use_llvm=yes)
            cache-name: windows-editor-clang
            target: editor
            tests: true
            sconsflags: debug_symbols=no windows_subsystem=console use_llvm=yes
            bin: ./bin/redot.windows.editor.x86_64.llvm.exe
            compiler: clang

          - name: Template (target=template_release, tests=yes)
            cache-name: windows-template
            target: template_release
            tests: true
            sconsflags: debug_symbols=no
            bin: ./bin/redot.windows.template_release.x86_64.console.exe
            compiler: msvc

          - name: Template w/ GCC (target=template_release, tests=yes, use_mingw=yes)
            cache-name: windows-template-gcc
            # MinGW takes MUCH longer to compile; save time by only targeting Template.
            target: template_release
            tests: true
            sconsflags: debug_symbols=no use_mingw=yes
            bin: ./bin/redot.windows.template_release.x86_64.console.exe
            compiler: gcc

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Restore Godot build cache
        uses: ./.github/actions/godot-cache-restore
        with:
          cache-name: ${{ matrix.cache-name }}
        continue-on-error: true

      - name: Setup Python and SCons
        uses: ./.github/actions/godot-deps

      - name: Download Direct3D 12 SDK components
        run: python ./misc/scripts/install_d3d12_sdk_windows.py

      - name: Download pre-built ANGLE static libraries
        uses: dsaltares/fetch-gh-release-asset@1.1.2
        with:
          repo: godotengine/godot-angle-static
          version: tags/chromium/6601.2
          file: godot-angle-static-x86_64-${{ matrix.compiler == 'gcc' && 'gcc' || 'msvc' }}-release.zip
          target: angle/angle.zip

      - name: Extract pre-built ANGLE static libraries
        run: Expand-Archive -Force angle/angle.zip ${{ github.workspace }}/

      - name: Download pre-built AccessKit
        uses: dsaltares/fetch-gh-release-asset@1.1.2
        with:
          repo: AccessKit/accesskit-c
          version: tags/0.15.1
          file: accesskit-c-0.15.1.zip
          target: accesskit-c-0.15.1/accesskit_c.zip

      - name: Extract pre-built AccessKit
        run: unzip -o accesskit-c-0.15.1/accesskit_c.zip

      - name: Compilation
        uses: ./.github/actions/godot-build
        with:
          sconsflags: ${{ env.SCONSFLAGS }} ${{ matrix.sconsflags }}
          platform: windows
          target: ${{ matrix.target }}
          tests: ${{ matrix.tests }}

      - name: Generate Glue Code
        if: ${{ matrix.cache-name == 'windows-editor-mono' }}
        run: ./bin/redot.windows.editor.x86_64.mono.exe --headless --generate-mono-glue modules/mono/glue

      - name: Build GodotSharp
        if: ${{ matrix.cache-name == 'windows-editor-mono' }}
        run: python modules/mono/build_scripts/build_assemblies.py --godot-output-dir=./bin

      - name: Save Godot build cache
        uses: ./.github/actions/godot-cache-save
        with:
          cache-name: ${{ matrix.cache-name }}
        continue-on-error: true

      - name: Prepare artifact
        if: matrix.compiler == 'msvc'
        run: |
          Remove-Item bin/* -Include *.exp,*.lib,*.pdb -Force

      - name: Upload artifact
        if: matrix.compiler == 'msvc'
        uses: ./.github/actions/upload-artifact
        with:
          name: ${{ matrix.cache-name }}

      - name: Unit tests
        if: matrix.tests
        run: |
          ${{ matrix.bin }} --version
          ${{ matrix.bin }} --help
          ${{ matrix.bin }} --test --force-colors
