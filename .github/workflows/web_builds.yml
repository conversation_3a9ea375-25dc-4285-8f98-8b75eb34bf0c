name: 🌐 Web Builds
on:
  workflow_call:

# Global Settings
env:
  SCONSFLAGS: verbose=yes warnings=extra werror=yes debug_symbols=no use_closure_compiler=yes strict_checks=yes
  EM_VERSION: 3.1.64

jobs:
  web-template:
    runs-on: ubuntu-24.04
    name: ${{ matrix.name }}
    timeout-minutes: 60
    strategy:
      fail-fast: false
      matrix:
        include:
          - name: Template w/ threads (target=template_release, threads=yes)
            cache-name: web-template
            target: template_release
            sconsflags: threads=yes
            tests: false
            artifact: true

          - name: Template w/o threads (target=template_release, threads=no)
            cache-name: web-nothreads-template
            target: template_release
            sconsflags: threads=no
            tests: false
            artifact: true

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Set up Emscripten latest
        uses: mymindstorm/setup-emsdk@v14
        with:
          version: ${{ env.EM_VERSION }}
          no-cache: true

      - name: Verify Emscripten setup
        run: |
          emcc -v

      - name: <PERSON>ore <PERSON> build cache
        uses: ./.github/actions/godot-cache-restore
        with:
          cache-name: ${{ matrix.cache-name }}
        continue-on-error: true

      - name: Setup Python and SCons
        uses: ./.github/actions/godot-deps

      - name: Compilation
        uses: ./.github/actions/godot-build
        with:
          sconsflags: ${{ env.SCONSFLAGS }} ${{ matrix.sconsflags }}
          platform: web
          target: ${{ matrix.target }}
          tests: ${{ matrix.tests }}

      - name: Save Godot build cache
        uses: ./.github/actions/godot-cache-save
        with:
          cache-name: ${{ matrix.cache-name }}
        continue-on-error: true

      - name: Upload artifact
        uses: ./.github/actions/upload-artifact
        if: matrix.artifact
        with:
          name: ${{ matrix.cache-name }}
