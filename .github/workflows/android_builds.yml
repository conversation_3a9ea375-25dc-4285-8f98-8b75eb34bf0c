name: 🤖 Android Builds
on:
  workflow_call:

# Global Settings
env:
  SCONSFLAGS: verbose=yes warnings=extra werror=yes debug_symbols=no module_text_server_fb_enabled=yes strict_checks=yes

jobs:
  build-android:
    runs-on: ubuntu-24.04
    name: ${{ matrix.name }}
    timeout-minutes: 60
    strategy:
      fail-fast: false
      matrix:
        include:
          - name: Editor (target=editor)
            cache-name: android-editor
            target: editor
            tests: false
            sconsflags: arch=arm64 production=yes swappy=yes

          - name: Template arm32 (target=template_release, arch=arm32)
            cache-name: android-template-arm32
            target: template_release
            tests: false
            sconsflags: arch=arm32 swappy=yes

          - name: Template arm64 (target=template_release, arch=arm64)
            cache-name: android-template-arm64
            target: template_release
            tests: false
            sconsflags: arch=arm64 swappy=yes

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Set up Java 17
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 17

      - name: <PERSON><PERSON> build cache
        uses: ./.github/actions/godot-cache-restore
        with:
          cache-name: ${{ matrix.cache-name }}
        continue-on-error: true

      - name: Setup Python and SCons
        uses: ./.github/actions/godot-deps

      - name: Download pre-built Android Swappy Frame Pacing Library
        uses: dsaltares/fetch-gh-release-asset@1.1.2
        with:
          repo: godotengine/godot-swappy
          version: tags/from-source-2025-01-31
          file: godot-swappy.7z
          target: swappy/godot-swappy.7z

      - name: Extract pre-built Android Swappy Frame Pacing Library
        run: 7za x -y swappy/godot-swappy.7z -o${{github.workspace}}/thirdparty/swappy-frame-pacing

      - name: Compilation
        uses: ./.github/actions/godot-build
        with:
          sconsflags: ${{ env.SCONSFLAGS }} ${{ matrix.sconsflags }}
          platform: android
          target: ${{ matrix.target }}
          tests: ${{ matrix.tests }}

      - name: Save Godot build cache
        uses: ./.github/actions/godot-cache-save
        with:
          cache-name: ${{ matrix.cache-name }}
        continue-on-error: true

      - name: Generate Godot templates
        if: matrix.target == 'template_release'
        run: |
          cd platform/android/java
          ./gradlew generateGodotTemplates
          cd ../../..
          ls -l bin/

      - name: Generate Godot editor
        if: matrix.target == 'editor'
        run: |
          cd platform/android/java
          ./gradlew generateGodotEditor
          ./gradlew generateGodotHorizonOSEditor
          ./gradlew generateGodotPicoOSEditor
          cd ../../..
          ls -l bin/android_editor_builds/

          # Separate different editors for multiple artifacts
          mkdir horizonos
          mv bin/android_editor_builds/*-horizonos-* horizonos
          mkdir picoos
          mv bin/android_editor_builds/*-picoos-* picoos

      - name: Upload artifact
        uses: ./.github/actions/upload-artifact
        with:
          name: ${{ matrix.cache-name }}

      - name: Upload artifact (Horizon OS)
        if: matrix.target == 'editor'
        uses: ./.github/actions/upload-artifact
        with:
          name: ${{ matrix.cache-name }}-horizonos
          path: horizonos

      - name: Upload artifact (PICO OS)
        if: matrix.target == 'editor'
        uses: ./.github/actions/upload-artifact
        with:
          name: ${{ matrix.cache-name }}-picoos
          path: picoos
