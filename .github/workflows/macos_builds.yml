name: 🍎 macOS Builds
on:
  workflow_call:

# Global Settings
env:
  SCONSFLAGS: verbose=yes warnings=extra werror=yes module_text_server_fb_enabled=yes strict_checks=yes "accesskit_sdk_path=${{ github.workspace }}/accesskit-c-0.15.1/"

jobs:
  build-macos:
    runs-on: macos-latest
    name: ${{ matrix.name }}
    timeout-minutes: 120
    strategy:
      fail-fast: false
      matrix:
        include:
          - name: Editor (target=editor, tests=yes)
            cache-name: macos-editor
            target: editor
            tests: true
            bin: ./bin/redot.macos.editor.universal

          - name: Template (target=template_release, tests=yes)
            cache-name: macos-template
            target: template_release
            tests: true
            sconsflags: debug_symbols=no
            bin: ./bin/redot.macos.template_release.universal

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Restore Godot build cache
        uses: ./.github/actions/godot-cache-restore
        with:
          cache-name: ${{ matrix.cache-name }}
        continue-on-error: true

      - name: Setup Python and SCons
        uses: ./.github/actions/godot-deps

      - name: Download pre-built AccessKit
        uses: dsaltares/fetch-gh-release-asset@1.1.2
        with:
          repo: AccessKit/accesskit-c
          version: tags/0.15.1
          file: accesskit-c-0.15.1.zip
          target: accesskit-c-0.15.1/accesskit_c.zip

      - name: Extract pre-built AccessKit
        run: unzip -o accesskit-c-0.15.1/accesskit_c.zip

      - name: Setup Vulkan SDK
        id: vulkan-sdk
        run: |
          if sh misc/scripts/install_vulkan_sdk_macos.sh; then
            echo "VULKAN_ENABLED=yes" >> "$GITHUB_OUTPUT"
          else
            echo "::warning::macOS: Vulkan SDK installation failed, building without Vulkan support."
            echo "VULKAN_ENABLED=no" >> "$GITHUB_OUTPUT"
          fi
        continue-on-error: true

      - name: Compilation (x86_64)
        uses: ./.github/actions/godot-build
        with:
          sconsflags: ${{ env.SCONSFLAGS }} arch=x86_64 vulkan=${{ steps.vulkan-sdk.outputs.VULKAN_ENABLED }}
          platform: macos
          target: ${{ matrix.target }}
          tests: ${{ matrix.tests }}

      - name: Compilation (arm64)
        uses: ./.github/actions/godot-build
        with:
          sconsflags: ${{ env.SCONSFLAGS }} arch=arm64 vulkan=${{ steps.vulkan-sdk.outputs.VULKAN_ENABLED }}
          platform: macos
          target: ${{ matrix.target }}
          tests: ${{ matrix.tests }}

      - name: Save Godot build cache
        uses: ./.github/actions/godot-cache-save
        with:
          cache-name: ${{ matrix.cache-name }}
        continue-on-error: true

      - name: Prepare artifact
        run: |
          lipo -create ./bin/redot.macos.${{ matrix.target }}.x86_64 ./bin/redot.macos.${{ matrix.target }}.arm64 -output ./bin/redot.macos.${{ matrix.target }}.universal
          rm ./bin/redot.macos.${{ matrix.target }}.x86_64 ./bin/redot.macos.${{ matrix.target }}.arm64
          strip bin/redot.*
          chmod +x bin/redot.*

      - name: Upload artifact
        uses: ./.github/actions/upload-artifact
        with:
          name: ${{ matrix.cache-name }}

      - name: Unit tests
        if: matrix.tests
        run: |
          ${{ matrix.bin }} --version
          ${{ matrix.bin }} --help
          ${{ matrix.bin }} --test --force-colors
