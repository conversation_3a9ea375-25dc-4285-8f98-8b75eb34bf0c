name: 📊 Static Checks
on:
  workflow_call:

env:
  DISABLE_REDOT_GET_CHANGED_FILES: false

jobs:
  static-checks:
    name: Code style, file formatting, and docs
    runs-on: ubuntu-24.04
    timeout-minutes: 30
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      # This needs to happen before Python and npm execution; it must happen before any extra files are written.
      - name: .gitignore checks (gitignore_check.sh)
        run: |
          bash ./misc/scripts/gitignore_check.sh

      - name: Get changed files
        if: ${{ env.DISABLE_REDOT_GET_CHANGED_FILES != 'true' }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          if [ "${{ github.event_name }}" == "pull_request" ]; then
            files=$(git diff-tree --no-commit-id --name-only -r HEAD^1..HEAD 2> /dev/null || true)
          elif [ "${{ github.event_name }}" == "push" -a "${{ github.event.forced }}" == "false" -a "${{ github.event.created }}" == "false" ]; then
            files=$(git diff-tree --no-commit-id --name-only -r ${{ github.event.before }}..${{ github.event.after }} 2> /dev/null || true)
          fi
          files=$(echo "$files" | xargs -I {} sh -c 'echo "\"./{}\""' | tr '\n' ' ')
          echo "CHANGED_FILES=$files" >> $GITHUB_ENV

      - name: Style checks via pre-commit
        if: ${{ env.DISABLE_REDOT_GET_CHANGED_FILES != 'true' }}
        uses: pre-commit/action@v3.0.1
        with:
          extra_args: --files ${{ env.CHANGED_FILES }}

      - name: Style checks via pre-commit
        if: ${{ env.DISABLE_REDOT_GET_CHANGED_FILES == 'true' }}
        uses: pre-commit/action@v3.0.1

      - name: Class reference schema checks
        run: |
          sudo apt update
          sudo apt install -y libxml2-utils
          xmllint --quiet --noout --schema doc/class.xsd doc/classes/*.xml modules/*/doc_classes/*.xml platform/*/doc_classes/*.xml

      - name: Run C compiler on `gdextension_interface.h`
        run: |
          gcc -c core/extension/gdextension_interface.h
