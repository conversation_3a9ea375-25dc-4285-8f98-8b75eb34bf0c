load("//bazel:rex_engine.bzl", "rex_engine_library")

package(default_visibility = ["//visibility:public"])

# Thirdparty libraries used by core
rex_engine_library(
    name = "thirdparty_misc",
    srcs = [
        "//thirdparty/misc:fastlz.c",
        "//thirdparty/misc:r128.c", 
        "//thirdparty/misc:smaz.c",
        "//thirdparty/misc:pcg.cpp",
        "//thirdparty/misc:polypartition.cpp",
        "//thirdparty/misc:smolv.cpp",
    ],
    hdrs = [
        "//thirdparty/misc:fastlz.h",
        "//thirdparty/misc:r128.h",
        "//thirdparty/misc:smaz.h", 
        "//thirdparty/misc:pcg.h",
        "//thirdparty/misc:polypartition.h",
        "//thirdparty/misc:smolv.h",
    ],
    includes = ["//thirdparty/misc"],
)

rex_engine_library(
    name = "thirdparty_brotli",
    srcs = select({
        "//bazel:builtin_brotli": [
            "//thirdparty/brotli/common:constants.c",
            "//thirdparty/brotli/common:context.c",
            "//thirdparty/brotli/common:dictionary.c",
            "//thirdparty/brotli/common:platform.c",
            "//thirdparty/brotli/common:shared_dictionary.c",
            "//thirdparty/brotli/common:transform.c",
            "//thirdparty/brotli/dec:bit_reader.c",
            "//thirdparty/brotli/dec:decode.c",
            "//thirdparty/brotli/dec:huffman.c",
            "//thirdparty/brotli/dec:state.c",
        ],
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:builtin_brotli": ["//thirdparty/brotli/include/brotli:decode.h"],
        "//conditions:default": [],
    }),
    includes = select({
        "//bazel:builtin_brotli": ["//thirdparty/brotli/include"],
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:builtin_brotli": ["BROTLI_ENABLED"],
        "//conditions:default": [],
    }),
)

rex_engine_library(
    name = "thirdparty_clipper2",
    srcs = select({
        "//bazel:builtin_clipper2": [
            "//thirdparty/clipper2/src:clipper.engine.cpp",
            "//thirdparty/clipper2/src:clipper.offset.cpp", 
            "//thirdparty/clipper2/src:clipper.rectclip.cpp",
        ],
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:builtin_clipper2": ["//thirdparty/clipper2/include/clipper2:clipper.h"],
        "//conditions:default": [],
    }),
    includes = select({
        "//bazel:builtin_clipper2": ["//thirdparty/clipper2/include"],
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:builtin_clipper2": ["CLIPPER2_ENABLED"],
        "//conditions:default": [],
    }),
)

rex_engine_library(
    name = "thirdparty_zlib",
    srcs = select({
        "//bazel:builtin_zlib": [
            "//thirdparty/zlib:adler32.c",
            "//thirdparty/zlib:compress.c",
            "//thirdparty/zlib:crc32.c",
            "//thirdparty/zlib:deflate.c",
            "//thirdparty/zlib:inffast.c",
            "//thirdparty/zlib:inflate.c",
            "//thirdparty/zlib:inftrees.c",
            "//thirdparty/zlib:trees.c",
            "//thirdparty/zlib:uncompr.c",
            "//thirdparty/zlib:zutil.c",
        ],
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:builtin_zlib": [
            "//thirdparty/zlib:zlib.h",
            "//thirdparty/zlib:zconf.h",
        ],
        "//conditions:default": [],
    }),
    includes = select({
        "//bazel:builtin_zlib": ["//thirdparty/zlib"],
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:builtin_zlib": ["ZLIB_ENABLED"],
        "//conditions:default": [],
    }),
)

rex_engine_library(
    name = "thirdparty_minizip",
    srcs = [
        "//thirdparty/minizip:ioapi.c",
        "//thirdparty/minizip:unzip.c",
        "//thirdparty/minizip:zip.c",
    ],
    hdrs = [
        "//thirdparty/minizip:ioapi.h",
        "//thirdparty/minizip:unzip.h",
        "//thirdparty/minizip:zip.h",
    ],
    includes = ["//thirdparty/minizip"],
    deps = [":thirdparty_zlib"],
)

rex_engine_library(
    name = "thirdparty_zstd",
    srcs = select({
        "//bazel:builtin_zstd": [
            "//thirdparty/zstd/common:debug.c",
            "//thirdparty/zstd/common:entropy_common.c",
            "//thirdparty/zstd/common:error_private.c",
            "//thirdparty/zstd/common:fse_decompress.c",
            "//thirdparty/zstd/common:pool.c",
            "//thirdparty/zstd/common:threading.c",
            "//thirdparty/zstd/common:xxhash.c",
            "//thirdparty/zstd/common:zstd_common.c",
            "//thirdparty/zstd/compress:fse_compress.c",
            "//thirdparty/zstd/compress:hist.c",
            "//thirdparty/zstd/compress:huf_compress.c",
            "//thirdparty/zstd/compress:zstd_compress.c",
            "//thirdparty/zstd/compress:zstd_double_fast.c",
            "//thirdparty/zstd/compress:zstd_fast.c",
            "//thirdparty/zstd/compress:zstd_lazy.c",
            "//thirdparty/zstd/compress:zstd_ldm.c",
            "//thirdparty/zstd/compress:zstd_opt.c",
            "//thirdparty/zstd/compress:zstdmt_compress.c",
            "//thirdparty/zstd/compress:zstd_compress_literals.c",
            "//thirdparty/zstd/compress:zstd_compress_sequences.c",
            "//thirdparty/zstd/compress:zstd_compress_superblock.c",
            "//thirdparty/zstd/decompress:huf_decompress.c",
            "//thirdparty/zstd/decompress:zstd_ddict.c",
            "//thirdparty/zstd/decompress:zstd_decompress_block.c",
            "//thirdparty/zstd/decompress:zstd_decompress.c",
        ] + select({
            "@platforms//cpu:x86_64": ["//thirdparty/zstd/decompress:huf_decompress_amd64.S"],
            "//conditions:default": [],
        }),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:builtin_zstd": [
            "//thirdparty/zstd:zstd.h",
            "//thirdparty/zstd:zstd_errors.h",
        ],
        "//conditions:default": [],
    }),
    includes = select({
        "//bazel:builtin_zstd": [
            "//thirdparty/zstd",
            "//thirdparty/zstd/common",
        ],
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:builtin_zstd": ["ZSTD_STATIC_LINKING_ONLY"],
        "//conditions:default": [],
    }),
)

# Generated files
genrule(
    name = "disabled_classes_gen",
    outs = ["disabled_classes.gen.h"],
    cmd = "echo '/* Generated disabled classes header */' > $@",
)

genrule(
    name = "version_generated_gen",
    outs = ["version_generated.gen.h"],
    cmd = "$(location //bazel:generate_version) > $@",
    tools = ["//bazel:generate_version"],
)

genrule(
    name = "version_hash_gen",
    outs = ["version_hash.gen.cpp"],
    cmd = "$(location //bazel:generate_version_hash) > $@",
    tools = ["//bazel:generate_version_hash"],
)

genrule(
    name = "script_encryption_key_gen",
    outs = ["script_encryption_key.gen.cpp"],
    cmd = "echo '/* Generated script encryption key */' > $@",
)

genrule(
    name = "certs_compressed_gen",
    outs = ["io/certs_compressed.gen.h"],
    cmd = "echo '/* Generated certificates header */' > $@",
)

genrule(
    name = "redot_authors_gen",
    outs = ["redot_authors.gen.h"],
    cmd = "echo '/* Generated ReX authors header */' > $@",
)

genrule(
    name = "authors_gen",
    outs = ["authors.gen.h"],
    cmd = "echo '/* Generated authors header */' > $@",
)

genrule(
    name = "donors_gen",
    outs = ["donors.gen.h"],
    cmd = "echo '/* Generated donors header */' > $@",
)

genrule(
    name = "license_gen",
    outs = ["license.gen.h"],
    cmd = "echo '/* Generated license header */' > $@",
)

# Core subdirectories
rex_engine_library(
    name = "core_os",
    srcs = glob(["os/*.cpp"]),
    hdrs = glob(["os/*.h"]),
    deps = [":thirdparty_misc"],
)

rex_engine_library(
    name = "core_math",
    srcs = glob(["math/*.cpp"]),
    hdrs = glob(["math/*.h"]),
)

rex_engine_library(
    name = "core_crypto",
    srcs = glob(["crypto/*.cpp"]),
    hdrs = glob(["crypto/*.h"]),
    deps = [":thirdparty_misc"],
)

rex_engine_library(
    name = "core_io",
    srcs = glob(["io/*.cpp"]) + [":certs_compressed_gen"],
    hdrs = glob(["io/*.h"]),
    deps = [
        ":core_math",
        ":core_os",
        ":thirdparty_zlib",
        ":thirdparty_minizip",
        ":thirdparty_zstd",
        ":thirdparty_brotli",
    ],
)

rex_engine_library(
    name = "core_debugger",
    srcs = glob(["debugger/*.cpp"]),
    hdrs = glob(["debugger/*.h"]),
    deps = [":core_io"],
)

rex_engine_library(
    name = "core_input",
    srcs = glob(["input/*.cpp"]),
    hdrs = glob(["input/*.h"]),
    deps = [":core_math"],
)

rex_engine_library(
    name = "core_variant",
    srcs = glob(["variant/*.cpp"]),
    hdrs = glob(["variant/*.h"]),
    deps = [
        ":core_math",
        ":core_io",
    ],
)

rex_engine_library(
    name = "core_extension",
    srcs = glob(["extension/*.cpp"]),
    hdrs = glob(["extension/*.h"]),
    deps = [":core_variant"],
)

rex_engine_library(
    name = "core_object",
    srcs = glob(["object/*.cpp"]),
    hdrs = glob(["object/*.h"]),
    deps = [
        ":core_variant",
        ":core_extension",
    ],
)

rex_engine_library(
    name = "core_templates",
    srcs = glob(["templates/*.cpp"]),
    hdrs = glob(["templates/*.h"]),
    deps = [":core_object"],
)

rex_engine_library(
    name = "core_string",
    srcs = glob(["string/*.cpp"]),
    hdrs = glob(["string/*.h"]),
    deps = [":core_math"],
)

rex_engine_library(
    name = "core_config",
    srcs = glob(["config/*.cpp"]),
    hdrs = glob(["config/*.h"]),
    deps = [":core_io"],
)

rex_engine_library(
    name = "core_error",
    srcs = glob(["error/*.cpp"]),
    hdrs = glob(["error/*.h"]),
)

# Main core library
rex_engine_library(
    name = "core",
    srcs = glob(["*.cpp"]) + [
        ":version_hash_gen",
        ":script_encryption_key_gen",
    ],
    hdrs = glob(["*.h"]) + [
        ":disabled_classes_gen",
        ":version_generated_gen",
        ":redot_authors_gen",
        ":authors_gen",
        ":donors_gen",
        ":license_gen",
    ],
    deps = [
        ":core_os",
        ":core_math",
        ":core_crypto",
        ":core_io",
        ":core_debugger",
        ":core_input",
        ":core_variant",
        ":core_extension",
        ":core_object",
        ":core_templates",
        ":core_string",
        ":core_config",
        ":core_error",
        ":thirdparty_misc",
        ":thirdparty_clipper2",
    ],
)
