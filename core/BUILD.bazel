# Minimal core build for testing

package(default_visibility = ["//visibility:public"])

# Minimal core build with essential files
cc_library(
    name = "core",
    srcs = [
        "core_constants.cpp",
        "string/string_name.cpp",
        "string/ustring.cpp",
        "string/print_string.cpp",
        "os/memory.cpp",
        "error/error_macros.cpp",
    ],
    hdrs = [
        "core_constants.h",
        "string/string_name.h",
        "string/ustring.h",
        "string/print_string.h",
        "os/memory.h",
        "error/error_macros.h",
        "typedefs.h",
        "version.h",
    ],
    defines = [
        "UNIX_ENABLED",
        "LINUXBSD_ENABLED",
    ],
    copts = [
        "-std=c++17",
        "-Wall",
        "-Wextra",
        "-I.",
    ],
    includes = ["."],
)
