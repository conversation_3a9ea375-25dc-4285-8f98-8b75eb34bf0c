/**************************************************************************/
/*  char_range.inc                                                        */
/**************************************************************************/
/*                         This file is part of:                          */
/*                             REDOT ENGINE                               */
/*                        https://redotengine.org                         */
/**************************************************************************/
/* Copyright (c) 2024-present Redot Engine contributors                   */
/*                                          (see REDOT_AUTHORS.md)        */
/* Copyright (c) 2014-present Godot Engine contributors (see AUTHORS.md). */
/* Copyright (c) 2007-2014 <PERSON>, <PERSON>.                  */
/*                                                                        */
/* Permission is hereby granted, free of charge, to any person obtaining  */
/* a copy of this software and associated documentation files (the        */
/* "Software"), to deal in the Software without restriction, including    */
/* without limitation the rights to use, copy, modify, merge, publish,    */
/* distribute, sublicense, and/or sell copies of the Software, and to     */
/* permit persons to whom the Software is furnished to do so, subject to  */
/* the following conditions:                                              */
/*                                                                        */
/* The above copyright notice and this permission notice shall be         */
/* included in all copies or substantial portions of the Software.        */
/*                                                                        */
/* THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,        */
/* EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF     */
/* MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. */
/* IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY   */
/* CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,   */
/* TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE      */
/* SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.                 */
/**************************************************************************/

#pragma once

#include "../typedefs.h"

// Unicode Derived Core Properties
// Source: https://www.unicode.org/Public/16.0.0/ucd/DerivedCoreProperties.txt

struct CharRange {
	char32_t start;
	char32_t end;
};

constexpr inline CharRange xid_start[] = {
	{ 0x41, 0x5a },
	{ 0x5f, 0x5f },
	{ 0x61, 0x7a },
	{ 0xaa, 0xaa },
	{ 0xb5, 0xb5 },
	{ 0xba, 0xba },
	{ 0xc0, 0xd6 },
	{ 0xd8, 0xf6 },
	{ 0xf8, 0x2c1 },
	{ 0x2c6, 0x2d1 },
	{ 0x2e0, 0x2e4 },
	{ 0x2ec, 0x2ec },
	{ 0x2ee, 0x2ee },
	{ 0x370, 0x374 },
	{ 0x376, 0x377 },
	{ 0x37b, 0x37d },
	{ 0x37f, 0x37f },
	{ 0x386, 0x386 },
	{ 0x388, 0x38a },
	{ 0x38c, 0x38c },
	{ 0x38e, 0x3a1 },
	{ 0x3a3, 0x3f5 },
	{ 0x3f7, 0x481 },
	{ 0x48a, 0x52f },
	{ 0x531, 0x556 },
	{ 0x559, 0x559 },
	{ 0x560, 0x588 },
	{ 0x5d0, 0x5ea },
	{ 0x5ef, 0x5f2 },
	{ 0x620, 0x64a },
	{ 0x66e, 0x66f },
	{ 0x671, 0x6d3 },
	{ 0x6d5, 0x6d5 },
	{ 0x6e5, 0x6e6 },
	{ 0x6ee, 0x6ef },
	{ 0x6fa, 0x6fc },
	{ 0x6ff, 0x6ff },
	{ 0x710, 0x710 },
	{ 0x712, 0x72f },
	{ 0x74d, 0x7a5 },
	{ 0x7b1, 0x7b1 },
	{ 0x7ca, 0x7ea },
	{ 0x7f4, 0x7f5 },
	{ 0x7fa, 0x7fa },
	{ 0x800, 0x815 },
	{ 0x81a, 0x81a },
	{ 0x824, 0x824 },
	{ 0x828, 0x828 },
	{ 0x840, 0x858 },
	{ 0x860, 0x86a },
	{ 0x870, 0x887 },
	{ 0x889, 0x88e },
	{ 0x8a0, 0x8c9 },
	{ 0x904, 0x939 },
	{ 0x93d, 0x93d },
	{ 0x950, 0x950 },
	{ 0x958, 0x961 },
	{ 0x971, 0x980 },
	{ 0x985, 0x98c },
	{ 0x98f, 0x990 },
	{ 0x993, 0x9a8 },
	{ 0x9aa, 0x9b0 },
	{ 0x9b2, 0x9b2 },
	{ 0x9b6, 0x9b9 },
	{ 0x9bd, 0x9bd },
	{ 0x9ce, 0x9ce },
	{ 0x9dc, 0x9dd },
	{ 0x9df, 0x9e1 },
	{ 0x9f0, 0x9f1 },
	{ 0x9fc, 0x9fc },
	{ 0xa05, 0xa0a },
	{ 0xa0f, 0xa10 },
	{ 0xa13, 0xa28 },
	{ 0xa2a, 0xa30 },
	{ 0xa32, 0xa33 },
	{ 0xa35, 0xa36 },
	{ 0xa38, 0xa39 },
	{ 0xa59, 0xa5c },
	{ 0xa5e, 0xa5e },
	{ 0xa72, 0xa74 },
	{ 0xa85, 0xa8d },
	{ 0xa8f, 0xa91 },
	{ 0xa93, 0xaa8 },
	{ 0xaaa, 0xab0 },
	{ 0xab2, 0xab3 },
	{ 0xab5, 0xab9 },
	{ 0xabd, 0xabd },
	{ 0xad0, 0xad0 },
	{ 0xae0, 0xae1 },
	{ 0xaf9, 0xaf9 },
	{ 0xb05, 0xb0c },
	{ 0xb0f, 0xb10 },
	{ 0xb13, 0xb28 },
	{ 0xb2a, 0xb30 },
	{ 0xb32, 0xb33 },
	{ 0xb35, 0xb39 },
	{ 0xb3d, 0xb3d },
	{ 0xb5c, 0xb5d },
	{ 0xb5f, 0xb61 },
	{ 0xb71, 0xb71 },
	{ 0xb83, 0xb83 },
	{ 0xb85, 0xb8a },
	{ 0xb8e, 0xb90 },
	{ 0xb92, 0xb95 },
	{ 0xb99, 0xb9a },
	{ 0xb9c, 0xb9c },
	{ 0xb9e, 0xb9f },
	{ 0xba3, 0xba4 },
	{ 0xba8, 0xbaa },
	{ 0xbae, 0xbb9 },
	{ 0xbd0, 0xbd0 },
	{ 0xc05, 0xc0c },
	{ 0xc0e, 0xc10 },
	{ 0xc12, 0xc28 },
	{ 0xc2a, 0xc39 },
	{ 0xc3d, 0xc3d },
	{ 0xc58, 0xc5a },
	{ 0xc5d, 0xc5d },
	{ 0xc60, 0xc61 },
	{ 0xc80, 0xc80 },
	{ 0xc85, 0xc8c },
	{ 0xc8e, 0xc90 },
	{ 0xc92, 0xca8 },
	{ 0xcaa, 0xcb3 },
	{ 0xcb5, 0xcb9 },
	{ 0xcbd, 0xcbd },
	{ 0xcdd, 0xcde },
	{ 0xce0, 0xce1 },
	{ 0xcf1, 0xcf2 },
	{ 0xd04, 0xd0c },
	{ 0xd0e, 0xd10 },
	{ 0xd12, 0xd3a },
	{ 0xd3d, 0xd3d },
	{ 0xd4e, 0xd4e },
	{ 0xd54, 0xd56 },
	{ 0xd5f, 0xd61 },
	{ 0xd7a, 0xd7f },
	{ 0xd85, 0xd96 },
	{ 0xd9a, 0xdb1 },
	{ 0xdb3, 0xdbb },
	{ 0xdbd, 0xdbd },
	{ 0xdc0, 0xdc6 },
	{ 0xe01, 0xe30 },
	{ 0xe32, 0xe32 },
	{ 0xe40, 0xe46 },
	{ 0xe81, 0xe82 },
	{ 0xe84, 0xe84 },
	{ 0xe86, 0xe8a },
	{ 0xe8c, 0xea3 },
	{ 0xea5, 0xea5 },
	{ 0xea7, 0xeb0 },
	{ 0xeb2, 0xeb2 },
	{ 0xebd, 0xebd },
	{ 0xec0, 0xec4 },
	{ 0xec6, 0xec6 },
	{ 0xedc, 0xedf },
	{ 0xf00, 0xf00 },
	{ 0xf40, 0xf47 },
	{ 0xf49, 0xf6c },
	{ 0xf88, 0xf8c },
	{ 0x1000, 0x102a },
	{ 0x103f, 0x103f },
	{ 0x1050, 0x1055 },
	{ 0x105a, 0x105d },
	{ 0x1061, 0x1061 },
	{ 0x1065, 0x1066 },
	{ 0x106e, 0x1070 },
	{ 0x1075, 0x1081 },
	{ 0x108e, 0x108e },
	{ 0x10a0, 0x10c5 },
	{ 0x10c7, 0x10c7 },
	{ 0x10cd, 0x10cd },
	{ 0x10d0, 0x10fa },
	{ 0x10fc, 0x1248 },
	{ 0x124a, 0x124d },
	{ 0x1250, 0x1256 },
	{ 0x1258, 0x1258 },
	{ 0x125a, 0x125d },
	{ 0x1260, 0x1288 },
	{ 0x128a, 0x128d },
	{ 0x1290, 0x12b0 },
	{ 0x12b2, 0x12b5 },
	{ 0x12b8, 0x12be },
	{ 0x12c0, 0x12c0 },
	{ 0x12c2, 0x12c5 },
	{ 0x12c8, 0x12d6 },
	{ 0x12d8, 0x1310 },
	{ 0x1312, 0x1315 },
	{ 0x1318, 0x135a },
	{ 0x1380, 0x138f },
	{ 0x13a0, 0x13f5 },
	{ 0x13f8, 0x13fd },
	{ 0x1401, 0x166c },
	{ 0x166f, 0x167f },
	{ 0x1681, 0x169a },
	{ 0x16a0, 0x16ea },
	{ 0x16ee, 0x16f8 },
	{ 0x1700, 0x1711 },
	{ 0x171f, 0x1731 },
	{ 0x1740, 0x1751 },
	{ 0x1760, 0x176c },
	{ 0x176e, 0x1770 },
	{ 0x1780, 0x17b3 },
	{ 0x17d7, 0x17d7 },
	{ 0x17dc, 0x17dc },
	{ 0x1820, 0x1878 },
	{ 0x1880, 0x18a8 },
	{ 0x18aa, 0x18aa },
	{ 0x18b0, 0x18f5 },
	{ 0x1900, 0x191e },
	{ 0x1950, 0x196d },
	{ 0x1970, 0x1974 },
	{ 0x1980, 0x19ab },
	{ 0x19b0, 0x19c9 },
	{ 0x1a00, 0x1a16 },
	{ 0x1a20, 0x1a54 },
	{ 0x1aa7, 0x1aa7 },
	{ 0x1b05, 0x1b33 },
	{ 0x1b45, 0x1b4c },
	{ 0x1b83, 0x1ba0 },
	{ 0x1bae, 0x1baf },
	{ 0x1bba, 0x1be5 },
	{ 0x1c00, 0x1c23 },
	{ 0x1c4d, 0x1c4f },
	{ 0x1c5a, 0x1c7d },
	{ 0x1c80, 0x1c8a },
	{ 0x1c90, 0x1cba },
	{ 0x1cbd, 0x1cbf },
	{ 0x1ce9, 0x1cec },
	{ 0x1cee, 0x1cf3 },
	{ 0x1cf5, 0x1cf6 },
	{ 0x1cfa, 0x1cfa },
	{ 0x1d00, 0x1dbf },
	{ 0x1e00, 0x1f15 },
	{ 0x1f18, 0x1f1d },
	{ 0x1f20, 0x1f45 },
	{ 0x1f48, 0x1f4d },
	{ 0x1f50, 0x1f57 },
	{ 0x1f59, 0x1f59 },
	{ 0x1f5b, 0x1f5b },
	{ 0x1f5d, 0x1f5d },
	{ 0x1f5f, 0x1f7d },
	{ 0x1f80, 0x1fb4 },
	{ 0x1fb6, 0x1fbc },
	{ 0x1fbe, 0x1fbe },
	{ 0x1fc2, 0x1fc4 },
	{ 0x1fc6, 0x1fcc },
	{ 0x1fd0, 0x1fd3 },
	{ 0x1fd6, 0x1fdb },
	{ 0x1fe0, 0x1fec },
	{ 0x1ff2, 0x1ff4 },
	{ 0x1ff6, 0x1ffc },
	{ 0x2071, 0x2071 },
	{ 0x207f, 0x207f },
	{ 0x2090, 0x209c },
	{ 0x2102, 0x2102 },
	{ 0x2107, 0x2107 },
	{ 0x210a, 0x2113 },
	{ 0x2115, 0x2115 },
	{ 0x2118, 0x211d },
	{ 0x2124, 0x2124 },
	{ 0x2126, 0x2126 },
	{ 0x2128, 0x2128 },
	{ 0x212a, 0x2139 },
	{ 0x213c, 0x213f },
	{ 0x2145, 0x2149 },
	{ 0x214e, 0x214e },
	{ 0x2160, 0x2188 },
	{ 0x2c00, 0x2ce4 },
	{ 0x2ceb, 0x2cee },
	{ 0x2cf2, 0x2cf3 },
	{ 0x2d00, 0x2d25 },
	{ 0x2d27, 0x2d27 },
	{ 0x2d2d, 0x2d2d },
	{ 0x2d30, 0x2d67 },
	{ 0x2d6f, 0x2d6f },
	{ 0x2d80, 0x2d96 },
	{ 0x2da0, 0x2da6 },
	{ 0x2da8, 0x2dae },
	{ 0x2db0, 0x2db6 },
	{ 0x2db8, 0x2dbe },
	{ 0x2dc0, 0x2dc6 },
	{ 0x2dc8, 0x2dce },
	{ 0x2dd0, 0x2dd6 },
	{ 0x2dd8, 0x2dde },
	{ 0x3005, 0x3007 },
	{ 0x3021, 0x3029 },
	{ 0x3031, 0x3035 },
	{ 0x3038, 0x303c },
	{ 0x3041, 0x3096 },
	{ 0x309d, 0x309f },
	{ 0x30a1, 0x30fa },
	{ 0x30fc, 0x30ff },
	{ 0x3105, 0x312f },
	{ 0x3131, 0x318e },
	{ 0x31a0, 0x31bf },
	{ 0x31f0, 0x31ff },
	{ 0x3400, 0x4dbf },
	{ 0x4e00, 0xa48c },
	{ 0xa4d0, 0xa4fd },
	{ 0xa500, 0xa60c },
	{ 0xa610, 0xa61f },
	{ 0xa62a, 0xa62b },
	{ 0xa640, 0xa66e },
	{ 0xa67f, 0xa69d },
	{ 0xa6a0, 0xa6ef },
	{ 0xa717, 0xa71f },
	{ 0xa722, 0xa788 },
	{ 0xa78b, 0xa7cd },
	{ 0xa7d0, 0xa7d1 },
	{ 0xa7d3, 0xa7d3 },
	{ 0xa7d5, 0xa7dc },
	{ 0xa7f2, 0xa801 },
	{ 0xa803, 0xa805 },
	{ 0xa807, 0xa80a },
	{ 0xa80c, 0xa822 },
	{ 0xa840, 0xa873 },
	{ 0xa882, 0xa8b3 },
	{ 0xa8f2, 0xa8f7 },
	{ 0xa8fb, 0xa8fb },
	{ 0xa8fd, 0xa8fe },
	{ 0xa90a, 0xa925 },
	{ 0xa930, 0xa946 },
	{ 0xa960, 0xa97c },
	{ 0xa984, 0xa9b2 },
	{ 0xa9cf, 0xa9cf },
	{ 0xa9e0, 0xa9e4 },
	{ 0xa9e6, 0xa9ef },
	{ 0xa9fa, 0xa9fe },
	{ 0xaa00, 0xaa28 },
	{ 0xaa40, 0xaa42 },
	{ 0xaa44, 0xaa4b },
	{ 0xaa60, 0xaa76 },
	{ 0xaa7a, 0xaa7a },
	{ 0xaa7e, 0xaaaf },
	{ 0xaab1, 0xaab1 },
	{ 0xaab5, 0xaab6 },
	{ 0xaab9, 0xaabd },
	{ 0xaac0, 0xaac0 },
	{ 0xaac2, 0xaac2 },
	{ 0xaadb, 0xaadd },
	{ 0xaae0, 0xaaea },
	{ 0xaaf2, 0xaaf4 },
	{ 0xab01, 0xab06 },
	{ 0xab09, 0xab0e },
	{ 0xab11, 0xab16 },
	{ 0xab20, 0xab26 },
	{ 0xab28, 0xab2e },
	{ 0xab30, 0xab5a },
	{ 0xab5c, 0xab69 },
	{ 0xab70, 0xabe2 },
	{ 0xac00, 0xd7a3 },
	{ 0xd7b0, 0xd7c6 },
	{ 0xd7cb, 0xd7fb },
	{ 0xf900, 0xfa6d },
	{ 0xfa70, 0xfad9 },
	{ 0xfb00, 0xfb06 },
	{ 0xfb13, 0xfb17 },
	{ 0xfb1d, 0xfb1d },
	{ 0xfb1f, 0xfb28 },
	{ 0xfb2a, 0xfb36 },
	{ 0xfb38, 0xfb3c },
	{ 0xfb3e, 0xfb3e },
	{ 0xfb40, 0xfb41 },
	{ 0xfb43, 0xfb44 },
	{ 0xfb46, 0xfbb1 },
	{ 0xfbd3, 0xfc5d },
	{ 0xfc64, 0xfd3d },
	{ 0xfd50, 0xfd8f },
	{ 0xfd92, 0xfdc7 },
	{ 0xfdf0, 0xfdf9 },
	{ 0xfe71, 0xfe71 },
	{ 0xfe73, 0xfe73 },
	{ 0xfe77, 0xfe77 },
	{ 0xfe79, 0xfe79 },
	{ 0xfe7b, 0xfe7b },
	{ 0xfe7d, 0xfe7d },
	{ 0xfe7f, 0xfefc },
	{ 0xff21, 0xff3a },
	{ 0xff41, 0xff5a },
	{ 0xff66, 0xff9d },
	{ 0xffa0, 0xffbe },
	{ 0xffc2, 0xffc7 },
	{ 0xffca, 0xffcf },
	{ 0xffd2, 0xffd7 },
	{ 0xffda, 0xffdc },
	{ 0x10000, 0x1000b },
	{ 0x1000d, 0x10026 },
	{ 0x10028, 0x1003a },
	{ 0x1003c, 0x1003d },
	{ 0x1003f, 0x1004d },
	{ 0x10050, 0x1005d },
	{ 0x10080, 0x100fa },
	{ 0x10140, 0x10174 },
	{ 0x10280, 0x1029c },
	{ 0x102a0, 0x102d0 },
	{ 0x10300, 0x1031f },
	{ 0x1032d, 0x1034a },
	{ 0x10350, 0x10375 },
	{ 0x10380, 0x1039d },
	{ 0x103a0, 0x103c3 },
	{ 0x103c8, 0x103cf },
	{ 0x103d1, 0x103d5 },
	{ 0x10400, 0x1049d },
	{ 0x104b0, 0x104d3 },
	{ 0x104d8, 0x104fb },
	{ 0x10500, 0x10527 },
	{ 0x10530, 0x10563 },
	{ 0x10570, 0x1057a },
	{ 0x1057c, 0x1058a },
	{ 0x1058c, 0x10592 },
	{ 0x10594, 0x10595 },
	{ 0x10597, 0x105a1 },
	{ 0x105a3, 0x105b1 },
	{ 0x105b3, 0x105b9 },
	{ 0x105bb, 0x105bc },
	{ 0x105c0, 0x105f3 },
	{ 0x10600, 0x10736 },
	{ 0x10740, 0x10755 },
	{ 0x10760, 0x10767 },
	{ 0x10780, 0x10785 },
	{ 0x10787, 0x107b0 },
	{ 0x107b2, 0x107ba },
	{ 0x10800, 0x10805 },
	{ 0x10808, 0x10808 },
	{ 0x1080a, 0x10835 },
	{ 0x10837, 0x10838 },
	{ 0x1083c, 0x1083c },
	{ 0x1083f, 0x10855 },
	{ 0x10860, 0x10876 },
	{ 0x10880, 0x1089e },
	{ 0x108e0, 0x108f2 },
	{ 0x108f4, 0x108f5 },
	{ 0x10900, 0x10915 },
	{ 0x10920, 0x10939 },
	{ 0x10980, 0x109b7 },
	{ 0x109be, 0x109bf },
	{ 0x10a00, 0x10a00 },
	{ 0x10a10, 0x10a13 },
	{ 0x10a15, 0x10a17 },
	{ 0x10a19, 0x10a35 },
	{ 0x10a60, 0x10a7c },
	{ 0x10a80, 0x10a9c },
	{ 0x10ac0, 0x10ac7 },
	{ 0x10ac9, 0x10ae4 },
	{ 0x10b00, 0x10b35 },
	{ 0x10b40, 0x10b55 },
	{ 0x10b60, 0x10b72 },
	{ 0x10b80, 0x10b91 },
	{ 0x10c00, 0x10c48 },
	{ 0x10c80, 0x10cb2 },
	{ 0x10cc0, 0x10cf2 },
	{ 0x10d00, 0x10d23 },
	{ 0x10d4a, 0x10d65 },
	{ 0x10d6f, 0x10d85 },
	{ 0x10e80, 0x10ea9 },
	{ 0x10eb0, 0x10eb1 },
	{ 0x10ec2, 0x10ec4 },
	{ 0x10f00, 0x10f1c },
	{ 0x10f27, 0x10f27 },
	{ 0x10f30, 0x10f45 },
	{ 0x10f70, 0x10f81 },
	{ 0x10fb0, 0x10fc4 },
	{ 0x10fe0, 0x10ff6 },
	{ 0x11003, 0x11037 },
	{ 0x11071, 0x11072 },
	{ 0x11075, 0x11075 },
	{ 0x11083, 0x110af },
	{ 0x110d0, 0x110e8 },
	{ 0x11103, 0x11126 },
	{ 0x11144, 0x11144 },
	{ 0x11147, 0x11147 },
	{ 0x11150, 0x11172 },
	{ 0x11176, 0x11176 },
	{ 0x11183, 0x111b2 },
	{ 0x111c1, 0x111c4 },
	{ 0x111da, 0x111da },
	{ 0x111dc, 0x111dc },
	{ 0x11200, 0x11211 },
	{ 0x11213, 0x1122b },
	{ 0x1123f, 0x11240 },
	{ 0x11280, 0x11286 },
	{ 0x11288, 0x11288 },
	{ 0x1128a, 0x1128d },
	{ 0x1128f, 0x1129d },
	{ 0x1129f, 0x112a8 },
	{ 0x112b0, 0x112de },
	{ 0x11305, 0x1130c },
	{ 0x1130f, 0x11310 },
	{ 0x11313, 0x11328 },
	{ 0x1132a, 0x11330 },
	{ 0x11332, 0x11333 },
	{ 0x11335, 0x11339 },
	{ 0x1133d, 0x1133d },
	{ 0x11350, 0x11350 },
	{ 0x1135d, 0x11361 },
	{ 0x11380, 0x11389 },
	{ 0x1138b, 0x1138b },
	{ 0x1138e, 0x1138e },
	{ 0x11390, 0x113b5 },
	{ 0x113b7, 0x113b7 },
	{ 0x113d1, 0x113d1 },
	{ 0x113d3, 0x113d3 },
	{ 0x11400, 0x11434 },
	{ 0x11447, 0x1144a },
	{ 0x1145f, 0x11461 },
	{ 0x11480, 0x114af },
	{ 0x114c4, 0x114c5 },
	{ 0x114c7, 0x114c7 },
	{ 0x11580, 0x115ae },
	{ 0x115d8, 0x115db },
	{ 0x11600, 0x1162f },
	{ 0x11644, 0x11644 },
	{ 0x11680, 0x116aa },
	{ 0x116b8, 0x116b8 },
	{ 0x11700, 0x1171a },
	{ 0x11740, 0x11746 },
	{ 0x11800, 0x1182b },
	{ 0x118a0, 0x118df },
	{ 0x118ff, 0x11906 },
	{ 0x11909, 0x11909 },
	{ 0x1190c, 0x11913 },
	{ 0x11915, 0x11916 },
	{ 0x11918, 0x1192f },
	{ 0x1193f, 0x1193f },
	{ 0x11941, 0x11941 },
	{ 0x119a0, 0x119a7 },
	{ 0x119aa, 0x119d0 },
	{ 0x119e1, 0x119e1 },
	{ 0x119e3, 0x119e3 },
	{ 0x11a00, 0x11a00 },
	{ 0x11a0b, 0x11a32 },
	{ 0x11a3a, 0x11a3a },
	{ 0x11a50, 0x11a50 },
	{ 0x11a5c, 0x11a89 },
	{ 0x11a9d, 0x11a9d },
	{ 0x11ab0, 0x11af8 },
	{ 0x11bc0, 0x11be0 },
	{ 0x11c00, 0x11c08 },
	{ 0x11c0a, 0x11c2e },
	{ 0x11c40, 0x11c40 },
	{ 0x11c72, 0x11c8f },
	{ 0x11d00, 0x11d06 },
	{ 0x11d08, 0x11d09 },
	{ 0x11d0b, 0x11d30 },
	{ 0x11d46, 0x11d46 },
	{ 0x11d60, 0x11d65 },
	{ 0x11d67, 0x11d68 },
	{ 0x11d6a, 0x11d89 },
	{ 0x11d98, 0x11d98 },
	{ 0x11ee0, 0x11ef2 },
	{ 0x11f02, 0x11f02 },
	{ 0x11f04, 0x11f10 },
	{ 0x11f12, 0x11f33 },
	{ 0x11fb0, 0x11fb0 },
	{ 0x12000, 0x12399 },
	{ 0x12400, 0x1246e },
	{ 0x12480, 0x12543 },
	{ 0x12f90, 0x12ff0 },
	{ 0x13000, 0x1342f },
	{ 0x13441, 0x13446 },
	{ 0x13460, 0x143fa },
	{ 0x14400, 0x14646 },
	{ 0x16100, 0x1611d },
	{ 0x16800, 0x16a38 },
	{ 0x16a40, 0x16a5e },
	{ 0x16a70, 0x16abe },
	{ 0x16ad0, 0x16aed },
	{ 0x16b00, 0x16b2f },
	{ 0x16b40, 0x16b43 },
	{ 0x16b63, 0x16b77 },
	{ 0x16b7d, 0x16b8f },
	{ 0x16d40, 0x16d6c },
	{ 0x16e40, 0x16e7f },
	{ 0x16f00, 0x16f4a },
	{ 0x16f50, 0x16f50 },
	{ 0x16f93, 0x16f9f },
	{ 0x16fe0, 0x16fe1 },
	{ 0x16fe3, 0x16fe3 },
	{ 0x17000, 0x187f7 },
	{ 0x18800, 0x18cd5 },
	{ 0x18cff, 0x18d08 },
	{ 0x1aff0, 0x1aff3 },
	{ 0x1aff5, 0x1affb },
	{ 0x1affd, 0x1affe },
	{ 0x1b000, 0x1b122 },
	{ 0x1b132, 0x1b132 },
	{ 0x1b150, 0x1b152 },
	{ 0x1b155, 0x1b155 },
	{ 0x1b164, 0x1b167 },
	{ 0x1b170, 0x1b2fb },
	{ 0x1bc00, 0x1bc6a },
	{ 0x1bc70, 0x1bc7c },
	{ 0x1bc80, 0x1bc88 },
	{ 0x1bc90, 0x1bc99 },
	{ 0x1d400, 0x1d454 },
	{ 0x1d456, 0x1d49c },
	{ 0x1d49e, 0x1d49f },
	{ 0x1d4a2, 0x1d4a2 },
	{ 0x1d4a5, 0x1d4a6 },
	{ 0x1d4a9, 0x1d4ac },
	{ 0x1d4ae, 0x1d4b9 },
	{ 0x1d4bb, 0x1d4bb },
	{ 0x1d4bd, 0x1d4c3 },
	{ 0x1d4c5, 0x1d505 },
	{ 0x1d507, 0x1d50a },
	{ 0x1d50d, 0x1d514 },
	{ 0x1d516, 0x1d51c },
	{ 0x1d51e, 0x1d539 },
	{ 0x1d53b, 0x1d53e },
	{ 0x1d540, 0x1d544 },
	{ 0x1d546, 0x1d546 },
	{ 0x1d54a, 0x1d550 },
	{ 0x1d552, 0x1d6a5 },
	{ 0x1d6a8, 0x1d6c0 },
	{ 0x1d6c2, 0x1d6da },
	{ 0x1d6dc, 0x1d6fa },
	{ 0x1d6fc, 0x1d714 },
	{ 0x1d716, 0x1d734 },
	{ 0x1d736, 0x1d74e },
	{ 0x1d750, 0x1d76e },
	{ 0x1d770, 0x1d788 },
	{ 0x1d78a, 0x1d7a8 },
	{ 0x1d7aa, 0x1d7c2 },
	{ 0x1d7c4, 0x1d7cb },
	{ 0x1df00, 0x1df1e },
	{ 0x1df25, 0x1df2a },
	{ 0x1e030, 0x1e06d },
	{ 0x1e100, 0x1e12c },
	{ 0x1e137, 0x1e13d },
	{ 0x1e14e, 0x1e14e },
	{ 0x1e290, 0x1e2ad },
	{ 0x1e2c0, 0x1e2eb },
	{ 0x1e4d0, 0x1e4eb },
	{ 0x1e5d0, 0x1e5ed },
	{ 0x1e5f0, 0x1e5f0 },
	{ 0x1e7e0, 0x1e7e6 },
	{ 0x1e7e8, 0x1e7eb },
	{ 0x1e7ed, 0x1e7ee },
	{ 0x1e7f0, 0x1e7fe },
	{ 0x1e800, 0x1e8c4 },
	{ 0x1e900, 0x1e943 },
	{ 0x1e94b, 0x1e94b },
	{ 0x1ee00, 0x1ee03 },
	{ 0x1ee05, 0x1ee1f },
	{ 0x1ee21, 0x1ee22 },
	{ 0x1ee24, 0x1ee24 },
	{ 0x1ee27, 0x1ee27 },
	{ 0x1ee29, 0x1ee32 },
	{ 0x1ee34, 0x1ee37 },
	{ 0x1ee39, 0x1ee39 },
	{ 0x1ee3b, 0x1ee3b },
	{ 0x1ee42, 0x1ee42 },
	{ 0x1ee47, 0x1ee47 },
	{ 0x1ee49, 0x1ee49 },
	{ 0x1ee4b, 0x1ee4b },
	{ 0x1ee4d, 0x1ee4f },
	{ 0x1ee51, 0x1ee52 },
	{ 0x1ee54, 0x1ee54 },
	{ 0x1ee57, 0x1ee57 },
	{ 0x1ee59, 0x1ee59 },
	{ 0x1ee5b, 0x1ee5b },
	{ 0x1ee5d, 0x1ee5d },
	{ 0x1ee5f, 0x1ee5f },
	{ 0x1ee61, 0x1ee62 },
	{ 0x1ee64, 0x1ee64 },
	{ 0x1ee67, 0x1ee6a },
	{ 0x1ee6c, 0x1ee72 },
	{ 0x1ee74, 0x1ee77 },
	{ 0x1ee79, 0x1ee7c },
	{ 0x1ee7e, 0x1ee7e },
	{ 0x1ee80, 0x1ee89 },
	{ 0x1ee8b, 0x1ee9b },
	{ 0x1eea1, 0x1eea3 },
	{ 0x1eea5, 0x1eea9 },
	{ 0x1eeab, 0x1eebb },
	{ 0x20000, 0x2a6df },
	{ 0x2a700, 0x2b739 },
	{ 0x2b740, 0x2b81d },
	{ 0x2b820, 0x2cea1 },
	{ 0x2ceb0, 0x2ebe0 },
	{ 0x2ebf0, 0x2ee5d },
	{ 0x2f800, 0x2fa1d },
	{ 0x30000, 0x3134a },
	{ 0x31350, 0x323af },
};

constexpr inline CharRange xid_continue[] = {
	{ 0x30, 0x39 },
	{ 0x41, 0x5a },
	{ 0x5f, 0x5f },
	{ 0x61, 0x7a },
	{ 0xaa, 0xaa },
	{ 0xb5, 0xb5 },
	{ 0xb7, 0xb7 },
	{ 0xba, 0xba },
	{ 0xc0, 0xd6 },
	{ 0xd8, 0xf6 },
	{ 0xf8, 0x2c1 },
	{ 0x2c6, 0x2d1 },
	{ 0x2e0, 0x2e4 },
	{ 0x2ec, 0x2ec },
	{ 0x2ee, 0x2ee },
	{ 0x300, 0x374 },
	{ 0x376, 0x377 },
	{ 0x37b, 0x37d },
	{ 0x37f, 0x37f },
	{ 0x386, 0x38a },
	{ 0x38c, 0x38c },
	{ 0x38e, 0x3a1 },
	{ 0x3a3, 0x3f5 },
	{ 0x3f7, 0x481 },
	{ 0x483, 0x487 },
	{ 0x48a, 0x52f },
	{ 0x531, 0x556 },
	{ 0x559, 0x559 },
	{ 0x560, 0x588 },
	{ 0x591, 0x5bd },
	{ 0x5bf, 0x5bf },
	{ 0x5c1, 0x5c2 },
	{ 0x5c4, 0x5c5 },
	{ 0x5c7, 0x5c7 },
	{ 0x5d0, 0x5ea },
	{ 0x5ef, 0x5f2 },
	{ 0x610, 0x61a },
	{ 0x620, 0x669 },
	{ 0x66e, 0x6d3 },
	{ 0x6d5, 0x6dc },
	{ 0x6df, 0x6e8 },
	{ 0x6ea, 0x6fc },
	{ 0x6ff, 0x6ff },
	{ 0x710, 0x74a },
	{ 0x74d, 0x7b1 },
	{ 0x7c0, 0x7f5 },
	{ 0x7fa, 0x7fa },
	{ 0x7fd, 0x7fd },
	{ 0x800, 0x82d },
	{ 0x840, 0x85b },
	{ 0x860, 0x86a },
	{ 0x870, 0x887 },
	{ 0x889, 0x88e },
	{ 0x897, 0x8e1 },
	{ 0x8e3, 0x963 },
	{ 0x966, 0x96f },
	{ 0x971, 0x983 },
	{ 0x985, 0x98c },
	{ 0x98f, 0x990 },
	{ 0x993, 0x9a8 },
	{ 0x9aa, 0x9b0 },
	{ 0x9b2, 0x9b2 },
	{ 0x9b6, 0x9b9 },
	{ 0x9bc, 0x9c4 },
	{ 0x9c7, 0x9c8 },
	{ 0x9cb, 0x9ce },
	{ 0x9d7, 0x9d7 },
	{ 0x9dc, 0x9dd },
	{ 0x9df, 0x9e3 },
	{ 0x9e6, 0x9f1 },
	{ 0x9fc, 0x9fc },
	{ 0x9fe, 0x9fe },
	{ 0xa01, 0xa03 },
	{ 0xa05, 0xa0a },
	{ 0xa0f, 0xa10 },
	{ 0xa13, 0xa28 },
	{ 0xa2a, 0xa30 },
	{ 0xa32, 0xa33 },
	{ 0xa35, 0xa36 },
	{ 0xa38, 0xa39 },
	{ 0xa3c, 0xa3c },
	{ 0xa3e, 0xa42 },
	{ 0xa47, 0xa48 },
	{ 0xa4b, 0xa4d },
	{ 0xa51, 0xa51 },
	{ 0xa59, 0xa5c },
	{ 0xa5e, 0xa5e },
	{ 0xa66, 0xa75 },
	{ 0xa81, 0xa83 },
	{ 0xa85, 0xa8d },
	{ 0xa8f, 0xa91 },
	{ 0xa93, 0xaa8 },
	{ 0xaaa, 0xab0 },
	{ 0xab2, 0xab3 },
	{ 0xab5, 0xab9 },
	{ 0xabc, 0xac5 },
	{ 0xac7, 0xac9 },
	{ 0xacb, 0xacd },
	{ 0xad0, 0xad0 },
	{ 0xae0, 0xae3 },
	{ 0xae6, 0xaef },
	{ 0xaf9, 0xaff },
	{ 0xb01, 0xb03 },
	{ 0xb05, 0xb0c },
	{ 0xb0f, 0xb10 },
	{ 0xb13, 0xb28 },
	{ 0xb2a, 0xb30 },
	{ 0xb32, 0xb33 },
	{ 0xb35, 0xb39 },
	{ 0xb3c, 0xb44 },
	{ 0xb47, 0xb48 },
	{ 0xb4b, 0xb4d },
	{ 0xb55, 0xb57 },
	{ 0xb5c, 0xb5d },
	{ 0xb5f, 0xb63 },
	{ 0xb66, 0xb6f },
	{ 0xb71, 0xb71 },
	{ 0xb82, 0xb83 },
	{ 0xb85, 0xb8a },
	{ 0xb8e, 0xb90 },
	{ 0xb92, 0xb95 },
	{ 0xb99, 0xb9a },
	{ 0xb9c, 0xb9c },
	{ 0xb9e, 0xb9f },
	{ 0xba3, 0xba4 },
	{ 0xba8, 0xbaa },
	{ 0xbae, 0xbb9 },
	{ 0xbbe, 0xbc2 },
	{ 0xbc6, 0xbc8 },
	{ 0xbca, 0xbcd },
	{ 0xbd0, 0xbd0 },
	{ 0xbd7, 0xbd7 },
	{ 0xbe6, 0xbef },
	{ 0xc00, 0xc0c },
	{ 0xc0e, 0xc10 },
	{ 0xc12, 0xc28 },
	{ 0xc2a, 0xc39 },
	{ 0xc3c, 0xc44 },
	{ 0xc46, 0xc48 },
	{ 0xc4a, 0xc4d },
	{ 0xc55, 0xc56 },
	{ 0xc58, 0xc5a },
	{ 0xc5d, 0xc5d },
	{ 0xc60, 0xc63 },
	{ 0xc66, 0xc6f },
	{ 0xc80, 0xc83 },
	{ 0xc85, 0xc8c },
	{ 0xc8e, 0xc90 },
	{ 0xc92, 0xca8 },
	{ 0xcaa, 0xcb3 },
	{ 0xcb5, 0xcb9 },
	{ 0xcbc, 0xcc4 },
	{ 0xcc6, 0xcc8 },
	{ 0xcca, 0xccd },
	{ 0xcd5, 0xcd6 },
	{ 0xcdd, 0xcde },
	{ 0xce0, 0xce3 },
	{ 0xce6, 0xcef },
	{ 0xcf1, 0xcf3 },
	{ 0xd00, 0xd0c },
	{ 0xd0e, 0xd10 },
	{ 0xd12, 0xd44 },
	{ 0xd46, 0xd48 },
	{ 0xd4a, 0xd4e },
	{ 0xd54, 0xd57 },
	{ 0xd5f, 0xd63 },
	{ 0xd66, 0xd6f },
	{ 0xd7a, 0xd7f },
	{ 0xd81, 0xd83 },
	{ 0xd85, 0xd96 },
	{ 0xd9a, 0xdb1 },
	{ 0xdb3, 0xdbb },
	{ 0xdbd, 0xdbd },
	{ 0xdc0, 0xdc6 },
	{ 0xdca, 0xdca },
	{ 0xdcf, 0xdd4 },
	{ 0xdd6, 0xdd6 },
	{ 0xdd8, 0xddf },
	{ 0xde6, 0xdef },
	{ 0xdf2, 0xdf3 },
	{ 0xe01, 0xe3a },
	{ 0xe40, 0xe4e },
	{ 0xe50, 0xe59 },
	{ 0xe81, 0xe82 },
	{ 0xe84, 0xe84 },
	{ 0xe86, 0xe8a },
	{ 0xe8c, 0xea3 },
	{ 0xea5, 0xea5 },
	{ 0xea7, 0xebd },
	{ 0xec0, 0xec4 },
	{ 0xec6, 0xec6 },
	{ 0xec8, 0xece },
	{ 0xed0, 0xed9 },
	{ 0xedc, 0xedf },
	{ 0xf00, 0xf00 },
	{ 0xf18, 0xf19 },
	{ 0xf20, 0xf29 },
	{ 0xf35, 0xf35 },
	{ 0xf37, 0xf37 },
	{ 0xf39, 0xf39 },
	{ 0xf3e, 0xf47 },
	{ 0xf49, 0xf6c },
	{ 0xf71, 0xf84 },
	{ 0xf86, 0xf97 },
	{ 0xf99, 0xfbc },
	{ 0xfc6, 0xfc6 },
	{ 0x1000, 0x1049 },
	{ 0x1050, 0x109d },
	{ 0x10a0, 0x10c5 },
	{ 0x10c7, 0x10c7 },
	{ 0x10cd, 0x10cd },
	{ 0x10d0, 0x10fa },
	{ 0x10fc, 0x1248 },
	{ 0x124a, 0x124d },
	{ 0x1250, 0x1256 },
	{ 0x1258, 0x1258 },
	{ 0x125a, 0x125d },
	{ 0x1260, 0x1288 },
	{ 0x128a, 0x128d },
	{ 0x1290, 0x12b0 },
	{ 0x12b2, 0x12b5 },
	{ 0x12b8, 0x12be },
	{ 0x12c0, 0x12c0 },
	{ 0x12c2, 0x12c5 },
	{ 0x12c8, 0x12d6 },
	{ 0x12d8, 0x1310 },
	{ 0x1312, 0x1315 },
	{ 0x1318, 0x135a },
	{ 0x135d, 0x135f },
	{ 0x1369, 0x1371 },
	{ 0x1380, 0x138f },
	{ 0x13a0, 0x13f5 },
	{ 0x13f8, 0x13fd },
	{ 0x1401, 0x166c },
	{ 0x166f, 0x167f },
	{ 0x1681, 0x169a },
	{ 0x16a0, 0x16ea },
	{ 0x16ee, 0x16f8 },
	{ 0x1700, 0x1715 },
	{ 0x171f, 0x1734 },
	{ 0x1740, 0x1753 },
	{ 0x1760, 0x176c },
	{ 0x176e, 0x1770 },
	{ 0x1772, 0x1773 },
	{ 0x1780, 0x17d3 },
	{ 0x17d7, 0x17d7 },
	{ 0x17dc, 0x17dd },
	{ 0x17e0, 0x17e9 },
	{ 0x180b, 0x180d },
	{ 0x180f, 0x1819 },
	{ 0x1820, 0x1878 },
	{ 0x1880, 0x18aa },
	{ 0x18b0, 0x18f5 },
	{ 0x1900, 0x191e },
	{ 0x1920, 0x192b },
	{ 0x1930, 0x193b },
	{ 0x1946, 0x196d },
	{ 0x1970, 0x1974 },
	{ 0x1980, 0x19ab },
	{ 0x19b0, 0x19c9 },
	{ 0x19d0, 0x19da },
	{ 0x1a00, 0x1a1b },
	{ 0x1a20, 0x1a5e },
	{ 0x1a60, 0x1a7c },
	{ 0x1a7f, 0x1a89 },
	{ 0x1a90, 0x1a99 },
	{ 0x1aa7, 0x1aa7 },
	{ 0x1ab0, 0x1abd },
	{ 0x1abf, 0x1ace },
	{ 0x1b00, 0x1b4c },
	{ 0x1b50, 0x1b59 },
	{ 0x1b6b, 0x1b73 },
	{ 0x1b80, 0x1bf3 },
	{ 0x1c00, 0x1c37 },
	{ 0x1c40, 0x1c49 },
	{ 0x1c4d, 0x1c7d },
	{ 0x1c80, 0x1c8a },
	{ 0x1c90, 0x1cba },
	{ 0x1cbd, 0x1cbf },
	{ 0x1cd0, 0x1cd2 },
	{ 0x1cd4, 0x1cfa },
	{ 0x1d00, 0x1f15 },
	{ 0x1f18, 0x1f1d },
	{ 0x1f20, 0x1f45 },
	{ 0x1f48, 0x1f4d },
	{ 0x1f50, 0x1f57 },
	{ 0x1f59, 0x1f59 },
	{ 0x1f5b, 0x1f5b },
	{ 0x1f5d, 0x1f5d },
	{ 0x1f5f, 0x1f7d },
	{ 0x1f80, 0x1fb4 },
	{ 0x1fb6, 0x1fbc },
	{ 0x1fbe, 0x1fbe },
	{ 0x1fc2, 0x1fc4 },
	{ 0x1fc6, 0x1fcc },
	{ 0x1fd0, 0x1fd3 },
	{ 0x1fd6, 0x1fdb },
	{ 0x1fe0, 0x1fec },
	{ 0x1ff2, 0x1ff4 },
	{ 0x1ff6, 0x1ffc },
	{ 0x200c, 0x200d },
	{ 0x203f, 0x2040 },
	{ 0x2054, 0x2054 },
	{ 0x2071, 0x2071 },
	{ 0x207f, 0x207f },
	{ 0x2090, 0x209c },
	{ 0x20d0, 0x20dc },
	{ 0x20e1, 0x20e1 },
	{ 0x20e5, 0x20f0 },
	{ 0x2102, 0x2102 },
	{ 0x2107, 0x2107 },
	{ 0x210a, 0x2113 },
	{ 0x2115, 0x2115 },
	{ 0x2118, 0x211d },
	{ 0x2124, 0x2124 },
	{ 0x2126, 0x2126 },
	{ 0x2128, 0x2128 },
	{ 0x212a, 0x2139 },
	{ 0x213c, 0x213f },
	{ 0x2145, 0x2149 },
	{ 0x214e, 0x214e },
	{ 0x2160, 0x2188 },
	{ 0x2c00, 0x2ce4 },
	{ 0x2ceb, 0x2cf3 },
	{ 0x2d00, 0x2d25 },
	{ 0x2d27, 0x2d27 },
	{ 0x2d2d, 0x2d2d },
	{ 0x2d30, 0x2d67 },
	{ 0x2d6f, 0x2d6f },
	{ 0x2d7f, 0x2d96 },
	{ 0x2da0, 0x2da6 },
	{ 0x2da8, 0x2dae },
	{ 0x2db0, 0x2db6 },
	{ 0x2db8, 0x2dbe },
	{ 0x2dc0, 0x2dc6 },
	{ 0x2dc8, 0x2dce },
	{ 0x2dd0, 0x2dd6 },
	{ 0x2dd8, 0x2dde },
	{ 0x2de0, 0x2dff },
	{ 0x3005, 0x3007 },
	{ 0x3021, 0x302f },
	{ 0x3031, 0x3035 },
	{ 0x3038, 0x303c },
	{ 0x3041, 0x3096 },
	{ 0x3099, 0x309a },
	{ 0x309d, 0x309f },
	{ 0x30a1, 0x30ff },
	{ 0x3105, 0x312f },
	{ 0x3131, 0x318e },
	{ 0x31a0, 0x31bf },
	{ 0x31f0, 0x31ff },
	{ 0x3400, 0x4dbf },
	{ 0x4e00, 0xa48c },
	{ 0xa4d0, 0xa4fd },
	{ 0xa500, 0xa60c },
	{ 0xa610, 0xa62b },
	{ 0xa640, 0xa66f },
	{ 0xa674, 0xa67d },
	{ 0xa67f, 0xa6f1 },
	{ 0xa717, 0xa71f },
	{ 0xa722, 0xa788 },
	{ 0xa78b, 0xa7cd },
	{ 0xa7d0, 0xa7d1 },
	{ 0xa7d3, 0xa7d3 },
	{ 0xa7d5, 0xa7dc },
	{ 0xa7f2, 0xa827 },
	{ 0xa82c, 0xa82c },
	{ 0xa840, 0xa873 },
	{ 0xa880, 0xa8c5 },
	{ 0xa8d0, 0xa8d9 },
	{ 0xa8e0, 0xa8f7 },
	{ 0xa8fb, 0xa8fb },
	{ 0xa8fd, 0xa92d },
	{ 0xa930, 0xa953 },
	{ 0xa960, 0xa97c },
	{ 0xa980, 0xa9c0 },
	{ 0xa9cf, 0xa9d9 },
	{ 0xa9e0, 0xa9fe },
	{ 0xaa00, 0xaa36 },
	{ 0xaa40, 0xaa4d },
	{ 0xaa50, 0xaa59 },
	{ 0xaa60, 0xaa76 },
	{ 0xaa7a, 0xaac2 },
	{ 0xaadb, 0xaadd },
	{ 0xaae0, 0xaaef },
	{ 0xaaf2, 0xaaf6 },
	{ 0xab01, 0xab06 },
	{ 0xab09, 0xab0e },
	{ 0xab11, 0xab16 },
	{ 0xab20, 0xab26 },
	{ 0xab28, 0xab2e },
	{ 0xab30, 0xab5a },
	{ 0xab5c, 0xab69 },
	{ 0xab70, 0xabea },
	{ 0xabec, 0xabed },
	{ 0xabf0, 0xabf9 },
	{ 0xac00, 0xd7a3 },
	{ 0xd7b0, 0xd7c6 },
	{ 0xd7cb, 0xd7fb },
	{ 0xf900, 0xfa6d },
	{ 0xfa70, 0xfad9 },
	{ 0xfb00, 0xfb06 },
	{ 0xfb13, 0xfb17 },
	{ 0xfb1d, 0xfb28 },
	{ 0xfb2a, 0xfb36 },
	{ 0xfb38, 0xfb3c },
	{ 0xfb3e, 0xfb3e },
	{ 0xfb40, 0xfb41 },
	{ 0xfb43, 0xfb44 },
	{ 0xfb46, 0xfbb1 },
	{ 0xfbd3, 0xfc5d },
	{ 0xfc64, 0xfd3d },
	{ 0xfd50, 0xfd8f },
	{ 0xfd92, 0xfdc7 },
	{ 0xfdf0, 0xfdf9 },
	{ 0xfe00, 0xfe0f },
	{ 0xfe20, 0xfe2f },
	{ 0xfe33, 0xfe34 },
	{ 0xfe4d, 0xfe4f },
	{ 0xfe71, 0xfe71 },
	{ 0xfe73, 0xfe73 },
	{ 0xfe77, 0xfe77 },
	{ 0xfe79, 0xfe79 },
	{ 0xfe7b, 0xfe7b },
	{ 0xfe7d, 0xfe7d },
	{ 0xfe7f, 0xfefc },
	{ 0xff10, 0xff19 },
	{ 0xff21, 0xff3a },
	{ 0xff3f, 0xff3f },
	{ 0xff41, 0xff5a },
	{ 0xff65, 0xffbe },
	{ 0xffc2, 0xffc7 },
	{ 0xffca, 0xffcf },
	{ 0xffd2, 0xffd7 },
	{ 0xffda, 0xffdc },
	{ 0x10000, 0x1000b },
	{ 0x1000d, 0x10026 },
	{ 0x10028, 0x1003a },
	{ 0x1003c, 0x1003d },
	{ 0x1003f, 0x1004d },
	{ 0x10050, 0x1005d },
	{ 0x10080, 0x100fa },
	{ 0x10140, 0x10174 },
	{ 0x101fd, 0x101fd },
	{ 0x10280, 0x1029c },
	{ 0x102a0, 0x102d0 },
	{ 0x102e0, 0x102e0 },
	{ 0x10300, 0x1031f },
	{ 0x1032d, 0x1034a },
	{ 0x10350, 0x1037a },
	{ 0x10380, 0x1039d },
	{ 0x103a0, 0x103c3 },
	{ 0x103c8, 0x103cf },
	{ 0x103d1, 0x103d5 },
	{ 0x10400, 0x1049d },
	{ 0x104a0, 0x104a9 },
	{ 0x104b0, 0x104d3 },
	{ 0x104d8, 0x104fb },
	{ 0x10500, 0x10527 },
	{ 0x10530, 0x10563 },
	{ 0x10570, 0x1057a },
	{ 0x1057c, 0x1058a },
	{ 0x1058c, 0x10592 },
	{ 0x10594, 0x10595 },
	{ 0x10597, 0x105a1 },
	{ 0x105a3, 0x105b1 },
	{ 0x105b3, 0x105b9 },
	{ 0x105bb, 0x105bc },
	{ 0x105c0, 0x105f3 },
	{ 0x10600, 0x10736 },
	{ 0x10740, 0x10755 },
	{ 0x10760, 0x10767 },
	{ 0x10780, 0x10785 },
	{ 0x10787, 0x107b0 },
	{ 0x107b2, 0x107ba },
	{ 0x10800, 0x10805 },
	{ 0x10808, 0x10808 },
	{ 0x1080a, 0x10835 },
	{ 0x10837, 0x10838 },
	{ 0x1083c, 0x1083c },
	{ 0x1083f, 0x10855 },
	{ 0x10860, 0x10876 },
	{ 0x10880, 0x1089e },
	{ 0x108e0, 0x108f2 },
	{ 0x108f4, 0x108f5 },
	{ 0x10900, 0x10915 },
	{ 0x10920, 0x10939 },
	{ 0x10980, 0x109b7 },
	{ 0x109be, 0x109bf },
	{ 0x10a00, 0x10a03 },
	{ 0x10a05, 0x10a06 },
	{ 0x10a0c, 0x10a13 },
	{ 0x10a15, 0x10a17 },
	{ 0x10a19, 0x10a35 },
	{ 0x10a38, 0x10a3a },
	{ 0x10a3f, 0x10a3f },
	{ 0x10a60, 0x10a7c },
	{ 0x10a80, 0x10a9c },
	{ 0x10ac0, 0x10ac7 },
	{ 0x10ac9, 0x10ae6 },
	{ 0x10b00, 0x10b35 },
	{ 0x10b40, 0x10b55 },
	{ 0x10b60, 0x10b72 },
	{ 0x10b80, 0x10b91 },
	{ 0x10c00, 0x10c48 },
	{ 0x10c80, 0x10cb2 },
	{ 0x10cc0, 0x10cf2 },
	{ 0x10d00, 0x10d27 },
	{ 0x10d30, 0x10d39 },
	{ 0x10d40, 0x10d65 },
	{ 0x10d69, 0x10d6d },
	{ 0x10d6f, 0x10d85 },
	{ 0x10e80, 0x10ea9 },
	{ 0x10eab, 0x10eac },
	{ 0x10eb0, 0x10eb1 },
	{ 0x10ec2, 0x10ec4 },
	{ 0x10efc, 0x10f1c },
	{ 0x10f27, 0x10f27 },
	{ 0x10f30, 0x10f50 },
	{ 0x10f70, 0x10f85 },
	{ 0x10fb0, 0x10fc4 },
	{ 0x10fe0, 0x10ff6 },
	{ 0x11000, 0x11046 },
	{ 0x11066, 0x11075 },
	{ 0x1107f, 0x110ba },
	{ 0x110c2, 0x110c2 },
	{ 0x110d0, 0x110e8 },
	{ 0x110f0, 0x110f9 },
	{ 0x11100, 0x11134 },
	{ 0x11136, 0x1113f },
	{ 0x11144, 0x11147 },
	{ 0x11150, 0x11173 },
	{ 0x11176, 0x11176 },
	{ 0x11180, 0x111c4 },
	{ 0x111c9, 0x111cc },
	{ 0x111ce, 0x111da },
	{ 0x111dc, 0x111dc },
	{ 0x11200, 0x11211 },
	{ 0x11213, 0x11237 },
	{ 0x1123e, 0x11241 },
	{ 0x11280, 0x11286 },
	{ 0x11288, 0x11288 },
	{ 0x1128a, 0x1128d },
	{ 0x1128f, 0x1129d },
	{ 0x1129f, 0x112a8 },
	{ 0x112b0, 0x112ea },
	{ 0x112f0, 0x112f9 },
	{ 0x11300, 0x11303 },
	{ 0x11305, 0x1130c },
	{ 0x1130f, 0x11310 },
	{ 0x11313, 0x11328 },
	{ 0x1132a, 0x11330 },
	{ 0x11332, 0x11333 },
	{ 0x11335, 0x11339 },
	{ 0x1133b, 0x11344 },
	{ 0x11347, 0x11348 },
	{ 0x1134b, 0x1134d },
	{ 0x11350, 0x11350 },
	{ 0x11357, 0x11357 },
	{ 0x1135d, 0x11363 },
	{ 0x11366, 0x1136c },
	{ 0x11370, 0x11374 },
	{ 0x11380, 0x11389 },
	{ 0x1138b, 0x1138b },
	{ 0x1138e, 0x1138e },
	{ 0x11390, 0x113b5 },
	{ 0x113b7, 0x113c0 },
	{ 0x113c2, 0x113c2 },
	{ 0x113c5, 0x113c5 },
	{ 0x113c7, 0x113ca },
	{ 0x113cc, 0x113d3 },
	{ 0x113e1, 0x113e2 },
	{ 0x11400, 0x1144a },
	{ 0x11450, 0x11459 },
	{ 0x1145e, 0x11461 },
	{ 0x11480, 0x114c5 },
	{ 0x114c7, 0x114c7 },
	{ 0x114d0, 0x114d9 },
	{ 0x11580, 0x115b5 },
	{ 0x115b8, 0x115c0 },
	{ 0x115d8, 0x115dd },
	{ 0x11600, 0x11640 },
	{ 0x11644, 0x11644 },
	{ 0x11650, 0x11659 },
	{ 0x11680, 0x116b8 },
	{ 0x116c0, 0x116c9 },
	{ 0x116d0, 0x116e3 },
	{ 0x11700, 0x1171a },
	{ 0x1171d, 0x1172b },
	{ 0x11730, 0x11739 },
	{ 0x11740, 0x11746 },
	{ 0x11800, 0x1183a },
	{ 0x118a0, 0x118e9 },
	{ 0x118ff, 0x11906 },
	{ 0x11909, 0x11909 },
	{ 0x1190c, 0x11913 },
	{ 0x11915, 0x11916 },
	{ 0x11918, 0x11935 },
	{ 0x11937, 0x11938 },
	{ 0x1193b, 0x11943 },
	{ 0x11950, 0x11959 },
	{ 0x119a0, 0x119a7 },
	{ 0x119aa, 0x119d7 },
	{ 0x119da, 0x119e1 },
	{ 0x119e3, 0x119e4 },
	{ 0x11a00, 0x11a3e },
	{ 0x11a47, 0x11a47 },
	{ 0x11a50, 0x11a99 },
	{ 0x11a9d, 0x11a9d },
	{ 0x11ab0, 0x11af8 },
	{ 0x11bc0, 0x11be0 },
	{ 0x11bf0, 0x11bf9 },
	{ 0x11c00, 0x11c08 },
	{ 0x11c0a, 0x11c36 },
	{ 0x11c38, 0x11c40 },
	{ 0x11c50, 0x11c59 },
	{ 0x11c72, 0x11c8f },
	{ 0x11c92, 0x11ca7 },
	{ 0x11ca9, 0x11cb6 },
	{ 0x11d00, 0x11d06 },
	{ 0x11d08, 0x11d09 },
	{ 0x11d0b, 0x11d36 },
	{ 0x11d3a, 0x11d3a },
	{ 0x11d3c, 0x11d3d },
	{ 0x11d3f, 0x11d47 },
	{ 0x11d50, 0x11d59 },
	{ 0x11d60, 0x11d65 },
	{ 0x11d67, 0x11d68 },
	{ 0x11d6a, 0x11d8e },
	{ 0x11d90, 0x11d91 },
	{ 0x11d93, 0x11d98 },
	{ 0x11da0, 0x11da9 },
	{ 0x11ee0, 0x11ef6 },
	{ 0x11f00, 0x11f10 },
	{ 0x11f12, 0x11f3a },
	{ 0x11f3e, 0x11f42 },
	{ 0x11f50, 0x11f5a },
	{ 0x11fb0, 0x11fb0 },
	{ 0x12000, 0x12399 },
	{ 0x12400, 0x1246e },
	{ 0x12480, 0x12543 },
	{ 0x12f90, 0x12ff0 },
	{ 0x13000, 0x1342f },
	{ 0x13440, 0x13455 },
	{ 0x13460, 0x143fa },
	{ 0x14400, 0x14646 },
	{ 0x16100, 0x16139 },
	{ 0x16800, 0x16a38 },
	{ 0x16a40, 0x16a5e },
	{ 0x16a60, 0x16a69 },
	{ 0x16a70, 0x16abe },
	{ 0x16ac0, 0x16ac9 },
	{ 0x16ad0, 0x16aed },
	{ 0x16af0, 0x16af4 },
	{ 0x16b00, 0x16b36 },
	{ 0x16b40, 0x16b43 },
	{ 0x16b50, 0x16b59 },
	{ 0x16b63, 0x16b77 },
	{ 0x16b7d, 0x16b8f },
	{ 0x16d40, 0x16d6c },
	{ 0x16d70, 0x16d79 },
	{ 0x16e40, 0x16e7f },
	{ 0x16f00, 0x16f4a },
	{ 0x16f4f, 0x16f87 },
	{ 0x16f8f, 0x16f9f },
	{ 0x16fe0, 0x16fe1 },
	{ 0x16fe3, 0x16fe4 },
	{ 0x16ff0, 0x16ff1 },
	{ 0x17000, 0x187f7 },
	{ 0x18800, 0x18cd5 },
	{ 0x18cff, 0x18d08 },
	{ 0x1aff0, 0x1aff3 },
	{ 0x1aff5, 0x1affb },
	{ 0x1affd, 0x1affe },
	{ 0x1b000, 0x1b122 },
	{ 0x1b132, 0x1b132 },
	{ 0x1b150, 0x1b152 },
	{ 0x1b155, 0x1b155 },
	{ 0x1b164, 0x1b167 },
	{ 0x1b170, 0x1b2fb },
	{ 0x1bc00, 0x1bc6a },
	{ 0x1bc70, 0x1bc7c },
	{ 0x1bc80, 0x1bc88 },
	{ 0x1bc90, 0x1bc99 },
	{ 0x1bc9d, 0x1bc9e },
	{ 0x1ccf0, 0x1ccf9 },
	{ 0x1cf00, 0x1cf2d },
	{ 0x1cf30, 0x1cf46 },
	{ 0x1d165, 0x1d169 },
	{ 0x1d16d, 0x1d172 },
	{ 0x1d17b, 0x1d182 },
	{ 0x1d185, 0x1d18b },
	{ 0x1d1aa, 0x1d1ad },
	{ 0x1d242, 0x1d244 },
	{ 0x1d400, 0x1d454 },
	{ 0x1d456, 0x1d49c },
	{ 0x1d49e, 0x1d49f },
	{ 0x1d4a2, 0x1d4a2 },
	{ 0x1d4a5, 0x1d4a6 },
	{ 0x1d4a9, 0x1d4ac },
	{ 0x1d4ae, 0x1d4b9 },
	{ 0x1d4bb, 0x1d4bb },
	{ 0x1d4bd, 0x1d4c3 },
	{ 0x1d4c5, 0x1d505 },
	{ 0x1d507, 0x1d50a },
	{ 0x1d50d, 0x1d514 },
	{ 0x1d516, 0x1d51c },
	{ 0x1d51e, 0x1d539 },
	{ 0x1d53b, 0x1d53e },
	{ 0x1d540, 0x1d544 },
	{ 0x1d546, 0x1d546 },
	{ 0x1d54a, 0x1d550 },
	{ 0x1d552, 0x1d6a5 },
	{ 0x1d6a8, 0x1d6c0 },
	{ 0x1d6c2, 0x1d6da },
	{ 0x1d6dc, 0x1d6fa },
	{ 0x1d6fc, 0x1d714 },
	{ 0x1d716, 0x1d734 },
	{ 0x1d736, 0x1d74e },
	{ 0x1d750, 0x1d76e },
	{ 0x1d770, 0x1d788 },
	{ 0x1d78a, 0x1d7a8 },
	{ 0x1d7aa, 0x1d7c2 },
	{ 0x1d7c4, 0x1d7cb },
	{ 0x1d7ce, 0x1d7ff },
	{ 0x1da00, 0x1da36 },
	{ 0x1da3b, 0x1da6c },
	{ 0x1da75, 0x1da75 },
	{ 0x1da84, 0x1da84 },
	{ 0x1da9b, 0x1da9f },
	{ 0x1daa1, 0x1daaf },
	{ 0x1df00, 0x1df1e },
	{ 0x1df25, 0x1df2a },
	{ 0x1e000, 0x1e006 },
	{ 0x1e008, 0x1e018 },
	{ 0x1e01b, 0x1e021 },
	{ 0x1e023, 0x1e024 },
	{ 0x1e026, 0x1e02a },
	{ 0x1e030, 0x1e06d },
	{ 0x1e08f, 0x1e08f },
	{ 0x1e100, 0x1e12c },
	{ 0x1e130, 0x1e13d },
	{ 0x1e140, 0x1e149 },
	{ 0x1e14e, 0x1e14e },
	{ 0x1e290, 0x1e2ae },
	{ 0x1e2c0, 0x1e2f9 },
	{ 0x1e4d0, 0x1e4f9 },
	{ 0x1e5d0, 0x1e5fa },
	{ 0x1e7e0, 0x1e7e6 },
	{ 0x1e7e8, 0x1e7eb },
	{ 0x1e7ed, 0x1e7ee },
	{ 0x1e7f0, 0x1e7fe },
	{ 0x1e800, 0x1e8c4 },
	{ 0x1e8d0, 0x1e8d6 },
	{ 0x1e900, 0x1e94b },
	{ 0x1e950, 0x1e959 },
	{ 0x1ee00, 0x1ee03 },
	{ 0x1ee05, 0x1ee1f },
	{ 0x1ee21, 0x1ee22 },
	{ 0x1ee24, 0x1ee24 },
	{ 0x1ee27, 0x1ee27 },
	{ 0x1ee29, 0x1ee32 },
	{ 0x1ee34, 0x1ee37 },
	{ 0x1ee39, 0x1ee39 },
	{ 0x1ee3b, 0x1ee3b },
	{ 0x1ee42, 0x1ee42 },
	{ 0x1ee47, 0x1ee47 },
	{ 0x1ee49, 0x1ee49 },
	{ 0x1ee4b, 0x1ee4b },
	{ 0x1ee4d, 0x1ee4f },
	{ 0x1ee51, 0x1ee52 },
	{ 0x1ee54, 0x1ee54 },
	{ 0x1ee57, 0x1ee57 },
	{ 0x1ee59, 0x1ee59 },
	{ 0x1ee5b, 0x1ee5b },
	{ 0x1ee5d, 0x1ee5d },
	{ 0x1ee5f, 0x1ee5f },
	{ 0x1ee61, 0x1ee62 },
	{ 0x1ee64, 0x1ee64 },
	{ 0x1ee67, 0x1ee6a },
	{ 0x1ee6c, 0x1ee72 },
	{ 0x1ee74, 0x1ee77 },
	{ 0x1ee79, 0x1ee7c },
	{ 0x1ee7e, 0x1ee7e },
	{ 0x1ee80, 0x1ee89 },
	{ 0x1ee8b, 0x1ee9b },
	{ 0x1eea1, 0x1eea3 },
	{ 0x1eea5, 0x1eea9 },
	{ 0x1eeab, 0x1eebb },
	{ 0x1fbf0, 0x1fbf9 },
	{ 0x20000, 0x2a6df },
	{ 0x2a700, 0x2b739 },
	{ 0x2b740, 0x2b81d },
	{ 0x2b820, 0x2cea1 },
	{ 0x2ceb0, 0x2ebe0 },
	{ 0x2ebf0, 0x2ee5d },
	{ 0x2f800, 0x2fa1d },
	{ 0x30000, 0x3134a },
	{ 0x31350, 0x323af },
	{ 0xe0100, 0xe01ef },
};

constexpr inline CharRange uppercase_letter[] = {
	{ 0x41, 0x5a },
	{ 0xc0, 0xd6 },
	{ 0xd8, 0xde },
	{ 0x100, 0x100 },
	{ 0x102, 0x102 },
	{ 0x104, 0x104 },
	{ 0x106, 0x106 },
	{ 0x108, 0x108 },
	{ 0x10a, 0x10a },
	{ 0x10c, 0x10c },
	{ 0x10e, 0x10e },
	{ 0x110, 0x110 },
	{ 0x112, 0x112 },
	{ 0x114, 0x114 },
	{ 0x116, 0x116 },
	{ 0x118, 0x118 },
	{ 0x11a, 0x11a },
	{ 0x11c, 0x11c },
	{ 0x11e, 0x11e },
	{ 0x120, 0x120 },
	{ 0x122, 0x122 },
	{ 0x124, 0x124 },
	{ 0x126, 0x126 },
	{ 0x128, 0x128 },
	{ 0x12a, 0x12a },
	{ 0x12c, 0x12c },
	{ 0x12e, 0x12e },
	{ 0x130, 0x130 },
	{ 0x132, 0x132 },
	{ 0x134, 0x134 },
	{ 0x136, 0x136 },
	{ 0x139, 0x139 },
	{ 0x13b, 0x13b },
	{ 0x13d, 0x13d },
	{ 0x13f, 0x13f },
	{ 0x141, 0x141 },
	{ 0x143, 0x143 },
	{ 0x145, 0x145 },
	{ 0x147, 0x147 },
	{ 0x14a, 0x14a },
	{ 0x14c, 0x14c },
	{ 0x14e, 0x14e },
	{ 0x150, 0x150 },
	{ 0x152, 0x152 },
	{ 0x154, 0x154 },
	{ 0x156, 0x156 },
	{ 0x158, 0x158 },
	{ 0x15a, 0x15a },
	{ 0x15c, 0x15c },
	{ 0x15e, 0x15e },
	{ 0x160, 0x160 },
	{ 0x162, 0x162 },
	{ 0x164, 0x164 },
	{ 0x166, 0x166 },
	{ 0x168, 0x168 },
	{ 0x16a, 0x16a },
	{ 0x16c, 0x16c },
	{ 0x16e, 0x16e },
	{ 0x170, 0x170 },
	{ 0x172, 0x172 },
	{ 0x174, 0x174 },
	{ 0x176, 0x176 },
	{ 0x178, 0x179 },
	{ 0x17b, 0x17b },
	{ 0x17d, 0x17d },
	{ 0x181, 0x182 },
	{ 0x184, 0x184 },
	{ 0x186, 0x187 },
	{ 0x189, 0x18b },
	{ 0x18e, 0x191 },
	{ 0x193, 0x194 },
	{ 0x196, 0x198 },
	{ 0x19c, 0x19d },
	{ 0x19f, 0x1a0 },
	{ 0x1a2, 0x1a2 },
	{ 0x1a4, 0x1a4 },
	{ 0x1a6, 0x1a7 },
	{ 0x1a9, 0x1a9 },
	{ 0x1ac, 0x1ac },
	{ 0x1ae, 0x1af },
	{ 0x1b1, 0x1b3 },
	{ 0x1b5, 0x1b5 },
	{ 0x1b7, 0x1b8 },
	{ 0x1bc, 0x1bc },
	{ 0x1c4, 0x1c4 },
	{ 0x1c7, 0x1c7 },
	{ 0x1ca, 0x1ca },
	{ 0x1cd, 0x1cd },
	{ 0x1cf, 0x1cf },
	{ 0x1d1, 0x1d1 },
	{ 0x1d3, 0x1d3 },
	{ 0x1d5, 0x1d5 },
	{ 0x1d7, 0x1d7 },
	{ 0x1d9, 0x1d9 },
	{ 0x1db, 0x1db },
	{ 0x1de, 0x1de },
	{ 0x1e0, 0x1e0 },
	{ 0x1e2, 0x1e2 },
	{ 0x1e4, 0x1e4 },
	{ 0x1e6, 0x1e6 },
	{ 0x1e8, 0x1e8 },
	{ 0x1ea, 0x1ea },
	{ 0x1ec, 0x1ec },
	{ 0x1ee, 0x1ee },
	{ 0x1f1, 0x1f1 },
	{ 0x1f4, 0x1f4 },
	{ 0x1f6, 0x1f8 },
	{ 0x1fa, 0x1fa },
	{ 0x1fc, 0x1fc },
	{ 0x1fe, 0x1fe },
	{ 0x200, 0x200 },
	{ 0x202, 0x202 },
	{ 0x204, 0x204 },
	{ 0x206, 0x206 },
	{ 0x208, 0x208 },
	{ 0x20a, 0x20a },
	{ 0x20c, 0x20c },
	{ 0x20e, 0x20e },
	{ 0x210, 0x210 },
	{ 0x212, 0x212 },
	{ 0x214, 0x214 },
	{ 0x216, 0x216 },
	{ 0x218, 0x218 },
	{ 0x21a, 0x21a },
	{ 0x21c, 0x21c },
	{ 0x21e, 0x21e },
	{ 0x220, 0x220 },
	{ 0x222, 0x222 },
	{ 0x224, 0x224 },
	{ 0x226, 0x226 },
	{ 0x228, 0x228 },
	{ 0x22a, 0x22a },
	{ 0x22c, 0x22c },
	{ 0x22e, 0x22e },
	{ 0x230, 0x230 },
	{ 0x232, 0x232 },
	{ 0x23a, 0x23b },
	{ 0x23d, 0x23e },
	{ 0x241, 0x241 },
	{ 0x243, 0x246 },
	{ 0x248, 0x248 },
	{ 0x24a, 0x24a },
	{ 0x24c, 0x24c },
	{ 0x24e, 0x24e },
	{ 0x370, 0x370 },
	{ 0x372, 0x372 },
	{ 0x376, 0x376 },
	{ 0x37f, 0x37f },
	{ 0x386, 0x386 },
	{ 0x388, 0x38a },
	{ 0x38c, 0x38c },
	{ 0x38e, 0x38f },
	{ 0x391, 0x3a1 },
	{ 0x3a3, 0x3ab },
	{ 0x3cf, 0x3cf },
	{ 0x3d2, 0x3d4 },
	{ 0x3d8, 0x3d8 },
	{ 0x3da, 0x3da },
	{ 0x3dc, 0x3dc },
	{ 0x3de, 0x3de },
	{ 0x3e0, 0x3e0 },
	{ 0x3e2, 0x3e2 },
	{ 0x3e4, 0x3e4 },
	{ 0x3e6, 0x3e6 },
	{ 0x3e8, 0x3e8 },
	{ 0x3ea, 0x3ea },
	{ 0x3ec, 0x3ec },
	{ 0x3ee, 0x3ee },
	{ 0x3f4, 0x3f4 },
	{ 0x3f7, 0x3f7 },
	{ 0x3f9, 0x3fa },
	{ 0x3fd, 0x42f },
	{ 0x460, 0x460 },
	{ 0x462, 0x462 },
	{ 0x464, 0x464 },
	{ 0x466, 0x466 },
	{ 0x468, 0x468 },
	{ 0x46a, 0x46a },
	{ 0x46c, 0x46c },
	{ 0x46e, 0x46e },
	{ 0x470, 0x470 },
	{ 0x472, 0x472 },
	{ 0x474, 0x474 },
	{ 0x476, 0x476 },
	{ 0x478, 0x478 },
	{ 0x47a, 0x47a },
	{ 0x47c, 0x47c },
	{ 0x47e, 0x47e },
	{ 0x480, 0x480 },
	{ 0x48a, 0x48a },
	{ 0x48c, 0x48c },
	{ 0x48e, 0x48e },
	{ 0x490, 0x490 },
	{ 0x492, 0x492 },
	{ 0x494, 0x494 },
	{ 0x496, 0x496 },
	{ 0x498, 0x498 },
	{ 0x49a, 0x49a },
	{ 0x49c, 0x49c },
	{ 0x49e, 0x49e },
	{ 0x4a0, 0x4a0 },
	{ 0x4a2, 0x4a2 },
	{ 0x4a4, 0x4a4 },
	{ 0x4a6, 0x4a6 },
	{ 0x4a8, 0x4a8 },
	{ 0x4aa, 0x4aa },
	{ 0x4ac, 0x4ac },
	{ 0x4ae, 0x4ae },
	{ 0x4b0, 0x4b0 },
	{ 0x4b2, 0x4b2 },
	{ 0x4b4, 0x4b4 },
	{ 0x4b6, 0x4b6 },
	{ 0x4b8, 0x4b8 },
	{ 0x4ba, 0x4ba },
	{ 0x4bc, 0x4bc },
	{ 0x4be, 0x4be },
	{ 0x4c0, 0x4c1 },
	{ 0x4c3, 0x4c3 },
	{ 0x4c5, 0x4c5 },
	{ 0x4c7, 0x4c7 },
	{ 0x4c9, 0x4c9 },
	{ 0x4cb, 0x4cb },
	{ 0x4cd, 0x4cd },
	{ 0x4d0, 0x4d0 },
	{ 0x4d2, 0x4d2 },
	{ 0x4d4, 0x4d4 },
	{ 0x4d6, 0x4d6 },
	{ 0x4d8, 0x4d8 },
	{ 0x4da, 0x4da },
	{ 0x4dc, 0x4dc },
	{ 0x4de, 0x4de },
	{ 0x4e0, 0x4e0 },
	{ 0x4e2, 0x4e2 },
	{ 0x4e4, 0x4e4 },
	{ 0x4e6, 0x4e6 },
	{ 0x4e8, 0x4e8 },
	{ 0x4ea, 0x4ea },
	{ 0x4ec, 0x4ec },
	{ 0x4ee, 0x4ee },
	{ 0x4f0, 0x4f0 },
	{ 0x4f2, 0x4f2 },
	{ 0x4f4, 0x4f4 },
	{ 0x4f6, 0x4f6 },
	{ 0x4f8, 0x4f8 },
	{ 0x4fa, 0x4fa },
	{ 0x4fc, 0x4fc },
	{ 0x4fe, 0x4fe },
	{ 0x500, 0x500 },
	{ 0x502, 0x502 },
	{ 0x504, 0x504 },
	{ 0x506, 0x506 },
	{ 0x508, 0x508 },
	{ 0x50a, 0x50a },
	{ 0x50c, 0x50c },
	{ 0x50e, 0x50e },
	{ 0x510, 0x510 },
	{ 0x512, 0x512 },
	{ 0x514, 0x514 },
	{ 0x516, 0x516 },
	{ 0x518, 0x518 },
	{ 0x51a, 0x51a },
	{ 0x51c, 0x51c },
	{ 0x51e, 0x51e },
	{ 0x520, 0x520 },
	{ 0x522, 0x522 },
	{ 0x524, 0x524 },
	{ 0x526, 0x526 },
	{ 0x528, 0x528 },
	{ 0x52a, 0x52a },
	{ 0x52c, 0x52c },
	{ 0x52e, 0x52e },
	{ 0x531, 0x556 },
	{ 0x10a0, 0x10c5 },
	{ 0x10c7, 0x10c7 },
	{ 0x10cd, 0x10cd },
	{ 0x13a0, 0x13f5 },
	{ 0x1c89, 0x1c89 },
	{ 0x1c90, 0x1cba },
	{ 0x1cbd, 0x1cbf },
	{ 0x1e00, 0x1e00 },
	{ 0x1e02, 0x1e02 },
	{ 0x1e04, 0x1e04 },
	{ 0x1e06, 0x1e06 },
	{ 0x1e08, 0x1e08 },
	{ 0x1e0a, 0x1e0a },
	{ 0x1e0c, 0x1e0c },
	{ 0x1e0e, 0x1e0e },
	{ 0x1e10, 0x1e10 },
	{ 0x1e12, 0x1e12 },
	{ 0x1e14, 0x1e14 },
	{ 0x1e16, 0x1e16 },
	{ 0x1e18, 0x1e18 },
	{ 0x1e1a, 0x1e1a },
	{ 0x1e1c, 0x1e1c },
	{ 0x1e1e, 0x1e1e },
	{ 0x1e20, 0x1e20 },
	{ 0x1e22, 0x1e22 },
	{ 0x1e24, 0x1e24 },
	{ 0x1e26, 0x1e26 },
	{ 0x1e28, 0x1e28 },
	{ 0x1e2a, 0x1e2a },
	{ 0x1e2c, 0x1e2c },
	{ 0x1e2e, 0x1e2e },
	{ 0x1e30, 0x1e30 },
	{ 0x1e32, 0x1e32 },
	{ 0x1e34, 0x1e34 },
	{ 0x1e36, 0x1e36 },
	{ 0x1e38, 0x1e38 },
	{ 0x1e3a, 0x1e3a },
	{ 0x1e3c, 0x1e3c },
	{ 0x1e3e, 0x1e3e },
	{ 0x1e40, 0x1e40 },
	{ 0x1e42, 0x1e42 },
	{ 0x1e44, 0x1e44 },
	{ 0x1e46, 0x1e46 },
	{ 0x1e48, 0x1e48 },
	{ 0x1e4a, 0x1e4a },
	{ 0x1e4c, 0x1e4c },
	{ 0x1e4e, 0x1e4e },
	{ 0x1e50, 0x1e50 },
	{ 0x1e52, 0x1e52 },
	{ 0x1e54, 0x1e54 },
	{ 0x1e56, 0x1e56 },
	{ 0x1e58, 0x1e58 },
	{ 0x1e5a, 0x1e5a },
	{ 0x1e5c, 0x1e5c },
	{ 0x1e5e, 0x1e5e },
	{ 0x1e60, 0x1e60 },
	{ 0x1e62, 0x1e62 },
	{ 0x1e64, 0x1e64 },
	{ 0x1e66, 0x1e66 },
	{ 0x1e68, 0x1e68 },
	{ 0x1e6a, 0x1e6a },
	{ 0x1e6c, 0x1e6c },
	{ 0x1e6e, 0x1e6e },
	{ 0x1e70, 0x1e70 },
	{ 0x1e72, 0x1e72 },
	{ 0x1e74, 0x1e74 },
	{ 0x1e76, 0x1e76 },
	{ 0x1e78, 0x1e78 },
	{ 0x1e7a, 0x1e7a },
	{ 0x1e7c, 0x1e7c },
	{ 0x1e7e, 0x1e7e },
	{ 0x1e80, 0x1e80 },
	{ 0x1e82, 0x1e82 },
	{ 0x1e84, 0x1e84 },
	{ 0x1e86, 0x1e86 },
	{ 0x1e88, 0x1e88 },
	{ 0x1e8a, 0x1e8a },
	{ 0x1e8c, 0x1e8c },
	{ 0x1e8e, 0x1e8e },
	{ 0x1e90, 0x1e90 },
	{ 0x1e92, 0x1e92 },
	{ 0x1e94, 0x1e94 },
	{ 0x1e9e, 0x1e9e },
	{ 0x1ea0, 0x1ea0 },
	{ 0x1ea2, 0x1ea2 },
	{ 0x1ea4, 0x1ea4 },
	{ 0x1ea6, 0x1ea6 },
	{ 0x1ea8, 0x1ea8 },
	{ 0x1eaa, 0x1eaa },
	{ 0x1eac, 0x1eac },
	{ 0x1eae, 0x1eae },
	{ 0x1eb0, 0x1eb0 },
	{ 0x1eb2, 0x1eb2 },
	{ 0x1eb4, 0x1eb4 },
	{ 0x1eb6, 0x1eb6 },
	{ 0x1eb8, 0x1eb8 },
	{ 0x1eba, 0x1eba },
	{ 0x1ebc, 0x1ebc },
	{ 0x1ebe, 0x1ebe },
	{ 0x1ec0, 0x1ec0 },
	{ 0x1ec2, 0x1ec2 },
	{ 0x1ec4, 0x1ec4 },
	{ 0x1ec6, 0x1ec6 },
	{ 0x1ec8, 0x1ec8 },
	{ 0x1eca, 0x1eca },
	{ 0x1ecc, 0x1ecc },
	{ 0x1ece, 0x1ece },
	{ 0x1ed0, 0x1ed0 },
	{ 0x1ed2, 0x1ed2 },
	{ 0x1ed4, 0x1ed4 },
	{ 0x1ed6, 0x1ed6 },
	{ 0x1ed8, 0x1ed8 },
	{ 0x1eda, 0x1eda },
	{ 0x1edc, 0x1edc },
	{ 0x1ede, 0x1ede },
	{ 0x1ee0, 0x1ee0 },
	{ 0x1ee2, 0x1ee2 },
	{ 0x1ee4, 0x1ee4 },
	{ 0x1ee6, 0x1ee6 },
	{ 0x1ee8, 0x1ee8 },
	{ 0x1eea, 0x1eea },
	{ 0x1eec, 0x1eec },
	{ 0x1eee, 0x1eee },
	{ 0x1ef0, 0x1ef0 },
	{ 0x1ef2, 0x1ef2 },
	{ 0x1ef4, 0x1ef4 },
	{ 0x1ef6, 0x1ef6 },
	{ 0x1ef8, 0x1ef8 },
	{ 0x1efa, 0x1efa },
	{ 0x1efc, 0x1efc },
	{ 0x1efe, 0x1efe },
	{ 0x1f08, 0x1f0f },
	{ 0x1f18, 0x1f1d },
	{ 0x1f28, 0x1f2f },
	{ 0x1f38, 0x1f3f },
	{ 0x1f48, 0x1f4d },
	{ 0x1f59, 0x1f59 },
	{ 0x1f5b, 0x1f5b },
	{ 0x1f5d, 0x1f5d },
	{ 0x1f5f, 0x1f5f },
	{ 0x1f68, 0x1f6f },
	{ 0x1fb8, 0x1fbb },
	{ 0x1fc8, 0x1fcb },
	{ 0x1fd8, 0x1fdb },
	{ 0x1fe8, 0x1fec },
	{ 0x1ff8, 0x1ffb },
	{ 0x2102, 0x2102 },
	{ 0x2107, 0x2107 },
	{ 0x210b, 0x210d },
	{ 0x2110, 0x2112 },
	{ 0x2115, 0x2115 },
	{ 0x2119, 0x211d },
	{ 0x2124, 0x2124 },
	{ 0x2126, 0x2126 },
	{ 0x2128, 0x2128 },
	{ 0x212a, 0x212d },
	{ 0x2130, 0x2133 },
	{ 0x213e, 0x213f },
	{ 0x2145, 0x2145 },
	{ 0x2160, 0x216f },
	{ 0x2183, 0x2183 },
	{ 0x24b6, 0x24cf },
	{ 0x2c00, 0x2c2f },
	{ 0x2c60, 0x2c60 },
	{ 0x2c62, 0x2c64 },
	{ 0x2c67, 0x2c67 },
	{ 0x2c69, 0x2c69 },
	{ 0x2c6b, 0x2c6b },
	{ 0x2c6d, 0x2c70 },
	{ 0x2c72, 0x2c72 },
	{ 0x2c75, 0x2c75 },
	{ 0x2c7e, 0x2c80 },
	{ 0x2c82, 0x2c82 },
	{ 0x2c84, 0x2c84 },
	{ 0x2c86, 0x2c86 },
	{ 0x2c88, 0x2c88 },
	{ 0x2c8a, 0x2c8a },
	{ 0x2c8c, 0x2c8c },
	{ 0x2c8e, 0x2c8e },
	{ 0x2c90, 0x2c90 },
	{ 0x2c92, 0x2c92 },
	{ 0x2c94, 0x2c94 },
	{ 0x2c96, 0x2c96 },
	{ 0x2c98, 0x2c98 },
	{ 0x2c9a, 0x2c9a },
	{ 0x2c9c, 0x2c9c },
	{ 0x2c9e, 0x2c9e },
	{ 0x2ca0, 0x2ca0 },
	{ 0x2ca2, 0x2ca2 },
	{ 0x2ca4, 0x2ca4 },
	{ 0x2ca6, 0x2ca6 },
	{ 0x2ca8, 0x2ca8 },
	{ 0x2caa, 0x2caa },
	{ 0x2cac, 0x2cac },
	{ 0x2cae, 0x2cae },
	{ 0x2cb0, 0x2cb0 },
	{ 0x2cb2, 0x2cb2 },
	{ 0x2cb4, 0x2cb4 },
	{ 0x2cb6, 0x2cb6 },
	{ 0x2cb8, 0x2cb8 },
	{ 0x2cba, 0x2cba },
	{ 0x2cbc, 0x2cbc },
	{ 0x2cbe, 0x2cbe },
	{ 0x2cc0, 0x2cc0 },
	{ 0x2cc2, 0x2cc2 },
	{ 0x2cc4, 0x2cc4 },
	{ 0x2cc6, 0x2cc6 },
	{ 0x2cc8, 0x2cc8 },
	{ 0x2cca, 0x2cca },
	{ 0x2ccc, 0x2ccc },
	{ 0x2cce, 0x2cce },
	{ 0x2cd0, 0x2cd0 },
	{ 0x2cd2, 0x2cd2 },
	{ 0x2cd4, 0x2cd4 },
	{ 0x2cd6, 0x2cd6 },
	{ 0x2cd8, 0x2cd8 },
	{ 0x2cda, 0x2cda },
	{ 0x2cdc, 0x2cdc },
	{ 0x2cde, 0x2cde },
	{ 0x2ce0, 0x2ce0 },
	{ 0x2ce2, 0x2ce2 },
	{ 0x2ceb, 0x2ceb },
	{ 0x2ced, 0x2ced },
	{ 0x2cf2, 0x2cf2 },
	{ 0xa640, 0xa640 },
	{ 0xa642, 0xa642 },
	{ 0xa644, 0xa644 },
	{ 0xa646, 0xa646 },
	{ 0xa648, 0xa648 },
	{ 0xa64a, 0xa64a },
	{ 0xa64c, 0xa64c },
	{ 0xa64e, 0xa64e },
	{ 0xa650, 0xa650 },
	{ 0xa652, 0xa652 },
	{ 0xa654, 0xa654 },
	{ 0xa656, 0xa656 },
	{ 0xa658, 0xa658 },
	{ 0xa65a, 0xa65a },
	{ 0xa65c, 0xa65c },
	{ 0xa65e, 0xa65e },
	{ 0xa660, 0xa660 },
	{ 0xa662, 0xa662 },
	{ 0xa664, 0xa664 },
	{ 0xa666, 0xa666 },
	{ 0xa668, 0xa668 },
	{ 0xa66a, 0xa66a },
	{ 0xa66c, 0xa66c },
	{ 0xa680, 0xa680 },
	{ 0xa682, 0xa682 },
	{ 0xa684, 0xa684 },
	{ 0xa686, 0xa686 },
	{ 0xa688, 0xa688 },
	{ 0xa68a, 0xa68a },
	{ 0xa68c, 0xa68c },
	{ 0xa68e, 0xa68e },
	{ 0xa690, 0xa690 },
	{ 0xa692, 0xa692 },
	{ 0xa694, 0xa694 },
	{ 0xa696, 0xa696 },
	{ 0xa698, 0xa698 },
	{ 0xa69a, 0xa69a },
	{ 0xa722, 0xa722 },
	{ 0xa724, 0xa724 },
	{ 0xa726, 0xa726 },
	{ 0xa728, 0xa728 },
	{ 0xa72a, 0xa72a },
	{ 0xa72c, 0xa72c },
	{ 0xa72e, 0xa72e },
	{ 0xa732, 0xa732 },
	{ 0xa734, 0xa734 },
	{ 0xa736, 0xa736 },
	{ 0xa738, 0xa738 },
	{ 0xa73a, 0xa73a },
	{ 0xa73c, 0xa73c },
	{ 0xa73e, 0xa73e },
	{ 0xa740, 0xa740 },
	{ 0xa742, 0xa742 },
	{ 0xa744, 0xa744 },
	{ 0xa746, 0xa746 },
	{ 0xa748, 0xa748 },
	{ 0xa74a, 0xa74a },
	{ 0xa74c, 0xa74c },
	{ 0xa74e, 0xa74e },
	{ 0xa750, 0xa750 },
	{ 0xa752, 0xa752 },
	{ 0xa754, 0xa754 },
	{ 0xa756, 0xa756 },
	{ 0xa758, 0xa758 },
	{ 0xa75a, 0xa75a },
	{ 0xa75c, 0xa75c },
	{ 0xa75e, 0xa75e },
	{ 0xa760, 0xa760 },
	{ 0xa762, 0xa762 },
	{ 0xa764, 0xa764 },
	{ 0xa766, 0xa766 },
	{ 0xa768, 0xa768 },
	{ 0xa76a, 0xa76a },
	{ 0xa76c, 0xa76c },
	{ 0xa76e, 0xa76e },
	{ 0xa779, 0xa779 },
	{ 0xa77b, 0xa77b },
	{ 0xa77d, 0xa77e },
	{ 0xa780, 0xa780 },
	{ 0xa782, 0xa782 },
	{ 0xa784, 0xa784 },
	{ 0xa786, 0xa786 },
	{ 0xa78b, 0xa78b },
	{ 0xa78d, 0xa78d },
	{ 0xa790, 0xa790 },
	{ 0xa792, 0xa792 },
	{ 0xa796, 0xa796 },
	{ 0xa798, 0xa798 },
	{ 0xa79a, 0xa79a },
	{ 0xa79c, 0xa79c },
	{ 0xa79e, 0xa79e },
	{ 0xa7a0, 0xa7a0 },
	{ 0xa7a2, 0xa7a2 },
	{ 0xa7a4, 0xa7a4 },
	{ 0xa7a6, 0xa7a6 },
	{ 0xa7a8, 0xa7a8 },
	{ 0xa7aa, 0xa7ae },
	{ 0xa7b0, 0xa7b4 },
	{ 0xa7b6, 0xa7b6 },
	{ 0xa7b8, 0xa7b8 },
	{ 0xa7ba, 0xa7ba },
	{ 0xa7bc, 0xa7bc },
	{ 0xa7be, 0xa7be },
	{ 0xa7c0, 0xa7c0 },
	{ 0xa7c2, 0xa7c2 },
	{ 0xa7c4, 0xa7c7 },
	{ 0xa7c9, 0xa7c9 },
	{ 0xa7cb, 0xa7cc },
	{ 0xa7d0, 0xa7d0 },
	{ 0xa7d6, 0xa7d6 },
	{ 0xa7d8, 0xa7d8 },
	{ 0xa7da, 0xa7da },
	{ 0xa7dc, 0xa7dc },
	{ 0xa7f5, 0xa7f5 },
	{ 0xff21, 0xff3a },
	{ 0x10400, 0x10427 },
	{ 0x104b0, 0x104d3 },
	{ 0x10570, 0x1057a },
	{ 0x1057c, 0x1058a },
	{ 0x1058c, 0x10592 },
	{ 0x10594, 0x10595 },
	{ 0x10c80, 0x10cb2 },
	{ 0x10d50, 0x10d65 },
	{ 0x118a0, 0x118bf },
	{ 0x16e40, 0x16e5f },
	{ 0x1d400, 0x1d419 },
	{ 0x1d434, 0x1d44d },
	{ 0x1d468, 0x1d481 },
	{ 0x1d49c, 0x1d49c },
	{ 0x1d49e, 0x1d49f },
	{ 0x1d4a2, 0x1d4a2 },
	{ 0x1d4a5, 0x1d4a6 },
	{ 0x1d4a9, 0x1d4ac },
	{ 0x1d4ae, 0x1d4b5 },
	{ 0x1d4d0, 0x1d4e9 },
	{ 0x1d504, 0x1d505 },
	{ 0x1d507, 0x1d50a },
	{ 0x1d50d, 0x1d514 },
	{ 0x1d516, 0x1d51c },
	{ 0x1d538, 0x1d539 },
	{ 0x1d53b, 0x1d53e },
	{ 0x1d540, 0x1d544 },
	{ 0x1d546, 0x1d546 },
	{ 0x1d54a, 0x1d550 },
	{ 0x1d56c, 0x1d585 },
	{ 0x1d5a0, 0x1d5b9 },
	{ 0x1d5d4, 0x1d5ed },
	{ 0x1d608, 0x1d621 },
	{ 0x1d63c, 0x1d655 },
	{ 0x1d670, 0x1d689 },
	{ 0x1d6a8, 0x1d6c0 },
	{ 0x1d6e2, 0x1d6fa },
	{ 0x1d71c, 0x1d734 },
	{ 0x1d756, 0x1d76e },
	{ 0x1d790, 0x1d7a8 },
	{ 0x1d7ca, 0x1d7ca },
	{ 0x1e900, 0x1e921 },
	{ 0x1f130, 0x1f149 },
	{ 0x1f150, 0x1f169 },
	{ 0x1f170, 0x1f189 },
};

constexpr inline CharRange lowercase_letter[] = {
	{ 0x61, 0x7a },
	{ 0xaa, 0xaa },
	{ 0xb5, 0xb5 },
	{ 0xba, 0xba },
	{ 0xdf, 0xf6 },
	{ 0xf8, 0xff },
	{ 0x101, 0x101 },
	{ 0x103, 0x103 },
	{ 0x105, 0x105 },
	{ 0x107, 0x107 },
	{ 0x109, 0x109 },
	{ 0x10b, 0x10b },
	{ 0x10d, 0x10d },
	{ 0x10f, 0x10f },
	{ 0x111, 0x111 },
	{ 0x113, 0x113 },
	{ 0x115, 0x115 },
	{ 0x117, 0x117 },
	{ 0x119, 0x119 },
	{ 0x11b, 0x11b },
	{ 0x11d, 0x11d },
	{ 0x11f, 0x11f },
	{ 0x121, 0x121 },
	{ 0x123, 0x123 },
	{ 0x125, 0x125 },
	{ 0x127, 0x127 },
	{ 0x129, 0x129 },
	{ 0x12b, 0x12b },
	{ 0x12d, 0x12d },
	{ 0x12f, 0x12f },
	{ 0x131, 0x131 },
	{ 0x133, 0x133 },
	{ 0x135, 0x135 },
	{ 0x137, 0x138 },
	{ 0x13a, 0x13a },
	{ 0x13c, 0x13c },
	{ 0x13e, 0x13e },
	{ 0x140, 0x140 },
	{ 0x142, 0x142 },
	{ 0x144, 0x144 },
	{ 0x146, 0x146 },
	{ 0x148, 0x149 },
	{ 0x14b, 0x14b },
	{ 0x14d, 0x14d },
	{ 0x14f, 0x14f },
	{ 0x151, 0x151 },
	{ 0x153, 0x153 },
	{ 0x155, 0x155 },
	{ 0x157, 0x157 },
	{ 0x159, 0x159 },
	{ 0x15b, 0x15b },
	{ 0x15d, 0x15d },
	{ 0x15f, 0x15f },
	{ 0x161, 0x161 },
	{ 0x163, 0x163 },
	{ 0x165, 0x165 },
	{ 0x167, 0x167 },
	{ 0x169, 0x169 },
	{ 0x16b, 0x16b },
	{ 0x16d, 0x16d },
	{ 0x16f, 0x16f },
	{ 0x171, 0x171 },
	{ 0x173, 0x173 },
	{ 0x175, 0x175 },
	{ 0x177, 0x177 },
	{ 0x17a, 0x17a },
	{ 0x17c, 0x17c },
	{ 0x17e, 0x180 },
	{ 0x183, 0x183 },
	{ 0x185, 0x185 },
	{ 0x188, 0x188 },
	{ 0x18c, 0x18d },
	{ 0x192, 0x192 },
	{ 0x195, 0x195 },
	{ 0x199, 0x19b },
	{ 0x19e, 0x19e },
	{ 0x1a1, 0x1a1 },
	{ 0x1a3, 0x1a3 },
	{ 0x1a5, 0x1a5 },
	{ 0x1a8, 0x1a8 },
	{ 0x1aa, 0x1ab },
	{ 0x1ad, 0x1ad },
	{ 0x1b0, 0x1b0 },
	{ 0x1b4, 0x1b4 },
	{ 0x1b6, 0x1b6 },
	{ 0x1b9, 0x1ba },
	{ 0x1bd, 0x1bf },
	{ 0x1c6, 0x1c6 },
	{ 0x1c9, 0x1c9 },
	{ 0x1cc, 0x1cc },
	{ 0x1ce, 0x1ce },
	{ 0x1d0, 0x1d0 },
	{ 0x1d2, 0x1d2 },
	{ 0x1d4, 0x1d4 },
	{ 0x1d6, 0x1d6 },
	{ 0x1d8, 0x1d8 },
	{ 0x1da, 0x1da },
	{ 0x1dc, 0x1dd },
	{ 0x1df, 0x1df },
	{ 0x1e1, 0x1e1 },
	{ 0x1e3, 0x1e3 },
	{ 0x1e5, 0x1e5 },
	{ 0x1e7, 0x1e7 },
	{ 0x1e9, 0x1e9 },
	{ 0x1eb, 0x1eb },
	{ 0x1ed, 0x1ed },
	{ 0x1ef, 0x1f0 },
	{ 0x1f3, 0x1f3 },
	{ 0x1f5, 0x1f5 },
	{ 0x1f9, 0x1f9 },
	{ 0x1fb, 0x1fb },
	{ 0x1fd, 0x1fd },
	{ 0x1ff, 0x1ff },
	{ 0x201, 0x201 },
	{ 0x203, 0x203 },
	{ 0x205, 0x205 },
	{ 0x207, 0x207 },
	{ 0x209, 0x209 },
	{ 0x20b, 0x20b },
	{ 0x20d, 0x20d },
	{ 0x20f, 0x20f },
	{ 0x211, 0x211 },
	{ 0x213, 0x213 },
	{ 0x215, 0x215 },
	{ 0x217, 0x217 },
	{ 0x219, 0x219 },
	{ 0x21b, 0x21b },
	{ 0x21d, 0x21d },
	{ 0x21f, 0x21f },
	{ 0x221, 0x221 },
	{ 0x223, 0x223 },
	{ 0x225, 0x225 },
	{ 0x227, 0x227 },
	{ 0x229, 0x229 },
	{ 0x22b, 0x22b },
	{ 0x22d, 0x22d },
	{ 0x22f, 0x22f },
	{ 0x231, 0x231 },
	{ 0x233, 0x239 },
	{ 0x23c, 0x23c },
	{ 0x23f, 0x240 },
	{ 0x242, 0x242 },
	{ 0x247, 0x247 },
	{ 0x249, 0x249 },
	{ 0x24b, 0x24b },
	{ 0x24d, 0x24d },
	{ 0x24f, 0x293 },
	{ 0x295, 0x2b8 },
	{ 0x2c0, 0x2c1 },
	{ 0x2e0, 0x2e4 },
	{ 0x345, 0x345 },
	{ 0x371, 0x371 },
	{ 0x373, 0x373 },
	{ 0x377, 0x377 },
	{ 0x37a, 0x37d },
	{ 0x390, 0x390 },
	{ 0x3ac, 0x3ce },
	{ 0x3d0, 0x3d1 },
	{ 0x3d5, 0x3d7 },
	{ 0x3d9, 0x3d9 },
	{ 0x3db, 0x3db },
	{ 0x3dd, 0x3dd },
	{ 0x3df, 0x3df },
	{ 0x3e1, 0x3e1 },
	{ 0x3e3, 0x3e3 },
	{ 0x3e5, 0x3e5 },
	{ 0x3e7, 0x3e7 },
	{ 0x3e9, 0x3e9 },
	{ 0x3eb, 0x3eb },
	{ 0x3ed, 0x3ed },
	{ 0x3ef, 0x3f3 },
	{ 0x3f5, 0x3f5 },
	{ 0x3f8, 0x3f8 },
	{ 0x3fb, 0x3fc },
	{ 0x430, 0x45f },
	{ 0x461, 0x461 },
	{ 0x463, 0x463 },
	{ 0x465, 0x465 },
	{ 0x467, 0x467 },
	{ 0x469, 0x469 },
	{ 0x46b, 0x46b },
	{ 0x46d, 0x46d },
	{ 0x46f, 0x46f },
	{ 0x471, 0x471 },
	{ 0x473, 0x473 },
	{ 0x475, 0x475 },
	{ 0x477, 0x477 },
	{ 0x479, 0x479 },
	{ 0x47b, 0x47b },
	{ 0x47d, 0x47d },
	{ 0x47f, 0x47f },
	{ 0x481, 0x481 },
	{ 0x48b, 0x48b },
	{ 0x48d, 0x48d },
	{ 0x48f, 0x48f },
	{ 0x491, 0x491 },
	{ 0x493, 0x493 },
	{ 0x495, 0x495 },
	{ 0x497, 0x497 },
	{ 0x499, 0x499 },
	{ 0x49b, 0x49b },
	{ 0x49d, 0x49d },
	{ 0x49f, 0x49f },
	{ 0x4a1, 0x4a1 },
	{ 0x4a3, 0x4a3 },
	{ 0x4a5, 0x4a5 },
	{ 0x4a7, 0x4a7 },
	{ 0x4a9, 0x4a9 },
	{ 0x4ab, 0x4ab },
	{ 0x4ad, 0x4ad },
	{ 0x4af, 0x4af },
	{ 0x4b1, 0x4b1 },
	{ 0x4b3, 0x4b3 },
	{ 0x4b5, 0x4b5 },
	{ 0x4b7, 0x4b7 },
	{ 0x4b9, 0x4b9 },
	{ 0x4bb, 0x4bb },
	{ 0x4bd, 0x4bd },
	{ 0x4bf, 0x4bf },
	{ 0x4c2, 0x4c2 },
	{ 0x4c4, 0x4c4 },
	{ 0x4c6, 0x4c6 },
	{ 0x4c8, 0x4c8 },
	{ 0x4ca, 0x4ca },
	{ 0x4cc, 0x4cc },
	{ 0x4ce, 0x4cf },
	{ 0x4d1, 0x4d1 },
	{ 0x4d3, 0x4d3 },
	{ 0x4d5, 0x4d5 },
	{ 0x4d7, 0x4d7 },
	{ 0x4d9, 0x4d9 },
	{ 0x4db, 0x4db },
	{ 0x4dd, 0x4dd },
	{ 0x4df, 0x4df },
	{ 0x4e1, 0x4e1 },
	{ 0x4e3, 0x4e3 },
	{ 0x4e5, 0x4e5 },
	{ 0x4e7, 0x4e7 },
	{ 0x4e9, 0x4e9 },
	{ 0x4eb, 0x4eb },
	{ 0x4ed, 0x4ed },
	{ 0x4ef, 0x4ef },
	{ 0x4f1, 0x4f1 },
	{ 0x4f3, 0x4f3 },
	{ 0x4f5, 0x4f5 },
	{ 0x4f7, 0x4f7 },
	{ 0x4f9, 0x4f9 },
	{ 0x4fb, 0x4fb },
	{ 0x4fd, 0x4fd },
	{ 0x4ff, 0x4ff },
	{ 0x501, 0x501 },
	{ 0x503, 0x503 },
	{ 0x505, 0x505 },
	{ 0x507, 0x507 },
	{ 0x509, 0x509 },
	{ 0x50b, 0x50b },
	{ 0x50d, 0x50d },
	{ 0x50f, 0x50f },
	{ 0x511, 0x511 },
	{ 0x513, 0x513 },
	{ 0x515, 0x515 },
	{ 0x517, 0x517 },
	{ 0x519, 0x519 },
	{ 0x51b, 0x51b },
	{ 0x51d, 0x51d },
	{ 0x51f, 0x51f },
	{ 0x521, 0x521 },
	{ 0x523, 0x523 },
	{ 0x525, 0x525 },
	{ 0x527, 0x527 },
	{ 0x529, 0x529 },
	{ 0x52b, 0x52b },
	{ 0x52d, 0x52d },
	{ 0x52f, 0x52f },
	{ 0x560, 0x588 },
	{ 0x10d0, 0x10fa },
	{ 0x10fc, 0x10ff },
	{ 0x13f8, 0x13fd },
	{ 0x1c80, 0x1c88 },
	{ 0x1c8a, 0x1c8a },
	{ 0x1d00, 0x1dbf },
	{ 0x1e01, 0x1e01 },
	{ 0x1e03, 0x1e03 },
	{ 0x1e05, 0x1e05 },
	{ 0x1e07, 0x1e07 },
	{ 0x1e09, 0x1e09 },
	{ 0x1e0b, 0x1e0b },
	{ 0x1e0d, 0x1e0d },
	{ 0x1e0f, 0x1e0f },
	{ 0x1e11, 0x1e11 },
	{ 0x1e13, 0x1e13 },
	{ 0x1e15, 0x1e15 },
	{ 0x1e17, 0x1e17 },
	{ 0x1e19, 0x1e19 },
	{ 0x1e1b, 0x1e1b },
	{ 0x1e1d, 0x1e1d },
	{ 0x1e1f, 0x1e1f },
	{ 0x1e21, 0x1e21 },
	{ 0x1e23, 0x1e23 },
	{ 0x1e25, 0x1e25 },
	{ 0x1e27, 0x1e27 },
	{ 0x1e29, 0x1e29 },
	{ 0x1e2b, 0x1e2b },
	{ 0x1e2d, 0x1e2d },
	{ 0x1e2f, 0x1e2f },
	{ 0x1e31, 0x1e31 },
	{ 0x1e33, 0x1e33 },
	{ 0x1e35, 0x1e35 },
	{ 0x1e37, 0x1e37 },
	{ 0x1e39, 0x1e39 },
	{ 0x1e3b, 0x1e3b },
	{ 0x1e3d, 0x1e3d },
	{ 0x1e3f, 0x1e3f },
	{ 0x1e41, 0x1e41 },
	{ 0x1e43, 0x1e43 },
	{ 0x1e45, 0x1e45 },
	{ 0x1e47, 0x1e47 },
	{ 0x1e49, 0x1e49 },
	{ 0x1e4b, 0x1e4b },
	{ 0x1e4d, 0x1e4d },
	{ 0x1e4f, 0x1e4f },
	{ 0x1e51, 0x1e51 },
	{ 0x1e53, 0x1e53 },
	{ 0x1e55, 0x1e55 },
	{ 0x1e57, 0x1e57 },
	{ 0x1e59, 0x1e59 },
	{ 0x1e5b, 0x1e5b },
	{ 0x1e5d, 0x1e5d },
	{ 0x1e5f, 0x1e5f },
	{ 0x1e61, 0x1e61 },
	{ 0x1e63, 0x1e63 },
	{ 0x1e65, 0x1e65 },
	{ 0x1e67, 0x1e67 },
	{ 0x1e69, 0x1e69 },
	{ 0x1e6b, 0x1e6b },
	{ 0x1e6d, 0x1e6d },
	{ 0x1e6f, 0x1e6f },
	{ 0x1e71, 0x1e71 },
	{ 0x1e73, 0x1e73 },
	{ 0x1e75, 0x1e75 },
	{ 0x1e77, 0x1e77 },
	{ 0x1e79, 0x1e79 },
	{ 0x1e7b, 0x1e7b },
	{ 0x1e7d, 0x1e7d },
	{ 0x1e7f, 0x1e7f },
	{ 0x1e81, 0x1e81 },
	{ 0x1e83, 0x1e83 },
	{ 0x1e85, 0x1e85 },
	{ 0x1e87, 0x1e87 },
	{ 0x1e89, 0x1e89 },
	{ 0x1e8b, 0x1e8b },
	{ 0x1e8d, 0x1e8d },
	{ 0x1e8f, 0x1e8f },
	{ 0x1e91, 0x1e91 },
	{ 0x1e93, 0x1e93 },
	{ 0x1e95, 0x1e9d },
	{ 0x1e9f, 0x1e9f },
	{ 0x1ea1, 0x1ea1 },
	{ 0x1ea3, 0x1ea3 },
	{ 0x1ea5, 0x1ea5 },
	{ 0x1ea7, 0x1ea7 },
	{ 0x1ea9, 0x1ea9 },
	{ 0x1eab, 0x1eab },
	{ 0x1ead, 0x1ead },
	{ 0x1eaf, 0x1eaf },
	{ 0x1eb1, 0x1eb1 },
	{ 0x1eb3, 0x1eb3 },
	{ 0x1eb5, 0x1eb5 },
	{ 0x1eb7, 0x1eb7 },
	{ 0x1eb9, 0x1eb9 },
	{ 0x1ebb, 0x1ebb },
	{ 0x1ebd, 0x1ebd },
	{ 0x1ebf, 0x1ebf },
	{ 0x1ec1, 0x1ec1 },
	{ 0x1ec3, 0x1ec3 },
	{ 0x1ec5, 0x1ec5 },
	{ 0x1ec7, 0x1ec7 },
	{ 0x1ec9, 0x1ec9 },
	{ 0x1ecb, 0x1ecb },
	{ 0x1ecd, 0x1ecd },
	{ 0x1ecf, 0x1ecf },
	{ 0x1ed1, 0x1ed1 },
	{ 0x1ed3, 0x1ed3 },
	{ 0x1ed5, 0x1ed5 },
	{ 0x1ed7, 0x1ed7 },
	{ 0x1ed9, 0x1ed9 },
	{ 0x1edb, 0x1edb },
	{ 0x1edd, 0x1edd },
	{ 0x1edf, 0x1edf },
	{ 0x1ee1, 0x1ee1 },
	{ 0x1ee3, 0x1ee3 },
	{ 0x1ee5, 0x1ee5 },
	{ 0x1ee7, 0x1ee7 },
	{ 0x1ee9, 0x1ee9 },
	{ 0x1eeb, 0x1eeb },
	{ 0x1eed, 0x1eed },
	{ 0x1eef, 0x1eef },
	{ 0x1ef1, 0x1ef1 },
	{ 0x1ef3, 0x1ef3 },
	{ 0x1ef5, 0x1ef5 },
	{ 0x1ef7, 0x1ef7 },
	{ 0x1ef9, 0x1ef9 },
	{ 0x1efb, 0x1efb },
	{ 0x1efd, 0x1efd },
	{ 0x1eff, 0x1f07 },
	{ 0x1f10, 0x1f15 },
	{ 0x1f20, 0x1f27 },
	{ 0x1f30, 0x1f37 },
	{ 0x1f40, 0x1f45 },
	{ 0x1f50, 0x1f57 },
	{ 0x1f60, 0x1f67 },
	{ 0x1f70, 0x1f7d },
	{ 0x1f80, 0x1f87 },
	{ 0x1f90, 0x1f97 },
	{ 0x1fa0, 0x1fa7 },
	{ 0x1fb0, 0x1fb4 },
	{ 0x1fb6, 0x1fb7 },
	{ 0x1fbe, 0x1fbe },
	{ 0x1fc2, 0x1fc4 },
	{ 0x1fc6, 0x1fc7 },
	{ 0x1fd0, 0x1fd3 },
	{ 0x1fd6, 0x1fd7 },
	{ 0x1fe0, 0x1fe7 },
	{ 0x1ff2, 0x1ff4 },
	{ 0x1ff6, 0x1ff7 },
	{ 0x2071, 0x2071 },
	{ 0x207f, 0x207f },
	{ 0x2090, 0x209c },
	{ 0x210a, 0x210a },
	{ 0x210e, 0x210f },
	{ 0x2113, 0x2113 },
	{ 0x212f, 0x212f },
	{ 0x2134, 0x2134 },
	{ 0x2139, 0x2139 },
	{ 0x213c, 0x213d },
	{ 0x2146, 0x2149 },
	{ 0x214e, 0x214e },
	{ 0x2170, 0x217f },
	{ 0x2184, 0x2184 },
	{ 0x24d0, 0x24e9 },
	{ 0x2c30, 0x2c5f },
	{ 0x2c61, 0x2c61 },
	{ 0x2c65, 0x2c66 },
	{ 0x2c68, 0x2c68 },
	{ 0x2c6a, 0x2c6a },
	{ 0x2c6c, 0x2c6c },
	{ 0x2c71, 0x2c71 },
	{ 0x2c73, 0x2c74 },
	{ 0x2c76, 0x2c7d },
	{ 0x2c81, 0x2c81 },
	{ 0x2c83, 0x2c83 },
	{ 0x2c85, 0x2c85 },
	{ 0x2c87, 0x2c87 },
	{ 0x2c89, 0x2c89 },
	{ 0x2c8b, 0x2c8b },
	{ 0x2c8d, 0x2c8d },
	{ 0x2c8f, 0x2c8f },
	{ 0x2c91, 0x2c91 },
	{ 0x2c93, 0x2c93 },
	{ 0x2c95, 0x2c95 },
	{ 0x2c97, 0x2c97 },
	{ 0x2c99, 0x2c99 },
	{ 0x2c9b, 0x2c9b },
	{ 0x2c9d, 0x2c9d },
	{ 0x2c9f, 0x2c9f },
	{ 0x2ca1, 0x2ca1 },
	{ 0x2ca3, 0x2ca3 },
	{ 0x2ca5, 0x2ca5 },
	{ 0x2ca7, 0x2ca7 },
	{ 0x2ca9, 0x2ca9 },
	{ 0x2cab, 0x2cab },
	{ 0x2cad, 0x2cad },
	{ 0x2caf, 0x2caf },
	{ 0x2cb1, 0x2cb1 },
	{ 0x2cb3, 0x2cb3 },
	{ 0x2cb5, 0x2cb5 },
	{ 0x2cb7, 0x2cb7 },
	{ 0x2cb9, 0x2cb9 },
	{ 0x2cbb, 0x2cbb },
	{ 0x2cbd, 0x2cbd },
	{ 0x2cbf, 0x2cbf },
	{ 0x2cc1, 0x2cc1 },
	{ 0x2cc3, 0x2cc3 },
	{ 0x2cc5, 0x2cc5 },
	{ 0x2cc7, 0x2cc7 },
	{ 0x2cc9, 0x2cc9 },
	{ 0x2ccb, 0x2ccb },
	{ 0x2ccd, 0x2ccd },
	{ 0x2ccf, 0x2ccf },
	{ 0x2cd1, 0x2cd1 },
	{ 0x2cd3, 0x2cd3 },
	{ 0x2cd5, 0x2cd5 },
	{ 0x2cd7, 0x2cd7 },
	{ 0x2cd9, 0x2cd9 },
	{ 0x2cdb, 0x2cdb },
	{ 0x2cdd, 0x2cdd },
	{ 0x2cdf, 0x2cdf },
	{ 0x2ce1, 0x2ce1 },
	{ 0x2ce3, 0x2ce4 },
	{ 0x2cec, 0x2cec },
	{ 0x2cee, 0x2cee },
	{ 0x2cf3, 0x2cf3 },
	{ 0x2d00, 0x2d25 },
	{ 0x2d27, 0x2d27 },
	{ 0x2d2d, 0x2d2d },
	{ 0xa641, 0xa641 },
	{ 0xa643, 0xa643 },
	{ 0xa645, 0xa645 },
	{ 0xa647, 0xa647 },
	{ 0xa649, 0xa649 },
	{ 0xa64b, 0xa64b },
	{ 0xa64d, 0xa64d },
	{ 0xa64f, 0xa64f },
	{ 0xa651, 0xa651 },
	{ 0xa653, 0xa653 },
	{ 0xa655, 0xa655 },
	{ 0xa657, 0xa657 },
	{ 0xa659, 0xa659 },
	{ 0xa65b, 0xa65b },
	{ 0xa65d, 0xa65d },
	{ 0xa65f, 0xa65f },
	{ 0xa661, 0xa661 },
	{ 0xa663, 0xa663 },
	{ 0xa665, 0xa665 },
	{ 0xa667, 0xa667 },
	{ 0xa669, 0xa669 },
	{ 0xa66b, 0xa66b },
	{ 0xa66d, 0xa66d },
	{ 0xa681, 0xa681 },
	{ 0xa683, 0xa683 },
	{ 0xa685, 0xa685 },
	{ 0xa687, 0xa687 },
	{ 0xa689, 0xa689 },
	{ 0xa68b, 0xa68b },
	{ 0xa68d, 0xa68d },
	{ 0xa68f, 0xa68f },
	{ 0xa691, 0xa691 },
	{ 0xa693, 0xa693 },
	{ 0xa695, 0xa695 },
	{ 0xa697, 0xa697 },
	{ 0xa699, 0xa699 },
	{ 0xa69b, 0xa69d },
	{ 0xa723, 0xa723 },
	{ 0xa725, 0xa725 },
	{ 0xa727, 0xa727 },
	{ 0xa729, 0xa729 },
	{ 0xa72b, 0xa72b },
	{ 0xa72d, 0xa72d },
	{ 0xa72f, 0xa731 },
	{ 0xa733, 0xa733 },
	{ 0xa735, 0xa735 },
	{ 0xa737, 0xa737 },
	{ 0xa739, 0xa739 },
	{ 0xa73b, 0xa73b },
	{ 0xa73d, 0xa73d },
	{ 0xa73f, 0xa73f },
	{ 0xa741, 0xa741 },
	{ 0xa743, 0xa743 },
	{ 0xa745, 0xa745 },
	{ 0xa747, 0xa747 },
	{ 0xa749, 0xa749 },
	{ 0xa74b, 0xa74b },
	{ 0xa74d, 0xa74d },
	{ 0xa74f, 0xa74f },
	{ 0xa751, 0xa751 },
	{ 0xa753, 0xa753 },
	{ 0xa755, 0xa755 },
	{ 0xa757, 0xa757 },
	{ 0xa759, 0xa759 },
	{ 0xa75b, 0xa75b },
	{ 0xa75d, 0xa75d },
	{ 0xa75f, 0xa75f },
	{ 0xa761, 0xa761 },
	{ 0xa763, 0xa763 },
	{ 0xa765, 0xa765 },
	{ 0xa767, 0xa767 },
	{ 0xa769, 0xa769 },
	{ 0xa76b, 0xa76b },
	{ 0xa76d, 0xa76d },
	{ 0xa76f, 0xa778 },
	{ 0xa77a, 0xa77a },
	{ 0xa77c, 0xa77c },
	{ 0xa77f, 0xa77f },
	{ 0xa781, 0xa781 },
	{ 0xa783, 0xa783 },
	{ 0xa785, 0xa785 },
	{ 0xa787, 0xa787 },
	{ 0xa78c, 0xa78c },
	{ 0xa78e, 0xa78e },
	{ 0xa791, 0xa791 },
	{ 0xa793, 0xa795 },
	{ 0xa797, 0xa797 },
	{ 0xa799, 0xa799 },
	{ 0xa79b, 0xa79b },
	{ 0xa79d, 0xa79d },
	{ 0xa79f, 0xa79f },
	{ 0xa7a1, 0xa7a1 },
	{ 0xa7a3, 0xa7a3 },
	{ 0xa7a5, 0xa7a5 },
	{ 0xa7a7, 0xa7a7 },
	{ 0xa7a9, 0xa7a9 },
	{ 0xa7af, 0xa7af },
	{ 0xa7b5, 0xa7b5 },
	{ 0xa7b7, 0xa7b7 },
	{ 0xa7b9, 0xa7b9 },
	{ 0xa7bb, 0xa7bb },
	{ 0xa7bd, 0xa7bd },
	{ 0xa7bf, 0xa7bf },
	{ 0xa7c1, 0xa7c1 },
	{ 0xa7c3, 0xa7c3 },
	{ 0xa7c8, 0xa7c8 },
	{ 0xa7ca, 0xa7ca },
	{ 0xa7cd, 0xa7cd },
	{ 0xa7d1, 0xa7d1 },
	{ 0xa7d3, 0xa7d3 },
	{ 0xa7d5, 0xa7d5 },
	{ 0xa7d7, 0xa7d7 },
	{ 0xa7d9, 0xa7d9 },
	{ 0xa7db, 0xa7db },
	{ 0xa7f2, 0xa7f4 },
	{ 0xa7f6, 0xa7f6 },
	{ 0xa7f8, 0xa7fa },
	{ 0xab30, 0xab5a },
	{ 0xab5c, 0xab69 },
	{ 0xab70, 0xabbf },
	{ 0xfb00, 0xfb06 },
	{ 0xfb13, 0xfb17 },
	{ 0xff41, 0xff5a },
	{ 0x10428, 0x1044f },
	{ 0x104d8, 0x104fb },
	{ 0x10597, 0x105a1 },
	{ 0x105a3, 0x105b1 },
	{ 0x105b3, 0x105b9 },
	{ 0x105bb, 0x105bc },
	{ 0x10780, 0x10780 },
	{ 0x10783, 0x10785 },
	{ 0x10787, 0x107b0 },
	{ 0x107b2, 0x107ba },
	{ 0x10cc0, 0x10cf2 },
	{ 0x10d70, 0x10d85 },
	{ 0x118c0, 0x118df },
	{ 0x16e60, 0x16e7f },
	{ 0x1d41a, 0x1d433 },
	{ 0x1d44e, 0x1d454 },
	{ 0x1d456, 0x1d467 },
	{ 0x1d482, 0x1d49b },
	{ 0x1d4b6, 0x1d4b9 },
	{ 0x1d4bb, 0x1d4bb },
	{ 0x1d4bd, 0x1d4c3 },
	{ 0x1d4c5, 0x1d4cf },
	{ 0x1d4ea, 0x1d503 },
	{ 0x1d51e, 0x1d537 },
	{ 0x1d552, 0x1d56b },
	{ 0x1d586, 0x1d59f },
	{ 0x1d5ba, 0x1d5d3 },
	{ 0x1d5ee, 0x1d607 },
	{ 0x1d622, 0x1d63b },
	{ 0x1d656, 0x1d66f },
	{ 0x1d68a, 0x1d6a5 },
	{ 0x1d6c2, 0x1d6da },
	{ 0x1d6dc, 0x1d6e1 },
	{ 0x1d6fc, 0x1d714 },
	{ 0x1d716, 0x1d71b },
	{ 0x1d736, 0x1d74e },
	{ 0x1d750, 0x1d755 },
	{ 0x1d770, 0x1d788 },
	{ 0x1d78a, 0x1d78f },
	{ 0x1d7aa, 0x1d7c2 },
	{ 0x1d7c4, 0x1d7c9 },
	{ 0x1d7cb, 0x1d7cb },
	{ 0x1df00, 0x1df09 },
	{ 0x1df0b, 0x1df1e },
	{ 0x1df25, 0x1df2a },
	{ 0x1e030, 0x1e06d },
	{ 0x1e922, 0x1e943 },
};

constexpr inline CharRange unicode_letter[] = {
	{ 0x41, 0x5a },
	{ 0x61, 0x7a },
	{ 0xaa, 0xaa },
	{ 0xb5, 0xb5 },
	{ 0xba, 0xba },
	{ 0xc0, 0xd6 },
	{ 0xd8, 0xf6 },
	{ 0xf8, 0x2c1 },
	{ 0x2c6, 0x2d1 },
	{ 0x2e0, 0x2e4 },
	{ 0x2ec, 0x2ec },
	{ 0x2ee, 0x2ee },
	{ 0x345, 0x345 },
	{ 0x363, 0x374 },
	{ 0x376, 0x377 },
	{ 0x37a, 0x37d },
	{ 0x37f, 0x37f },
	{ 0x386, 0x386 },
	{ 0x388, 0x38a },
	{ 0x38c, 0x38c },
	{ 0x38e, 0x3a1 },
	{ 0x3a3, 0x3f5 },
	{ 0x3f7, 0x481 },
	{ 0x48a, 0x52f },
	{ 0x531, 0x556 },
	{ 0x559, 0x559 },
	{ 0x560, 0x588 },
	{ 0x5b0, 0x5bd },
	{ 0x5bf, 0x5bf },
	{ 0x5c1, 0x5c2 },
	{ 0x5c4, 0x5c5 },
	{ 0x5c7, 0x5c7 },
	{ 0x5d0, 0x5ea },
	{ 0x5ef, 0x5f2 },
	{ 0x610, 0x61a },
	{ 0x620, 0x657 },
	{ 0x659, 0x65f },
	{ 0x66e, 0x6d3 },
	{ 0x6d5, 0x6dc },
	{ 0x6e1, 0x6e8 },
	{ 0x6ed, 0x6ef },
	{ 0x6fa, 0x6fc },
	{ 0x6ff, 0x6ff },
	{ 0x710, 0x73f },
	{ 0x74d, 0x7b1 },
	{ 0x7ca, 0x7ea },
	{ 0x7f4, 0x7f5 },
	{ 0x7fa, 0x7fa },
	{ 0x800, 0x817 },
	{ 0x81a, 0x82c },
	{ 0x840, 0x858 },
	{ 0x860, 0x86a },
	{ 0x870, 0x887 },
	{ 0x889, 0x88e },
	{ 0x897, 0x897 },
	{ 0x8a0, 0x8c9 },
	{ 0x8d4, 0x8df },
	{ 0x8e3, 0x8e9 },
	{ 0x8f0, 0x93b },
	{ 0x93d, 0x94c },
	{ 0x94e, 0x950 },
	{ 0x955, 0x963 },
	{ 0x971, 0x983 },
	{ 0x985, 0x98c },
	{ 0x98f, 0x990 },
	{ 0x993, 0x9a8 },
	{ 0x9aa, 0x9b0 },
	{ 0x9b2, 0x9b2 },
	{ 0x9b6, 0x9b9 },
	{ 0x9bd, 0x9c4 },
	{ 0x9c7, 0x9c8 },
	{ 0x9cb, 0x9cc },
	{ 0x9ce, 0x9ce },
	{ 0x9d7, 0x9d7 },
	{ 0x9dc, 0x9dd },
	{ 0x9df, 0x9e3 },
	{ 0x9f0, 0x9f1 },
	{ 0x9fc, 0x9fc },
	{ 0xa01, 0xa03 },
	{ 0xa05, 0xa0a },
	{ 0xa0f, 0xa10 },
	{ 0xa13, 0xa28 },
	{ 0xa2a, 0xa30 },
	{ 0xa32, 0xa33 },
	{ 0xa35, 0xa36 },
	{ 0xa38, 0xa39 },
	{ 0xa3e, 0xa42 },
	{ 0xa47, 0xa48 },
	{ 0xa4b, 0xa4c },
	{ 0xa51, 0xa51 },
	{ 0xa59, 0xa5c },
	{ 0xa5e, 0xa5e },
	{ 0xa70, 0xa75 },
	{ 0xa81, 0xa83 },
	{ 0xa85, 0xa8d },
	{ 0xa8f, 0xa91 },
	{ 0xa93, 0xaa8 },
	{ 0xaaa, 0xab0 },
	{ 0xab2, 0xab3 },
	{ 0xab5, 0xab9 },
	{ 0xabd, 0xac5 },
	{ 0xac7, 0xac9 },
	{ 0xacb, 0xacc },
	{ 0xad0, 0xad0 },
	{ 0xae0, 0xae3 },
	{ 0xaf9, 0xafc },
	{ 0xb01, 0xb03 },
	{ 0xb05, 0xb0c },
	{ 0xb0f, 0xb10 },
	{ 0xb13, 0xb28 },
	{ 0xb2a, 0xb30 },
	{ 0xb32, 0xb33 },
	{ 0xb35, 0xb39 },
	{ 0xb3d, 0xb44 },
	{ 0xb47, 0xb48 },
	{ 0xb4b, 0xb4c },
	{ 0xb56, 0xb57 },
	{ 0xb5c, 0xb5d },
	{ 0xb5f, 0xb63 },
	{ 0xb71, 0xb71 },
	{ 0xb82, 0xb83 },
	{ 0xb85, 0xb8a },
	{ 0xb8e, 0xb90 },
	{ 0xb92, 0xb95 },
	{ 0xb99, 0xb9a },
	{ 0xb9c, 0xb9c },
	{ 0xb9e, 0xb9f },
	{ 0xba3, 0xba4 },
	{ 0xba8, 0xbaa },
	{ 0xbae, 0xbb9 },
	{ 0xbbe, 0xbc2 },
	{ 0xbc6, 0xbc8 },
	{ 0xbca, 0xbcc },
	{ 0xbd0, 0xbd0 },
	{ 0xbd7, 0xbd7 },
	{ 0xc00, 0xc0c },
	{ 0xc0e, 0xc10 },
	{ 0xc12, 0xc28 },
	{ 0xc2a, 0xc39 },
	{ 0xc3d, 0xc44 },
	{ 0xc46, 0xc48 },
	{ 0xc4a, 0xc4c },
	{ 0xc55, 0xc56 },
	{ 0xc58, 0xc5a },
	{ 0xc5d, 0xc5d },
	{ 0xc60, 0xc63 },
	{ 0xc80, 0xc83 },
	{ 0xc85, 0xc8c },
	{ 0xc8e, 0xc90 },
	{ 0xc92, 0xca8 },
	{ 0xcaa, 0xcb3 },
	{ 0xcb5, 0xcb9 },
	{ 0xcbd, 0xcc4 },
	{ 0xcc6, 0xcc8 },
	{ 0xcca, 0xccc },
	{ 0xcd5, 0xcd6 },
	{ 0xcdd, 0xcde },
	{ 0xce0, 0xce3 },
	{ 0xcf1, 0xcf3 },
	{ 0xd00, 0xd0c },
	{ 0xd0e, 0xd10 },
	{ 0xd12, 0xd3a },
	{ 0xd3d, 0xd44 },
	{ 0xd46, 0xd48 },
	{ 0xd4a, 0xd4c },
	{ 0xd4e, 0xd4e },
	{ 0xd54, 0xd57 },
	{ 0xd5f, 0xd63 },
	{ 0xd7a, 0xd7f },
	{ 0xd81, 0xd83 },
	{ 0xd85, 0xd96 },
	{ 0xd9a, 0xdb1 },
	{ 0xdb3, 0xdbb },
	{ 0xdbd, 0xdbd },
	{ 0xdc0, 0xdc6 },
	{ 0xdcf, 0xdd4 },
	{ 0xdd6, 0xdd6 },
	{ 0xdd8, 0xddf },
	{ 0xdf2, 0xdf3 },
	{ 0xe01, 0xe3a },
	{ 0xe40, 0xe46 },
	{ 0xe4d, 0xe4d },
	{ 0xe81, 0xe82 },
	{ 0xe84, 0xe84 },
	{ 0xe86, 0xe8a },
	{ 0xe8c, 0xea3 },
	{ 0xea5, 0xea5 },
	{ 0xea7, 0xeb9 },
	{ 0xebb, 0xebd },
	{ 0xec0, 0xec4 },
	{ 0xec6, 0xec6 },
	{ 0xecd, 0xecd },
	{ 0xedc, 0xedf },
	{ 0xf00, 0xf00 },
	{ 0xf40, 0xf47 },
	{ 0xf49, 0xf6c },
	{ 0xf71, 0xf83 },
	{ 0xf88, 0xf97 },
	{ 0xf99, 0xfbc },
	{ 0x1000, 0x1036 },
	{ 0x1038, 0x1038 },
	{ 0x103b, 0x103f },
	{ 0x1050, 0x108f },
	{ 0x109a, 0x109d },
	{ 0x10a0, 0x10c5 },
	{ 0x10c7, 0x10c7 },
	{ 0x10cd, 0x10cd },
	{ 0x10d0, 0x10fa },
	{ 0x10fc, 0x1248 },
	{ 0x124a, 0x124d },
	{ 0x1250, 0x1256 },
	{ 0x1258, 0x1258 },
	{ 0x125a, 0x125d },
	{ 0x1260, 0x1288 },
	{ 0x128a, 0x128d },
	{ 0x1290, 0x12b0 },
	{ 0x12b2, 0x12b5 },
	{ 0x12b8, 0x12be },
	{ 0x12c0, 0x12c0 },
	{ 0x12c2, 0x12c5 },
	{ 0x12c8, 0x12d6 },
	{ 0x12d8, 0x1310 },
	{ 0x1312, 0x1315 },
	{ 0x1318, 0x135a },
	{ 0x1380, 0x138f },
	{ 0x13a0, 0x13f5 },
	{ 0x13f8, 0x13fd },
	{ 0x1401, 0x166c },
	{ 0x166f, 0x167f },
	{ 0x1681, 0x169a },
	{ 0x16a0, 0x16ea },
	{ 0x16ee, 0x16f8 },
	{ 0x1700, 0x1713 },
	{ 0x171f, 0x1733 },
	{ 0x1740, 0x1753 },
	{ 0x1760, 0x176c },
	{ 0x176e, 0x1770 },
	{ 0x1772, 0x1773 },
	{ 0x1780, 0x17b3 },
	{ 0x17b6, 0x17c8 },
	{ 0x17d7, 0x17d7 },
	{ 0x17dc, 0x17dc },
	{ 0x1820, 0x1878 },
	{ 0x1880, 0x18aa },
	{ 0x18b0, 0x18f5 },
	{ 0x1900, 0x191e },
	{ 0x1920, 0x192b },
	{ 0x1930, 0x1938 },
	{ 0x1950, 0x196d },
	{ 0x1970, 0x1974 },
	{ 0x1980, 0x19ab },
	{ 0x19b0, 0x19c9 },
	{ 0x1a00, 0x1a1b },
	{ 0x1a20, 0x1a5e },
	{ 0x1a61, 0x1a74 },
	{ 0x1aa7, 0x1aa7 },
	{ 0x1abf, 0x1ac0 },
	{ 0x1acc, 0x1ace },
	{ 0x1b00, 0x1b33 },
	{ 0x1b35, 0x1b43 },
	{ 0x1b45, 0x1b4c },
	{ 0x1b80, 0x1ba9 },
	{ 0x1bac, 0x1baf },
	{ 0x1bba, 0x1be5 },
	{ 0x1be7, 0x1bf1 },
	{ 0x1c00, 0x1c36 },
	{ 0x1c4d, 0x1c4f },
	{ 0x1c5a, 0x1c7d },
	{ 0x1c80, 0x1c8a },
	{ 0x1c90, 0x1cba },
	{ 0x1cbd, 0x1cbf },
	{ 0x1ce9, 0x1cec },
	{ 0x1cee, 0x1cf3 },
	{ 0x1cf5, 0x1cf6 },
	{ 0x1cfa, 0x1cfa },
	{ 0x1d00, 0x1dbf },
	{ 0x1dd3, 0x1df4 },
	{ 0x1e00, 0x1f15 },
	{ 0x1f18, 0x1f1d },
	{ 0x1f20, 0x1f45 },
	{ 0x1f48, 0x1f4d },
	{ 0x1f50, 0x1f57 },
	{ 0x1f59, 0x1f59 },
	{ 0x1f5b, 0x1f5b },
	{ 0x1f5d, 0x1f5d },
	{ 0x1f5f, 0x1f7d },
	{ 0x1f80, 0x1fb4 },
	{ 0x1fb6, 0x1fbc },
	{ 0x1fbe, 0x1fbe },
	{ 0x1fc2, 0x1fc4 },
	{ 0x1fc6, 0x1fcc },
	{ 0x1fd0, 0x1fd3 },
	{ 0x1fd6, 0x1fdb },
	{ 0x1fe0, 0x1fec },
	{ 0x1ff2, 0x1ff4 },
	{ 0x1ff6, 0x1ffc },
	{ 0x2071, 0x2071 },
	{ 0x207f, 0x207f },
	{ 0x2090, 0x209c },
	{ 0x2102, 0x2102 },
	{ 0x2107, 0x2107 },
	{ 0x210a, 0x2113 },
	{ 0x2115, 0x2115 },
	{ 0x2119, 0x211d },
	{ 0x2124, 0x2124 },
	{ 0x2126, 0x2126 },
	{ 0x2128, 0x2128 },
	{ 0x212a, 0x212d },
	{ 0x212f, 0x2139 },
	{ 0x213c, 0x213f },
	{ 0x2145, 0x2149 },
	{ 0x214e, 0x214e },
	{ 0x2160, 0x2188 },
	{ 0x24b6, 0x24e9 },
	{ 0x2c00, 0x2ce4 },
	{ 0x2ceb, 0x2cee },
	{ 0x2cf2, 0x2cf3 },
	{ 0x2d00, 0x2d25 },
	{ 0x2d27, 0x2d27 },
	{ 0x2d2d, 0x2d2d },
	{ 0x2d30, 0x2d67 },
	{ 0x2d6f, 0x2d6f },
	{ 0x2d80, 0x2d96 },
	{ 0x2da0, 0x2da6 },
	{ 0x2da8, 0x2dae },
	{ 0x2db0, 0x2db6 },
	{ 0x2db8, 0x2dbe },
	{ 0x2dc0, 0x2dc6 },
	{ 0x2dc8, 0x2dce },
	{ 0x2dd0, 0x2dd6 },
	{ 0x2dd8, 0x2dde },
	{ 0x2de0, 0x2dff },
	{ 0x2e2f, 0x2e2f },
	{ 0x3005, 0x3007 },
	{ 0x3021, 0x3029 },
	{ 0x3031, 0x3035 },
	{ 0x3038, 0x303c },
	{ 0x3041, 0x3096 },
	{ 0x309d, 0x309f },
	{ 0x30a1, 0x30fa },
	{ 0x30fc, 0x30ff },
	{ 0x3105, 0x312f },
	{ 0x3131, 0x318e },
	{ 0x31a0, 0x31bf },
	{ 0x31f0, 0x31ff },
	{ 0x3400, 0x4dbf },
	{ 0x4e00, 0xa48c },
	{ 0xa4d0, 0xa4fd },
	{ 0xa500, 0xa60c },
	{ 0xa610, 0xa61f },
	{ 0xa62a, 0xa62b },
	{ 0xa640, 0xa66e },
	{ 0xa674, 0xa67b },
	{ 0xa67f, 0xa6ef },
	{ 0xa717, 0xa71f },
	{ 0xa722, 0xa788 },
	{ 0xa78b, 0xa7cd },
	{ 0xa7d0, 0xa7d1 },
	{ 0xa7d3, 0xa7d3 },
	{ 0xa7d5, 0xa7dc },
	{ 0xa7f2, 0xa805 },
	{ 0xa807, 0xa827 },
	{ 0xa840, 0xa873 },
	{ 0xa880, 0xa8c3 },
	{ 0xa8c5, 0xa8c5 },
	{ 0xa8f2, 0xa8f7 },
	{ 0xa8fb, 0xa8fb },
	{ 0xa8fd, 0xa8ff },
	{ 0xa90a, 0xa92a },
	{ 0xa930, 0xa952 },
	{ 0xa960, 0xa97c },
	{ 0xa980, 0xa9b2 },
	{ 0xa9b4, 0xa9bf },
	{ 0xa9cf, 0xa9cf },
	{ 0xa9e0, 0xa9ef },
	{ 0xa9fa, 0xa9fe },
	{ 0xaa00, 0xaa36 },
	{ 0xaa40, 0xaa4d },
	{ 0xaa60, 0xaa76 },
	{ 0xaa7a, 0xaabe },
	{ 0xaac0, 0xaac0 },
	{ 0xaac2, 0xaac2 },
	{ 0xaadb, 0xaadd },
	{ 0xaae0, 0xaaef },
	{ 0xaaf2, 0xaaf5 },
	{ 0xab01, 0xab06 },
	{ 0xab09, 0xab0e },
	{ 0xab11, 0xab16 },
	{ 0xab20, 0xab26 },
	{ 0xab28, 0xab2e },
	{ 0xab30, 0xab5a },
	{ 0xab5c, 0xab69 },
	{ 0xab70, 0xabea },
	{ 0xac00, 0xd7a3 },
	{ 0xd7b0, 0xd7c6 },
	{ 0xd7cb, 0xd7fb },
	{ 0xf900, 0xfa6d },
	{ 0xfa70, 0xfad9 },
	{ 0xfb00, 0xfb06 },
	{ 0xfb13, 0xfb17 },
	{ 0xfb1d, 0xfb28 },
	{ 0xfb2a, 0xfb36 },
	{ 0xfb38, 0xfb3c },
	{ 0xfb3e, 0xfb3e },
	{ 0xfb40, 0xfb41 },
	{ 0xfb43, 0xfb44 },
	{ 0xfb46, 0xfbb1 },
	{ 0xfbd3, 0xfd3d },
	{ 0xfd50, 0xfd8f },
	{ 0xfd92, 0xfdc7 },
	{ 0xfdf0, 0xfdfb },
	{ 0xfe70, 0xfe74 },
	{ 0xfe76, 0xfefc },
	{ 0xff21, 0xff3a },
	{ 0xff41, 0xff5a },
	{ 0xff66, 0xffbe },
	{ 0xffc2, 0xffc7 },
	{ 0xffca, 0xffcf },
	{ 0xffd2, 0xffd7 },
	{ 0xffda, 0xffdc },
	{ 0x10000, 0x1000b },
	{ 0x1000d, 0x10026 },
	{ 0x10028, 0x1003a },
	{ 0x1003c, 0x1003d },
	{ 0x1003f, 0x1004d },
	{ 0x10050, 0x1005d },
	{ 0x10080, 0x100fa },
	{ 0x10140, 0x10174 },
	{ 0x10280, 0x1029c },
	{ 0x102a0, 0x102d0 },
	{ 0x10300, 0x1031f },
	{ 0x1032d, 0x1034a },
	{ 0x10350, 0x1037a },
	{ 0x10380, 0x1039d },
	{ 0x103a0, 0x103c3 },
	{ 0x103c8, 0x103cf },
	{ 0x103d1, 0x103d5 },
	{ 0x10400, 0x1049d },
	{ 0x104b0, 0x104d3 },
	{ 0x104d8, 0x104fb },
	{ 0x10500, 0x10527 },
	{ 0x10530, 0x10563 },
	{ 0x10570, 0x1057a },
	{ 0x1057c, 0x1058a },
	{ 0x1058c, 0x10592 },
	{ 0x10594, 0x10595 },
	{ 0x10597, 0x105a1 },
	{ 0x105a3, 0x105b1 },
	{ 0x105b3, 0x105b9 },
	{ 0x105bb, 0x105bc },
	{ 0x105c0, 0x105f3 },
	{ 0x10600, 0x10736 },
	{ 0x10740, 0x10755 },
	{ 0x10760, 0x10767 },
	{ 0x10780, 0x10785 },
	{ 0x10787, 0x107b0 },
	{ 0x107b2, 0x107ba },
	{ 0x10800, 0x10805 },
	{ 0x10808, 0x10808 },
	{ 0x1080a, 0x10835 },
	{ 0x10837, 0x10838 },
	{ 0x1083c, 0x1083c },
	{ 0x1083f, 0x10855 },
	{ 0x10860, 0x10876 },
	{ 0x10880, 0x1089e },
	{ 0x108e0, 0x108f2 },
	{ 0x108f4, 0x108f5 },
	{ 0x10900, 0x10915 },
	{ 0x10920, 0x10939 },
	{ 0x10980, 0x109b7 },
	{ 0x109be, 0x109bf },
	{ 0x10a00, 0x10a03 },
	{ 0x10a05, 0x10a06 },
	{ 0x10a0c, 0x10a13 },
	{ 0x10a15, 0x10a17 },
	{ 0x10a19, 0x10a35 },
	{ 0x10a60, 0x10a7c },
	{ 0x10a80, 0x10a9c },
	{ 0x10ac0, 0x10ac7 },
	{ 0x10ac9, 0x10ae4 },
	{ 0x10b00, 0x10b35 },
	{ 0x10b40, 0x10b55 },
	{ 0x10b60, 0x10b72 },
	{ 0x10b80, 0x10b91 },
	{ 0x10c00, 0x10c48 },
	{ 0x10c80, 0x10cb2 },
	{ 0x10cc0, 0x10cf2 },
	{ 0x10d00, 0x10d27 },
	{ 0x10d4a, 0x10d65 },
	{ 0x10d69, 0x10d69 },
	{ 0x10d6f, 0x10d85 },
	{ 0x10e80, 0x10ea9 },
	{ 0x10eab, 0x10eac },
	{ 0x10eb0, 0x10eb1 },
	{ 0x10ec2, 0x10ec4 },
	{ 0x10efc, 0x10efc },
	{ 0x10f00, 0x10f1c },
	{ 0x10f27, 0x10f27 },
	{ 0x10f30, 0x10f45 },
	{ 0x10f70, 0x10f81 },
	{ 0x10fb0, 0x10fc4 },
	{ 0x10fe0, 0x10ff6 },
	{ 0x11000, 0x11045 },
	{ 0x11071, 0x11075 },
	{ 0x11080, 0x110b8 },
	{ 0x110c2, 0x110c2 },
	{ 0x110d0, 0x110e8 },
	{ 0x11100, 0x11132 },
	{ 0x11144, 0x11147 },
	{ 0x11150, 0x11172 },
	{ 0x11176, 0x11176 },
	{ 0x11180, 0x111bf },
	{ 0x111c1, 0x111c4 },
	{ 0x111ce, 0x111cf },
	{ 0x111da, 0x111da },
	{ 0x111dc, 0x111dc },
	{ 0x11200, 0x11211 },
	{ 0x11213, 0x11234 },
	{ 0x11237, 0x11237 },
	{ 0x1123e, 0x11241 },
	{ 0x11280, 0x11286 },
	{ 0x11288, 0x11288 },
	{ 0x1128a, 0x1128d },
	{ 0x1128f, 0x1129d },
	{ 0x1129f, 0x112a8 },
	{ 0x112b0, 0x112e8 },
	{ 0x11300, 0x11303 },
	{ 0x11305, 0x1130c },
	{ 0x1130f, 0x11310 },
	{ 0x11313, 0x11328 },
	{ 0x1132a, 0x11330 },
	{ 0x11332, 0x11333 },
	{ 0x11335, 0x11339 },
	{ 0x1133d, 0x11344 },
	{ 0x11347, 0x11348 },
	{ 0x1134b, 0x1134c },
	{ 0x11350, 0x11350 },
	{ 0x11357, 0x11357 },
	{ 0x1135d, 0x11363 },
	{ 0x11380, 0x11389 },
	{ 0x1138b, 0x1138b },
	{ 0x1138e, 0x1138e },
	{ 0x11390, 0x113b5 },
	{ 0x113b7, 0x113c0 },
	{ 0x113c2, 0x113c2 },
	{ 0x113c5, 0x113c5 },
	{ 0x113c7, 0x113ca },
	{ 0x113cc, 0x113cd },
	{ 0x113d1, 0x113d1 },
	{ 0x113d3, 0x113d3 },
	{ 0x11400, 0x11441 },
	{ 0x11443, 0x11445 },
	{ 0x11447, 0x1144a },
	{ 0x1145f, 0x11461 },
	{ 0x11480, 0x114c1 },
	{ 0x114c4, 0x114c5 },
	{ 0x114c7, 0x114c7 },
	{ 0x11580, 0x115b5 },
	{ 0x115b8, 0x115be },
	{ 0x115d8, 0x115dd },
	{ 0x11600, 0x1163e },
	{ 0x11640, 0x11640 },
	{ 0x11644, 0x11644 },
	{ 0x11680, 0x116b5 },
	{ 0x116b8, 0x116b8 },
	{ 0x11700, 0x1171a },
	{ 0x1171d, 0x1172a },
	{ 0x11740, 0x11746 },
	{ 0x11800, 0x11838 },
	{ 0x118a0, 0x118df },
	{ 0x118ff, 0x11906 },
	{ 0x11909, 0x11909 },
	{ 0x1190c, 0x11913 },
	{ 0x11915, 0x11916 },
	{ 0x11918, 0x11935 },
	{ 0x11937, 0x11938 },
	{ 0x1193b, 0x1193c },
	{ 0x1193f, 0x11942 },
	{ 0x119a0, 0x119a7 },
	{ 0x119aa, 0x119d7 },
	{ 0x119da, 0x119df },
	{ 0x119e1, 0x119e1 },
	{ 0x119e3, 0x119e4 },
	{ 0x11a00, 0x11a32 },
	{ 0x11a35, 0x11a3e },
	{ 0x11a50, 0x11a97 },
	{ 0x11a9d, 0x11a9d },
	{ 0x11ab0, 0x11af8 },
	{ 0x11bc0, 0x11be0 },
	{ 0x11c00, 0x11c08 },
	{ 0x11c0a, 0x11c36 },
	{ 0x11c38, 0x11c3e },
	{ 0x11c40, 0x11c40 },
	{ 0x11c72, 0x11c8f },
	{ 0x11c92, 0x11ca7 },
	{ 0x11ca9, 0x11cb6 },
	{ 0x11d00, 0x11d06 },
	{ 0x11d08, 0x11d09 },
	{ 0x11d0b, 0x11d36 },
	{ 0x11d3a, 0x11d3a },
	{ 0x11d3c, 0x11d3d },
	{ 0x11d3f, 0x11d41 },
	{ 0x11d43, 0x11d43 },
	{ 0x11d46, 0x11d47 },
	{ 0x11d60, 0x11d65 },
	{ 0x11d67, 0x11d68 },
	{ 0x11d6a, 0x11d8e },
	{ 0x11d90, 0x11d91 },
	{ 0x11d93, 0x11d96 },
	{ 0x11d98, 0x11d98 },
	{ 0x11ee0, 0x11ef6 },
	{ 0x11f00, 0x11f10 },
	{ 0x11f12, 0x11f3a },
	{ 0x11f3e, 0x11f40 },
	{ 0x11fb0, 0x11fb0 },
	{ 0x12000, 0x12399 },
	{ 0x12400, 0x1246e },
	{ 0x12480, 0x12543 },
	{ 0x12f90, 0x12ff0 },
	{ 0x13000, 0x1342f },
	{ 0x13441, 0x13446 },
	{ 0x13460, 0x143fa },
	{ 0x14400, 0x14646 },
	{ 0x16100, 0x1612e },
	{ 0x16800, 0x16a38 },
	{ 0x16a40, 0x16a5e },
	{ 0x16a70, 0x16abe },
	{ 0x16ad0, 0x16aed },
	{ 0x16b00, 0x16b2f },
	{ 0x16b40, 0x16b43 },
	{ 0x16b63, 0x16b77 },
	{ 0x16b7d, 0x16b8f },
	{ 0x16d40, 0x16d6c },
	{ 0x16e40, 0x16e7f },
	{ 0x16f00, 0x16f4a },
	{ 0x16f4f, 0x16f87 },
	{ 0x16f8f, 0x16f9f },
	{ 0x16fe0, 0x16fe1 },
	{ 0x16fe3, 0x16fe3 },
	{ 0x16ff0, 0x16ff1 },
	{ 0x17000, 0x187f7 },
	{ 0x18800, 0x18cd5 },
	{ 0x18cff, 0x18d08 },
	{ 0x1aff0, 0x1aff3 },
	{ 0x1aff5, 0x1affb },
	{ 0x1affd, 0x1affe },
	{ 0x1b000, 0x1b122 },
	{ 0x1b132, 0x1b132 },
	{ 0x1b150, 0x1b152 },
	{ 0x1b155, 0x1b155 },
	{ 0x1b164, 0x1b167 },
	{ 0x1b170, 0x1b2fb },
	{ 0x1bc00, 0x1bc6a },
	{ 0x1bc70, 0x1bc7c },
	{ 0x1bc80, 0x1bc88 },
	{ 0x1bc90, 0x1bc99 },
	{ 0x1bc9e, 0x1bc9e },
	{ 0x1d400, 0x1d454 },
	{ 0x1d456, 0x1d49c },
	{ 0x1d49e, 0x1d49f },
	{ 0x1d4a2, 0x1d4a2 },
	{ 0x1d4a5, 0x1d4a6 },
	{ 0x1d4a9, 0x1d4ac },
	{ 0x1d4ae, 0x1d4b9 },
	{ 0x1d4bb, 0x1d4bb },
	{ 0x1d4bd, 0x1d4c3 },
	{ 0x1d4c5, 0x1d505 },
	{ 0x1d507, 0x1d50a },
	{ 0x1d50d, 0x1d514 },
	{ 0x1d516, 0x1d51c },
	{ 0x1d51e, 0x1d539 },
	{ 0x1d53b, 0x1d53e },
	{ 0x1d540, 0x1d544 },
	{ 0x1d546, 0x1d546 },
	{ 0x1d54a, 0x1d550 },
	{ 0x1d552, 0x1d6a5 },
	{ 0x1d6a8, 0x1d6c0 },
	{ 0x1d6c2, 0x1d6da },
	{ 0x1d6dc, 0x1d6fa },
	{ 0x1d6fc, 0x1d714 },
	{ 0x1d716, 0x1d734 },
	{ 0x1d736, 0x1d74e },
	{ 0x1d750, 0x1d76e },
	{ 0x1d770, 0x1d788 },
	{ 0x1d78a, 0x1d7a8 },
	{ 0x1d7aa, 0x1d7c2 },
	{ 0x1d7c4, 0x1d7cb },
	{ 0x1df00, 0x1df1e },
	{ 0x1df25, 0x1df2a },
	{ 0x1e000, 0x1e006 },
	{ 0x1e008, 0x1e018 },
	{ 0x1e01b, 0x1e021 },
	{ 0x1e023, 0x1e024 },
	{ 0x1e026, 0x1e02a },
	{ 0x1e030, 0x1e06d },
	{ 0x1e08f, 0x1e08f },
	{ 0x1e100, 0x1e12c },
	{ 0x1e137, 0x1e13d },
	{ 0x1e14e, 0x1e14e },
	{ 0x1e290, 0x1e2ad },
	{ 0x1e2c0, 0x1e2eb },
	{ 0x1e4d0, 0x1e4eb },
	{ 0x1e5d0, 0x1e5ed },
	{ 0x1e5f0, 0x1e5f0 },
	{ 0x1e7e0, 0x1e7e6 },
	{ 0x1e7e8, 0x1e7eb },
	{ 0x1e7ed, 0x1e7ee },
	{ 0x1e7f0, 0x1e7fe },
	{ 0x1e800, 0x1e8c4 },
	{ 0x1e900, 0x1e943 },
	{ 0x1e947, 0x1e947 },
	{ 0x1e94b, 0x1e94b },
	{ 0x1ee00, 0x1ee03 },
	{ 0x1ee05, 0x1ee1f },
	{ 0x1ee21, 0x1ee22 },
	{ 0x1ee24, 0x1ee24 },
	{ 0x1ee27, 0x1ee27 },
	{ 0x1ee29, 0x1ee32 },
	{ 0x1ee34, 0x1ee37 },
	{ 0x1ee39, 0x1ee39 },
	{ 0x1ee3b, 0x1ee3b },
	{ 0x1ee42, 0x1ee42 },
	{ 0x1ee47, 0x1ee47 },
	{ 0x1ee49, 0x1ee49 },
	{ 0x1ee4b, 0x1ee4b },
	{ 0x1ee4d, 0x1ee4f },
	{ 0x1ee51, 0x1ee52 },
	{ 0x1ee54, 0x1ee54 },
	{ 0x1ee57, 0x1ee57 },
	{ 0x1ee59, 0x1ee59 },
	{ 0x1ee5b, 0x1ee5b },
	{ 0x1ee5d, 0x1ee5d },
	{ 0x1ee5f, 0x1ee5f },
	{ 0x1ee61, 0x1ee62 },
	{ 0x1ee64, 0x1ee64 },
	{ 0x1ee67, 0x1ee6a },
	{ 0x1ee6c, 0x1ee72 },
	{ 0x1ee74, 0x1ee77 },
	{ 0x1ee79, 0x1ee7c },
	{ 0x1ee7e, 0x1ee7e },
	{ 0x1ee80, 0x1ee89 },
	{ 0x1ee8b, 0x1ee9b },
	{ 0x1eea1, 0x1eea3 },
	{ 0x1eea5, 0x1eea9 },
	{ 0x1eeab, 0x1eebb },
	{ 0x1f130, 0x1f149 },
	{ 0x1f150, 0x1f169 },
	{ 0x1f170, 0x1f189 },
	{ 0x20000, 0x2a6df },
	{ 0x2a700, 0x2b739 },
	{ 0x2b740, 0x2b81d },
	{ 0x2b820, 0x2cea1 },
	{ 0x2ceb0, 0x2ebe0 },
	{ 0x2ebf0, 0x2ee5d },
	{ 0x2f800, 0x2fa1d },
	{ 0x30000, 0x3134a },
	{ 0x31350, 0x323af },
};

// OEM encodings for Alt codes input.

inline constexpr char32_t alt_code_oem437[256] = {
	0x0000, 0x263a, 0x263b, 0x2665, 0x2666, 0x2663, 0x2660, 0x2022, 0x25d8, 0x25cb, 0x25d9, 0x2642, 0x2640, 0x266a, 0x266b, 0x263c,
	0x25ba, 0x25c4, 0x2195, 0x203c, 0x00b6, 0x00a7, 0x25ac, 0x21a8, 0x2191, 0x2193, 0x2192, 0x2190, 0x221f, 0x2194, 0x25b2, 0x25bc,
	0x0020, 0x0021, 0x0022, 0x0023, 0x0024, 0x0025, 0x0026, 0x0027, 0x0028, 0x0029, 0x002a, 0x002b, 0x002c, 0x002d, 0x002e, 0x002f,
	0x0030, 0x0031, 0x0032, 0x0033, 0x0034, 0x0035, 0x0036, 0x0037, 0x0038, 0x0039, 0x003a, 0x003b, 0x003c, 0x003d, 0x003e, 0x003f,
	0x0040, 0x0041, 0x0042, 0x0043, 0x0044, 0x0045, 0x0046, 0x0047, 0x0048, 0x0049, 0x004a, 0x004b, 0x004c, 0x004d, 0x004e, 0x004f,
	0x0050, 0x0051, 0x0052, 0x0053, 0x0054, 0x0055, 0x0056, 0x0057, 0x0058, 0x0059, 0x005a, 0x005b, 0x005c, 0x005d, 0x005e, 0x005f,
	0x0060, 0x0061, 0x0062, 0x0063, 0x0064, 0x0065, 0x0066, 0x0067, 0x0068, 0x0069, 0x006a, 0x006b, 0x006c, 0x006d, 0x006e, 0x006f,
	0x0070, 0x0071, 0x0072, 0x0073, 0x0074, 0x0075, 0x0076, 0x0077, 0x0078, 0x0079, 0x007a, 0x007b, 0x007c, 0x007d, 0x007e, 0x2302,
	0x00c7, 0x00fc, 0x00e9, 0x00e2, 0x00e4, 0x00e0, 0x00e5, 0x00e7, 0x00ea, 0x00eb, 0x00e8, 0x00ef, 0x00ee, 0x00ec, 0x00c4, 0x00c5,
	0x00c9, 0x00e6, 0x00c6, 0x00f4, 0x00f6, 0x00f2, 0x00fb, 0x00f9, 0x00ff, 0x00d6, 0x00dc, 0x00a2, 0x00a3, 0x00a5, 0x20a7, 0x0192,
	0x00e1, 0x00ed, 0x00f3, 0x00fa, 0x00f1, 0x00d1, 0x00aa, 0x00ba, 0x00bf, 0x2310, 0x00ac, 0x00bd, 0x00bc, 0x00a1, 0x00ab, 0x00bb,
	0x2591, 0x2592, 0x2593, 0x2502, 0x2524, 0x2561, 0x2562, 0x2556, 0x2555, 0x2563, 0x2551, 0x2557, 0x255d, 0x255c, 0x255b, 0x2510,
	0x2514, 0x2534, 0x252c, 0x251c, 0x2500, 0x253c, 0x255e, 0x255f, 0x255a, 0x2554, 0x2569, 0x2566, 0x2560, 0x2550, 0x256c, 0x2567,
	0x2568, 0x2564, 0x2565, 0x2559, 0x2558, 0x2552, 0x2553, 0x256b, 0x256a, 0x2518, 0x250c, 0x2588, 0x2584, 0x258c, 0x2590, 0x2580,
	0x03b1, 0x00df, 0x0393, 0x03c0, 0x03a3, 0x03c3, 0x00b5, 0x03a4, 0x03a6, 0x0398, 0x03a9, 0x03b4, 0x221e, 0x03c6, 0x03b5, 0x2229,
	0x2261, 0x00b1, 0x2265, 0x2264, 0x2320, 0x2321, 0x00f7, 0x2248, 0x00b0, 0x2219, 0x00b7, 0x221a, 0x207f, 0x00b2, 0x25a0, 0x00a0
};

inline constexpr char32_t alt_code_cp1252[256] = {
	0x0000, 0x0001, 0x0002, 0x0003, 0x0004, 0x0005, 0x0006, 0x0007, 0x0008, 0x0009, 0x000a, 0x000b, 0x000c, 0x000d, 0x000e, 0x000f,
	0x0010, 0x0011, 0x0012, 0x0013, 0x0014, 0x0015, 0x0016, 0x0017, 0x0018, 0x0019, 0x001a, 0x001b, 0x001c, 0x001d, 0x001e, 0x001f,
	0x0020, 0x0021, 0x0022, 0x0023, 0x0024, 0x0025, 0x0026, 0x0027, 0x0028, 0x0029, 0x002a, 0x002b, 0x002c, 0x002d, 0x002e, 0x002f,
	0x0030, 0x0031, 0x0032, 0x0033, 0x0034, 0x0035, 0x0036, 0x0037, 0x0038, 0x0039, 0x003a, 0x003b, 0x003c, 0x003d, 0x003e, 0x003f,
	0x0040, 0x0041, 0x0042, 0x0043, 0x0044, 0x0045, 0x0046, 0x0047, 0x0048, 0x0049, 0x004a, 0x004b, 0x004c, 0x004d, 0x004e, 0x004f,
	0x0050, 0x0051, 0x0052, 0x0053, 0x0054, 0x0055, 0x0056, 0x0057, 0x0058, 0x0059, 0x005a, 0x005b, 0x005c, 0x005d, 0x005e, 0x005f,
	0x0060, 0x0061, 0x0062, 0x0063, 0x0064, 0x0065, 0x0066, 0x0067, 0x0068, 0x0069, 0x006a, 0x006b, 0x006c, 0x006d, 0x006e, 0x006f,
	0x0070, 0x0071, 0x0072, 0x0073, 0x0074, 0x0075, 0x0076, 0x0077, 0x0078, 0x0079, 0x007a, 0x007b, 0x007c, 0x007d, 0x007e, 0x007f,
	0x20ac, 0x0081, 0x201a, 0x0192, 0x201e, 0x2026, 0x2020, 0x2021, 0x02c6, 0x2030, 0x0160, 0x2039, 0x0152, 0x008d, 0x017d, 0x008f,
	0x0090, 0x2018, 0x2019, 0x201c, 0x201d, 0x2022, 0x2013, 0x2014, 0x02dc, 0x2122, 0x0161, 0x203a, 0x0153, 0x009d, 0x017e, 0x0178,
	0x00a0, 0x00a1, 0x00a2, 0x00a3, 0x00a4, 0x00a5, 0x00a6, 0x00a7, 0x00a8, 0x00a9, 0x00aa, 0x00ab, 0x00ac, 0x00ad, 0x00ae, 0x00af,
	0x00b0, 0x00b1, 0x00b2, 0x00b3, 0x00b4, 0x00b5, 0x00b6, 0x00b7, 0x00b8, 0x00b9, 0x00ba, 0x00bb, 0x00bc, 0x00bd, 0x00be, 0x00bf,
	0x00c0, 0x00c1, 0x00c2, 0x00c3, 0x00c4, 0x00c5, 0x00c6, 0x00c7, 0x00c8, 0x00c9, 0x00ca, 0x00cb, 0x00cc, 0x00cd, 0x00ce, 0x00cf,
	0x00d0, 0x00d1, 0x00d2, 0x00d3, 0x00d4, 0x00d5, 0x00d6, 0x00d7, 0x00d8, 0x00d9, 0x00da, 0x00db, 0x00dc, 0x00dd, 0x00de, 0x00df,
	0x00e0, 0x00e1, 0x00e2, 0x00e3, 0x00e4, 0x00e5, 0x00e6, 0x00e7, 0x00e8, 0x00e9, 0x00ea, 0x00eb, 0x00ec, 0x00ed, 0x00ee, 0x00ef,
	0x00f0, 0x00f1, 0x00f2, 0x00f3, 0x00f4, 0x00f5, 0x00f6, 0x00f7, 0x00f8, 0x00f9, 0x00fa, 0x00fb, 0x00fc, 0x00fd, 0x00fe, 0x00ff
};
