# Simplified core build for testing

package(default_visibility = ["//visibility:public"])

# Simple core library with basic files
cc_library(
    name = "core",
    srcs = glob([
        "*.cpp",
        "os/*.cpp",
        "math/*.cpp",
        "io/*.cpp",
        "string/*.cpp",
        "config/*.cpp",
        "error/*.cpp",
    ]),
    hdrs = glob([
        "*.h",
        "os/*.h", 
        "math/*.h",
        "io/*.h",
        "string/*.h",
        "config/*.h",
        "error/*.h",
    ]),
    defines = [
        "UNIX_ENABLED",
        "LINUXBSD_ENABLED",
    ],
    copts = [
        "-std=c++17",
        "-Wall",
        "-Wextra",
    ],
)
