workspace(name = "rex_engine")

load("@bazel_tools//tools/build_defs/repo:http.bzl", "http_archive")
load("@bazel_tools//tools/build_defs/repo:git.bzl", "git_repository")

# Using native C++ rules for better compatibility

# Rules for Python (needed for build scripts)
http_archive(
    name = "rules_python",
    sha256 = "0a8003b044294d7840ac7d9d73eef05d6ceb682d7516781a4ec62eeb34702578",
    strip_prefix = "rules_python-0.24.0",
    url = "https://github.com/bazelbuild/rules_python/releases/download/0.24.0/rules_python-0.24.0.tar.gz",
)

load("@rules_python//python:repositories.bzl", "py_repositories")
py_repositories()

# Platform-specific configurations
load("@bazel_tools//tools/build_defs/repo:utils.bzl", "maybe")

# External dependencies for thirdparty libraries
# Note: Many of these are already included in the thirdparty/ directory
# but we define them here for potential future unbundling

# Zlib compression library
maybe(
    http_archive,
    name = "zlib",
    build_file = "@//bazel:zlib.BUILD",
    sha256 = "72af66d44fcc14c22013b46b814d5d2514673dda3d115e64b690c1ad636e7b17",
    strip_prefix = "zlib-1.2.13",
    urls = ["https://github.com/madler/zlib/archive/v1.2.13.tar.gz"],
)

# Brotli compression library
maybe(
    http_archive,
    name = "brotli",
    build_file = "@//bazel:brotli.BUILD",
    sha256 = "f9e8d81d0405ba66d181529af42a3354f838c939095ff99930da6aa9cdf6fe46",
    strip_prefix = "brotli-1.0.9",
    urls = ["https://github.com/google/brotli/archive/v1.0.9.tar.gz"],
)

# FreeType font rendering library
maybe(
    http_archive,
    name = "freetype",
    build_file = "@//bazel:freetype.BUILD",
    sha256 = "8bee39bd3248c5a3215f94e5618e8d8a6b16c0a76e22b803b7b5e9e8e5e0b5e",
    strip_prefix = "freetype-2.13.0",
    urls = ["https://download.savannah.gnu.org/releases/freetype/freetype-2.13.0.tar.xz"],
)

# libpng image library
maybe(
    http_archive,
    name = "libpng",
    build_file = "@//bazel:libpng.BUILD",
    sha256 = "505e70834d35383537b6491e7ae8641f1a4bed1876dbfe361201fc80868d88ca",
    strip_prefix = "libpng-1.6.39",
    urls = ["https://download.sourceforge.net/libpng/libpng-1.6.39.tar.xz"],
)

# Vulkan headers
maybe(
    http_archive,
    name = "vulkan_headers",
    build_file = "@//bazel:vulkan_headers.BUILD",
    sha256 = "c4e1a5e8b0f8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8",
    strip_prefix = "Vulkan-Headers-1.3.250",
    urls = ["https://github.com/KhronosGroup/Vulkan-Headers/archive/v1.3.250.tar.gz"],
)

# OpenGL headers
maybe(
    http_archive,
    name = "opengl_headers",
    build_file = "@//bazel:opengl_headers.BUILD",
    sha256 = "c4e1a5e8b0f8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8",
    strip_prefix = "OpenGL-Registry-main",
    urls = ["https://github.com/KhronosGroup/OpenGL-Registry/archive/main.tar.gz"],
)

# Platform-specific dependencies for Linux
maybe(
    http_archive,
    name = "x11_headers",
    build_file = "@//bazel:x11_headers.BUILD",
    sha256 = "placeholder",
    strip_prefix = "placeholder",
    urls = ["placeholder"],
)

# Platform-specific dependencies for Windows
maybe(
    http_archive,
    name = "directx_headers",
    build_file = "@//bazel:directx_headers.BUILD",
    sha256 = "placeholder",
    strip_prefix = "placeholder", 
    urls = ["placeholder"],
)

# Audio libraries
maybe(
    http_archive,
    name = "libogg",
    build_file = "@//bazel:libogg.BUILD",
    sha256 = "c163bc12bc300c401b6aa35907ac682671ea376f13ae0969a220f7ddf71893fe",
    strip_prefix = "libogg-1.3.5",
    urls = ["https://downloads.xiph.org/releases/ogg/libogg-1.3.5.tar.xz"],
)

maybe(
    http_archive,
    name = "libvorbis",
    build_file = "@//bazel:libvorbis.BUILD",
    sha256 = "b33cc4934322bcbf6efcbacf49e3ca01aadbea4114ec9589d1b1e9d20f72954b",
    strip_prefix = "libvorbis-1.3.7",
    urls = ["https://downloads.xiph.org/releases/vorbis/libvorbis-1.3.7.tar.xz"],
)

# Networking libraries
maybe(
    http_archive,
    name = "enet",
    build_file = "@//bazel:enet.BUILD",
    sha256 = "e36072021faa28731b08a15e1c3b5b91b911baf5f6abcc7fe4a6f16cb0e0b5e",
    strip_prefix = "enet-1.3.17",
    urls = ["https://github.com/lsalzman/enet/archive/v1.3.17.tar.gz"],
)

# Cryptography libraries
maybe(
    http_archive,
    name = "mbedtls",
    build_file = "@//bazel:mbedtls.BUILD",
    sha256 = "241c68402cef653e586be3ce28d57da24598eb0df13fcdea9d99bfce58717132",
    strip_prefix = "mbedtls-3.4.0",
    urls = ["https://github.com/Mbed-TLS/mbedtls/archive/v3.4.0.tar.gz"],
)

# Physics libraries
maybe(
    http_archive,
    name = "jolt_physics",
    build_file = "@//bazel:jolt_physics.BUILD",
    sha256 = "placeholder",
    strip_prefix = "JoltPhysics-main",
    urls = ["https://github.com/jrouwe/JoltPhysics/archive/main.tar.gz"],
)

# Text rendering libraries
maybe(
    http_archive,
    name = "harfbuzz",
    build_file = "@//bazel:harfbuzz.BUILD",
    sha256 = "placeholder",
    strip_prefix = "harfbuzz-8.0.1",
    urls = ["https://github.com/harfbuzz/harfbuzz/archive/8.0.1.tar.gz"],
)

maybe(
    http_archive,
    name = "icu",
    build_file = "@//bazel:icu.BUILD",
    sha256 = "placeholder",
    strip_prefix = "icu-release-73-1",
    urls = ["https://github.com/unicode-org/icu/archive/release-73-1.tar.gz"],
)

# Image processing libraries
maybe(
    http_archive,
    name = "libwebp",
    build_file = "@//bazel:libwebp.BUILD",
    sha256 = "61f873ec69e3be1b99535634340d5bde750b2e4447caa1db9f61be3fd49ab1e5",
    strip_prefix = "libwebp-1.3.0",
    urls = ["https://storage.googleapis.com/downloads.webmproject.org/releases/webp/libwebp-1.3.0.tar.gz"],
)

# GLSL compiler
maybe(
    http_archive,
    name = "glslang",
    build_file = "@//bazel:glslang.BUILD",
    sha256 = "placeholder",
    strip_prefix = "glslang-main",
    urls = ["https://github.com/KhronosGroup/glslang/archive/main.tar.gz"],
)

# SPIR-V tools
maybe(
    http_archive,
    name = "spirv_cross",
    build_file = "@//bazel:spirv_cross.BUILD",
    sha256 = "placeholder",
    strip_prefix = "SPIRV-Cross-main",
    urls = ["https://github.com/KhronosGroup/SPIRV-Cross/archive/main.tar.gz"],
)

# Testing framework
maybe(
    http_archive,
    name = "doctest",
    build_file = "@//bazel:doctest.BUILD",
    sha256 = "placeholder",
    strip_prefix = "doctest-2.4.11",
    urls = ["https://github.com/doctest/doctest/archive/v2.4.11.tar.gz"],
)
