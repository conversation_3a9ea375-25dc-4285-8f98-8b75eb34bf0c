# ReX Engine Bazel Configuration
# This file contains build configuration options for the ReX Engine

# Common flags for all builds
build --cxxopt=-std=gnu++17
build --copt=-std=gnu17
build --cxxopt=-fno-exceptions
build --cxxopt=-fno-rtti

# Enable colored output
build --color=yes

# Use C++17 standard
build --cxxopt=-std=c++17

# Common include paths
build --copt=-I.
build --cxxopt=-I.

# Platform detection
build:linux --define=platform=linuxbsd
build:macos --define=platform=macos  
build:windows --define=platform=windows
build:android --define=platform=android
build:ios --define=platform=ios
build:web --define=platform=web

# Architecture detection
build:x86_64 --define=arch=x86_64
build:arm64 --define=arch=arm64
build:x86_32 --define=arch=x86_32

# Build targets
build:editor --define=target=editor
build:template_release --define=target=template_release
build:template_debug --define=target=template_debug

# Optimization levels
build:debug --compilation_mode=dbg
build:debug --copt=-O0
build:debug --copt=-g3
build:debug --define=optimize=debug

build:release --compilation_mode=opt
build:release --copt=-O3
build:release --define=optimize=speed

build:speed_trace --compilation_mode=opt
build:speed_trace --copt=-O2
build:speed_trace --define=optimize=speed_trace

build:size --compilation_mode=opt
build:size --copt=-Os
build:size --define=optimize=size

# Development build
build:dev --compilation_mode=dbg
build:dev --copt=-Og
build:dev --copt=-g3
build:dev --define=dev_build=true

# Production build
build:production --compilation_mode=opt
build:production --copt=-O3
build:production --strip=always
build:production --define=production=true

# Warning levels
build:warnings_extra --copt=-Wall
build:warnings_extra --copt=-Wextra
build:warnings_extra --copt=-Wwrite-strings
build:warnings_extra --copt=-Wno-unused-parameter

build:warnings_all --copt=-Wall

build:warnings_moderate --copt=-Wall
build:warnings_moderate --copt=-Wno-unused

build:warnings_none --copt=-w

# Treat warnings as errors
build:werror --copt=-Werror

# Platform-specific configurations

# Linux/BSD configuration
build:linux --copt=-fPIC
build:linux --cxxopt=-fPIC
build:linux --linkopt=-pthread
build:linux --define=use_x11=true
build:linux --define=use_wayland=true
build:linux --define=use_pulseaudio=true
build:linux --define=use_alsa=true

# macOS configuration  
build:macos --copt=-mmacosx-version-min=10.14
build:macos --cxxopt=-mmacosx-version-min=10.14
build:macos --linkopt=-mmacosx-version-min=10.14
build:macos --define=use_metal=true

# Windows configuration
build:windows --copt=/std:c++17
build:windows --cxxopt=/std:c++17
build:windows --copt=/permissive-
build:windows --cxxopt=/permissive-
build:windows --define=use_d3d12=true

# Android configuration
build:android --crosstool_top=//external:android/crosstool
build:android --host_crosstool_top=@bazel_tools//tools/cpp:toolchain
build:android --define=use_vulkan=true

# iOS configuration
build:ios --apple_platform_type=ios
build:ios --define=use_metal=true

# Web/Emscripten configuration
build:web --crosstool_top=//external:emscripten/crosstool
build:web --define=use_webgl=true

# Graphics API configurations
build:vulkan --define=use_vulkan=true
build:opengl3 --define=use_opengl3=true
build:d3d12 --define=use_d3d12=true
build:metal --define=use_metal=true

# Feature toggles
build:disable_3d --define=disable_3d=true
build:disable_advanced_gui --define=disable_advanced_gui=true
build:disable_physics_2d --define=disable_physics_2d=true
build:disable_physics_3d --define=disable_physics_3d=true
build:disable_navigation_2d --define=disable_navigation_2d=true
build:disable_navigation_3d --define=disable_navigation_3d=true
build:disable_xr --define=disable_xr=true

# Module configurations
build:builtin_brotli --define=builtin_brotli=true
build:builtin_freetype --define=builtin_freetype=true
build:builtin_libpng --define=builtin_libpng=true
build:builtin_zlib --define=builtin_zlib=true
build:builtin_zstd --define=builtin_zstd=true
build:builtin_clipper2 --define=builtin_clipper2=true

# Engine module configurations
build:module_gdscript_enabled --define=module_gdscript_enabled=true
build:module_mono_enabled --define=module_mono_enabled=true
build:module_regex_enabled --define=module_regex_enabled=true
build:module_jsonrpc_enabled --define=module_jsonrpc_enabled=true
build:module_websocket_enabled --define=module_websocket_enabled=true
build:module_openxr_enabled --define=module_openxr_enabled=true
build:module_basis_universal_enabled --define=module_basis_universal_enabled=true
build:module_astcenc_enabled --define=module_astcenc_enabled=true
build:module_etcpak_enabled --define=module_etcpak_enabled=true
build:module_squish_enabled --define=module_squish_enabled=true
build:module_theora_enabled --define=module_theora_enabled=true
build:module_vorbis_enabled --define=module_vorbis_enabled=true
build:module_opus_enabled --define=module_opus_enabled=true

# Platform feature configurations
build:use_x11 --define=use_x11=true
build:use_wayland --define=use_wayland=true
build:use_pulseaudio --define=use_pulseaudio=true
build:use_alsa --define=use_alsa=true
build:use_sowrap --define=use_sowrap=true
build:speechd --define=speechd=true
build:fontconfig --define=fontconfig=true
build:udev --define=udev=true
build:dbus --define=dbus=true
build:steamapi --define=steamapi=true

# Threading
build:threads --define=threads=true
build:no_threads --define=threads=false

# Precision
build:single_precision --define=precision=single
build:double_precision --define=precision=double

# Tests
build:tests --define=tests=true
test:tests --define=tests=true

# LTO (Link Time Optimization)
build:lto --copt=-flto
build:lto --linkopt=-flto

# Debug symbols
build:debug_symbols --copt=-g
build:debug_symbols --linkopt=-g

# Separate debug symbols
build:separate_debug_symbols --define=separate_debug_symbols=true

# Verbose output
build:verbose --subcommands=pretty_print
build:verbose --verbose_failures

# Fast unsafe build (for development)
build:fast_unsafe --define=fast_unsafe=true

# SCU (Single Compilation Unit) build
build:scu --define=scu_build=true

# Sanitizers
build:asan --copt=-fsanitize=address
build:asan --linkopt=-fsanitize=address

build:tsan --copt=-fsanitize=thread
build:tsan --linkopt=-fsanitize=thread

build:ubsan --copt=-fsanitize=undefined
build:ubsan --linkopt=-fsanitize=undefined

build:msan --copt=-fsanitize=memory
build:msan --linkopt=-fsanitize=memory

# Default configurations
build --config=warnings_all
build --config=threads

# Try to import user-specific configuration
try-import %workspace%/.bazelrc.user
