load("@rules_cc//cc:defs.bzl", "cc_binary", "cc_library")
load("//bazel:rex_engine.bzl", "rex_engine_binary", "rex_engine_library")

package(default_visibility = ["//visibility:public"])

# Version information
genrule(
    name = "version_generated",
    outs = ["core/version_generated.gen.h"],
    cmd = "$(location //bazel:generate_version) > $@",
    tools = ["//bazel:generate_version"],
)

genrule(
    name = "version_hash",
    outs = ["core/version_hash.gen.cpp"],
    cmd = "$(location //bazel:generate_version_hash) > $@",
    tools = ["//bazel:generate_version_hash"],
)

# Main ReX Engine library
rex_engine_library(
    name = "rex_engine",
    deps = [
        "//core:core",
        "//main:main",
        "//servers:servers",
        "//scene:scene",
        "//editor:editor",
        "//modules:modules",
        "//drivers:drivers",
    ],
)

# Platform-specific binaries

# Linux/BSD binary
rex_engine_binary(
    name = "redot_linuxbsd",
    srcs = ["platform/linuxbsd/godot_linuxbsd.cpp"],
    target_compatible_with = [
        "@platforms//os:linux",
    ],
    deps = [
        ":rex_engine",
        "//platform/linuxbsd:linuxbsd_platform",
    ],
)

# Windows binary
rex_engine_binary(
    name = "redot_windows",
    srcs = ["platform/windows/godot_windows.cpp"],
    target_compatible_with = [
        "@platforms//os:windows",
    ],
    deps = [
        ":rex_engine",
        "//platform/windows:windows_platform",
    ],
)

# macOS binary
rex_engine_binary(
    name = "redot_macos",
    srcs = ["platform/macos/godot_macos.mm"],
    target_compatible_with = [
        "@platforms//os:macos",
    ],
    deps = [
        ":rex_engine",
        "//platform/macos:macos_platform",
    ],
)

# Android binary
rex_engine_binary(
    name = "redot_android",
    srcs = ["platform/android/java_godot_lib_jni.cpp"],
    target_compatible_with = [
        "@platforms//os:android",
    ],
    deps = [
        ":rex_engine",
        "//platform/android:android_platform",
    ],
)

# iOS binary
rex_engine_binary(
    name = "redot_ios",
    srcs = ["platform/ios/godot_ios.mm"],
    target_compatible_with = [
        "@platforms//os:ios",
    ],
    deps = [
        ":rex_engine",
        "//platform/ios:ios_platform",
    ],
)

# Web binary
rex_engine_binary(
    name = "redot_web",
    srcs = ["platform/web/godot_web.cpp"],
    target_compatible_with = [
        "@platforms//cpu:wasm32",
    ],
    deps = [
        ":rex_engine",
        "//platform/web:web_platform",
    ],
)

# Default binary (platform-specific)
alias(
    name = "redot",
    actual = select({
        "@platforms//os:linux": ":redot_linuxbsd",
        "@platforms//os:macos": ":redot_macos", 
        "@platforms//os:windows": ":redot_windows",
        "@platforms//os:android": ":redot_android",
        "@platforms//os:ios": ":redot_ios",
        "//conditions:default": ":redot_linuxbsd",
    }),
)

# Editor binary (includes editor functionality)
rex_engine_binary(
    name = "redot_editor",
    srcs = select({
        "@platforms//os:linux": ["platform/linuxbsd/godot_linuxbsd.cpp"],
        "@platforms//os:macos": ["platform/macos/godot_macos.mm"],
        "@platforms//os:windows": ["platform/windows/godot_windows.cpp"],
        "//conditions:default": ["platform/linuxbsd/godot_linuxbsd.cpp"],
    }),
    defines = ["TOOLS_ENABLED"],
    deps = [
        ":rex_engine",
        "//editor:editor_full",
    ] + select({
        "@platforms//os:linux": ["//platform/linuxbsd:linuxbsd_platform"],
        "@platforms//os:macos": ["//platform/macos:macos_platform"],
        "@platforms//os:windows": ["//platform/windows:windows_platform"],
        "//conditions:default": ["//platform/linuxbsd:linuxbsd_platform"],
    }),
)

# Template binaries for export
rex_engine_binary(
    name = "redot_template_release",
    srcs = select({
        "@platforms//os:linux": ["platform/linuxbsd/godot_linuxbsd.cpp"],
        "@platforms//os:macos": ["platform/macos/godot_macos.mm"],
        "@platforms//os:windows": ["platform/windows/godot_windows.cpp"],
        "//conditions:default": ["platform/linuxbsd/godot_linuxbsd.cpp"],
    }),
    defines = ["TOOLS_DISABLED"],
    deps = [
        ":rex_engine",
    ] + select({
        "@platforms//os:linux": ["//platform/linuxbsd:linuxbsd_platform"],
        "@platforms//os:macos": ["//platform/macos:macos_platform"],
        "@platforms//os:windows": ["//platform/windows:windows_platform"],
        "//conditions:default": ["//platform/linuxbsd:linuxbsd_platform"],
    }),
)

rex_engine_binary(
    name = "redot_template_debug",
    srcs = select({
        "@platforms//os:linux": ["platform/linuxbsd/godot_linuxbsd.cpp"],
        "@platforms//os:macos": ["platform/macos/godot_macos.mm"],
        "@platforms//os:windows": ["platform/windows/godot_windows.cpp"],
        "//conditions:default": ["platform/linuxbsd/godot_linuxbsd.cpp"],
    }),
    defines = [
        "TOOLS_DISABLED",
        "DEBUG_ENABLED",
    ],
    deps = [
        ":rex_engine",
    ] + select({
        "@platforms//os:linux": ["//platform/linuxbsd:linuxbsd_platform"],
        "@platforms//os:macos": ["//platform/macos:macos_platform"],
        "@platforms//os:windows": ["//platform/windows:windows_platform"],
        "//conditions:default": ["//platform/linuxbsd:linuxbsd_platform"],
    }),
)

# Test targets
cc_binary(
    name = "test_runner",
    srcs = ["tests/test_main.cpp"],
    deps = [
        ":rex_engine",
        "//tests:all_tests",
        "@doctest//:doctest",
    ],
)

# Filegroup for all source files (useful for IDE integration)
filegroup(
    name = "all_sources",
    srcs = glob([
        "**/*.cpp",
        "**/*.c", 
        "**/*.h",
        "**/*.hpp",
        "**/*.mm",
    ]),
)

# Filegroup for configuration files
filegroup(
    name = "config_files",
    srcs = [
        "SConstruct",
        "version.py",
        "methods.py",
        "platform_methods.py",
        ".bazelrc",
        "WORKSPACE",
    ],
)

# Documentation
filegroup(
    name = "docs",
    srcs = glob([
        "*.md",
        "*.txt",
        "doc/**/*",
    ]),
)
