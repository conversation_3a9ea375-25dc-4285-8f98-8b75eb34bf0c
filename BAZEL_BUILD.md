# ReX Engine Bazel Build System

This document describes how to use the Bazel build system for ReX Engine as an alternative to the existing SCons build system.

## Prerequisites

1. **Install Bazel**: Download and install Bazel from https://bazel.build/install
   - Minimum version: 6.0.0
   - Recommended version: 7.0.0 or later

2. **Install Build Dependencies**: Ensure you have the necessary build tools:
   - **Linux**: `build-essential`, `pkg-config`, `libx11-dev`, `libxcursor-dev`, `libxinerama-dev`, `libgl1-mesa-dev`, `libglu1-mesa-dev`, `libasound2-dev`, `libpulse-dev`, `libudev-dev`, `libxi-dev`, `libxrandr-dev`
   - **macOS**: Xcode command line tools
   - **Windows**: Visual Studio 2019 or later with C++ tools

## Quick Start

### Building the Engine

```bash
# Build the default ReX Engine binary for your platform
bazel build //:redot

# Build the editor version
bazel build //:redot_editor --config=editor

# Build release templates
bazel build //:redot_template_release --config=template_release
bazel build //:redot_template_debug --config=template_debug
```

### Platform-Specific Builds

```bash
# Linux/BSD
bazel build //:redot_linuxbsd --config=linux

# Windows
bazel build //:redot_windows --config=windows

# macOS
bazel build //:redot_macos --config=macos

# Android
bazel build //:redot_android --config=android

# iOS
bazel build //:redot_ios --config=ios

# Web
bazel build //:redot_web --config=web
```

## Build Configurations

### Optimization Levels

```bash
# Debug build (default for development)
bazel build //:redot --config=debug

# Development build (optimized debug)
bazel build //:redot --config=dev

# Release build (full optimization)
bazel build //:redot --config=release

# Size-optimized build
bazel build //:redot --config=size

# Speed-optimized build
bazel build //:redot --config=speed
```

### Graphics APIs

```bash
# Vulkan (default on most platforms)
bazel build //:redot --config=vulkan

# OpenGL 3.3/ES 3.0
bazel build //:redot --config=opengl3

# Direct3D 12 (Windows only)
bazel build //:redot_windows --config=d3d12 --config=windows

# Metal (macOS/iOS only)
bazel build //:redot_macos --config=metal --config=macos
```

### Feature Toggles

```bash
# Disable 3D rendering
bazel build //:redot --config=disable_3d

# Disable advanced GUI features
bazel build //:redot --config=disable_advanced_gui

# Enable/disable threading
bazel build //:redot --config=threads        # Enable (default)
bazel build //:redot --config=no_threads     # Disable

# Precision settings
bazel build //:redot --config=single_precision  # Single precision (default)
bazel build //:redot --config=double_precision  # Double precision
```

### Module Configuration

```bash
# Build with specific modules enabled/disabled
bazel build //:redot --define=module_gdscript_enabled=true
bazel build //:redot --define=module_mono_enabled=false

# Use builtin libraries
bazel build //:redot --config=builtin_freetype --config=builtin_zlib
```

## Testing

### Running Tests

```bash
# Run all tests
bazel test //tests:test_runner --config=tests

# Run specific test suites
bazel test //tests:core_tests --config=tests
bazel test //tests:scene_tests --config=tests
bazel test //tests:servers_tests --config=tests
```

### Test with Different Configurations

```bash
# Test with debug build
bazel test //tests:test_runner --config=tests --config=debug

# Test with sanitizers
bazel test //tests:test_runner --config=tests --config=asan
bazel test //tests:test_runner --config=tests --config=tsan
bazel test //tests:test_runner --config=tests --config=ubsan
```

## Advanced Usage

### Custom Build Configurations

Create a `.bazelrc.user` file in the project root for personal configurations:

```bash
# .bazelrc.user
build --config=debug
build --config=warnings_extra
build --jobs=8
```

### Building Individual Components

```bash
# Build only the core library
bazel build //core:core

# Build only the scene system
bazel build //scene:scene

# Build platform-specific components
bazel build //platform/linuxbsd:linuxbsd_platform

# Build specific modules
bazel build //modules:gdscript
bazel build //modules:openxr
```

### Cross-Compilation

```bash
# Android cross-compilation
bazel build //:redot_android --config=android --cpu=arm64-v8a

# iOS cross-compilation
bazel build //:redot_ios --config=ios --cpu=ios_arm64

# Web compilation
bazel build //:redot_web --config=web --cpu=wasm32
```

## Differences from SCons Build

### Command Equivalents

| SCons Command | Bazel Equivalent |
|---------------|------------------|
| `scons` | `bazel build //:redot` |
| `scons tools=yes` | `bazel build //:redot_editor --config=editor` |
| `scons target=template_release` | `bazel build //:redot_template_release --config=template_release` |
| `scons platform=linuxbsd` | `bazel build //:redot_linuxbsd --config=linux` |
| `scons optimize=debug` | `bazel build //:redot --config=debug` |
| `scons optimize=speed` | `bazel build //:redot --config=speed` |
| `scons tests=yes` | `bazel test //tests:test_runner --config=tests` |

### Configuration Differences

1. **Module Configuration**: Instead of `module_*_enabled=yes/no`, use `--define=module_*_enabled=true/false`
2. **Platform Detection**: Automatic based on host platform, override with `--config=<platform>`
3. **Optimization**: Use `--config=<optimization_level>` instead of `optimize=<level>`
4. **Build Output**: Located in `bazel-bin/` instead of `bin/`

### Advantages of Bazel

1. **Incremental Builds**: Only rebuilds what changed
2. **Parallel Builds**: Better parallelization than SCons
3. **Caching**: Local and remote build caching support
4. **Reproducible Builds**: Hermetic builds with consistent results
5. **Cross-Platform**: Unified build system across all platforms

### Limitations

1. **Learning Curve**: Different syntax and concepts from SCons
2. **External Dependencies**: Some system libraries may need manual configuration
3. **IDE Integration**: May require additional setup for some IDEs

## Troubleshooting

### Common Issues

1. **Missing Dependencies**: Ensure all system dependencies are installed
2. **Permission Errors**: Check file permissions in the workspace
3. **Cache Issues**: Clear Bazel cache with `bazel clean --expunge`
4. **Platform Detection**: Explicitly specify platform with `--config=<platform>`

### Debug Build Issues

```bash
# Verbose output
bazel build //:redot --config=verbose

# Show all compiler commands
bazel build //:redot --subcommands

# Debug configuration issues
bazel build //:redot --explain=explanation.log
```

### Getting Help

1. Check the Bazel documentation: https://bazel.build/docs
2. ReX Engine community forums and Discord
3. File issues on the ReX Engine GitHub repository

## Performance Tips

1. **Use Remote Caching**: Set up remote cache for team builds
2. **Adjust Parallelism**: Use `--jobs=N` to control parallel jobs
3. **Local Cache**: Keep `~/.cache/bazel` for faster rebuilds
4. **Incremental Builds**: Use `bazel build` instead of `bazel clean && bazel build`

## Migration from SCons

To migrate from SCons to Bazel:

1. **Keep SCons**: Both build systems can coexist
2. **Test Builds**: Verify Bazel builds produce equivalent binaries
3. **Update Scripts**: Modify CI/CD scripts to use Bazel commands
4. **Team Training**: Ensure team members understand Bazel basics
5. **Gradual Adoption**: Start with development builds, then move to production

The Bazel build system is designed to complement, not replace, the existing SCons system. Both can be used simultaneously during the transition period.
