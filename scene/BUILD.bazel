load("//bazel:rex_engine.bzl", "rex_engine_library")

package(default_visibility = ["//visibility:public"])

# 2D scene components
rex_engine_library(
    name = "2d",
    srcs = glob(["2d/*.cpp"]),
    hdrs = glob(["2d/*.h"]),
    deps = [
        "//core:core",
        "//servers:servers",
    ],
)

# 3D scene components
rex_engine_library(
    name = "3d",
    srcs = glob(["3d/*.cpp"]),
    hdrs = glob(["3d/*.h"]),
    deps = [
        "//core:core",
        "//servers:servers",
    ],
)

# Animation system
rex_engine_library(
    name = "animation",
    srcs = glob(["animation/*.cpp"]),
    hdrs = glob(["animation/*.h"]),
    deps = [
        "//core:core",
        "//servers:servers",
    ],
)

# Audio scene components
rex_engine_library(
    name = "audio",
    srcs = glob(["audio/*.cpp"]),
    hdrs = glob(["audio/*.h"]),
    deps = [
        "//core:core",
        "//servers:servers",
    ],
)

# Debugger scene components
rex_engine_library(
    name = "debugger",
    srcs = glob(["debugger/*.cpp"]),
    hdrs = glob(["debugger/*.h"]),
    deps = [
        "//core:core",
        "//servers:servers",
    ],
)

# GUI system
rex_engine_library(
    name = "gui",
    srcs = glob(["gui/*.cpp"]),
    hdrs = glob(["gui/*.h"]),
    deps = [
        "//core:core",
        "//servers:servers",
    ],
)

# Main scene components
rex_engine_library(
    name = "main",
    srcs = glob(["main/*.cpp"]),
    hdrs = glob(["main/*.h"]),
    deps = [
        "//core:core",
        "//servers:servers",
    ],
)

# Multiplayer system
rex_engine_library(
    name = "multiplayer",
    srcs = glob(["multiplayer/*.cpp"]),
    hdrs = glob(["multiplayer/*.h"]),
    deps = [
        "//core:core",
        "//servers:servers",
    ],
)

# Resources system
rex_engine_library(
    name = "resources",
    srcs = glob(["resources/*.cpp"]),
    hdrs = glob(["resources/*.h"]),
    deps = [
        "//core:core",
        "//servers:servers",
    ],
)

# Theme system
rex_engine_library(
    name = "theme",
    srcs = glob(["theme/*.cpp"]),
    hdrs = glob(["theme/*.h"]),
    deps = [
        "//core:core",
        "//servers:servers",
    ],
)

# Main scene library
rex_engine_library(
    name = "scene",
    srcs = glob(["*.cpp"]),
    hdrs = glob(["*.h"]),
    deps = [
        ":2d",
        ":3d",
        ":animation",
        ":audio",
        ":debugger",
        ":gui",
        ":main",
        ":multiplayer",
        ":resources",
        ":theme",
        "//core:core",
        "//servers:servers",
    ],
)
