load("//bazel:rex_engine.bzl", "rex_engine_library")

package(default_visibility = ["//visibility:public"])

# Audio server
rex_engine_library(
    name = "audio",
    srcs = glob(["audio/*.cpp"]),
    hdrs = glob(["audio/*.h"]),
    deps = [
        "//core:core",
    ],
)

# Camera server
rex_engine_library(
    name = "camera",
    srcs = glob(["camera/*.cpp"]),
    hdrs = glob(["camera/*.h"]),
    deps = [
        "//core:core",
    ],
)

# Display server
rex_engine_library(
    name = "display",
    srcs = glob(["display/*.cpp"]),
    hdrs = glob(["display/*.h"]),
    deps = [
        "//core:core",
    ],
)

# Extensions server
rex_engine_library(
    name = "extensions",
    srcs = glob(["extensions/*.cpp"]),
    hdrs = glob(["extensions/*.h"]),
    deps = [
        "//core:core",
    ],
)

# Movie writer
rex_engine_library(
    name = "movie_writer",
    srcs = glob(["movie_writer/*.cpp"]),
    hdrs = glob(["movie_writer/*.h"]),
    deps = [
        "//core:core",
    ],
)

# Navigation server
rex_engine_library(
    name = "navigation",
    srcs = glob(["navigation/*.cpp"]),
    hdrs = glob(["navigation/*.h"]),
    deps = [
        "//core:core",
    ],
)

# Physics server 2D
rex_engine_library(
    name = "physics_2d",
    srcs = glob(["physics_2d/*.cpp"]),
    hdrs = glob(["physics_2d/*.h"]),
    deps = [
        "//core:core",
    ],
)

# Physics server 3D
rex_engine_library(
    name = "physics_3d",
    srcs = glob(["physics_3d/*.cpp"]),
    hdrs = glob(["physics_3d/*.h"]),
    deps = [
        "//core:core",
    ],
)

# Rendering server
rex_engine_library(
    name = "rendering",
    srcs = glob(["rendering/*.cpp"]),
    hdrs = glob(["rendering/*.h"]),
    deps = [
        "//core:core",
    ],
)

# Text server
rex_engine_library(
    name = "text",
    srcs = glob(["text/*.cpp"]),
    hdrs = glob(["text/*.h"]),
    deps = [
        "//core:core",
    ],
)

# XR server
rex_engine_library(
    name = "xr",
    srcs = glob(["xr/*.cpp"]),
    hdrs = glob(["xr/*.h"]),
    deps = [
        "//core:core",
    ],
)

# Main servers library
rex_engine_library(
    name = "servers",
    srcs = glob(["*.cpp"]),
    hdrs = glob(["*.h"]),
    deps = [
        ":audio",
        ":camera", 
        ":display",
        ":extensions",
        ":movie_writer",
        ":navigation",
        ":physics_2d",
        ":physics_3d",
        ":rendering",
        ":text",
        ":xr",
        "//core:core",
    ],
)
