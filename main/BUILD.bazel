load("//bazel:rex_engine.bzl", "rex_engine_library")

package(default_visibility = ["//visibility:public"])

# Generated files
genrule(
    name = "splash_gen",
    srcs = ["splash.png"],
    outs = ["splash.gen.h"],
    cmd = "echo '/* Generated splash header */' > $@",
)

genrule(
    name = "splash_editor_gen",
    srcs = select({
        "//bazel:editor": ["splash_editor.png"],
        "//conditions:default": [],
    }),
    outs = select({
        "//bazel:editor": ["splash_editor.gen.h"],
        "//conditions:default": [],
    }),
    cmd = select({
        "//bazel:editor": "echo '/* Generated editor splash header */' > $@",
        "//conditions:default": "touch $@",
    }),
)

genrule(
    name = "app_icon_gen",
    srcs = ["app_icon.png"],
    outs = ["app_icon.gen.h"],
    cmd = "echo '/* Generated app icon header */' > $@",
)

# Main library
rex_engine_library(
    name = "main",
    srcs = glob(["*.cpp"]),
    hdrs = glob(["*.h"]) + [
        ":splash_gen",
        ":app_icon_gen",
    ] + select({
        "//bazel:editor": [":splash_editor_gen"],
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:editor": [],
        "//conditions:default": ["TOOLS_DISABLED"],
    }) + select({
        "//bazel:steamapi": ["STEAMAPI_ENABLED"],
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
    ],
)
