load("//bazel:rex_engine.bzl", "rex_engine_library")

package(default_visibility = ["//visibility:public"])

# OpenGL/GLES drivers
rex_engine_library(
    name = "gl_context",
    srcs = glob(["gl_context/*.cpp"]),
    hdrs = glob(["gl_context/*.h"]),
    deps = [
        "//core:core",
        "//thirdparty/glad:glad",
    ],
)

rex_engine_library(
    name = "gles3",
    srcs = select({
        "//bazel:opengl3": glob(["gles3/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:opengl3": glob(["gles3/*.h"]),
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:opengl3": ["GLES3_ENABLED"],
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        ":gl_context",
    ],
)

# Vulkan driver
rex_engine_library(
    name = "vulkan",
    srcs = select({
        "//bazel:vulkan": glob(["vulkan/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:vulkan": glob(["vulkan/*.h"]),
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:vulkan": ["VULKAN_ENABLED"],
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        "//thirdparty/vulkan:vulkan",
        "//thirdparty/volk:volk",
    ],
)

# D3D12 driver (Windows only)
rex_engine_library(
    name = "d3d12",
    srcs = select({
        "//bazel:d3d12": glob(["d3d12/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:d3d12": glob(["d3d12/*.h"]),
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:d3d12": ["D3D12_ENABLED"],
        "//conditions:default": [],
    }),
    target_compatible_with = select({
        "//bazel:d3d12": ["@platforms//os:windows"],
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        "//thirdparty/d3d12ma:d3d12ma",
        "//thirdparty/directx_headers:directx_headers",
    ],
)

# Metal driver (macOS/iOS only)
rex_engine_library(
    name = "metal",
    srcs = select({
        "//bazel:metal": glob(["metal/*.mm"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:metal": glob(["metal/*.h"]),
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:metal": ["METAL_ENABLED"],
        "//conditions:default": [],
    }),
    target_compatible_with = select({
        "//bazel:metal": [
            "@platforms//os:macos",
            "@platforms//os:ios",
        ],
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
    ],
)

# Audio drivers
rex_engine_library(
    name = "alsa",
    srcs = select({
        "//bazel:use_alsa": glob(["alsa/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:use_alsa": glob(["alsa/*.h"]),
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:use_alsa": ["ALSA_ENABLED"],
        "//conditions:default": [],
    }),
    linkopts = select({
        "//bazel:use_alsa": ["-lasound"],
        "//conditions:default": [],
    }),
    target_compatible_with = [
        "@platforms//os:linux",
    ],
    deps = [
        "//core:core",
    ],
)

rex_engine_library(
    name = "pulseaudio",
    srcs = select({
        "//bazel:use_pulseaudio": glob(["pulseaudio/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:use_pulseaudio": glob(["pulseaudio/*.h"]),
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:use_pulseaudio": ["PULSEAUDIO_ENABLED"],
        "//conditions:default": [],
    }),
    linkopts = select({
        "//bazel:use_pulseaudio": ["-lpulse", "-lpulse-simple"],
        "//conditions:default": [],
    }),
    target_compatible_with = [
        "@platforms//os:linux",
    ],
    deps = [
        "//core:core",
    ],
)

rex_engine_library(
    name = "coreaudio",
    srcs = select({
        "@platforms//os:macos": glob(["coreaudio/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "@platforms//os:macos": glob(["coreaudio/*.h"]),
        "//conditions:default": [],
    }),
    defines = select({
        "@platforms//os:macos": ["COREAUDIO_ENABLED"],
        "//conditions:default": [],
    }),
    target_compatible_with = [
        "@platforms//os:macos",
        "@platforms//os:ios",
    ],
    deps = [
        "//core:core",
    ],
)

rex_engine_library(
    name = "wasapi",
    srcs = select({
        "@platforms//os:windows": glob(["wasapi/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "@platforms//os:windows": glob(["wasapi/*.h"]),
        "//conditions:default": [],
    }),
    defines = select({
        "@platforms//os:windows": ["WASAPI_ENABLED"],
        "//conditions:default": [],
    }),
    target_compatible_with = [
        "@platforms//os:windows",
    ],
    deps = [
        "//core:core",
    ],
)

# Input drivers
rex_engine_library(
    name = "xkbcommon",
    srcs = select({
        "//bazel:use_wayland": glob(["xkbcommon/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:use_wayland": glob(["xkbcommon/*.h"]),
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:use_wayland": ["XKBCOMMON_ENABLED"],
        "//conditions:default": [],
    }),
    linkopts = select({
        "//bazel:use_wayland": ["-lxkbcommon"],
        "//conditions:default": [],
    }),
    target_compatible_with = [
        "@platforms//os:linux",
    ],
    deps = [
        "//core:core",
    ],
)

# Main drivers library
rex_engine_library(
    name = "drivers",
    srcs = glob(["*.cpp"]),
    hdrs = glob(["*.h"]),
    deps = [
        "//core:core",
        ":gl_context",
    ] + select({
        "//bazel:opengl3": [":gles3"],
        "//conditions:default": [],
    }) + select({
        "//bazel:vulkan": [":vulkan"],
        "//conditions:default": [],
    }) + select({
        "//bazel:d3d12": [":d3d12"],
        "//conditions:default": [],
    }) + select({
        "//bazel:metal": [":metal"],
        "//conditions:default": [],
    }) + select({
        "//bazel:use_alsa": [":alsa"],
        "//conditions:default": [],
    }) + select({
        "//bazel:use_pulseaudio": [":pulseaudio"],
        "//conditions:default": [],
    }) + select({
        "@platforms//os:macos": [":coreaudio"],
        "//conditions:default": [],
    }) + select({
        "@platforms//os:windows": [":wasapi"],
        "//conditions:default": [],
    }) + select({
        "//bazel:use_wayland": [":xkbcommon"],
        "//conditions:default": [],
    }),
)
