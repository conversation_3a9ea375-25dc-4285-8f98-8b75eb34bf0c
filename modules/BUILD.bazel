load("//bazel:rex_engine.bzl", "rex_engine_library")

package(default_visibility = ["//visibility:public"])

# GDScript module
rex_engine_library(
    name = "gdscript",
    srcs = select({
        "//bazel:module_gdscript_enabled": glob(["gdscript/*.cpp"]) + select({
            "//bazel:editor": glob(["gdscript/editor/*.cpp"]),
            "//conditions:default": [],
        }) + select({
            "//bazel:module_jsonrpc_enabled": select({
                "//bazel:module_websocket_enabled": glob(["gdscript/language_server/*.cpp"]),
                "//conditions:default": [],
            }),
            "//conditions:default": [],
        }) + select({
            "//bazel:tests": glob(["gdscript/tests/*.cpp"]),
            "//conditions:default": [],
        }),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:module_gdscript_enabled": glob(["gdscript/*.h"]) + select({
            "//bazel:editor": glob(["gdscript/editor/*.h"]),
            "//conditions:default": [],
        }) + select({
            "//bazel:module_jsonrpc_enabled": select({
                "//bazel:module_websocket_enabled": glob(["gdscript/language_server/*.h"]),
                "//conditions:default": [],
            }),
            "//conditions:default": [],
        }) + select({
            "//bazel:tests": glob(["gdscript/tests/*.h"]),
            "//conditions:default": [],
        }),
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:module_gdscript_enabled": ["GDSCRIPT_ENABLED"],
        "//conditions:default": [],
    }) + select({
        "//bazel:module_jsonrpc_enabled": select({
            "//bazel:module_websocket_enabled": [],
            "//conditions:default": ["GDSCRIPT_NO_LSP"],
        }),
        "//conditions:default": ["GDSCRIPT_NO_LSP"],
    }),
    deps = [
        "//core:core",
        "//scene:scene",
    ] + select({
        "//bazel:module_jsonrpc_enabled": [":jsonrpc"],
        "//conditions:default": [],
    }) + select({
        "//bazel:module_websocket_enabled": [":websocket"],
        "//conditions:default": [],
    }),
)

# C# module
rex_engine_library(
    name = "mono",
    srcs = select({
        "//bazel:module_mono_enabled": glob(["mono/*.cpp"]) + select({
            "//bazel:editor": glob(["mono/editor/*.cpp"]),
            "//conditions:default": [],
        }),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:module_mono_enabled": glob(["mono/*.h"]) + select({
            "//bazel:editor": glob(["mono/editor/*.h"]),
            "//conditions:default": [],
        }),
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:module_mono_enabled": ["MONO_ENABLED"],
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        "//scene:scene",
    ],
)

# Regex module
rex_engine_library(
    name = "regex",
    srcs = select({
        "//bazel:module_regex_enabled": glob(["regex/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:module_regex_enabled": glob(["regex/*.h"]),
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:module_regex_enabled": ["REGEX_ENABLED"],
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        "//thirdparty/pcre2:pcre2",
    ],
)

# JSON-RPC module
rex_engine_library(
    name = "jsonrpc",
    srcs = select({
        "//bazel:module_jsonrpc_enabled": glob(["jsonrpc/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:module_jsonrpc_enabled": glob(["jsonrpc/*.h"]),
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:module_jsonrpc_enabled": ["JSONRPC_ENABLED"],
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
    ],
)

# WebSocket module
rex_engine_library(
    name = "websocket",
    srcs = select({
        "//bazel:module_websocket_enabled": glob(["websocket/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:module_websocket_enabled": glob(["websocket/*.h"]),
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:module_websocket_enabled": ["WEBSOCKET_ENABLED"],
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        "//thirdparty/wslay:wslay",
    ],
)

# OpenXR module
rex_engine_library(
    name = "openxr",
    srcs = select({
        "//bazel:module_openxr_enabled": glob(["openxr/*.cpp"]) + select({
            "//bazel:editor": glob(["openxr/editor/*.cpp"]),
            "//conditions:default": [],
        }),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:module_openxr_enabled": glob(["openxr/*.h"]) + select({
            "//bazel:editor": glob(["openxr/editor/*.h"]),
            "//conditions:default": [],
        }),
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:module_openxr_enabled": ["OPENXR_ENABLED"],
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        "//servers:servers",
        "//thirdparty/openxr:openxr",
    ],
)

# Basis Universal texture compression
rex_engine_library(
    name = "basis_universal",
    srcs = select({
        "//bazel:module_basis_universal_enabled": glob(["basis_universal/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:module_basis_universal_enabled": glob(["basis_universal/*.h"]),
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:module_basis_universal_enabled": ["BASIS_UNIVERSAL_ENABLED"],
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        "//thirdparty/basis_universal:basis_universal",
    ],
)

# ASTC texture compression
rex_engine_library(
    name = "astcenc",
    srcs = select({
        "//bazel:module_astcenc_enabled": glob(["astcenc/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:module_astcenc_enabled": glob(["astcenc/*.h"]),
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:module_astcenc_enabled": ["ASTCENC_ENABLED"],
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        "//thirdparty/astcenc:astcenc",
    ],
)

# ETC texture compression
rex_engine_library(
    name = "etcpak",
    srcs = select({
        "//bazel:module_etcpak_enabled": glob(["etcpak/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:module_etcpak_enabled": glob(["etcpak/*.h"]),
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:module_etcpak_enabled": ["ETCPAK_ENABLED"],
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        "//thirdparty/etcpak:etcpak",
    ],
)

# Squish texture compression
rex_engine_library(
    name = "squish",
    srcs = select({
        "//bazel:module_squish_enabled": glob(["squish/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:module_squish_enabled": glob(["squish/*.h"]),
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:module_squish_enabled": ["SQUISH_ENABLED"],
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
    ],
)

# Theora video codec
rex_engine_library(
    name = "theora",
    srcs = select({
        "//bazel:module_theora_enabled": glob(["theora/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:module_theora_enabled": glob(["theora/*.h"]),
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:module_theora_enabled": ["THEORA_ENABLED"],
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        "//thirdparty/libtheora:libtheora",
        "//thirdparty/libogg:libogg",
        "//thirdparty/libvorbis:libvorbis",
    ],
)

# Vorbis audio codec
rex_engine_library(
    name = "vorbis",
    srcs = select({
        "//bazel:module_vorbis_enabled": glob(["vorbis/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:module_vorbis_enabled": glob(["vorbis/*.h"]),
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:module_vorbis_enabled": ["VORBIS_ENABLED"],
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        "//thirdparty/libogg:libogg",
        "//thirdparty/libvorbis:libvorbis",
    ],
)

# Opus audio codec
rex_engine_library(
    name = "opus",
    srcs = select({
        "//bazel:module_opus_enabled": glob(["opus/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:module_opus_enabled": glob(["opus/*.h"]),
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:module_opus_enabled": ["OPUS_ENABLED"],
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
    ],
)

# Main modules library
rex_engine_library(
    name = "modules",
    deps = [
        ":gdscript",
        ":mono",
        ":regex",
        ":jsonrpc",
        ":websocket",
        ":openxr",
        ":basis_universal",
        ":astcenc",
        ":etcpak",
        ":squish",
        ":theora",
        ":vorbis",
        ":opus",
    ],
)
