#!/usr/bin/env python3
"""Generate version hash source file for ReX Engine."""

import subprocess
import sys
import os

def get_git_hash():
    """Get the current git commit hash."""
    try:
        # Try to get the git hash
        result = subprocess.run(
            ["git", "rev-parse", "HEAD"],
            capture_output=True,
            text=True,
            cwd=os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        )
        if result.returncode == 0:
            return result.stdout.strip()
    except (subprocess.SubprocessError, FileNotFoundError):
        pass
    
    # Fallback if git is not available
    return "unknown"

def get_git_branch():
    """Get the current git branch."""
    try:
        result = subprocess.run(
            ["git", "rev-parse", "--abbrev-ref", "HEAD"],
            capture_output=True,
            text=True,
            cwd=os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        )
        if result.returncode == 0:
            return result.stdout.strip()
    except (subprocess.SubprocessError, FileNotFoundError):
        pass
    
    return "unknown"

def generate_version_hash_source():
    """Generate the version hash source content."""
    git_hash = get_git_hash()
    git_branch = get_git_branch()
    
    source = f'''/* THIS FILE IS GENERATED DO NOT EDIT */
#include "core/version.h"

const char *const VERSION_HASH = "{git_hash}";
const char *const VERSION_FULL_CONFIG = VERSION_FULL_BUILD;
const char *const VERSION_FULL_BUILD_CONFIG = VERSION_FULL_BUILD;

// Git information
const char *const GIT_COMMIT_HASH = "{git_hash}";
const char *const GIT_BRANCH = "{git_branch}";

// Build information
const char *const BUILD_DATE = __DATE__;
const char *const BUILD_TIME = __TIME__;

#ifdef TOOLS_ENABLED
const char *const VERSION_FULL_NAME_WITH_HASH = VERSION_FULL_NAME " (" GIT_COMMIT_HASH ")";
#else
const char *const VERSION_FULL_NAME_WITH_HASH = VERSION_FULL_NAME;
#endif
'''
    return source

if __name__ == "__main__":
    print(generate_version_hash_source())
