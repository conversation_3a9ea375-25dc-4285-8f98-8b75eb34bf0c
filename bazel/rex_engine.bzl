"""ReX Engine Bazel build rules."""

load("@rules_cc//cc:defs.bzl", "cc_binary", "cc_library")

def _get_platform_defines():
    """Get platform-specific defines."""
    return select({
        "@platforms//os:linux": ["UNIX_ENABLED", "LINUXBSD_ENABLED"],
        "@platforms//os:macos": ["UNIX_ENABLED", "MACOS_ENABLED"],
        "@platforms//os:windows": ["WINDOWS_ENABLED"],
        "@platforms//os:android": ["UNIX_ENABLED", "ANDROID_ENABLED"],
        "@platforms//os:ios": ["UNIX_ENABLED", "IOS_ENABLED"],
        "//conditions:default": ["UNIX_ENABLED", "LINUXBSD_ENABLED"],
    })

def _get_arch_defines():
    """Get architecture-specific defines."""
    return select({
        "//bazel:x86_64": ["X86_64_ENABLED"],
        "//bazel:arm64": ["ARM64_ENABLED"],
        "//bazel:x86_32": ["X86_32_ENABLED"],
        "//conditions:default": [],
    })

def _get_target_defines():
    """Get target-specific defines."""
    return select({
        "//bazel:editor": ["TOOLS_ENABLED"],
        "//bazel:template_release": ["TOOLS_DISABLED"],
        "//bazel:template_debug": ["TOOLS_DISABLED", "DEBUG_ENABLED"],
        "//conditions:default": [],
    })

def _get_optimization_defines():
    """Get optimization-specific defines.""" 
    return select({
        "//bazel:debug": ["DEBUG_ENABLED", "DEV_ENABLED"],
        "//bazel:dev": ["DEBUG_ENABLED", "DEV_ENABLED"],
        "//bazel:release": [],
        "//bazel:speed": ["OPTIMIZE_SPEED"],
        "//bazel:size": ["OPTIMIZE_SIZE"],
        "//conditions:default": [],
    })

def _get_feature_defines():
    """Get feature-specific defines."""
    defines = []
    
    # Graphics APIs
    defines += select({
        "//bazel:vulkan": ["VULKAN_ENABLED"],
        "//conditions:default": [],
    })
    
    defines += select({
        "//bazel:opengl3": ["GLES3_ENABLED"],
        "//conditions:default": [],
    })
    
    defines += select({
        "//bazel:d3d12": ["D3D12_ENABLED"],
        "//conditions:default": [],
    })
    
    defines += select({
        "//bazel:metal": ["METAL_ENABLED"],
        "//conditions:default": [],
    })
    
    # Feature toggles
    defines += select({
        "//bazel:disable_3d": ["_3D_DISABLED"],
        "//conditions:default": [],
    })
    
    defines += select({
        "//bazel:disable_advanced_gui": ["ADVANCED_GUI_DISABLED"],
        "//conditions:default": [],
    })
    
    # Threading
    defines += select({
        "//bazel:threads": ["THREADS_ENABLED"],
        "//bazel:no_threads": ["NO_THREADS"],
        "//conditions:default": ["THREADS_ENABLED"],
    })
    
    # Precision
    defines += select({
        "//bazel:single_precision": ["REAL_T_IS_FLOAT"],
        "//bazel:double_precision": ["REAL_T_IS_DOUBLE"],
        "//conditions:default": ["REAL_T_IS_FLOAT"],
    })
    
    return defines

def _get_common_copts():
    """Get common compiler options."""
    copts = [
        "-std=gnu++17",
        "-fno-exceptions",
        "-fno-rtti",
        "-fpermissive",
        "-Wwrite-strings",
    ]
    
    # Platform-specific options
    copts += select({
        "@platforms//os:linux": ["-fPIC"],
        "@platforms//os:macos": ["-mmacosx-version-min=10.14"],
        "@platforms//os:windows": ["/std:c++17", "/permissive-"],
        "//conditions:default": [],
    })
    
    # Optimization options
    copts += select({
        "//bazel:debug": ["-O0", "-g3"],
        "//bazel:dev": ["-Og", "-g3"],
        "//bazel:release": ["-O3"],
        "//bazel:speed": ["-O2"],
        "//bazel:size": ["-Os"],
        "//conditions:default": ["-O2"],
    })
    
    # Warning options
    copts += select({
        "//bazel:warnings_extra": ["-Wall", "-Wextra", "-Wwrite-strings", "-Wno-unused-parameter"],
        "//bazel:warnings_all": ["-Wall"],
        "//bazel:warnings_moderate": ["-Wall", "-Wno-unused"],
        "//bazel:warnings_none": ["-w"],
        "//conditions:default": ["-Wall"],
    })
    
    return copts

def _get_common_linkopts():
    """Get common linker options."""
    linkopts = []
    
    # Platform-specific options
    linkopts += select({
        "@platforms//os:linux": ["-pthread"],
        "@platforms//os:macos": ["-mmacosx-version-min=10.14"],
        "//conditions:default": [],
    })
    
    # LTO options
    linkopts += select({
        "//bazel:lto": ["-flto"],
        "//conditions:default": [],
    })
    
    return linkopts

def rex_engine_library(name, srcs = [], hdrs = [], deps = [], defines = [], copts = [], linkopts = [], **kwargs):
    """Create a ReX Engine library with common settings."""
    
    all_defines = (
        _get_platform_defines() +
        _get_arch_defines() +
        _get_target_defines() +
        _get_optimization_defines() +
        _get_feature_defines() +
        defines
    )
    
    all_copts = _get_common_copts() + copts
    all_linkopts = _get_common_linkopts() + linkopts
    
    cc_library(
        name = name,
        srcs = srcs,
        hdrs = hdrs,
        deps = deps,
        defines = all_defines,
        copts = all_copts,
        linkopts = all_linkopts,
        **kwargs
    )

def rex_engine_binary(name, srcs = [], deps = [], defines = [], copts = [], linkopts = [], **kwargs):
    """Create a ReX Engine binary with common settings."""
    
    all_defines = (
        _get_platform_defines() +
        _get_arch_defines() +
        _get_target_defines() +
        _get_optimization_defines() +
        _get_feature_defines() +
        defines
    )
    
    all_copts = _get_common_copts() + copts
    all_linkopts = _get_common_linkopts() + linkopts
    
    cc_binary(
        name = name,
        srcs = srcs,
        deps = deps,
        defines = all_defines,
        copts = all_copts,
        linkopts = all_linkopts,
        **kwargs
    )

def rex_engine_test(name, srcs = [], deps = [], **kwargs):
    """Create a ReX Engine test with common settings."""

    rex_engine_binary(
        name = name,
        srcs = srcs,
        deps = deps + ["@doctest//:doctest"],
        defines = ["TESTS_ENABLED"],
        testonly = True,
        **kwargs
    )

# Configuration helpers
def _config_setting(name, define_values = {}, values = {}):
    """Helper to create config_setting rules."""
    native.config_setting(
        name = name,
        define_values = define_values,
        values = values,
    )
