load("@rules_python//python:defs.bzl", "py_binary")

package(default_visibility = ["//visibility:public"])

# Python scripts for code generation
py_binary(
    name = "generate_version",
    srcs = ["generate_version.py"],
    deps = [],
)

py_binary(
    name = "generate_version_hash",
    srcs = ["generate_version_hash.py"],
    deps = [],
)

py_binary(
    name = "generate_authors",
    srcs = ["generate_authors.py"],
    deps = [],
)

py_binary(
    name = "generate_donors",
    srcs = ["generate_donors.py"],
    deps = [],
)

py_binary(
    name = "generate_license",
    srcs = ["generate_license.py"],
    deps = [],
)

# Build rule definitions
exports_files([
    "rex_engine.bzl",
    "platform_config.bzl",
    "module_config.bzl",
])

# External library BUILD files
exports_files([
    "zlib.BUILD",
    "brotli.BUILD",
    "freetype.BUILD",
    "libpng.BUILD",
    "vulkan_headers.BUILD",
    "opengl_headers.BUILD",
    "x11_headers.BUILD",
    "directx_headers.BUILD",
    "libogg.BUILD",
    "libvorbis.BUILD",
    "enet.BUILD",
    "mbedtls.BUILD",
    "jolt_physics.BUILD",
    "harfbuzz.BUILD",
    "icu.BUILD",
    "libwebp.BUILD",
    "glslang.BUILD",
    "spirv_cross.BUILD",
    "doctest.BUILD",
])
