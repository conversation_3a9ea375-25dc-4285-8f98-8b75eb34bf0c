#!/usr/bin/env python3
"""Generate version header for ReX Engine."""

import os
import sys

# Add the root directory to the path to import version.py
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import version
except ImportError:
    # Fallback values if version.py is not available
    class version:
        major = 4
        minor = 5
        patch = 0
        status = "alpha"
        module_config = ""
        year = 2024
        website = "https://github.com/ReX-Engine/ReX-Engine"
        docs_branch = "main"

def generate_version_header():
    """Generate the version header content."""
    header = f'''/* THIS FILE IS GENERATED DO NOT EDIT */
#ifndef VERSION_GENERATED_GEN_H
#define VERSION_GENERATED_GEN_H

#define VERSION_SHORT_NAME "{version.name if hasattr(version, 'name') else 'ReX Engine'}"
#define VERSION_NAME "{version.name if hasattr(version, 'name') else 'ReX Engine'}"
#define VERSION_MAJOR {version.major}
#define VERSION_MINOR {version.minor}
#define VERSION_PATCH {version.patch}
#define VERSION_STATUS "{version.status}"
#define VERSION_BUILD "{version.module_config if hasattr(version, 'module_config') else ''}"
#define VERSION_MODULE_CONFIG "{version.module_config if hasattr(version, 'module_config') else ''}"
#define VERSION_YEAR {version.year if hasattr(version, 'year') else 2024}
#define VERSION_WEBSITE "{version.website if hasattr(version, 'website') else 'https://github.com/ReX-Engine/ReX-Engine'}"
#define VERSION_DOCS_BRANCH "{version.docs_branch if hasattr(version, 'docs_branch') else 'main'}"
#define VERSION_DOCS_URL "https://docs.godotengine.org/en/" VERSION_DOCS_BRANCH

// Version string in the format "major.minor.patch-status"
#define VERSION_BRANCH "{version.major}.{version.minor}"
#define VERSION_NUMBER "{version.major}.{version.minor}.{version.patch}"
#define VERSION_FULL_BUILD VERSION_NUMBER "-" VERSION_STATUS
#define VERSION_FULL_NAME VERSION_NAME " v" VERSION_FULL_BUILD

// For compatibility with Godot
#define GODOT_VERSION_MAJOR VERSION_MAJOR
#define GODOT_VERSION_MINOR VERSION_MINOR
#define GODOT_VERSION_PATCH VERSION_PATCH
#define GODOT_VERSION_STATUS VERSION_STATUS

#endif // VERSION_GENERATED_GEN_H
'''
    return header

if __name__ == "__main__":
    print(generate_version_header())
