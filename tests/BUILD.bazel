load("//bazel:rex_engine.bzl", "rex_engine_test", "rex_engine_library")

package(default_visibility = ["//visibility:public"])

# Core tests
rex_engine_library(
    name = "core_tests",
    srcs = select({
        "//bazel:tests": glob(["core/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:tests": glob(["core/*.h"]),
        "//conditions:default": [],
    }),
    testonly = True,
    deps = [
        "//core:core",
        "@doctest//:doctest",
    ],
)

# Scene tests
rex_engine_library(
    name = "scene_tests",
    srcs = select({
        "//bazel:tests": glob(["scene/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:tests": glob(["scene/*.h"]),
        "//conditions:default": [],
    }),
    testonly = True,
    deps = [
        "//core:core",
        "//scene:scene",
        "@doctest//:doctest",
    ],
)

# Servers tests
rex_engine_library(
    name = "servers_tests",
    srcs = select({
        "//bazel:tests": glob(["servers/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:tests": glob(["servers/*.h"]),
        "//conditions:default": [],
    }),
    testonly = True,
    deps = [
        "//core:core",
        "//servers:servers",
        "@doctest//:doctest",
    ],
)

# All tests library
rex_engine_library(
    name = "all_tests",
    srcs = select({
        "//bazel:tests": glob(["*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:tests": glob(["*.h"]),
        "//conditions:default": [],
    }),
    testonly = True,
    deps = [
        ":core_tests",
        ":scene_tests", 
        ":servers_tests",
        "@doctest//:doctest",
    ],
)

# Test runner
rex_engine_test(
    name = "test_runner",
    srcs = select({
        "//bazel:tests": ["test_main.cpp"],
        "//conditions:default": [],
    }),
    deps = [
        ":all_tests",
    ],
)
