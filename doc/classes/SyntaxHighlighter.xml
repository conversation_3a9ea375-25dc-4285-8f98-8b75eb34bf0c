<?xml version="1.0" encoding="UTF-8" ?>
<class name="Syntax<PERSON><PERSON>light<PERSON>" inherits="Resource" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Base class for syntax highlighters. Provides syntax highlighting data to a [TextEdit].
	</brief_description>
	<description>
		Base class for syntax highlighters. Provides syntax highlighting data to a [TextEdit]. The associated [TextEdit] will call into the [SyntaxHighlighter] on an as-needed basis.
		[b]Note:[/b] A [SyntaxHighlighter] instance should not be used across multiple [TextEdit] nodes.
	</description>
	<tutorials>
	</tutorials>
	<methods>
		<method name="_clear_highlighting_cache" qualifiers="virtual">
			<return type="void" />
			<description>
				Virtual method which can be overridden to clear any local caches.
			</description>
		</method>
		<method name="_get_line_syntax_highlighting" qualifiers="virtual const">
			<return type="Dictionary" />
			<param index="0" name="line" type="int" />
			<description>
				Virtual method which can be overridden to return syntax highlighting data.
				See [method get_line_syntax_highlighting] for more details.
			</description>
		</method>
		<method name="_update_cache" qualifiers="virtual">
			<return type="void" />
			<description>
				Virtual method which can be overridden to update any local caches.
			</description>
		</method>
		<method name="clear_highlighting_cache">
			<return type="void" />
			<description>
				Clears all cached syntax highlighting data.
				Then calls overridable method [method _clear_highlighting_cache].
			</description>
		</method>
		<method name="get_line_syntax_highlighting">
			<return type="Dictionary" />
			<param index="0" name="line" type="int" />
			<description>
				Returns the syntax highlighting data for the line at index [param line]. If the line is not cached, calls [method _get_line_syntax_highlighting] first to calculate the data.
				Each entry is a column number containing a nested [Dictionary]. The column number denotes the start of a region, the region will end if another region is found, or at the end of the line. The nested [Dictionary] contains the data for that region. Currently only the key [code]"color"[/code] is supported.
				[b]Example:[/b] Possible return value. This means columns [code]0[/code] to [code]4[/code] should be red, and columns [code]5[/code] to the end of the line should be green:
				[codeblock]
				{
				    0: {
				        "color": Color(1, 0, 0)
				    },
				    5: {
				        "color": Color(0, 1, 0)
				    }
				}
				[/codeblock]
			</description>
		</method>
		<method name="get_text_edit" qualifiers="const">
			<return type="TextEdit" />
			<description>
				Returns the associated [TextEdit] node.
			</description>
		</method>
		<method name="update_cache">
			<return type="void" />
			<description>
				Clears then updates the [SyntaxHighlighter] caches. Override [method _update_cache] for a callback.
				[b]Note:[/b] This is called automatically when the associated [TextEdit] node, updates its own cache.
			</description>
		</method>
	</methods>
</class>
