<?xml version="1.0" encoding="UTF-8" ?>
<class name="ResourceImporterTexture" inherits="ResourceImporter" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Imports an image for use in 2D or 3D rendering.
	</brief_description>
	<description>
		This importer imports [CompressedTexture2D] resources. If you need to process the image in scripts in a more convenient way, use [ResourceImporterImage] instead. See also [ResourceImporterLayeredTexture].
	</description>
	<tutorials>
		<link title="Importing images">$DOCS_URL/tutorials/assets_pipeline/importing_images.html</link>
	</tutorials>
	<members>
		<member name="compress/channel_pack" type="int" setter="" getter="" default="0">
			Controls how color channels should be used in the imported texture.
			[b]sRGB Friendly:[/b] Prevents the RG color format from being used, as it does not support sRGB color.
			[b]Optimized:[/b] Allows the RG color format to be used if the texture does not use the blue channel. This reduces memory usage if the texture's blue channel can be discarded (all pixels must have a blue value of [code]0[/code]).
		</member>
		<member name="compress/hdr_compression" type="int" setter="" getter="" default="1">
			Controls how VRAM compression should be performed for HDR images.
			[b]Disabled:[/b] Never use VRAM compression for HDR textures, regardless of whether they're opaque or transparent. Instead, the texture is converted to RGBE9995 (9-bits per channel + 5-bit exponent = 32 bits per pixel) to reduce memory usage compared to a half-float or single-precision float image format.
			[b]Opaque Only:[/b] Only uses VRAM compression for opaque HDR textures. This is due to a limitation of HDR formats, as there is no VRAM-compressed HDR format that supports transparency at the same time.
			[b]Always:[/b] Force VRAM compression even for HDR textures with an alpha channel. To perform this, the alpha channel is discarded on import.
			[b]Note:[/b] Only effective on Radiance HDR ([code].hdr[/code]) and OpenEXR ([code].exr[/code]) images.
		</member>
		<member name="compress/high_quality" type="bool" setter="" getter="" default="false">
			If [code]true[/code], uses BPTC compression on desktop platforms and ASTC compression on mobile platforms. When using BPTC, BC7 is used for SDR textures and BC6H is used for HDR textures.
			If [code]false[/code], uses the faster but lower-quality S3TC compression on desktop platforms and ETC2 on mobile/web platforms. When using S3TC, DXT1 (BC1) is used for opaque textures and DXT5 (BC3) is used for transparent or normal map (RGTC) textures.
			BPTC and ASTC support VRAM compression for HDR textures, but S3TC and ETC2 do not (see [member compress/hdr_compression]).
		</member>
		<member name="compress/lossy_quality" type="float" setter="" getter="" default="0.7">
			The quality to use when using the [b]Lossy[/b] compression mode. Higher values result in better quality, at the cost of larger file sizes. Lossy quality does not affect memory usage of the imported texture, only its file size on disk.
		</member>
		<member name="compress/mode" type="int" setter="" getter="" default="0">
			The compression mode to use. Each compression mode provides a different tradeoff:
			[b]Lossless[/b]: Original quality, high memory usage, high size on disk, fast import.
			[b]Lossy:[/b] Reduced quality, high memory usage, low size on disk, fast import.
			[b]VRAM Compressed:[/b] Reduced quality, low memory usage, low size on disk, slowest import. Only use for textures in 3D scenes, not for 2D elements.
			[b]VRAM Uncompressed:[/b] Original quality, high memory usage, highest size on disk, fastest import.
			[b]Basis Universal:[/b] Reduced quality, low memory usage, lowest size on disk, slow import. Only use for textures in 3D scenes, not for 2D elements.
			See [url=$DOCS_URL/tutorials/assets_pipeline/importing_images.html#compress-mode]Compress mode[/url] in the manual for more details.
		</member>
		<member name="compress/normal_map" type="int" setter="" getter="" default="0">
			When using a texture as normal map, only the red and green channels are required. Given regular texture compression algorithms produce artifacts that don't look that nice in normal maps, the RGTC compression format is the best fit for this data. Forcing this option to Enable will make Redot import the image as RGTC compressed. By default, it's set to Detect. This means that if the texture is ever detected to be used as a normal map, it will be changed to Enable and reimported automatically.
			Note that RGTC compression affects the resulting normal map image. You will have to adjust custom shaders that use the normal map's blue channel to take this into account. Built-in material shaders already ignore the blue channel in a normal map (regardless of the actual normal map's contents).
		</member>
		<member name="detect_3d/compress_to" type="int" setter="" getter="" default="1">
			This changes the [member compress/mode] option that is used when a texture is detected as being used in 3D.
			Changing this import option only has an effect if a texture is detected as being used in 3D. Changing this to [b]Disabled[/b] then reimporting will not change the existing compress mode on a texture (if it's detected to be used in 3D), but choosing [b]VRAM Compressed[/b] or [b]Basis Universal[/b] will.
		</member>
		<member name="editor/convert_colors_with_editor_theme" type="bool" setter="" getter="" default="false">
			If [code]true[/code], converts the imported image's colors to match [member EditorSettings.interface/theme/icon_and_font_color]. This assumes the image uses the exact same colors as [url=$DOCS_URL/contributing/development/editor/creating_icons.html]Godot's own color palette for editor icons[/url], with the source file designed for a dark editor theme. This should be enabled for editor plugin icons and custom class icons, but should be left disabled otherwise.
			[b]Note:[/b] Only available for SVG images.
		</member>
		<member name="editor/scale_with_editor_scale" type="bool" setter="" getter="" default="false">
			If [code]true[/code], scales the imported image to match [member EditorSettings.interface/editor/custom_display_scale]. This should be enabled for editor plugin icons and custom class icons, but should be left disabled otherwise.
			[b]Note:[/b] Only available for SVG images.
		</member>
		<member name="mipmaps/generate" type="bool" setter="" getter="" default="false">
			If [code]true[/code], smaller versions of the texture are generated on import. For example, a 64×64 texture will generate 6 mipmaps (32×32, 16×16, 8×8, 4×4, 2×2, 1×1). This has several benefits:
			- Textures will not become grainy in the distance (in 3D), or if scaled down due to [Camera2D] zoom or [CanvasItem] scale (in 2D).
			- Performance will improve if the texture is displayed in the distance, since sampling smaller versions of the original texture is faster and requires less memory bandwidth.
			The downside of mipmaps is that they increase memory usage by roughly 33%.
			It's recommended to enable mipmaps in 3D. However, in 2D, this should only be enabled if your project visibly benefits from having mipmaps enabled. If the camera never zooms out significantly, there won't be a benefit to enabling mipmaps but memory usage will increase.
		</member>
		<member name="mipmaps/limit" type="int" setter="" getter="" default="-1">
			Unimplemented. This currently has no effect when changed.
		</member>
		<member name="process/fix_alpha_border" type="bool" setter="" getter="" default="true">
			If [code]true[/code], puts pixels of the same surrounding color in transition from transparent to opaque areas. For textures displayed with bilinear filtering, this helps to reduce the outline effect when exporting images from an image editor.
			It's recommended to leave this enabled (as it is by default), unless this causes issues for a particular image.
		</member>
		<member name="process/hdr_as_srgb" type="bool" setter="" getter="" default="false">
			Some HDR images you can find online may be broken and contain sRGB color data (instead of linear color data). It is advised not to use those files. If you absolutely have to, enabling [member process/hdr_as_srgb] will make them look correct.
			[b]Warning:[/b] Enabling [member process/hdr_as_srgb] on well-formatted HDR images will cause the resulting image to look too dark, so leave this on [code]false[/code] if unsure.
		</member>
		<member name="process/hdr_clamp_exposure" type="bool" setter="" getter="" default="false">
			If [code]true[/code], clamps exposure in the imported high dynamic range images using a smart clamping formula (without introducing [i]visible[/i] clipping).
			Some HDR panorama images you can find online may contain extremely bright pixels, due to being taken from real life sources without any clipping.
			While these HDR panorama images are accurate to real life, this can cause the radiance map generated by Redot to contain sparkles when used as a background sky. This can be seen in material reflections (even on rough materials in extreme cases). Enabling [member process/hdr_clamp_exposure] can resolve this.
		</member>
		<member name="process/normal_map_invert_y" type="bool" setter="" getter="" default="false">
			If [code]true[/code], convert the normal map from Y- (DirectX-style) to Y+ (OpenGL-style) by inverting its green color channel. This is the normal map convention expected by Redot.
			More information about normal maps (including a coordinate order table for popular engines) can be found [url=http://wiki.polycount.com/wiki/Normal_Map_Technical_Details]here[/url].
		</member>
		<member name="process/premult_alpha" type="bool" setter="" getter="" default="false">
			An alternative to fixing darkened borders with [member process/fix_alpha_border] is to use premultiplied alpha. By enabling this option, the texture will be converted to this format. A premultiplied alpha texture requires specific materials to be displayed correctly:
			- In 2D, a [CanvasItemMaterial] will need to be created and configured to use the [constant CanvasItemMaterial.BLEND_MODE_PREMULT_ALPHA] blend mode on [CanvasItem]s that use this texture. In custom [code]@canvas_item[/code] shaders, [code]render_mode blend_premul_alpha;[/code] should be used.
			- In 3D, a [BaseMaterial3D] will need to be created and configured to use the [constant BaseMaterial3D.BLEND_MODE_PREMULT_ALPHA] blend mode on materials that use this texture. In custom [code]spatial[/code] shaders, [code]render_mode blend_premul_alpha;[/code] should be used.
		</member>
		<member name="process/size_limit" type="int" setter="" getter="" default="0">
			If set to a value greater than [code]0[/code], the size of the texture is limited on import to a value smaller than or equal to the value specified here. For non-square textures, the size limit affects the longer dimension, with the shorter dimension scaled to preserve aspect ratio. Resizing is performed using cubic interpolation.
			This can be used to reduce memory usage without affecting the source images, or avoid issues with textures not displaying on mobile/web platforms (as these usually can't display textures larger than 4096×4096).
			[b]Note:[/b] Even if this is set to [code]0[/code], import size is limited to the following dimensions for technical reasons. Depending on [member compress/mode], textures will be downsampled on import if necessary:
			- [b]Lossy:[/b] 16383 pixels width or height, whichever is larger;
			- [b]Basis Universal:[/b] 16384 pixels width or height, whichever is larger;
			- [b]All other modes:[/b] 32768 pixels width or height, whichever is larger.
		</member>
		<member name="roughness/mode" type="int" setter="" getter="" default="0">
			The color channel to consider as a roughness map in this texture. Only effective if [member roughness/src_normal] is not empty.
		</member>
		<member name="roughness/src_normal" type="String" setter="" getter="" default="&quot;&quot;">
			The path to the texture to consider as a normal map for roughness filtering on import. Specifying this can help decrease specular aliasing slightly in 3D.
			Roughness filtering on import is only used in 3D rendering, not 2D.
		</member>
		<member name="svg/scale" type="float" setter="" getter="" default="1.0">
			The scale the SVG should be rendered at, with [code]1.0[/code] being the original design size. Higher values result in a larger image. Note that unlike font oversampling, this affects the size the SVG is rendered at in 2D. See also [member editor/scale_with_editor_scale].
			[b]Note:[/b] Only available for SVG images.
		</member>
	</members>
</class>
