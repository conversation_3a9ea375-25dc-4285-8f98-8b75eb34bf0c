<?xml version="1.0" encoding="UTF-8" ?>
<class name="Rect2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A 2D axis-aligned bounding box using floating-point coordinates.
	</brief_description>
	<description>
		The [Rect2] built-in [Variant] type represents an axis-aligned rectangle in a 2D space. It is defined by its [member position] and [member size], which are [Vector2]. It is frequently used for fast overlap tests (see [method intersects]). Although [Rect2] itself is axis-aligned, it can be combined with [Transform2D] to represent a rotated or skewed rectangle.
		For integer coordinates, use [Rect2i]. The 3D equivalent to [Rect2] is [AABB].
		[b]Note:[/b] Negative values for [member size] are not supported. With negative size, most [Rect2] methods do not work correctly. Use [method abs] to get an equivalent [Rect2] with a non-negative size.
		[b]Note:[/b] In a boolean context, a [Rect2] evaluates to [code]false[/code] if both [member position] and [member size] are zero (equal to [constant Vector2.ZERO]). Otherwise, it always evaluates to [code]true[/code].
	</description>
	<tutorials>
		<link title="Math documentation index">$DOCS_URL/tutorials/math/index.html</link>
		<link title="Vector math">$DOCS_URL/tutorials/math/vector_math.html</link>
		<link title="Advanced vector math">$DOCS_URL/tutorials/math/vectors_advanced.html</link>
	</tutorials>
	<constructors>
		<constructor name="Rect2">
			<return type="Rect2" />
			<description>
				Constructs a [Rect2] with its [member position] and [member size] set to [constant Vector2.ZERO].
			</description>
		</constructor>
		<constructor name="Rect2">
			<return type="Rect2" />
			<param index="0" name="from" type="Rect2" />
			<description>
				Constructs a [Rect2] as a copy of the given [Rect2].
			</description>
		</constructor>
		<constructor name="Rect2">
			<return type="Rect2" />
			<param index="0" name="from" type="Rect2i" />
			<description>
				Constructs a [Rect2] from a [Rect2i].
			</description>
		</constructor>
		<constructor name="Rect2">
			<return type="Rect2" />
			<param index="0" name="position" type="Vector2" />
			<param index="1" name="size" type="Vector2" />
			<description>
				Constructs a [Rect2] by [param position] and [param size].
			</description>
		</constructor>
		<constructor name="Rect2">
			<return type="Rect2" />
			<param index="0" name="x" type="float" />
			<param index="1" name="y" type="float" />
			<param index="2" name="width" type="float" />
			<param index="3" name="height" type="float" />
			<description>
				Constructs a [Rect2] by setting its [member position] to ([param x], [param y]), and its [member size] to ([param width], [param height]).
			</description>
		</constructor>
	</constructors>
	<methods>
		<method name="abs" qualifiers="const">
			<return type="Rect2" />
			<description>
				Returns a [Rect2] equivalent to this rectangle, with its width and height modified to be non-negative values, and with its [member position] being the top-left corner of the rectangle.
				[codeblocks]
				[gdscript]
				var rect = Rect2(25, 25, -100, -50)
				var absolute = rect.abs() # absolute is Rect2(-75, -25, 100, 50)
				[/gdscript]
				[csharp]
				var rect = new Rect2(25, 25, -100, -50);
				var absolute = rect.Abs(); // absolute is Rect2(-75, -25, 100, 50)
				[/csharp]
				[/codeblocks]
				[b]Note:[/b] It's recommended to use this method when [member size] is negative, as most other methods in Redot assume that the [member position] is the top-left corner, and the [member end] is the bottom-right corner.
			</description>
		</method>
		<method name="encloses" qualifiers="const">
			<return type="bool" />
			<param index="0" name="b" type="Rect2" />
			<description>
				Returns [code]true[/code] if this rectangle [i]completely[/i] encloses the [param b] rectangle.
			</description>
		</method>
		<method name="expand" qualifiers="const">
			<return type="Rect2" />
			<param index="0" name="to" type="Vector2" />
			<description>
				Returns a copy of this rectangle expanded to align the edges with the given [param to] point, if necessary.
				[codeblocks]
				[gdscript]
				var rect = Rect2(0, 0, 5, 2)

				rect = rect.expand(Vector2(10, 0)) # rect is Rect2(0, 0, 10, 2)
				rect = rect.expand(Vector2(-5, 5)) # rect is Rect2(-5, 0, 15, 5)
				[/gdscript]
				[csharp]
				var rect = new Rect2(0, 0, 5, 2);

				rect = rect.Expand(new Vector2(10, 0)); // rect is Rect2(0, 0, 10, 2)
				rect = rect.Expand(new Vector2(-5, 5)); // rect is Rect2(-5, 0, 15, 5)
				[/csharp]
				[/codeblocks]
			</description>
		</method>
		<method name="get_area" qualifiers="const">
			<return type="float" />
			<description>
				Returns the rectangle's area. This is equivalent to [code]size.x * size.y[/code]. See also [method has_area].
			</description>
		</method>
		<method name="get_center" qualifiers="const">
			<return type="Vector2" />
			<description>
				Returns the center point of the rectangle. This is the same as [code]position + (size / 2.0)[/code].
			</description>
		</method>
		<method name="get_support" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="direction" type="Vector2" />
			<description>
				Returns the vertex's position of this rect that's the farthest in the given direction. This point is commonly known as the support point in collision detection algorithms.
			</description>
		</method>
		<method name="grow" qualifiers="const">
			<return type="Rect2" />
			<param index="0" name="amount" type="float" />
			<description>
				Returns a copy of this rectangle extended on all sides by the given [param amount]. A negative [param amount] shrinks the rectangle instead. See also [method grow_individual] and [method grow_side].
				[codeblocks]
				[gdscript]
				var a = Rect2(4, 4, 8, 8).grow(4) # a is Rect2(0, 0, 16, 16)
				var b = Rect2(0, 0, 8, 4).grow(2) # b is Rect2(-2, -2, 12, 8)
				[/gdscript]
				[csharp]
				var a = new Rect2(4, 4, 8, 8).Grow(4); // a is Rect2(0, 0, 16, 16)
				var b = new Rect2(0, 0, 8, 4).Grow(2); // b is Rect2(-2, -2, 12, 8)
				[/csharp]
				[/codeblocks]
			</description>
		</method>
		<method name="grow_individual" qualifiers="const">
			<return type="Rect2" />
			<param index="0" name="left" type="float" />
			<param index="1" name="top" type="float" />
			<param index="2" name="right" type="float" />
			<param index="3" name="bottom" type="float" />
			<description>
				Returns a copy of this rectangle with its [param left], [param top], [param right], and [param bottom] sides extended by the given amounts. Negative values shrink the sides, instead. See also [method grow] and [method grow_side].
			</description>
		</method>
		<method name="grow_side" qualifiers="const">
			<return type="Rect2" />
			<param index="0" name="side" type="int" />
			<param index="1" name="amount" type="float" />
			<description>
				Returns a copy of this rectangle with its [param side] extended by the given [param amount] (see [enum Side] constants). A negative [param amount] shrinks the rectangle, instead. See also [method grow] and [method grow_individual].
			</description>
		</method>
		<method name="has_area" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if this rectangle has positive width and height. See also [method get_area].
			</description>
		</method>
		<method name="has_point" qualifiers="const">
			<return type="bool" />
			<param index="0" name="point" type="Vector2" />
			<description>
				Returns [code]true[/code] if the rectangle contains the given [param point]. By convention, points on the right and bottom edges are [b]not[/b] included.
				[b]Note:[/b] This method is not reliable for [Rect2] with a [i]negative[/i] [member size]. Use [method abs] first to get a valid rectangle.
			</description>
		</method>
		<method name="intersection" qualifiers="const">
			<return type="Rect2" />
			<param index="0" name="b" type="Rect2" />
			<description>
				Returns the intersection between this rectangle and [param b]. If the rectangles do not intersect, returns an empty [Rect2].
				[codeblocks]
				[gdscript]
				var rect1 = Rect2(0, 0, 5, 10)
				var rect2 = Rect2(2, 0, 8, 4)

				var a = rect1.intersection(rect2) # a is Rect2(2, 0, 3, 4)
				[/gdscript]
				[csharp]
				var rect1 = new Rect2(0, 0, 5, 10);
				var rect2 = new Rect2(2, 0, 8, 4);

				var a = rect1.Intersection(rect2); // a is Rect2(2, 0, 3, 4)
				[/csharp]
				[/codeblocks]
				[b]Note:[/b] If you only need to know whether two rectangles are overlapping, use [method intersects], instead.
			</description>
		</method>
		<method name="intersects" qualifiers="const">
			<return type="bool" />
			<param index="0" name="b" type="Rect2" />
			<param index="1" name="include_borders" type="bool" default="false" />
			<description>
				Returns [code]true[/code] if this rectangle overlaps with the [param b] rectangle. The edges of both rectangles are excluded, unless [param include_borders] is [code]true[/code].
			</description>
		</method>
		<method name="is_equal_approx" qualifiers="const">
			<return type="bool" />
			<param index="0" name="rect" type="Rect2" />
			<description>
				Returns [code]true[/code] if this rectangle and [param rect] are approximately equal, by calling [method Vector2.is_equal_approx] on the [member position] and the [member size].
			</description>
		</method>
		<method name="is_finite" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if this rectangle's values are finite, by calling [method Vector2.is_finite] on the [member position] and the [member size].
			</description>
		</method>
		<method name="merge" qualifiers="const">
			<return type="Rect2" />
			<param index="0" name="b" type="Rect2" />
			<description>
				Returns a [Rect2] that encloses both this rectangle and [param b] around the edges. See also [method encloses].
			</description>
		</method>
	</methods>
	<members>
		<member name="end" type="Vector2" setter="" getter="" default="Vector2(0, 0)">
			The ending point. This is usually the bottom-right corner of the rectangle, and is equivalent to [code]position + size[/code]. Setting this point affects the [member size].
		</member>
		<member name="position" type="Vector2" setter="" getter="" default="Vector2(0, 0)">
			The origin point. This is usually the top-left corner of the rectangle.
		</member>
		<member name="size" type="Vector2" setter="" getter="" default="Vector2(0, 0)">
			The rectangle's width and height, starting from [member position]. Setting this value also affects the [member end] point.
			[b]Note:[/b] It's recommended setting the width and height to non-negative values, as most methods in Redot assume that the [member position] is the top-left corner, and the [member end] is the bottom-right corner. To get an equivalent rectangle with non-negative size, use [method abs].
		</member>
	</members>
	<operators>
		<operator name="operator !=">
			<return type="bool" />
			<param index="0" name="right" type="Rect2" />
			<description>
				Returns [code]true[/code] if the [member position] or [member size] of both rectangles are not equal.
				[b]Note:[/b] Due to floating-point precision errors, consider using [method is_equal_approx] instead, which is more reliable.
			</description>
		</operator>
		<operator name="operator *">
			<return type="Rect2" />
			<param index="0" name="right" type="Transform2D" />
			<description>
				Inversely transforms (multiplies) the [Rect2] by the given [Transform2D] transformation matrix, under the assumption that the transformation basis is orthonormal (i.e. rotation/reflection is fine, scaling/skew is not).
				[code]rect * transform[/code] is equivalent to [code]transform.inverse() * rect[/code]. See [method Transform2D.inverse].
				For transforming by inverse of an affine transformation (e.g. with scaling) [code]transform.affine_inverse() * rect[/code] can be used instead. See [method Transform2D.affine_inverse].
			</description>
		</operator>
		<operator name="operator ==">
			<return type="bool" />
			<param index="0" name="right" type="Rect2" />
			<description>
				Returns [code]true[/code] if both [member position] and [member size] of the rectangles are exactly equal, respectively.
				[b]Note:[/b] Due to floating-point precision errors, consider using [method is_equal_approx] instead, which is more reliable.
			</description>
		</operator>
	</operators>
</class>
