<?xml version="1.0" encoding="UTF-8" ?>
<class name="Signal" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A built-in type representing a signal of an [Object].
	</brief_description>
	<description>
		[Signal] is a built-in [Variant] type that represents a signal of an [Object] instance. Like all [Variant] types, it can be stored in variables and passed to functions. Signals allow all connected [Callable]s (and by extension their respective objects) to listen and react to events, without directly referencing one another. This keeps the code flexible and easier to manage. You can check whether an [Object] has a given signal name using [method Object.has_signal].
		In GDScript, signals can be declared with the [code]signal[/code] keyword. In C#, you may use the [code][Signal][/code] attribute on a delegate.
		[codeblocks]
		[gdscript]
		signal attacked

		# Additional arguments may be declared.
		# These arguments must be passed when the signal is emitted.
		signal item_dropped(item_name, amount)
		[/gdscript]
		[csharp]
		[Signal]
		delegate void AttackedEventHandler();

		// Additional arguments may be declared.
		// These arguments must be passed when the signal is emitted.
		[Signal]
		delegate void ItemDroppedEventHandler(string itemName, int amount);
		[/csharp]
		[/codeblocks]
		Connecting signals is one of the most common operations in Redot and the API gives many options to do so, which are described further down. The code block below shows the recommended approach.
		[codeblocks]
		[gdscript]
		func _ready():
		    var button = Button.new()
		    # `button_down` here is a Signal Variant type. We therefore call the Signal.connect() method, not Object.connect().
		    # See discussion below for a more in-depth overview of the API.
		    button.button_down.connect(_on_button_down)

		    # This assumes that a `Player` class exists, which defines a `hit` signal.
		    var player = Player.new()
		    # We use Signal.connect() again, and we also use the Callable.bind() method,
		    # which returns a new Callable with the parameter binds.
		    player.hit.connect(_on_player_hit.bind("sword", 100))

		func _on_button_down():
		    print("Button down!")

		func _on_player_hit(weapon_type, damage):
		    print("Hit with weapon %s for %d damage." % [weapon_type, damage])
		[/gdscript]
		[csharp]
		public override void _Ready()
		{
		    var button = new Button();
		    // C# supports passing signals as events, so we can use this idiomatic construct:
		    button.ButtonDown += OnButtonDown;

		    // This assumes that a `Player` class exists, which defines a `Hit` signal.
		    var player = new Player();
		    // We can use lambdas when we need to bind additional parameters.
		    player.Hit += () =&gt; OnPlayerHit("sword", 100);
		}

		private void OnButtonDown()
		{
		    GD.Print("Button down!");
		}

		private void OnPlayerHit(string weaponType, int damage)
		{
		    GD.Print($"Hit with weapon {weaponType} for {damage} damage.");
		}
		[/csharp]
		[/codeblocks]
		[b][code skip-lint]Object.connect()[/code] or [code skip-lint]Signal.connect()[/code]?[/b]
		As seen above, the recommended method to connect signals is not [method Object.connect]. The code block below shows the four options for connecting signals, using either this legacy method or the recommended [method Signal.connect], and using either an implicit [Callable] or a manually defined one.
		[codeblocks]
		[gdscript]
		func _ready():
		    var button = Button.new()
		    # Option 1: Object.connect() with an implicit Callable for the defined function.
		    button.connect("button_down", _on_button_down)
		    # Option 2: Object.connect() with a constructed Callable using a target object and method name.
		    button.connect("button_down", Callable(self, "_on_button_down"))
		    # Option 3: Signal.connect() with an implicit Callable for the defined function.
		    button.button_down.connect(_on_button_down)
		    # Option 4: Signal.connect() with a constructed Callable using a target object and method name.
		    button.button_down.connect(Callable(self, "_on_button_down"))

		func _on_button_down():
		    print("Button down!")
		[/gdscript]
		[csharp]
		public override void _Ready()
		{
		    var button = new Button();
		    // Option 1: In C#, we can use signals as events and connect with this idiomatic syntax:
		    button.ButtonDown += OnButtonDown;
		    // Option 2: GodotObject.Connect() with a constructed Callable from a method group.
		    button.Connect(Button.SignalName.ButtonDown, Callable.From(OnButtonDown));
		    // Option 3: GodotObject.Connect() with a constructed Callable using a target object and method name.
		    button.Connect(Button.SignalName.ButtonDown, new Callable(this, MethodName.OnButtonDown));
		}

		private void OnButtonDown()
		{
		    GD.Print("Button down!");
		}
		[/csharp]
		[/codeblocks]
		While all options have the same outcome ([code]button[/code]'s [signal BaseButton.button_down] signal will be connected to [code]_on_button_down[/code]), [b]option 3[/b] offers the best validation: it will print a compile-time error if either the [code]button_down[/code] [Signal] or the [code]_on_button_down[/code] [Callable] are not defined. On the other hand, [b]option 2[/b] only relies on string names and will only be able to validate either names at runtime: it will generate an error at runtime if [code]"button_down"[/code] is not a signal, or if [code]"_on_button_down"[/code] is not a method in the object [code]self[/code]. The main reason for using options 1, 2, or 4 would be if you actually need to use strings (e.g. to connect signals programmatically based on strings read from a configuration file). Otherwise, option 3 is the recommended (and fastest) method.
		[b]Binding and passing parameters:[/b]
		The syntax to bind parameters is through [method Callable.bind], which returns a copy of the [Callable] with its parameters bound.
		When calling [method emit] or [method Object.emit_signal], the signal parameters can be also passed. The examples below show the relationship between these signal parameters and bound parameters.
		[codeblocks]
		[gdscript]
		func _ready():
		    # This assumes that a `Player` class exists, which defines a `hit` signal.
		    var player = Player.new()
		    # Using Callable.bind().
		    player.hit.connect(_on_player_hit.bind("sword", 100))

		    # Parameters added when emitting the signal are passed first.
		    player.hit.emit("Dark lord", 5)

		# We pass two arguments when emitting (`hit_by`, `level`),
		# and bind two more arguments when connecting (`weapon_type`, `damage`).
		func _on_player_hit(hit_by, level, weapon_type, damage):
		    print("Hit by %s (level %d) with weapon %s for %d damage." % [hit_by, level, weapon_type, damage])
		[/gdscript]
		[csharp]
		public override void _Ready()
		{
		    // This assumes that a `Player` class exists, which defines a `Hit` signal.
		    var player = new Player();
		    // Using lambda expressions that create a closure that captures the additional parameters.
		    // The lambda only receives the parameters defined by the signal's delegate.
		    player.Hit += (hitBy, level) =&gt; OnPlayerHit(hitBy, level, "sword", 100);

		    // Parameters added when emitting the signal are passed first.
		    player.EmitSignal(SignalName.Hit, "Dark lord", 5);
		}

		// We pass two arguments when emitting (`hit_by`, `level`),
		// and bind two more arguments when connecting (`weapon_type`, `damage`).
		private void OnPlayerHit(string hitBy, int level, string weaponType, int damage)
		{
		    GD.Print($"Hit by {hitBy} (level {level}) with weapon {weaponType} for {damage} damage.");
		}
		[/csharp]
		[/codeblocks]
	</description>
	<tutorials>
		<link title="Using Signals">$DOCS_URL/getting_started/step_by_step/signals.html</link>
		<link title="GDScript Basics">$DOCS_URL/tutorials/scripting/gdscript/gdscript_basics.html#signals</link>
	</tutorials>
	<constructors>
		<constructor name="Signal">
			<return type="Signal" />
			<description>
				Constructs an empty [Signal] with no object nor signal name bound.
			</description>
		</constructor>
		<constructor name="Signal">
			<return type="Signal" />
			<param index="0" name="from" type="Signal" />
			<description>
				Constructs a [Signal] as a copy of the given [Signal].
			</description>
		</constructor>
		<constructor name="Signal">
			<return type="Signal" />
			<param index="0" name="object" type="Object" />
			<param index="1" name="signal" type="StringName" />
			<description>
				Creates a [Signal] object referencing a signal named [param signal] in the specified [param object].
			</description>
		</constructor>
	</constructors>
	<methods>
		<method name="connect">
			<return type="int" />
			<param index="0" name="callable" type="Callable" />
			<param index="1" name="flags" type="int" default="0" />
			<description>
				Connects this signal to the specified [param callable]. Optional [param flags] can be also added to configure the connection's behavior (see [enum Object.ConnectFlags] constants). You can provide additional arguments to the connected [param callable] by using [method Callable.bind].
				A signal can only be connected once to the same [Callable]. If the signal is already connected, this method returns [constant ERR_INVALID_PARAMETER] and generates an error, unless the signal is connected with [constant Object.CONNECT_REFERENCE_COUNTED]. To prevent this, use [method is_connected] first to check for existing connections.
				[codeblock]
				for button in $Buttons.get_children():
				    button.pressed.connect(_on_pressed.bind(button))

				func _on_pressed(button):
				    print(button.name, " was pressed")
				[/codeblock]
				[b]Note:[/b] If the [param callable]'s object is freed, the connection will be lost.
			</description>
		</method>
		<method name="disconnect">
			<return type="void" />
			<param index="0" name="callable" type="Callable" />
			<description>
				Disconnects this signal from the specified [Callable]. If the connection does not exist, generates an error. Use [method is_connected] to make sure that the connection exists.
			</description>
		</method>
		<method name="emit" qualifiers="vararg const">
			<return type="void" />
			<description>
				Emits this signal. All [Callable]s connected to this signal will be triggered. This method supports a variable number of arguments, so parameters can be passed as a comma separated list.
			</description>
		</method>
		<method name="get_connections" qualifiers="const">
			<return type="Array" />
			<description>
				Returns an [Array] of connections for this signal. Each connection is represented as a [Dictionary] that contains three entries:
				- [code]signal[/code] is a reference to this signal;
				- [code]callable[/code] is a reference to the connected [Callable];
				- [code]flags[/code] is a combination of [enum Object.ConnectFlags].
			</description>
		</method>
		<method name="get_name" qualifiers="const">
			<return type="StringName" />
			<description>
				Returns the name of this signal.
			</description>
		</method>
		<method name="get_object" qualifiers="const">
			<return type="Object" />
			<description>
				Returns the object emitting this signal.
			</description>
		</method>
		<method name="get_object_id" qualifiers="const">
			<return type="int" />
			<description>
				Returns the ID of the object emitting this signal (see [method Object.get_instance_id]).
			</description>
		</method>
		<method name="has_connections" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if any [Callable] is connected to this signal.
			</description>
		</method>
		<method name="is_connected" qualifiers="const">
			<return type="bool" />
			<param index="0" name="callable" type="Callable" />
			<description>
				Returns [code]true[/code] if the specified [Callable] is connected to this signal.
			</description>
		</method>
		<method name="is_null" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if this [Signal] has no object and the signal name is empty. Equivalent to [code]signal == Signal()[/code].
			</description>
		</method>
	</methods>
	<operators>
		<operator name="operator !=">
			<return type="bool" />
			<param index="0" name="right" type="Signal" />
			<description>
				Returns [code]true[/code] if the signals do not share the same object and name.
			</description>
		</operator>
		<operator name="operator ==">
			<return type="bool" />
			<param index="0" name="right" type="Signal" />
			<description>
				Returns [code]true[/code] if both signals share the same object and name.
			</description>
		</operator>
	</operators>
</class>
