<?xml version="1.0" encoding="UTF-8" ?>
<class name="VisualShaderNodeUIntOp" inherits="VisualShaderNode" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		An unsigned integer scalar operator to be used within the visual shader graph.
	</brief_description>
	<description>
		Applies [member operator] to two unsigned integer inputs: [code]a[/code] and [code]b[/code].
	</description>
	<tutorials>
	</tutorials>
	<members>
		<member name="operator" type="int" setter="set_operator" getter="get_operator" enum="VisualShaderNodeUIntOp.Operator" default="0">
			An operator to be applied to the inputs. See [enum Operator] for options.
		</member>
	</members>
	<constants>
		<constant name="OP_ADD" value="0" enum="Operator">
			Sums two numbers using [code]a + b[/code].
		</constant>
		<constant name="OP_SUB" value="1" enum="Operator">
			Subtracts two numbers using [code]a - b[/code].
		</constant>
		<constant name="OP_MUL" value="2" enum="Operator">
			Multiplies two numbers using [code]a * b[/code].
		</constant>
		<constant name="OP_DIV" value="3" enum="Operator">
			Divides two numbers using [code]a / b[/code].
		</constant>
		<constant name="OP_MOD" value="4" enum="Operator">
			Calculates the remainder of two numbers using [code]a % b[/code].
		</constant>
		<constant name="OP_MAX" value="5" enum="Operator">
			Returns the greater of two numbers. Translates to [code]max(a, b)[/code] in the Godot Shader Language.
		</constant>
		<constant name="OP_MIN" value="6" enum="Operator">
			Returns the lesser of two numbers. Translates to [code]max(a, b)[/code] in the Godot Shader Language.
		</constant>
		<constant name="OP_BITWISE_AND" value="7" enum="Operator">
			Returns the result of bitwise [code]AND[/code] operation on the integer. Translates to [code]a &amp; b[/code] in the Godot Shader Language.
		</constant>
		<constant name="OP_BITWISE_OR" value="8" enum="Operator">
			Returns the result of bitwise [code]OR[/code] operation for two integers. Translates to [code]a | b[/code] in the Godot Shader Language.
		</constant>
		<constant name="OP_BITWISE_XOR" value="9" enum="Operator">
			Returns the result of bitwise [code]XOR[/code] operation for two integers. Translates to [code]a ^ b[/code] in the Godot Shader Language.
		</constant>
		<constant name="OP_BITWISE_LEFT_SHIFT" value="10" enum="Operator">
			Returns the result of bitwise left shift operation on the integer. Translates to [code]a &lt;&lt; b[/code] in the Godot Shader Language.
		</constant>
		<constant name="OP_BITWISE_RIGHT_SHIFT" value="11" enum="Operator">
			Returns the result of bitwise right shift operation on the integer. Translates to [code]a &gt;&gt; b[/code] in the Godot Shader Language.
		</constant>
		<constant name="OP_ENUM_SIZE" value="12" enum="Operator">
			Represents the size of the [enum Operator] enum.
		</constant>
	</constants>
</class>
