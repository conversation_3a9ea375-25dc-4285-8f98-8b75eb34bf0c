<?xml version="1.0" encoding="UTF-8" ?>
<class name="Tree" inherits="Control" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A control used to show a set of internal [TreeItem]s in a hierarchical structure.
	</brief_description>
	<description>
		A control used to show a set of internal [TreeItem]s in a hierarchical structure. The tree items can be selected, expanded and collapsed. The tree can have multiple columns with custom controls like [LineEdit]s, buttons and popups. It can be useful for structured displays and interactions.
		Trees are built via code, using [TreeItem] objects to create the structure. They have a single root, but multiple roots can be simulated with [member hide_root]:
		[codeblocks]
		[gdscript]
		func _ready():
		    var tree = Tree.new()
		    var root = tree.create_item()
		    tree.hide_root = true
		    var child1 = tree.create_item(root)
		    var child2 = tree.create_item(root)
		    var subchild1 = tree.create_item(child1)
		    subchild1.set_text(0, "Subchild1")
		[/gdscript]
		[csharp]
		public override void _Ready()
		{
		    var tree = new Tree();
		    TreeItem root = tree.CreateItem();
		    tree.HideRoot = true;
		    TreeItem child1 = tree.CreateItem(root);
		    TreeItem child2 = tree.CreateItem(root);
		    TreeItem subchild1 = tree.CreateItem(child1);
		    subchild1.SetText(0, "Subchild1");
		}
		[/csharp]
		[/codeblocks]
		To iterate over all the [TreeItem] objects in a [Tree] object, use [method TreeItem.get_next] and [method TreeItem.get_first_child] after getting the root through [method get_root]. You can use [method Object.free] on a [TreeItem] to remove it from the [Tree].
		[b]Incremental search:[/b] Like [ItemList] and [PopupMenu], [Tree] supports searching within the list while the control is focused. Press a key that matches the first letter of an item's name to select the first item starting with the given letter. After that point, there are two ways to perform incremental search: 1) Press the same key again before the timeout duration to select the next item starting with the same letter. 2) Press letter keys that match the rest of the word before the timeout duration to match to select the item in question directly. Both of these actions will be reset to the beginning of the list if the timeout duration has passed since the last keystroke was registered. You can adjust the timeout duration by changing [member ProjectSettings.gui/timers/incremental_search_max_interval_msec].
	</description>
	<tutorials>
	</tutorials>
	<methods>
		<method name="clear">
			<return type="void" />
			<description>
				Clears the tree. This removes all items.
			</description>
		</method>
		<method name="create_item">
			<return type="TreeItem" />
			<param index="0" name="parent" type="TreeItem" default="null" />
			<param index="1" name="index" type="int" default="-1" />
			<description>
				Creates an item in the tree and adds it as a child of [param parent], which can be either a valid [TreeItem] or [code]null[/code].
				If [param parent] is [code]null[/code], the root item will be the parent, or the new item will be the root itself if the tree is empty.
				The new item will be the [param index]-th child of parent, or it will be the last child if there are not enough siblings.
			</description>
		</method>
		<method name="deselect_all">
			<return type="void" />
			<description>
				Deselects all tree items (rows and columns). In [constant SELECT_MULTI] mode also removes selection cursor.
			</description>
		</method>
		<method name="edit_selected">
			<return type="bool" />
			<param index="0" name="force_edit" type="bool" default="false" />
			<description>
				Edits the selected tree item as if it was clicked.
				Either the item must be set editable with [method TreeItem.set_editable] or [param force_edit] must be [code]true[/code].
				Returns [code]true[/code] if the item could be edited. Fails if no item is selected.
			</description>
		</method>
		<method name="ensure_cursor_is_visible">
			<return type="void" />
			<description>
				Makes the currently focused cell visible.
				This will scroll the tree if necessary. In [constant SELECT_ROW] mode, this will not do horizontal scrolling, as all the cells in the selected row is focused logically.
				[b]Note:[/b] Despite the name of this method, the focus cursor itself is only visible in [constant SELECT_MULTI] mode.
			</description>
		</method>
		<method name="get_button_id_at_position" qualifiers="const">
			<return type="int" />
			<param index="0" name="position" type="Vector2" />
			<description>
				Returns the button ID at [param position], or -1 if no button is there.
			</description>
		</method>
		<method name="get_column_at_position" qualifiers="const">
			<return type="int" />
			<param index="0" name="position" type="Vector2" />
			<description>
				Returns the column index at [param position], or -1 if no item is there.
			</description>
		</method>
		<method name="get_column_expand_ratio" qualifiers="const">
			<return type="int" />
			<param index="0" name="column" type="int" />
			<description>
				Returns the expand ratio assigned to the column.
			</description>
		</method>
		<method name="get_column_title" qualifiers="const">
			<return type="String" />
			<param index="0" name="column" type="int" />
			<description>
				Returns the column's title.
			</description>
		</method>
		<method name="get_column_title_alignment" qualifiers="const">
			<return type="int" enum="HorizontalAlignment" />
			<param index="0" name="column" type="int" />
			<description>
				Returns the column title alignment.
			</description>
		</method>
		<method name="get_column_title_direction" qualifiers="const">
			<return type="int" enum="Control.TextDirection" />
			<param index="0" name="column" type="int" />
			<description>
				Returns column title base writing direction.
			</description>
		</method>
		<method name="get_column_title_language" qualifiers="const">
			<return type="String" />
			<param index="0" name="column" type="int" />
			<description>
				Returns column title language code.
			</description>
		</method>
		<method name="get_column_width" qualifiers="const">
			<return type="int" />
			<param index="0" name="column" type="int" />
			<description>
				Returns the column's width in pixels.
			</description>
		</method>
		<method name="get_custom_popup_rect" qualifiers="const">
			<return type="Rect2" />
			<description>
				Returns the rectangle for custom popups. Helper to create custom cell controls that display a popup. See [method TreeItem.set_cell_mode].
			</description>
		</method>
		<method name="get_drop_section_at_position" qualifiers="const">
			<return type="int" />
			<param index="0" name="position" type="Vector2" />
			<description>
				Returns the drop section at [param position], or -100 if no item is there.
				Values -1, 0, or 1 will be returned for the "above item", "on item", and "below item" drop sections, respectively. See [enum DropModeFlags] for a description of each drop section.
				To get the item which the returned drop section is relative to, use [method get_item_at_position].
			</description>
		</method>
		<method name="get_edited" qualifiers="const">
			<return type="TreeItem" />
			<description>
				Returns the currently edited item. Can be used with [signal item_edited] to get the item that was modified.
				[codeblocks]
				[gdscript]
				func _ready():
				    $Tree.item_edited.connect(on_Tree_item_edited)

				func on_Tree_item_edited():
				    print($Tree.get_edited()) # This item just got edited (e.g. checked).
				[/gdscript]
				[csharp]
				public override void _Ready()
				{
				    GetNode&lt;Tree&gt;("Tree").ItemEdited += OnTreeItemEdited;
				}

				public void OnTreeItemEdited()
				{
				    GD.Print(GetNode&lt;Tree&gt;("Tree").GetEdited()); // This item just got edited (e.g. checked).
				}
				[/csharp]
				[/codeblocks]
			</description>
		</method>
		<method name="get_edited_column" qualifiers="const">
			<return type="int" />
			<description>
				Returns the column for the currently edited item.
			</description>
		</method>
		<method name="get_item_area_rect" qualifiers="const">
			<return type="Rect2" />
			<param index="0" name="item" type="TreeItem" />
			<param index="1" name="column" type="int" default="-1" />
			<param index="2" name="button_index" type="int" default="-1" />
			<description>
				Returns the rectangle area for the specified [TreeItem]. If [param column] is specified, only get the position and size of that column, otherwise get the rectangle containing all columns. If a button index is specified, the rectangle of that button will be returned.
			</description>
		</method>
		<method name="get_item_at_position" qualifiers="const">
			<return type="TreeItem" />
			<param index="0" name="position" type="Vector2" />
			<description>
				Returns the tree item at the specified position (relative to the tree origin position).
			</description>
		</method>
		<method name="get_next_selected">
			<return type="TreeItem" />
			<param index="0" name="from" type="TreeItem" />
			<description>
				Returns the next selected [TreeItem] after the given one, or [code]null[/code] if the end is reached.
				If [param from] is [code]null[/code], this returns the first selected item.
			</description>
		</method>
		<method name="get_pressed_button" qualifiers="const">
			<return type="int" />
			<description>
				Returns the last pressed button's index.
			</description>
		</method>
		<method name="get_root" qualifiers="const">
			<return type="TreeItem" />
			<description>
				Returns the tree's root item, or [code]null[/code] if the tree is empty.
			</description>
		</method>
		<method name="get_scroll" qualifiers="const">
			<return type="Vector2" />
			<description>
				Returns the current scrolling position.
			</description>
		</method>
		<method name="get_selected" qualifiers="const">
			<return type="TreeItem" />
			<description>
				Returns the currently focused item, or [code]null[/code] if no item is focused.
				In [constant SELECT_ROW] and [constant SELECT_SINGLE] modes, the focused item is same as the selected item. In [constant SELECT_MULTI] mode, the focused item is the item under the focus cursor, not necessarily selected.
				To get the currently selected item(s), use [method get_next_selected].
			</description>
		</method>
		<method name="get_selected_column" qualifiers="const">
			<return type="int" />
			<description>
				Returns the currently focused column, or -1 if no column is focused.
				In [constant SELECT_SINGLE] mode, the focused column is the selected column. In [constant SELECT_ROW] mode, the focused column is always 0 if any item is selected. In [constant SELECT_MULTI] mode, the focused column is the column under the focus cursor, and there are not necessarily any column selected.
				To tell whether a column of an item is selected, use [method TreeItem.is_selected].
			</description>
		</method>
		<method name="is_column_clipping_content" qualifiers="const">
			<return type="bool" />
			<param index="0" name="column" type="int" />
			<description>
				Returns [code]true[/code] if the column has enabled clipping (see [method set_column_clip_content]).
			</description>
		</method>
		<method name="is_column_expanding" qualifiers="const">
			<return type="bool" />
			<param index="0" name="column" type="int" />
			<description>
				Returns [code]true[/code] if the column has enabled expanding (see [method set_column_expand]).
			</description>
		</method>
		<method name="scroll_to_item">
			<return type="void" />
			<param index="0" name="item" type="TreeItem" />
			<param index="1" name="center_on_item" type="bool" default="false" />
			<description>
				Causes the [Tree] to jump to the specified [TreeItem].
			</description>
		</method>
		<method name="set_column_clip_content">
			<return type="void" />
			<param index="0" name="column" type="int" />
			<param index="1" name="enable" type="bool" />
			<description>
				Allows to enable clipping for column's content, making the content size ignored.
			</description>
		</method>
		<method name="set_column_custom_minimum_width">
			<return type="void" />
			<param index="0" name="column" type="int" />
			<param index="1" name="min_width" type="int" />
			<description>
				Overrides the calculated minimum width of a column. It can be set to [code]0[/code] to restore the default behavior. Columns that have the "Expand" flag will use their "min_width" in a similar fashion to [member Control.size_flags_stretch_ratio].
			</description>
		</method>
		<method name="set_column_expand">
			<return type="void" />
			<param index="0" name="column" type="int" />
			<param index="1" name="expand" type="bool" />
			<description>
				If [code]true[/code], the column will have the "Expand" flag of [Control]. Columns that have the "Expand" flag will use their expand ratio in a similar fashion to [member Control.size_flags_stretch_ratio] (see [method set_column_expand_ratio]).
			</description>
		</method>
		<method name="set_column_expand_ratio">
			<return type="void" />
			<param index="0" name="column" type="int" />
			<param index="1" name="ratio" type="int" />
			<description>
				Sets the relative expand ratio for a column. See [method set_column_expand].
			</description>
		</method>
		<method name="set_column_title">
			<return type="void" />
			<param index="0" name="column" type="int" />
			<param index="1" name="title" type="String" />
			<description>
				Sets the title of a column.
			</description>
		</method>
		<method name="set_column_title_alignment">
			<return type="void" />
			<param index="0" name="column" type="int" />
			<param index="1" name="title_alignment" type="int" enum="HorizontalAlignment" />
			<description>
				Sets the column title alignment. Note that [constant @GlobalScope.HORIZONTAL_ALIGNMENT_FILL] is not supported for column titles.
			</description>
		</method>
		<method name="set_column_title_direction">
			<return type="void" />
			<param index="0" name="column" type="int" />
			<param index="1" name="direction" type="int" enum="Control.TextDirection" />
			<description>
				Sets column title base writing direction.
			</description>
		</method>
		<method name="set_column_title_language">
			<return type="void" />
			<param index="0" name="column" type="int" />
			<param index="1" name="language" type="String" />
			<description>
				Sets language code of column title used for line-breaking and text shaping algorithms, if left empty current locale is used instead.
			</description>
		</method>
		<method name="set_selected">
			<return type="void" />
			<param index="0" name="item" type="TreeItem" />
			<param index="1" name="column" type="int" />
			<description>
				Selects the specified [TreeItem] and column.
			</description>
		</method>
	</methods>
	<members>
		<member name="allow_reselect" type="bool" setter="set_allow_reselect" getter="get_allow_reselect" default="false">
			If [code]true[/code], the currently selected cell may be selected again.
		</member>
		<member name="allow_rmb_select" type="bool" setter="set_allow_rmb_select" getter="get_allow_rmb_select" default="false">
			If [code]true[/code], a right mouse button click can select items.
		</member>
		<member name="allow_search" type="bool" setter="set_allow_search" getter="get_allow_search" default="true">
			If [code]true[/code], allows navigating the [Tree] with letter keys through incremental search.
		</member>
		<member name="auto_tooltip" type="bool" setter="set_auto_tooltip" getter="is_auto_tooltip_enabled" default="true">
			If [code]true[/code], tree items with no tooltip assigned display their text as their tooltip. See also [method TreeItem.get_tooltip_text] and [method TreeItem.get_button_tooltip_text].
		</member>
		<member name="clip_contents" type="bool" setter="set_clip_contents" getter="is_clipping_contents" overrides="Control" default="true" />
		<member name="column_titles_visible" type="bool" setter="set_column_titles_visible" getter="are_column_titles_visible" default="false">
			If [code]true[/code], column titles are visible.
		</member>
		<member name="columns" type="int" setter="set_columns" getter="get_columns" default="1">
			The number of columns.
		</member>
		<member name="drop_mode_flags" type="int" setter="set_drop_mode_flags" getter="get_drop_mode_flags" default="0">
			The drop mode as an OR combination of flags. See [enum DropModeFlags] constants. Once dropping is done, reverts to [constant DROP_MODE_DISABLED]. Setting this during [method Control._can_drop_data] is recommended.
			This controls the drop sections, i.e. the decision and drawing of possible drop locations based on the mouse position.
		</member>
		<member name="enable_recursive_folding" type="bool" setter="set_enable_recursive_folding" getter="is_recursive_folding_enabled" default="true">
			If [code]true[/code], recursive folding is enabled for this [Tree]. Holding down [kbd]Shift[/kbd] while clicking the fold arrow or using [code]ui_right[/code]/[code]ui_left[/code] shortcuts collapses or uncollapses the [TreeItem] and all its descendants.
		</member>
		<member name="focus_mode" type="int" setter="set_focus_mode" getter="get_focus_mode" overrides="Control" enum="Control.FocusMode" default="2" />
		<member name="hide_folding" type="bool" setter="set_hide_folding" getter="is_folding_hidden" default="false">
			If [code]true[/code], the folding arrow is hidden.
		</member>
		<member name="hide_root" type="bool" setter="set_hide_root" getter="is_root_hidden" default="false">
			If [code]true[/code], the tree's root is hidden.
		</member>
		<member name="scroll_horizontal_enabled" type="bool" setter="set_h_scroll_enabled" getter="is_h_scroll_enabled" default="true">
			If [code]true[/code], enables horizontal scrolling.
		</member>
		<member name="scroll_vertical_enabled" type="bool" setter="set_v_scroll_enabled" getter="is_v_scroll_enabled" default="true">
			If [code]true[/code], enables vertical scrolling.
		</member>
		<member name="select_mode" type="int" setter="set_select_mode" getter="get_select_mode" enum="Tree.SelectMode" default="0">
			Allows single or multiple selection. See the [enum SelectMode] constants.
		</member>
	</members>
	<signals>
		<signal name="button_clicked">
			<param index="0" name="item" type="TreeItem" />
			<param index="1" name="column" type="int" />
			<param index="2" name="id" type="int" />
			<param index="3" name="mouse_button_index" type="int" />
			<description>
				Emitted when a button on the tree was pressed (see [method TreeItem.add_button]).
			</description>
		</signal>
		<signal name="cell_selected">
			<description>
				Emitted when a cell is selected.
			</description>
		</signal>
		<signal name="check_propagated_to_item">
			<param index="0" name="item" type="TreeItem" />
			<param index="1" name="column" type="int" />
			<description>
				Emitted when [method TreeItem.propagate_check] is called. Connect to this signal to process the items that are affected when [method TreeItem.propagate_check] is invoked. The order that the items affected will be processed is as follows: the item that invoked the method, children of that item, and finally parents of that item.
			</description>
		</signal>
		<signal name="column_title_clicked">
			<param index="0" name="column" type="int" />
			<param index="1" name="mouse_button_index" type="int" />
			<description>
				Emitted when a column's title is clicked with either [constant MOUSE_BUTTON_LEFT] or [constant MOUSE_BUTTON_RIGHT].
			</description>
		</signal>
		<signal name="custom_item_clicked">
			<param index="0" name="mouse_button_index" type="int" />
			<description>
				Emitted when an item with [constant TreeItem.CELL_MODE_CUSTOM] is clicked with a mouse button.
			</description>
		</signal>
		<signal name="custom_popup_edited">
			<param index="0" name="arrow_clicked" type="bool" />
			<description>
				Emitted when a cell with the [constant TreeItem.CELL_MODE_CUSTOM] is clicked to be edited.
			</description>
		</signal>
		<signal name="empty_clicked">
			<param index="0" name="click_position" type="Vector2" />
			<param index="1" name="mouse_button_index" type="int" />
			<description>
				Emitted when a mouse button is clicked in the empty space of the tree.
			</description>
		</signal>
		<signal name="item_activated">
			<description>
				Emitted when an item is double-clicked, or selected with a [code]ui_accept[/code] input event (e.g. using [kbd]Enter[/kbd] or [kbd]Space[/kbd] on the keyboard).
			</description>
		</signal>
		<signal name="item_collapsed">
			<param index="0" name="item" type="TreeItem" />
			<description>
				Emitted when an item is collapsed by a click on the folding arrow.
			</description>
		</signal>
		<signal name="item_edited">
			<description>
				Emitted when an item is edited.
			</description>
		</signal>
		<signal name="item_icon_double_clicked">
			<description>
				Emitted when an item's icon is double-clicked. For a signal that emits when any part of the item is double-clicked, see [signal item_activated].
			</description>
		</signal>
		<signal name="item_mouse_selected">
			<param index="0" name="mouse_position" type="Vector2" />
			<param index="1" name="mouse_button_index" type="int" />
			<description>
				Emitted when an item is selected with a mouse button.
			</description>
		</signal>
		<signal name="item_selected">
			<description>
				Emitted when an item is selected.
			</description>
		</signal>
		<signal name="multi_selected">
			<param index="0" name="item" type="TreeItem" />
			<param index="1" name="column" type="int" />
			<param index="2" name="selected" type="bool" />
			<description>
				Emitted instead of [signal item_selected] if [member select_mode] is set to [constant SELECT_MULTI].
			</description>
		</signal>
		<signal name="nothing_selected">
			<description>
				Emitted when a left mouse button click does not select any item.
			</description>
		</signal>
	</signals>
	<constants>
		<constant name="SELECT_SINGLE" value="0" enum="SelectMode">
			Allows selection of a single cell at a time. From the perspective of items, only a single item is allowed to be selected. And there is only one column selected in the selected item.
			The focus cursor is always hidden in this mode, but it is positioned at the current selection, making the currently selected item the currently focused item.
		</constant>
		<constant name="SELECT_ROW" value="1" enum="SelectMode">
			Allows selection of a single row at a time. From the perspective of items, only a single items is allowed to be selected. And all the columns are selected in the selected item.
			The focus cursor is always hidden in this mode, but it is positioned at the first column of the current selection, making the currently selected item the currently focused item.
		</constant>
		<constant name="SELECT_MULTI" value="2" enum="SelectMode">
			Allows selection of multiple cells at the same time. From the perspective of items, multiple items are allowed to be selected. And there can be multiple columns selected in each selected item.
			The focus cursor is visible in this mode, the item or column under the cursor is not necessarily selected.
		</constant>
		<constant name="DROP_MODE_DISABLED" value="0" enum="DropModeFlags">
			Disables all drop sections, but still allows to detect the "on item" drop section by [method get_drop_section_at_position].
			[b]Note:[/b] This is the default flag, it has no effect when combined with other flags.
		</constant>
		<constant name="DROP_MODE_ON_ITEM" value="1" enum="DropModeFlags">
			Enables the "on item" drop section. This drop section covers the entire item.
			When combined with [constant DROP_MODE_INBETWEEN], this drop section halves the height and stays centered vertically.
		</constant>
		<constant name="DROP_MODE_INBETWEEN" value="2" enum="DropModeFlags">
			Enables "above item" and "below item" drop sections. The "above item" drop section covers the top half of the item, and the "below item" drop section covers the bottom half.
			When combined with [constant DROP_MODE_ON_ITEM], these drop sections halves the height and stays on top / bottom accordingly.
		</constant>
	</constants>
	<theme_items>
		<theme_item name="children_hl_line_color" data_type="color" type="Color" default="Color(0.27, 0.27, 0.27, 1)">
			The [Color] of the relationship lines between the selected [TreeItem] and its children.
		</theme_item>
		<theme_item name="custom_button_font_highlight" data_type="color" type="Color" default="Color(0.95, 0.95, 0.95, 1)">
			Text [Color] for a [constant TreeItem.CELL_MODE_CUSTOM] mode cell when it's hovered.
		</theme_item>
		<theme_item name="drop_position_color" data_type="color" type="Color" default="Color(1, 1, 1, 1)">
			[Color] used to draw possible drop locations. See [enum DropModeFlags] constants for further description of drop locations.
		</theme_item>
		<theme_item name="font_color" data_type="color" type="Color" default="Color(0.7, 0.7, 0.7, 1)">
			Default text [Color] of the item.
		</theme_item>
		<theme_item name="font_disabled_color" data_type="color" type="Color" default="Color(0.875, 0.875, 0.875, 0.5)">
			Text [Color] for a [constant TreeItem.CELL_MODE_CHECK] mode cell when it's non-editable (see [method TreeItem.set_editable]).
		</theme_item>
		<theme_item name="font_hovered_color" data_type="color" type="Color" default="Color(0.95, 0.95, 0.95, 1)">
			Text [Color] used when the item is hovered and not selected yet.
		</theme_item>
		<theme_item name="font_hovered_dimmed_color" data_type="color" type="Color" default="Color(0.875, 0.875, 0.875, 1)">
			Text [Color] used when the item is hovered, while a button of the same item is hovered as the same time.
		</theme_item>
		<theme_item name="font_hovered_selected_color" data_type="color" type="Color" default="Color(1, 1, 1, 1)">
			Text [Color] used when the item is hovered and selected.
		</theme_item>
		<theme_item name="font_outline_color" data_type="color" type="Color" default="Color(0, 0, 0, 1)">
			The tint of text outline of the item.
		</theme_item>
		<theme_item name="font_selected_color" data_type="color" type="Color" default="Color(1, 1, 1, 1)">
			Text [Color] used when the item is selected.
		</theme_item>
		<theme_item name="guide_color" data_type="color" type="Color" default="Color(0.7, 0.7, 0.7, 0.25)">
			[Color] of the guideline.
		</theme_item>
		<theme_item name="parent_hl_line_color" data_type="color" type="Color" default="Color(0.27, 0.27, 0.27, 1)">
			The [Color] of the relationship lines between the selected [TreeItem] and its parents.
		</theme_item>
		<theme_item name="relationship_line_color" data_type="color" type="Color" default="Color(0.27, 0.27, 0.27, 1)">
			The default [Color] of the relationship lines.
		</theme_item>
		<theme_item name="title_button_color" data_type="color" type="Color" default="Color(0.875, 0.875, 0.875, 1)">
			Default text [Color] of the title button.
		</theme_item>
		<theme_item name="button_margin" data_type="constant" type="int" default="4">
			The horizontal space between each button in a cell.
		</theme_item>
		<theme_item name="children_hl_line_width" data_type="constant" type="int" default="1">
			The width of the relationship lines between the selected [TreeItem] and its children.
		</theme_item>
		<theme_item name="draw_guides" data_type="constant" type="int" default="1">
			Draws the guidelines if not zero, this acts as a boolean. The guideline is a horizontal line drawn at the bottom of each item.
		</theme_item>
		<theme_item name="draw_relationship_lines" data_type="constant" type="int" default="0">
			Draws the relationship lines if not zero, this acts as a boolean. Relationship lines are drawn at the start of child items to show hierarchy.
		</theme_item>
		<theme_item name="h_separation" data_type="constant" type="int" default="4">
			The horizontal space between item cells. This is also used as the margin at the start of an item when folding is disabled.
		</theme_item>
		<theme_item name="icon_max_width" data_type="constant" type="int" default="0">
			The maximum allowed width of the icon in item's cells. This limit is applied on top of the default size of the icon, but before the value set with [method TreeItem.set_icon_max_width]. The height is adjusted according to the icon's ratio.
		</theme_item>
		<theme_item name="inner_item_margin_bottom" data_type="constant" type="int" default="0">
			The inner bottom margin of a cell.
		</theme_item>
		<theme_item name="inner_item_margin_left" data_type="constant" type="int" default="0">
			The inner left margin of a cell.
		</theme_item>
		<theme_item name="inner_item_margin_right" data_type="constant" type="int" default="0">
			The inner right margin of a cell.
		</theme_item>
		<theme_item name="inner_item_margin_top" data_type="constant" type="int" default="0">
			The inner top margin of a cell.
		</theme_item>
		<theme_item name="item_margin" data_type="constant" type="int" default="16">
			The horizontal margin at the start of an item. This is used when folding is enabled for the item.
		</theme_item>
		<theme_item name="outline_size" data_type="constant" type="int" default="0">
			The size of the text outline.
			[b]Note:[/b] If using a font with [member FontFile.multichannel_signed_distance_field] enabled, its [member FontFile.msdf_pixel_range] must be set to at least [i]twice[/i] the value of [theme_item outline_size] for outline rendering to look correct. Otherwise, the outline may appear to be cut off earlier than intended.
		</theme_item>
		<theme_item name="parent_hl_line_margin" data_type="constant" type="int" default="0">
			The space between the parent relationship lines for the selected [TreeItem] and the relationship lines to its siblings that are not selected.
		</theme_item>
		<theme_item name="parent_hl_line_width" data_type="constant" type="int" default="1">
			The width of the relationship lines between the selected [TreeItem] and its parents.
		</theme_item>
		<theme_item name="relationship_line_width" data_type="constant" type="int" default="1">
			The default width of the relationship lines.
		</theme_item>
		<theme_item name="scroll_border" data_type="constant" type="int" default="4">
			The maximum distance between the mouse cursor and the control's border to trigger border scrolling when dragging.
		</theme_item>
		<theme_item name="scroll_speed" data_type="constant" type="int" default="12">
			The speed of border scrolling.
		</theme_item>
		<theme_item name="scrollbar_h_separation" data_type="constant" type="int" default="4">
			The horizontal separation of tree content and scrollbar.
		</theme_item>
		<theme_item name="scrollbar_margin_bottom" data_type="constant" type="int" default="-1">
			The bottom margin of the scrollbars. When negative, uses [theme_item panel] bottom margin.
		</theme_item>
		<theme_item name="scrollbar_margin_left" data_type="constant" type="int" default="-1">
			The left margin of the horizontal scrollbar. When negative, uses [theme_item panel] left margin.
		</theme_item>
		<theme_item name="scrollbar_margin_right" data_type="constant" type="int" default="-1">
			The right margin of the scrollbars. When negative, uses [theme_item panel] right margin.
		</theme_item>
		<theme_item name="scrollbar_margin_top" data_type="constant" type="int" default="-1">
			The top margin of the vertical scrollbar. When negative, uses [theme_item panel] top margin.
		</theme_item>
		<theme_item name="scrollbar_v_separation" data_type="constant" type="int" default="4">
			The vertical separation of tree content and scrollbar.
		</theme_item>
		<theme_item name="v_separation" data_type="constant" type="int" default="4">
			The vertical padding inside each item, i.e. the distance between the item's content and top/bottom border.
		</theme_item>
		<theme_item name="font" data_type="font" type="Font">
			[Font] of the item's text.
		</theme_item>
		<theme_item name="title_button_font" data_type="font" type="Font">
			[Font] of the title button's text.
		</theme_item>
		<theme_item name="font_size" data_type="font_size" type="int">
			Font size of the item's text.
		</theme_item>
		<theme_item name="title_button_font_size" data_type="font_size" type="int">
			Font size of the title button's text.
		</theme_item>
		<theme_item name="arrow" data_type="icon" type="Texture2D">
			The arrow icon used when a foldable item is not collapsed.
		</theme_item>
		<theme_item name="arrow_collapsed" data_type="icon" type="Texture2D">
			The arrow icon used when a foldable item is collapsed (for left-to-right layouts).
		</theme_item>
		<theme_item name="arrow_collapsed_mirrored" data_type="icon" type="Texture2D">
			The arrow icon used when a foldable item is collapsed (for right-to-left layouts).
		</theme_item>
		<theme_item name="checked" data_type="icon" type="Texture2D">
			The check icon to display when the [constant TreeItem.CELL_MODE_CHECK] mode cell is checked and editable (see [method TreeItem.set_editable]).
		</theme_item>
		<theme_item name="checked_disabled" data_type="icon" type="Texture2D">
			The check icon to display when the [constant TreeItem.CELL_MODE_CHECK] mode cell is checked and non-editable (see [method TreeItem.set_editable]).
		</theme_item>
		<theme_item name="indeterminate" data_type="icon" type="Texture2D">
			The check icon to display when the [constant TreeItem.CELL_MODE_CHECK] mode cell is indeterminate and editable (see [method TreeItem.set_editable]).
		</theme_item>
		<theme_item name="indeterminate_disabled" data_type="icon" type="Texture2D">
			The check icon to display when the [constant TreeItem.CELL_MODE_CHECK] mode cell is indeterminate and non-editable (see [method TreeItem.set_editable]).
		</theme_item>
		<theme_item name="select_arrow" data_type="icon" type="Texture2D">
			The arrow icon to display for the [constant TreeItem.CELL_MODE_RANGE] mode cell.
		</theme_item>
		<theme_item name="unchecked" data_type="icon" type="Texture2D">
			The check icon to display when the [constant TreeItem.CELL_MODE_CHECK] mode cell is unchecked and editable (see [method TreeItem.set_editable]).
		</theme_item>
		<theme_item name="unchecked_disabled" data_type="icon" type="Texture2D">
			The check icon to display when the [constant TreeItem.CELL_MODE_CHECK] mode cell is unchecked and non-editable (see [method TreeItem.set_editable]).
		</theme_item>
		<theme_item name="updown" data_type="icon" type="Texture2D">
			The updown arrow icon to display for the [constant TreeItem.CELL_MODE_RANGE] mode cell.
		</theme_item>
		<theme_item name="button_hover" data_type="style" type="StyleBox">
			[StyleBox] used when a button in the tree is hovered.
		</theme_item>
		<theme_item name="button_pressed" data_type="style" type="StyleBox">
			[StyleBox] used when a button in the tree is pressed.
		</theme_item>
		<theme_item name="cursor" data_type="style" type="StyleBox">
			[StyleBox] used for the cursor, when the [Tree] is being focused.
		</theme_item>
		<theme_item name="cursor_unfocused" data_type="style" type="StyleBox">
			[StyleBox] used for the cursor, when the [Tree] is not being focused.
		</theme_item>
		<theme_item name="custom_button" data_type="style" type="StyleBox">
			Default [StyleBox] for a [constant TreeItem.CELL_MODE_CUSTOM] mode cell when button is enabled with [method TreeItem.set_custom_as_button].
		</theme_item>
		<theme_item name="custom_button_hover" data_type="style" type="StyleBox">
			[StyleBox] for a [constant TreeItem.CELL_MODE_CUSTOM] mode button cell when it's hovered.
		</theme_item>
		<theme_item name="custom_button_pressed" data_type="style" type="StyleBox">
			[StyleBox] for a [constant TreeItem.CELL_MODE_CUSTOM] mode button cell when it's pressed.
		</theme_item>
		<theme_item name="focus" data_type="style" type="StyleBox">
			The focused style for the [Tree], drawn on top of everything.
		</theme_item>
		<theme_item name="hovered" data_type="style" type="StyleBox">
			[StyleBox] for the item being hovered, but not selected.
		</theme_item>
		<theme_item name="hovered_dimmed" data_type="style" type="StyleBox">
			[StyleBox] for the item being hovered, while a button of the same item is hovered as the same time.
		</theme_item>
		<theme_item name="hovered_selected" data_type="style" type="StyleBox">
			[StyleBox] for the hovered and selected items, used when the [Tree] is not being focused.
		</theme_item>
		<theme_item name="hovered_selected_focus" data_type="style" type="StyleBox">
			[StyleBox] for the hovered and selected items, used when the [Tree] is being focused.
		</theme_item>
		<theme_item name="panel" data_type="style" type="StyleBox">
			The background style for the [Tree].
		</theme_item>
		<theme_item name="selected" data_type="style" type="StyleBox">
			[StyleBox] for the selected items, used when the [Tree] is not being focused.
		</theme_item>
		<theme_item name="selected_focus" data_type="style" type="StyleBox">
			[StyleBox] for the selected items, used when the [Tree] is being focused.
		</theme_item>
		<theme_item name="title_button_hover" data_type="style" type="StyleBox">
			[StyleBox] used when the title button is being hovered.
		</theme_item>
		<theme_item name="title_button_normal" data_type="style" type="StyleBox">
			Default [StyleBox] for the title button.
		</theme_item>
		<theme_item name="title_button_pressed" data_type="style" type="StyleBox">
			[StyleBox] used when the title button is being pressed.
		</theme_item>
	</theme_items>
</class>
