<?xml version="1.0" encoding="UTF-8" ?>
<class name="VisualShader" inherits="Shader" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A custom shader program with a visual editor.
	</brief_description>
	<description>
		This class provides a graph-like visual editor for creating a [Shader]. Although [VisualShader]s do not require coding, they share the same logic with script shaders. They use [VisualShaderNode]s that can be connected to each other to control the flow of the shader. The visual shader graph is converted to a script shader behind the scenes.
	</description>
	<tutorials>
		<link title="Using VisualShaders">$DOCS_URL/tutorials/shaders/visual_shaders.html</link>
	</tutorials>
	<methods>
		<method name="add_node">
			<return type="void" />
			<param index="0" name="type" type="int" enum="VisualShader.Type" />
			<param index="1" name="node" type="VisualShaderNode" />
			<param index="2" name="position" type="Vector2" />
			<param index="3" name="id" type="int" />
			<description>
				Adds the specified [param node] to the shader.
			</description>
		</method>
		<method name="add_varying">
			<return type="void" />
			<param index="0" name="name" type="String" />
			<param index="1" name="mode" type="int" enum="VisualShader.VaryingMode" />
			<param index="2" name="type" type="int" enum="VisualShader.VaryingType" />
			<description>
				Adds a new varying value node to the shader.
			</description>
		</method>
		<method name="attach_node_to_frame">
			<return type="void" />
			<param index="0" name="type" type="int" enum="VisualShader.Type" />
			<param index="1" name="id" type="int" />
			<param index="2" name="frame" type="int" />
			<description>
				Attaches the given node to the given frame.
			</description>
		</method>
		<method name="can_connect_nodes" qualifiers="const">
			<return type="bool" />
			<param index="0" name="type" type="int" enum="VisualShader.Type" />
			<param index="1" name="from_node" type="int" />
			<param index="2" name="from_port" type="int" />
			<param index="3" name="to_node" type="int" />
			<param index="4" name="to_port" type="int" />
			<description>
				Returns [code]true[/code] if the specified nodes and ports can be connected together.
			</description>
		</method>
		<method name="connect_nodes">
			<return type="int" enum="Error" />
			<param index="0" name="type" type="int" enum="VisualShader.Type" />
			<param index="1" name="from_node" type="int" />
			<param index="2" name="from_port" type="int" />
			<param index="3" name="to_node" type="int" />
			<param index="4" name="to_port" type="int" />
			<description>
				Connects the specified nodes and ports.
			</description>
		</method>
		<method name="connect_nodes_forced">
			<return type="void" />
			<param index="0" name="type" type="int" enum="VisualShader.Type" />
			<param index="1" name="from_node" type="int" />
			<param index="2" name="from_port" type="int" />
			<param index="3" name="to_node" type="int" />
			<param index="4" name="to_port" type="int" />
			<description>
				Connects the specified nodes and ports, even if they can't be connected. Such connection is invalid and will not function properly.
			</description>
		</method>
		<method name="detach_node_from_frame">
			<return type="void" />
			<param index="0" name="type" type="int" enum="VisualShader.Type" />
			<param index="1" name="id" type="int" />
			<description>
				Detaches the given node from the frame it is attached to.
			</description>
		</method>
		<method name="disconnect_nodes">
			<return type="void" />
			<param index="0" name="type" type="int" enum="VisualShader.Type" />
			<param index="1" name="from_node" type="int" />
			<param index="2" name="from_port" type="int" />
			<param index="3" name="to_node" type="int" />
			<param index="4" name="to_port" type="int" />
			<description>
				Connects the specified nodes and ports.
			</description>
		</method>
		<method name="get_node" qualifiers="const">
			<return type="VisualShaderNode" />
			<param index="0" name="type" type="int" enum="VisualShader.Type" />
			<param index="1" name="id" type="int" />
			<description>
				Returns the shader node instance with specified [param type] and [param id].
			</description>
		</method>
		<method name="get_node_connections" qualifiers="const">
			<return type="Dictionary[]" />
			<param index="0" name="type" type="int" enum="VisualShader.Type" />
			<description>
				Returns the list of connected nodes with the specified type.
			</description>
		</method>
		<method name="get_node_list" qualifiers="const">
			<return type="PackedInt32Array" />
			<param index="0" name="type" type="int" enum="VisualShader.Type" />
			<description>
				Returns the list of all nodes in the shader with the specified type.
			</description>
		</method>
		<method name="get_node_position" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="type" type="int" enum="VisualShader.Type" />
			<param index="1" name="id" type="int" />
			<description>
				Returns the position of the specified node within the shader graph.
			</description>
		</method>
		<method name="get_valid_node_id" qualifiers="const">
			<return type="int" />
			<param index="0" name="type" type="int" enum="VisualShader.Type" />
			<description>
				Returns next valid node ID that can be added to the shader graph.
			</description>
		</method>
		<method name="has_varying" qualifiers="const">
			<return type="bool" />
			<param index="0" name="name" type="String" />
			<description>
				Returns [code]true[/code] if the shader has a varying with the given [param name].
			</description>
		</method>
		<method name="is_node_connection" qualifiers="const">
			<return type="bool" />
			<param index="0" name="type" type="int" enum="VisualShader.Type" />
			<param index="1" name="from_node" type="int" />
			<param index="2" name="from_port" type="int" />
			<param index="3" name="to_node" type="int" />
			<param index="4" name="to_port" type="int" />
			<description>
				Returns [code]true[/code] if the specified node and port connection exist.
			</description>
		</method>
		<method name="remove_node">
			<return type="void" />
			<param index="0" name="type" type="int" enum="VisualShader.Type" />
			<param index="1" name="id" type="int" />
			<description>
				Removes the specified node from the shader.
			</description>
		</method>
		<method name="remove_varying">
			<return type="void" />
			<param index="0" name="name" type="String" />
			<description>
				Removes a varying value node with the given [param name]. Prints an error if a node with this name is not found.
			</description>
		</method>
		<method name="replace_node">
			<return type="void" />
			<param index="0" name="type" type="int" enum="VisualShader.Type" />
			<param index="1" name="id" type="int" />
			<param index="2" name="new_class" type="StringName" />
			<description>
				Replaces the specified node with a node of new class type.
			</description>
		</method>
		<method name="set_mode">
			<return type="void" />
			<param index="0" name="mode" type="int" enum="Shader.Mode" />
			<description>
				Sets the mode of this shader.
			</description>
		</method>
		<method name="set_node_position">
			<return type="void" />
			<param index="0" name="type" type="int" enum="VisualShader.Type" />
			<param index="1" name="id" type="int" />
			<param index="2" name="position" type="Vector2" />
			<description>
				Sets the position of the specified node.
			</description>
		</method>
	</methods>
	<members>
		<member name="graph_offset" type="Vector2" setter="set_graph_offset" getter="get_graph_offset" default="Vector2(0, 0)">
			The offset vector of the whole graph.
		</member>
	</members>
	<constants>
		<constant name="TYPE_VERTEX" value="0" enum="Type">
			A vertex shader, operating on vertices.
		</constant>
		<constant name="TYPE_FRAGMENT" value="1" enum="Type">
			A fragment shader, operating on fragments (pixels).
		</constant>
		<constant name="TYPE_LIGHT" value="2" enum="Type">
			A shader for light calculations.
		</constant>
		<constant name="TYPE_START" value="3" enum="Type">
			A function for the "start" stage of particle shader.
		</constant>
		<constant name="TYPE_PROCESS" value="4" enum="Type">
			A function for the "process" stage of particle shader.
		</constant>
		<constant name="TYPE_COLLIDE" value="5" enum="Type">
			A function for the "collide" stage (particle collision handler) of particle shader.
		</constant>
		<constant name="TYPE_START_CUSTOM" value="6" enum="Type">
			A function for the "start" stage of particle shader, with customized output.
		</constant>
		<constant name="TYPE_PROCESS_CUSTOM" value="7" enum="Type">
			A function for the "process" stage of particle shader, with customized output.
		</constant>
		<constant name="TYPE_SKY" value="8" enum="Type">
			A shader for 3D environment's sky.
		</constant>
		<constant name="TYPE_FOG" value="9" enum="Type">
			A compute shader that runs for each froxel of the volumetric fog map.
		</constant>
		<constant name="TYPE_MAX" value="10" enum="Type">
			Represents the size of the [enum Type] enum.
		</constant>
		<constant name="VARYING_MODE_VERTEX_TO_FRAG_LIGHT" value="0" enum="VaryingMode">
			Varying is passed from [code]Vertex[/code] function to [code]Fragment[/code] and [code]Light[/code] functions.
		</constant>
		<constant name="VARYING_MODE_FRAG_TO_LIGHT" value="1" enum="VaryingMode">
			Varying is passed from [code]Fragment[/code] function to [code]Light[/code] function.
		</constant>
		<constant name="VARYING_MODE_MAX" value="2" enum="VaryingMode">
			Represents the size of the [enum VaryingMode] enum.
		</constant>
		<constant name="VARYING_TYPE_FLOAT" value="0" enum="VaryingType">
			Varying is of type [float].
		</constant>
		<constant name="VARYING_TYPE_INT" value="1" enum="VaryingType">
			Varying is of type [int].
		</constant>
		<constant name="VARYING_TYPE_UINT" value="2" enum="VaryingType">
			Varying is of type unsigned [int].
		</constant>
		<constant name="VARYING_TYPE_VECTOR_2D" value="3" enum="VaryingType">
			Varying is of type [Vector2].
		</constant>
		<constant name="VARYING_TYPE_VECTOR_3D" value="4" enum="VaryingType">
			Varying is of type [Vector3].
		</constant>
		<constant name="VARYING_TYPE_VECTOR_4D" value="5" enum="VaryingType">
			Varying is of type [Vector4].
		</constant>
		<constant name="VARYING_TYPE_BOOLEAN" value="6" enum="VaryingType">
			Varying is of type [bool].
		</constant>
		<constant name="VARYING_TYPE_TRANSFORM" value="7" enum="VaryingType">
			Varying is of type [Transform3D].
		</constant>
		<constant name="VARYING_TYPE_MAX" value="8" enum="VaryingType">
			Represents the size of the [enum VaryingType] enum.
		</constant>
		<constant name="NODE_ID_INVALID" value="-1">
			Indicates an invalid [VisualShader] node.
		</constant>
		<constant name="NODE_ID_OUTPUT" value="0">
			Indicates an output node of [VisualShader].
		</constant>
	</constants>
</class>
