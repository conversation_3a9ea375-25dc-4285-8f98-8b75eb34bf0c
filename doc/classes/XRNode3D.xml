<?xml version="1.0" encoding="UTF-8" ?>
<class name="XRNode3D" inherits="Node3D" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A 3D node that has its position automatically updated by the [XRServer].
	</brief_description>
	<description>
		This node can be bound to a specific pose of a [XRPositionalTracker] and will automatically have its [member Node3D.transform] updated by the [XRServer]. Nodes of this type must be added as children of the [XROrigin3D] node.
	</description>
	<tutorials>
		<link title="XR documentation index">$DOCS_URL/tutorials/xr/index.html</link>
	</tutorials>
	<methods>
		<method name="get_has_tracking_data" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if the [member tracker] has current tracking data for the [member pose] being tracked.
			</description>
		</method>
		<method name="get_is_active" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if the [member tracker] has been registered and the [member pose] is being tracked.
			</description>
		</method>
		<method name="get_pose">
			<return type="XRPose" />
			<description>
				Returns the [XRPose] containing the current state of the pose being tracked. This gives access to additional properties of this pose.
			</description>
		</method>
		<method name="trigger_haptic_pulse">
			<return type="void" />
			<param index="0" name="action_name" type="String" />
			<param index="1" name="frequency" type="float" />
			<param index="2" name="amplitude" type="float" />
			<param index="3" name="duration_sec" type="float" />
			<param index="4" name="delay_sec" type="float" />
			<description>
				Triggers a haptic pulse on a device associated with this interface.
				[param action_name] is the name of the action for this pulse.
				[param frequency] is the frequency of the pulse, set to [code]0.0[/code] to have the system use a default frequency.
				[param amplitude] is the amplitude of the pulse between [code]0.0[/code] and [code]1.0[/code].
				[param duration_sec] is the duration of the pulse in seconds.
				[param delay_sec] is a delay in seconds before the pulse is given.
			</description>
		</method>
	</methods>
	<members>
		<member name="physics_interpolation_mode" type="int" setter="set_physics_interpolation_mode" getter="get_physics_interpolation_mode" overrides="Node" enum="Node.PhysicsInterpolationMode" default="2" />
		<member name="pose" type="StringName" setter="set_pose_name" getter="get_pose_name" default="&amp;&quot;default&quot;">
			The name of the pose we're bound to. Which poses a tracker supports is not known during design time.
			Redot defines number of standard pose names such as [code]aim[/code] and [code]grip[/code] but other may be configured within a given [XRInterface].
		</member>
		<member name="show_when_tracked" type="bool" setter="set_show_when_tracked" getter="get_show_when_tracked" default="false">
			Enables showing the node when tracking starts, and hiding the node when tracking is lost.
		</member>
		<member name="tracker" type="StringName" setter="set_tracker" getter="get_tracker" default="&amp;&quot;&quot;">
			The name of the tracker we're bound to. Which trackers are available is not known during design time.
			Redot defines a number of standard trackers such as [code]left_hand[/code] and [code]right_hand[/code] but others may be configured within a given [XRInterface].
		</member>
	</members>
	<signals>
		<signal name="tracking_changed">
			<param index="0" name="tracking" type="bool" />
			<description>
				Emitted when the [member tracker] starts or stops receiving updated tracking data for the [member pose] being tracked. The [param tracking] argument indicates whether the tracker is getting updated tracking data.
			</description>
		</signal>
	</signals>
</class>
