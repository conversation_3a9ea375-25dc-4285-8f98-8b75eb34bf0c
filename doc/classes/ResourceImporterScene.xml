<?xml version="1.0" encoding="UTF-8" ?>
<class name="ResourceImporterScene" inherits="ResourceImporter" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Imports a glTF, FBX, Collada or Blender 3D scene.
	</brief_description>
	<description>
		See also [ResourceImporterOBJ], which is used for OBJ models that can be imported as an independent [Mesh] or a scene.
		Additional options (such as extracting individual meshes or materials to files) are available in the [b]Advanced Import Settings[/b] dialog. This dialog can be accessed by double-clicking a 3D scene in the FileSystem dock or by selecting a 3D scene in the FileSystem dock, going to the Import dock and choosing [b]Advanced[/b].
		[b]Note:[/b] [ResourceImporterScene] is [i]not[/i] used for [PackedScene]s, such as [code].tscn[/code] and [code].scn[/code] files.
	</description>
	<tutorials>
		<link title="Importing 3D scenes">$DOCS_URL/tutorials/assets_pipeline/importing_3d_scenes/index.html</link>
	</tutorials>
	<members>
		<member name="_subresources" type="Dictionary" setter="" getter="" default="{}">
			Contains properties for the scene's subresources. This is an internal option which is not visible in the Import dock.
		</member>
		<member name="animation/fps" type="float" setter="" getter="" default="30">
			The number of frames per second to use for baking animation curves to a series of points with linear interpolation. It's recommended to configure this value to match the value you're using as a baseline in your 3D modeling software. Higher values result in more precise animation with fast movement changes, at the cost of higher file sizes and memory usage. Thanks to interpolation, there is usually not much benefit in going above 30 FPS (as the animation will still appear smooth at higher rendering framerates).
		</member>
		<member name="animation/import" type="bool" setter="" getter="" default="true">
			If [code]true[/code], import animations from the 3D scene.
		</member>
		<member name="animation/import_rest_as_RESET" type="bool" setter="" getter="" default="false">
			If [code]true[/code], adds an [Animation] named [code]RESET[/code], containing the [method Skeleton3D.get_bone_rest] from [Skeleton3D] nodes. This can be useful to extract an animation in the reference pose.
		</member>
		<member name="animation/remove_immutable_tracks" type="bool" setter="" getter="" default="true">
			If [code]true[/code], remove animation tracks that only contain default values. This can reduce output file size and memory usage with certain 3D scenes, depending on the contents of their animation tracks.
		</member>
		<member name="animation/trimming" type="bool" setter="" getter="" default="false">
			If [code]true[/code], trim the beginning and end of animations if there are no keyframe changes. This can reduce output file size and memory usage with certain 3D scenes, depending on the contents of their animation tracks.
		</member>
		<member name="import_script/path" type="String" setter="" getter="" default="&quot;&quot;">
			Path to an import script, which can run code after the import process has completed for custom processing. See [url=$DOCS_URL/tutorials/assets_pipeline/importing_3d_scenes/import_configuration.html#using-import-scripts-for-automation]Using import scripts for automation[/url] for more information.
		</member>
		<member name="meshes/create_shadow_meshes" type="bool" setter="" getter="" default="true">
			If [code]true[/code], enables the generation of shadow meshes on import. This optimizes shadow rendering without reducing quality by welding vertices together when possible. This in turn reduces the memory bandwidth required to render shadows. Shadow mesh generation currently doesn't support using a lower detail level than the source mesh (but shadow rendering will make use of LODs when relevant).
		</member>
		<member name="meshes/ensure_tangents" type="bool" setter="" getter="" default="true">
			If [code]true[/code], generate vertex tangents using [url=http://www.mikktspace.com/]Mikktspace[/url] if the input meshes don't have tangent data. When possible, it's recommended to let the 3D modeling software generate tangents on export instead on relying on this option. Tangents are required for correct display of normal and height maps, along with any material/shader features that require tangents.
			If you don't need material features that require tangents, disabling this can reduce output file size and speed up importing if the source 3D file doesn't contain tangents.
		</member>
		<member name="meshes/force_disable_compression" type="bool" setter="" getter="" default="false">
			If [code]true[/code], mesh compression will not be used. Consider enabling if you notice blocky artifacts in your mesh normals or UVs, or if you have meshes that are larger than a few thousand meters in each direction.
		</member>
		<member name="meshes/generate_lods" type="bool" setter="" getter="" default="true">
			If [code]true[/code], generates lower detail variants of the mesh which will be displayed in the distance to improve rendering performance. Not all meshes benefit from LOD, especially if they are never rendered from far away. Disabling this can reduce output file size and speed up importing. See [url=$DOCS_URL/tutorials/3d/mesh_lod.html#doc-mesh-lod]Mesh level of detail (LOD)[/url] for more information.
		</member>
		<member name="meshes/light_baking" type="int" setter="" getter="" default="1">
			Configures the meshes' [member GeometryInstance3D.gi_mode] in the 3D scene. If set to [b]Static Lightmaps[/b], sets the meshes' GI mode to Static and generates UV2 on import for [LightmapGI] baking.
		</member>
		<member name="meshes/lightmap_texel_size" type="float" setter="" getter="" default="0.2">
			Controls the size of each texel on the baked lightmap. A smaller value results in more precise lightmaps, at the cost of larger lightmap sizes and longer bake times.
			[b]Note:[/b] Only effective if [member meshes/light_baking] is set to [b]Static Lightmaps[/b].
		</member>
		<member name="nodes/apply_root_scale" type="bool" setter="" getter="" default="true">
			If [code]true[/code], [member nodes/root_scale] will be applied to the descendant nodes, meshes, animations, bones, etc. This means that if you add a child node later on within the imported scene, it won't be scaled. If [code]false[/code], [member nodes/root_scale] will multiply the scale of the root node instead.
		</member>
		<member name="nodes/import_as_skeleton_bones" type="bool" setter="" getter="" default="false">
			Treat all nodes in the imported scene as if they are bones within a single [Skeleton3D]. Can be used to guarantee that imported animations target skeleton bones rather than nodes. May also be used to assign the [code]"Root"[/code] bone in a [BoneMap]. See [url=$DOCS_URL/tutorials/assets_pipeline/retargeting_3d_skeletons.html]Retargeting 3D Skeletons[/url] for more information.
		</member>
		<member name="nodes/root_name" type="String" setter="" getter="" default="&quot;&quot;">
			Override for the root node name. If empty, the root node will use what the scene specifies, or the file name if the scene does not specify a root name.
		</member>
		<member name="nodes/root_scale" type="float" setter="" getter="" default="1.0">
			The uniform scale to use for the scene root. The default value of [code]1.0[/code] will not perform any rescaling. See [member nodes/apply_root_scale] for details of how this scale is applied.
		</member>
		<member name="nodes/root_type" type="String" setter="" getter="" default="&quot;&quot;">
			Override for the root node type. If empty, the root node will use what the scene specifies, or [Node3D] if the scene does not specify a root type. Using a node type that inherits from [Node3D] is recommended. Otherwise, you'll lose the ability to position the node directly in the 3D editor.
		</member>
		<member name="nodes/use_name_suffixes" type="bool" setter="" getter="" default="true">
			If [code]true[/code], will use suffixes in the names of imported objects such as nodes and resources to determine types and properties, such as [code]-noimp[/code] to skip import of a node or animation, [code]-alpha[/code] to enable alpha transparency on a material, and [code]-vcol[/code] to enable vertex colors on a material. Disabling this makes editor-imported files more similar to the original files, and more similar to files imported at runtime. See [url=$DOCS_URL/tutorials/assets_pipeline/importing_3d_scenes/node_type_customization.html]Node type customization using name suffixes[/url] for more information.
		</member>
		<member name="nodes/use_node_type_suffixes" type="bool" setter="" getter="" default="true">
			If [code]true[/code], will use suffixes in the node names to determine the node type, such as [code]-col[/code] for collision shapes. This is only used when [member nodes/use_name_suffixes] is [code]true[/code]. Disabling this makes editor-imported files more similar to the original files, and more similar to files imported at runtime. See [url=$DOCS_URL/tutorials/assets_pipeline/importing_3d_scenes/node_type_customization.html]Node type customization using name suffixes[/url] for more information.
		</member>
		<member name="skins/use_named_skins" type="bool" setter="" getter="" default="true">
			If checked, use named [Skin]s for animation. The [MeshInstance3D] node contains 3 properties of relevance here: a skeleton [NodePath] pointing to the [Skeleton3D] node (usually [code]..[/code]), a mesh, and a skin:
			- The [Skeleton3D] node contains a list of bones with names, their pose and rest, a name and a parent bone.
			- The mesh is all of the raw vertex data needed to display a mesh. In terms of the mesh, it knows how vertices are weight-painted and uses some internal numbering often imported from 3D modeling software.
			- The skin contains the information necessary to bind this mesh onto this Skeleton3D. For every one of the internal bone IDs chosen by the 3D modeling software, it contains two things. Firstly, a matrix known as the Bind Pose Matrix, Inverse Bind Matrix, or IBM for short. Secondly, the [Skin] contains each bone's name (if [member skins/use_named_skins] is [code]true[/code]), or the bone's index within the [Skeleton3D] list (if [member skins/use_named_skins] is [code]false[/code]).
			Together, this information is enough to tell Redot how to use the bone poses in the [Skeleton3D] node to render the mesh from each [MeshInstance3D]. Note that each [MeshInstance3D] may share binds, as is common in models exported from Blender, or each [MeshInstance3D] may use a separate [Skin] object, as is common in models exported from other tools such as Maya.
		</member>
	</members>
</class>
