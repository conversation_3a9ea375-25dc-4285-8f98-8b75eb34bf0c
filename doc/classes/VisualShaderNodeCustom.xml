<?xml version="1.0" encoding="UTF-8" ?>
<class name="VisualShaderNodeCustom" inherits="VisualShaderNode" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Virtual class to define custom [VisualShaderNode]s for use in the Visual Shader Editor.
	</brief_description>
	<description>
		By inheriting this class you can create a custom [VisualShader] script addon which will be automatically added to the Visual Shader Editor. The [VisualShaderNode]'s behavior is defined by overriding the provided virtual methods.
		In order for the node to be registered as an editor addon, you must use the [code]@tool[/code] annotation and provide a [code]class_name[/code] for your custom script. For example:
		[codeblock]
		@tool
		extends VisualShaderNodeCustom
		class_name VisualShaderNodeNoise
		[/codeblock]
	</description>
	<tutorials>
		<link title="Visual Shader plugins">$DOCS_URL/tutorials/plugins/editor/visual_shader_plugins.html</link>
	</tutorials>
	<methods>
		<method name="_get_category" qualifiers="virtual const">
			<return type="String" />
			<description>
				Override this method to define the path to the associated custom node in the Visual Shader Editor's members dialog. The path may look like [code]"MyGame/MyFunctions/Noise"[/code].
				Defining this method is [b]optional[/b]. If not overridden, the node will be filed under the "Addons" category.
			</description>
		</method>
		<method name="_get_code" qualifiers="virtual const">
			<return type="String" />
			<param index="0" name="input_vars" type="String[]" />
			<param index="1" name="output_vars" type="String[]" />
			<param index="2" name="mode" type="int" enum="Shader.Mode" />
			<param index="3" name="type" type="int" enum="VisualShader.Type" />
			<description>
				Override this method to define the actual shader code of the associated custom node. The shader code should be returned as a string, which can have multiple lines (the [code]"""[/code] multiline string construct can be used for convenience).
				The [param input_vars] and [param output_vars] arrays contain the string names of the various input and output variables, as defined by [code]_get_input_*[/code] and [code]_get_output_*[/code] virtual methods in this class.
				The output ports can be assigned values in the shader code. For example, [code]return output_vars[0] + " = " + input_vars[0] + ";"[/code].
				You can customize the generated code based on the shader [param mode] (see [enum Shader.Mode]) and/or [param type] (see [enum VisualShader.Type]).
				Defining this method is [b]required[/b].
			</description>
		</method>
		<method name="_get_default_input_port" qualifiers="virtual const">
			<return type="int" />
			<param index="0" name="type" type="int" enum="VisualShaderNode.PortType" />
			<description>
				Override this method to define the input port which should be connected by default when this node is created as a result of dragging a connection from an existing node to the empty space on the graph.
				Defining this method is [b]optional[/b]. If not overridden, the connection will be created to the first valid port.
			</description>
		</method>
		<method name="_get_description" qualifiers="virtual const">
			<return type="String" />
			<description>
				Override this method to define the description of the associated custom node in the Visual Shader Editor's members dialog.
				Defining this method is [b]optional[/b].
			</description>
		</method>
		<method name="_get_func_code" qualifiers="virtual const">
			<return type="String" />
			<param index="0" name="mode" type="int" enum="Shader.Mode" />
			<param index="1" name="type" type="int" enum="VisualShader.Type" />
			<description>
				Override this method to add a shader code to the beginning of each shader function (once). The shader code should be returned as a string, which can have multiple lines (the [code]"""[/code] multiline string construct can be used for convenience).
				If there are multiple custom nodes of different types which use this feature the order of each insertion is undefined.
				You can customize the generated code based on the shader [param mode] (see [enum Shader.Mode]) and/or [param type] (see [enum VisualShader.Type]).
				Defining this method is [b]optional[/b].
			</description>
		</method>
		<method name="_get_global_code" qualifiers="virtual const">
			<return type="String" />
			<param index="0" name="mode" type="int" enum="Shader.Mode" />
			<description>
				Override this method to add shader code on top of the global shader, to define your own standard library of reusable methods, varyings, constants, uniforms, etc. The shader code should be returned as a string, which can have multiple lines (the [code]"""[/code] multiline string construct can be used for convenience).
				Be careful with this functionality as it can cause name conflicts with other custom nodes, so be sure to give the defined entities unique names.
				You can customize the generated code based on the shader [param mode] (see [enum Shader.Mode]).
				Defining this method is [b]optional[/b].
			</description>
		</method>
		<method name="_get_input_port_count" qualifiers="virtual const">
			<return type="int" />
			<description>
				Override this method to define the number of input ports of the associated custom node.
				Defining this method is [b]required[/b]. If not overridden, the node has no input ports.
			</description>
		</method>
		<method name="_get_input_port_default_value" qualifiers="virtual const">
			<return type="Variant" />
			<param index="0" name="port" type="int" />
			<description>
				Override this method to define the default value for the specified input port. Prefer use this over [method VisualShaderNode.set_input_port_default_value].
				Defining this method is [b]required[/b]. If not overridden, the node has no default values for their input ports.
			</description>
		</method>
		<method name="_get_input_port_name" qualifiers="virtual const">
			<return type="String" />
			<param index="0" name="port" type="int" />
			<description>
				Override this method to define the names of input ports of the associated custom node. The names are used both for the input slots in the editor and as identifiers in the shader code, and are passed in the [code]input_vars[/code] array in [method _get_code].
				Defining this method is [b]optional[/b], but recommended. If not overridden, input ports are named as [code]"in" + str(port)[/code].
			</description>
		</method>
		<method name="_get_input_port_type" qualifiers="virtual const">
			<return type="int" enum="VisualShaderNode.PortType" />
			<param index="0" name="port" type="int" />
			<description>
				Override this method to define the returned type of each input port of the associated custom node (see [enum VisualShaderNode.PortType] for possible types).
				Defining this method is [b]optional[/b], but recommended. If not overridden, input ports will return the [constant VisualShaderNode.PORT_TYPE_SCALAR] type.
			</description>
		</method>
		<method name="_get_name" qualifiers="virtual const">
			<return type="String" />
			<description>
				Override this method to define the name of the associated custom node in the Visual Shader Editor's members dialog and graph.
				Defining this method is [b]optional[/b], but recommended. If not overridden, the node will be named as "Unnamed".
			</description>
		</method>
		<method name="_get_output_port_count" qualifiers="virtual const">
			<return type="int" />
			<description>
				Override this method to define the number of output ports of the associated custom node.
				Defining this method is [b]required[/b]. If not overridden, the node has no output ports.
			</description>
		</method>
		<method name="_get_output_port_name" qualifiers="virtual const">
			<return type="String" />
			<param index="0" name="port" type="int" />
			<description>
				Override this method to define the names of output ports of the associated custom node. The names are used both for the output slots in the editor and as identifiers in the shader code, and are passed in the [code]output_vars[/code] array in [method _get_code].
				Defining this method is [b]optional[/b], but recommended. If not overridden, output ports are named as [code]"out" + str(port)[/code].
			</description>
		</method>
		<method name="_get_output_port_type" qualifiers="virtual const">
			<return type="int" enum="VisualShaderNode.PortType" />
			<param index="0" name="port" type="int" />
			<description>
				Override this method to define the returned type of each output port of the associated custom node (see [enum VisualShaderNode.PortType] for possible types).
				Defining this method is [b]optional[/b], but recommended. If not overridden, output ports will return the [constant VisualShaderNode.PORT_TYPE_SCALAR] type.
			</description>
		</method>
		<method name="_get_property_count" qualifiers="virtual const">
			<return type="int" />
			<description>
				Override this method to define the number of the properties.
				Defining this method is [b]optional[/b].
			</description>
		</method>
		<method name="_get_property_default_index" qualifiers="virtual const">
			<return type="int" />
			<param index="0" name="index" type="int" />
			<description>
				Override this method to define the default index of the property of the associated custom node.
				Defining this method is [b]optional[/b].
			</description>
		</method>
		<method name="_get_property_name" qualifiers="virtual const">
			<return type="String" />
			<param index="0" name="index" type="int" />
			<description>
				Override this method to define the names of the property of the associated custom node.
				Defining this method is [b]optional[/b].
			</description>
		</method>
		<method name="_get_property_options" qualifiers="virtual const">
			<return type="PackedStringArray" />
			<param index="0" name="index" type="int" />
			<description>
				Override this method to define the options inside the drop-down list property of the associated custom node.
				Defining this method is [b]optional[/b].
			</description>
		</method>
		<method name="_get_return_icon_type" qualifiers="virtual const">
			<return type="int" enum="VisualShaderNode.PortType" />
			<description>
				Override this method to define the return icon of the associated custom node in the Visual Shader Editor's members dialog.
				Defining this method is [b]optional[/b]. If not overridden, no return icon is shown.
			</description>
		</method>
		<method name="_is_available" qualifiers="virtual const">
			<return type="bool" />
			<param index="0" name="mode" type="int" enum="Shader.Mode" />
			<param index="1" name="type" type="int" enum="VisualShader.Type" />
			<description>
				Override this method to prevent the node to be visible in the member dialog for the certain [param mode] (see [enum Shader.Mode]) and/or [param type] (see [enum VisualShader.Type]).
				Defining this method is [b]optional[/b]. If not overridden, it's [code]true[/code].
			</description>
		</method>
		<method name="_is_highend" qualifiers="virtual const">
			<return type="bool" />
			<description>
				Override this method to enable high-end mark in the Visual Shader Editor's members dialog.
				Defining this method is [b]optional[/b]. If not overridden, it's [code]false[/code].
			</description>
		</method>
		<method name="get_option_index" qualifiers="const">
			<return type="int" />
			<param index="0" name="option" type="int" />
			<description>
				Returns the selected index of the drop-down list option within a graph. You may use this function to define the specific behavior in the [method _get_code] or [method _get_global_code].
			</description>
		</method>
	</methods>
</class>
