<?xml version="1.0" encoding="UTF-8" ?>
<class name="float" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A built-in type for floating-point numbers.
	</brief_description>
	<description>
		The [float] built-in type is a 64-bit double-precision floating-point number, equivalent to [code]double[/code] in C++. This type has 14 reliable decimal digits of precision. The maximum value of [float] is approximately [code]1.79769e308[/code], and the minimum is approximately [code]-1.79769e308[/code].
		Many methods and properties in the engine use 32-bit single-precision floating-point numbers instead, equivalent to [code skip-lint]float[/code] in C++, which have 6 reliable decimal digits of precision. For data structures such as [Vector2] and [Vector3], Redot uses 32-bit floating-point numbers by default, but it can be changed to use 64-bit doubles if Redot is compiled with the [code]precision=double[/code] option.
		Math done using the [float] type is not guaranteed to be exact and will often result in small errors. You should usually use the [method @GlobalScope.is_equal_approx] and [method @GlobalScope.is_zero_approx] methods instead of [code]==[/code] to compare [float] values for equality.
	</description>
	<tutorials>
		<link title="Wikipedia: Double-precision floating-point format">https://en.wikipedia.org/wiki/Double-precision_floating-point_format</link>
		<link title="Wikipedia: Single-precision floating-point format">https://en.wikipedia.org/wiki/Single-precision_floating-point_format</link>
	</tutorials>
	<constructors>
		<constructor name="float">
			<return type="float" />
			<description>
				Constructs a default-initialized [float] set to [code]0.0[/code].
			</description>
		</constructor>
		<constructor name="float">
			<return type="float" />
			<param index="0" name="from" type="float" />
			<description>
				Constructs a [float] as a copy of the given [float].
			</description>
		</constructor>
		<constructor name="float">
			<return type="float" />
			<param index="0" name="from" type="String" />
			<description>
				Converts a [String] to a [float], following the same rules as [method String.to_float].
			</description>
		</constructor>
		<constructor name="float">
			<return type="float" />
			<param index="0" name="from" type="bool" />
			<description>
				Cast a [bool] value to a floating-point value, [code]float(true)[/code] will be equal to 1.0 and [code]float(false)[/code] will be equal to 0.0.
			</description>
		</constructor>
		<constructor name="float">
			<return type="float" />
			<param index="0" name="from" type="int" />
			<description>
				Cast an [int] value to a floating-point value, [code]float(1)[/code] will be equal to [code]1.0[/code].
			</description>
		</constructor>
	</constructors>
	<operators>
		<operator name="operator !=">
			<return type="bool" />
			<param index="0" name="right" type="float" />
			<description>
				Returns [code]true[/code] if two floats are different from each other.
				[b]Note:[/b] [constant @GDScript.NAN] doesn't behave the same as other numbers. Therefore, the results from this operator may not be accurate if NaNs are included.
			</description>
		</operator>
		<operator name="operator !=">
			<return type="bool" />
			<param index="0" name="right" type="int" />
			<description>
				Returns [code]true[/code] if the integer has different value than the float.
			</description>
		</operator>
		<operator name="operator *">
			<return type="Color" />
			<param index="0" name="right" type="Color" />
			<description>
				Multiplies each component of the [Color], including the alpha, by the given [float].
				[codeblock]
				print(1.5 * Color(0.5, 0.5, 0.5)) # Prints (0.75, 0.75, 0.75, 1.5)
				[/codeblock]
			</description>
		</operator>
		<operator name="operator *">
			<return type="Quaternion" />
			<param index="0" name="right" type="Quaternion" />
			<description>
				Multiplies each component of the [Quaternion] by the given [float]. This operation is not meaningful on its own, but it can be used as a part of a larger expression.
			</description>
		</operator>
		<operator name="operator *">
			<return type="Vector2" />
			<param index="0" name="right" type="Vector2" />
			<description>
				Multiplies each component of the [Vector2] by the given [float].
				[codeblock]
				print(2.5 * Vector2(1, 3)) # Prints (2.5, 7.5)
				[/codeblock]
			</description>
		</operator>
		<operator name="operator *">
			<return type="Vector2" />
			<param index="0" name="right" type="Vector2i" />
			<description>
				Multiplies each component of the [Vector2i] by the given [float]. Returns a [Vector2].
				[codeblock]
				print(0.9 * Vector2i(10, 15)) # Prints (9.0, 13.5)
				[/codeblock]
			</description>
		</operator>
		<operator name="operator *">
			<return type="Vector3" />
			<param index="0" name="right" type="Vector3" />
			<description>
				Multiplies each component of the [Vector3] by the given [float].
			</description>
		</operator>
		<operator name="operator *">
			<return type="Vector3" />
			<param index="0" name="right" type="Vector3i" />
			<description>
				Multiplies each component of the [Vector3i] by the given [float]. Returns a [Vector3].
				[codeblock]
				print(0.9 * Vector3i(10, 15, 20)) # Prints (9.0, 13.5, 18.0)
				[/codeblock]
			</description>
		</operator>
		<operator name="operator *">
			<return type="Vector4" />
			<param index="0" name="right" type="Vector4" />
			<description>
				Multiplies each component of the [Vector4] by the given [float].
			</description>
		</operator>
		<operator name="operator *">
			<return type="Vector4" />
			<param index="0" name="right" type="Vector4i" />
			<description>
				Multiplies each component of the [Vector4i] by the given [float]. Returns a [Vector4].
				[codeblock]
				print(0.9 * Vector4i(10, 15, 20, -10)) # Prints (9.0, 13.5, 18.0, -9.0)
				[/codeblock]
			</description>
		</operator>
		<operator name="operator *">
			<return type="float" />
			<param index="0" name="right" type="float" />
			<description>
				Multiplies two [float]s.
			</description>
		</operator>
		<operator name="operator *">
			<return type="float" />
			<param index="0" name="right" type="int" />
			<description>
				Multiplies a [float] and an [int]. The result is a [float].
			</description>
		</operator>
		<operator name="operator **">
			<return type="float" />
			<param index="0" name="right" type="float" />
			<description>
				Raises a [float] to a power of a [float].
				[codeblock]
				print(39.0625**0.25) # 2.5
				[/codeblock]
			</description>
		</operator>
		<operator name="operator **">
			<return type="float" />
			<param index="0" name="right" type="int" />
			<description>
				Raises a [float] to a power of an [int]. The result is a [float].
				[codeblock]
				print(0.9**3) # 0.729
				[/codeblock]
			</description>
		</operator>
		<operator name="operator +">
			<return type="float" />
			<param index="0" name="right" type="float" />
			<description>
				Adds two floats.
			</description>
		</operator>
		<operator name="operator +">
			<return type="float" />
			<param index="0" name="right" type="int" />
			<description>
				Adds a [float] and an [int]. The result is a [float].
			</description>
		</operator>
		<operator name="operator -">
			<return type="float" />
			<param index="0" name="right" type="float" />
			<description>
				Subtracts a float from a float.
			</description>
		</operator>
		<operator name="operator -">
			<return type="float" />
			<param index="0" name="right" type="int" />
			<description>
				Subtracts an [int] from a [float]. The result is a [float].
			</description>
		</operator>
		<operator name="operator /">
			<return type="float" />
			<param index="0" name="right" type="float" />
			<description>
				Divides two floats.
			</description>
		</operator>
		<operator name="operator /">
			<return type="float" />
			<param index="0" name="right" type="int" />
			<description>
				Divides a [float] by an [int]. The result is a [float].
			</description>
		</operator>
		<operator name="operator &lt;">
			<return type="bool" />
			<param index="0" name="right" type="float" />
			<description>
				Returns [code]true[/code] if the left float is less than the right one.
				[b]Note:[/b] [constant @GDScript.NAN] doesn't behave the same as other numbers. Therefore, the results from this operator may not be accurate if NaNs are included.
			</description>
		</operator>
		<operator name="operator &lt;">
			<return type="bool" />
			<param index="0" name="right" type="int" />
			<description>
				Returns [code]true[/code] if this [float] is less than the given [int].
			</description>
		</operator>
		<operator name="operator &lt;=">
			<return type="bool" />
			<param index="0" name="right" type="float" />
			<description>
				Returns [code]true[/code] if the left float is less than or equal to the right one.
				[b]Note:[/b] [constant @GDScript.NAN] doesn't behave the same as other numbers. Therefore, the results from this operator may not be accurate if NaNs are included.
			</description>
		</operator>
		<operator name="operator &lt;=">
			<return type="bool" />
			<param index="0" name="right" type="int" />
			<description>
				Returns [code]true[/code] if this [float] is less than or equal to the given [int].
			</description>
		</operator>
		<operator name="operator ==">
			<return type="bool" />
			<param index="0" name="right" type="float" />
			<description>
				Returns [code]true[/code] if both floats are exactly equal.
				[b]Note:[/b] Due to floating-point precision errors, consider using [method @GlobalScope.is_equal_approx] or [method @GlobalScope.is_zero_approx] instead, which are more reliable.
				[b]Note:[/b] [constant @GDScript.NAN] doesn't behave the same as other numbers. Therefore, the results from this operator may not be accurate if NaNs are included.
			</description>
		</operator>
		<operator name="operator ==">
			<return type="bool" />
			<param index="0" name="right" type="int" />
			<description>
				Returns [code]true[/code] if the [float] and the given [int] are equal.
			</description>
		</operator>
		<operator name="operator &gt;">
			<return type="bool" />
			<param index="0" name="right" type="float" />
			<description>
				Returns [code]true[/code] if the left float is greater than the right one.
				[b]Note:[/b] [constant @GDScript.NAN] doesn't behave the same as other numbers. Therefore, the results from this operator may not be accurate if NaNs are included.
			</description>
		</operator>
		<operator name="operator &gt;">
			<return type="bool" />
			<param index="0" name="right" type="int" />
			<description>
				Returns [code]true[/code] if this [float] is greater than the given [int].
			</description>
		</operator>
		<operator name="operator &gt;=">
			<return type="bool" />
			<param index="0" name="right" type="float" />
			<description>
				Returns [code]true[/code] if the left float is greater than or equal to the right one.
				[b]Note:[/b] [constant @GDScript.NAN] doesn't behave the same as other numbers. Therefore, the results from this operator may not be accurate if NaNs are included.
			</description>
		</operator>
		<operator name="operator &gt;=">
			<return type="bool" />
			<param index="0" name="right" type="int" />
			<description>
				Returns [code]true[/code] if this [float] is greater than or equal to the given [int].
			</description>
		</operator>
		<operator name="operator unary+">
			<return type="float" />
			<description>
				Returns the same value as if the [code]+[/code] was not there. Unary [code]+[/code] does nothing, but sometimes it can make your code more readable.
			</description>
		</operator>
		<operator name="operator unary-">
			<return type="float" />
			<description>
				Returns the negative value of the [float]. If positive, turns the number negative. If negative, turns the number positive. With floats, the number zero can be either positive or negative.
			</description>
		</operator>
	</operators>
</class>
