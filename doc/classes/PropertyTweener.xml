<?xml version="1.0" encoding="UTF-8" ?>
<class name="PropertyTweener" inherits="Tweener" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Interpolates an [Object]'s property over time.
	</brief_description>
	<description>
		[PropertyTweener] is used to interpolate a property in an object. See [method Tween.tween_property] for more usage information.
		The tweener will finish automatically if the target object is freed.
		[b]Note:[/b] [method Tween.tween_property] is the only correct way to create [PropertyTweener]. Any [PropertyTweener] created manually will not function correctly.
	</description>
	<tutorials>
	</tutorials>
	<methods>
		<method name="as_relative">
			<return type="PropertyTweener" />
			<description>
				When called, the final value will be used as a relative value instead.
				[b]Example:[/b] Move the node by [code]100[/code] pixels to the right.
				[codeblocks]
				[gdscript]
				var tween = get_tree().create_tween()
				tween.tween_property(self, "position", Vector2.RIGHT * 100, 1).as_relative()
				[/gdscript]
				[csharp]
				Tween tween = GetTree().CreateTween();
				tween.TweenProperty(this, "position", Vector2.Right * 100.0f, 1.0f).AsRelative();
				[/csharp]
				[/codeblocks]
			</description>
		</method>
		<method name="from">
			<return type="PropertyTweener" />
			<param index="0" name="value" type="Variant" />
			<description>
				Sets a custom initial value to the [PropertyTweener].
				[b]Example:[/b] Move the node from position [code](100, 100)[/code] to [code](200, 100)[/code].
				[codeblocks]
				[gdscript]
				var tween = get_tree().create_tween()
				tween.tween_property(self, "position", Vector2(200, 100), 1).from(Vector2(100, 100))
				[/gdscript]
				[csharp]
				Tween tween = GetTree().CreateTween();
				tween.TweenProperty(this, "position", new Vector2(200.0f, 100.0f), 1.0f).From(new Vector2(100.0f, 100.0f));
				[/csharp]
				[/codeblocks]
			</description>
		</method>
		<method name="from_current">
			<return type="PropertyTweener" />
			<description>
				Makes the [PropertyTweener] use the current property value (i.e. at the time of creating this [PropertyTweener]) as a starting point. This is equivalent of using [method from] with the current value. These two calls will do the same:
				[codeblocks]
				[gdscript]
				tween.tween_property(self, "position", Vector2(200, 100), 1).from(position)
				tween.tween_property(self, "position", Vector2(200, 100), 1).from_current()
				[/gdscript]
				[csharp]
				tween.TweenProperty(this, "position", new Vector2(200.0f, 100.0f), 1.0f).From(Position);
				tween.TweenProperty(this, "position", new Vector2(200.0f, 100.0f), 1.0f).FromCurrent();
				[/csharp]
				[/codeblocks]
			</description>
		</method>
		<method name="set_custom_interpolator">
			<return type="PropertyTweener" />
			<param index="0" name="interpolator_method" type="Callable" />
			<description>
				Allows interpolating the value with a custom easing function. The provided [param interpolator_method] will be called with a value ranging from [code]0.0[/code] to [code]1.0[/code] and is expected to return a value within the same range (values outside the range can be used for overshoot). The return value of the method is then used for interpolation between initial and final value. Note that the parameter passed to the method is still subject to the tweener's own easing.
				[codeblocks]
				[gdscript]
				@export var curve: Curve

				func _ready():
				    var tween = create_tween()
				    # Interpolate the value using a custom curve.
				    tween.tween_property(self, "position:x", 300, 1).as_relative().set_custom_interpolator(tween_curve)

				func tween_curve(v):
				    return curve.sample_baked(v)
				[/gdscript]
				[csharp]
				[Export]
				public Curve Curve { get; set; }

				public override void _Ready()
				{
				    Tween tween = CreateTween();
				    // Interpolate the value using a custom curve.
				    Callable tweenCurveCallable = Callable.From&lt;float, float&gt;(TweenCurve);
				    tween.TweenProperty(this, "position:x", 300.0f, 1.0f).AsRelative().SetCustomInterpolator(tweenCurveCallable);
				}

				private float TweenCurve(float value)
				{
				    return Curve.SampleBaked(value);
				}
				[/csharp]
				[/codeblocks]
			</description>
		</method>
		<method name="set_delay">
			<return type="PropertyTweener" />
			<param index="0" name="delay" type="float" />
			<description>
				Sets the time in seconds after which the [PropertyTweener] will start interpolating. By default there's no delay.
			</description>
		</method>
		<method name="set_ease">
			<return type="PropertyTweener" />
			<param index="0" name="ease" type="int" enum="Tween.EaseType" />
			<description>
				Sets the type of used easing from [enum Tween.EaseType]. If not set, the default easing is used from the [Tween] that contains this Tweener.
			</description>
		</method>
		<method name="set_trans">
			<return type="PropertyTweener" />
			<param index="0" name="trans" type="int" enum="Tween.TransitionType" />
			<description>
				Sets the type of used transition from [enum Tween.TransitionType]. If not set, the default transition is used from the [Tween] that contains this Tweener.
			</description>
		</method>
	</methods>
</class>
