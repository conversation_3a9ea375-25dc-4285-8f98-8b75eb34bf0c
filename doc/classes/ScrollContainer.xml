<?xml version="1.0" encoding="UTF-8" ?>
<class name="ScrollContainer" inherits="Container" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A container used to provide scrollbars to a child control when needed.
	</brief_description>
	<description>
		A container used to provide a child control with scrollbars when needed. Scrollbars will automatically be drawn at the right (for vertical) or bottom (for horizontal) and will enable dragging to move the viewable Control (and its children) within the ScrollContainer. Scrollbars will also automatically resize the grabber based on the [member Control.custom_minimum_size] of the Control relative to the ScrollContainer.
	</description>
	<tutorials>
		<link title="Using Containers">$DOCS_URL/tutorials/ui/gui_containers.html</link>
	</tutorials>
	<methods>
		<method name="ensure_control_visible">
			<return type="void" />
			<param index="0" name="control" type="Control" />
			<description>
				Ensures the given [param control] is visible (must be a direct or indirect child of the ScrollContainer). Used by [member follow_focus].
				[b]Note:[/b] This will not work on a node that was just added during the same frame. If you want to scroll to a newly added child, you must wait until the next frame using [signal SceneTree.process_frame]:
				[codeblock]
				add_child(child_node)
				await get_tree().process_frame
				ensure_control_visible(child_node)
				[/codeblock]
			</description>
		</method>
		<method name="get_h_scroll_bar">
			<return type="HScrollBar" />
			<description>
				Returns the horizontal scrollbar [HScrollBar] of this [ScrollContainer].
				[b]Warning:[/b] This is a required internal node, removing and freeing it may cause a crash. If you wish to disable or hide a scrollbar, you can use [member horizontal_scroll_mode].
			</description>
		</method>
		<method name="get_v_scroll_bar">
			<return type="VScrollBar" />
			<description>
				Returns the vertical scrollbar [VScrollBar] of this [ScrollContainer].
				[b]Warning:[/b] This is a required internal node, removing and freeing it may cause a crash. If you wish to disable or hide a scrollbar, you can use [member vertical_scroll_mode].
			</description>
		</method>
	</methods>
	<members>
		<member name="clip_contents" type="bool" setter="set_clip_contents" getter="is_clipping_contents" overrides="Control" default="true" />
		<member name="draw_focus_border" type="bool" setter="set_draw_focus_border" getter="get_draw_focus_border" default="false">
			If [code]true[/code], [theme_item focus] is drawn when the ScrollContainer or one of its descendant nodes is focused.
		</member>
		<member name="follow_focus" type="bool" setter="set_follow_focus" getter="is_following_focus" default="false">
			If [code]true[/code], the ScrollContainer will automatically scroll to focused children (including indirect children) to make sure they are fully visible.
		</member>
		<member name="horizontal_scroll_mode" type="int" setter="set_horizontal_scroll_mode" getter="get_horizontal_scroll_mode" enum="ScrollContainer.ScrollMode" default="1">
			Controls whether horizontal scrollbar can be used and when it should be visible. See [enum ScrollMode] for options.
		</member>
		<member name="scroll_deadzone" type="int" setter="set_deadzone" getter="get_deadzone" default="0">
			Deadzone for touch scrolling. Lower deadzone makes the scrolling more sensitive.
		</member>
		<member name="scroll_horizontal" type="int" setter="set_h_scroll" getter="get_h_scroll" default="0">
			The current horizontal scroll value.
			[b]Note:[/b] If you are setting this value in the [method Node._ready] function or earlier, it needs to be wrapped with [method Object.set_deferred], since scroll bar's [member Range.max_value] is not initialized yet.
			[codeblock]
			func _ready():
			    set_deferred("scroll_horizontal", 600)
			[/codeblock]
		</member>
		<member name="scroll_horizontal_custom_step" type="float" setter="set_horizontal_custom_step" getter="get_horizontal_custom_step" default="-1.0">
			Overrides the [member ScrollBar.custom_step] used when clicking the internal scroll bar's horizontal increment and decrement buttons or when using arrow keys when the [ScrollBar] is focused.
		</member>
		<member name="scroll_vertical" type="int" setter="set_v_scroll" getter="get_v_scroll" default="0">
			The current vertical scroll value.
			[b]Note:[/b] Setting it early needs to be deferred, just like in [member scroll_horizontal].
			[codeblock]
			func _ready():
			    set_deferred("scroll_vertical", 600)
			[/codeblock]
		</member>
		<member name="scroll_vertical_custom_step" type="float" setter="set_vertical_custom_step" getter="get_vertical_custom_step" default="-1.0">
			Overrides the [member ScrollBar.custom_step] used when clicking the internal scroll bar's vertical increment and decrement buttons or when using arrow keys when the [ScrollBar] is focused.
		</member>
		<member name="vertical_scroll_mode" type="int" setter="set_vertical_scroll_mode" getter="get_vertical_scroll_mode" enum="ScrollContainer.ScrollMode" default="1">
			Controls whether vertical scrollbar can be used and when it should be visible. See [enum ScrollMode] for options.
		</member>
	</members>
	<signals>
		<signal name="scroll_ended">
			<description>
				Emitted when scrolling stops when dragging the scrollable area [i]with a touch event[/i]. This signal is [i]not[/i] emitted when scrolling by dragging the scrollbar, scrolling with the mouse wheel or scrolling with keyboard/gamepad events.
				[b]Note:[/b] This signal is only emitted on Android or iOS, or on desktop/web platforms when [member ProjectSettings.input_devices/pointing/emulate_touch_from_mouse] is enabled.
			</description>
		</signal>
		<signal name="scroll_started">
			<description>
				Emitted when scrolling starts when dragging the scrollable area w[i]ith a touch event[/i]. This signal is [i]not[/i] emitted when scrolling by dragging the scrollbar, scrolling with the mouse wheel or scrolling with keyboard/gamepad events.
				[b]Note:[/b] This signal is only emitted on Android or iOS, or on desktop/web platforms when [member ProjectSettings.input_devices/pointing/emulate_touch_from_mouse] is enabled.
			</description>
		</signal>
	</signals>
	<constants>
		<constant name="SCROLL_MODE_DISABLED" value="0" enum="ScrollMode">
			Scrolling disabled, scrollbar will be invisible.
		</constant>
		<constant name="SCROLL_MODE_AUTO" value="1" enum="ScrollMode">
			Scrolling enabled, scrollbar will be visible only if necessary, i.e. container's content is bigger than the container.
		</constant>
		<constant name="SCROLL_MODE_SHOW_ALWAYS" value="2" enum="ScrollMode">
			Scrolling enabled, scrollbar will be always visible.
		</constant>
		<constant name="SCROLL_MODE_SHOW_NEVER" value="3" enum="ScrollMode">
			Scrolling enabled, scrollbar will be hidden.
		</constant>
		<constant name="SCROLL_MODE_RESERVE" value="4" enum="ScrollMode">
			Combines [constant SCROLL_MODE_AUTO] and [constant SCROLL_MODE_SHOW_ALWAYS]. The scrollbar is only visible if necessary, but the content size is adjusted as if it was always visible. It's useful for ensuring that content size stays the same regardless if the scrollbar is visible.
		</constant>
	</constants>
	<theme_items>
		<theme_item name="focus" data_type="style" type="StyleBox">
			The focus border [StyleBox] of the [ScrollContainer]. Only used if [member draw_focus_border] is [code]true[/code].
		</theme_item>
		<theme_item name="panel" data_type="style" type="StyleBox">
			The background [StyleBox] of the [ScrollContainer].
		</theme_item>
	</theme_items>
</class>
