<?xml version="1.0" encoding="UTF-8" ?>
<class name="TouchScreenButton" inherits="Node2D" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Button for touch screen devices for gameplay use.
	</brief_description>
	<description>
		TouchScreenButton allows you to create on-screen buttons for touch devices. It's intended for gameplay use, such as a unit you have to touch to move. Unlike [Button], TouchScreenButton supports multitouch out of the box. Several TouchScreenButtons can be pressed at the same time with touch input.
		This node inherits from [Node2D]. Unlike with [Control] nodes, you cannot set anchors on it. If you want to create menus or user interfaces, you may want to use [Button] nodes instead. To make button nodes react to touch events, you can enable [member ProjectSettings.input_devices/pointing/emulate_mouse_from_touch] in the Project Settings.
		You can configure TouchScreenButton to be visible only on touch devices, helping you develop your game both for desktop and mobile devices.
	</description>
	<tutorials>
	</tutorials>
	<methods>
		<method name="is_pressed" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if this button is currently pressed.
			</description>
		</method>
	</methods>
	<members>
		<member name="action" type="String" setter="set_action" getter="get_action" default="&quot;&quot;">
			The button's action. Actions can be handled with [InputEventAction].
		</member>
		<member name="bitmask" type="BitMap" setter="set_bitmask" getter="get_bitmask">
			The button's bitmask.
		</member>
		<member name="passby_press" type="bool" setter="set_passby_press" getter="is_passby_press_enabled" default="false">
			If [code]true[/code], the [signal pressed] and [signal released] signals are emitted whenever a pressed finger goes in and out of the button, even if the pressure started outside the active area of the button.
			[b]Note:[/b] This is a "pass-by" (not "bypass") press mode.
		</member>
		<member name="shape" type="Shape2D" setter="set_shape" getter="get_shape">
			The button's shape.
		</member>
		<member name="shape_centered" type="bool" setter="set_shape_centered" getter="is_shape_centered" default="true">
			If [code]true[/code], the button's shape is centered in the provided texture. If no texture is used, this property has no effect.
		</member>
		<member name="shape_visible" type="bool" setter="set_shape_visible" getter="is_shape_visible" default="true">
			If [code]true[/code], the button's shape is visible in the editor.
		</member>
		<member name="texture_normal" type="Texture2D" setter="set_texture_normal" getter="get_texture_normal">
			The button's texture for the normal state.
		</member>
		<member name="texture_pressed" type="Texture2D" setter="set_texture_pressed" getter="get_texture_pressed">
			The button's texture for the pressed state.
		</member>
		<member name="visibility_mode" type="int" setter="set_visibility_mode" getter="get_visibility_mode" enum="TouchScreenButton.VisibilityMode" default="0">
			The button's visibility mode. See [enum VisibilityMode] for possible values.
		</member>
	</members>
	<signals>
		<signal name="pressed">
			<description>
				Emitted when the button is pressed (down).
			</description>
		</signal>
		<signal name="released">
			<description>
				Emitted when the button is released (up).
			</description>
		</signal>
	</signals>
	<constants>
		<constant name="VISIBILITY_ALWAYS" value="0" enum="VisibilityMode">
			Always visible.
		</constant>
		<constant name="VISIBILITY_TOUCHSCREEN_ONLY" value="1" enum="VisibilityMode">
			Visible on touch screens only.
		</constant>
	</constants>
</class>
