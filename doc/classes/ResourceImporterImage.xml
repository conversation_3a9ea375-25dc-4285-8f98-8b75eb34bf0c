<?xml version="1.0" encoding="UTF-8" ?>
<class name="ResourceImporterImage" inherits="ResourceImporter" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Imports a image for use in scripting, with no rendering capabilities.
	</brief_description>
	<description>
		This importer imports [Image] resources, as opposed to [CompressedTexture2D]. If you need to render the image in 2D or 3D, use [ResourceImporterTexture] instead.
	</description>
	<tutorials>
		<link title="Importing images">$DOCS_URL/tutorials/assets_pipeline/importing_images.html</link>
	</tutorials>
</class>
