<?xml version="1.0" encoding="UTF-8" ?>
<class name="RenderingServer" inherits="Object" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Server for anything visible.
	</brief_description>
	<description>
		The rendering server is the API backend for everything visible. The whole scene system mounts on it to display. The rendering server is completely opaque: the internals are entirely implementation-specific and cannot be accessed.
		The rendering server can be used to bypass the scene/[Node] system entirely. This can improve performance in cases where the scene system is the bottleneck, but won't improve performance otherwise (for instance, if the GPU is already fully utilized).
		Resources are created using the [code]*_create[/code] functions. These functions return [RID]s which are not references to the objects themselves, but opaque [i]pointers[/i] towards these objects.
		All objects are drawn to a viewport. You can use the [Viewport] attached to the [SceneTree] or you can create one yourself with [method viewport_create]. When using a custom scenario or canvas, the scenario or canvas needs to be attached to the viewport using [method viewport_set_scenario] or [method viewport_attach_canvas].
		[b]Scenarios:[/b] In 3D, all visual objects must be associated with a scenario. The scenario is a visual representation of the world. If accessing the rendering server from a running game, the scenario can be accessed from the scene tree from any [Node3D] node with [method Node3D.get_world_3d]. Otherwise, a scenario can be created with [method scenario_create].
		Similarly, in 2D, a canvas is needed to draw all canvas items.
		[b]3D:[/b] In 3D, all visible objects are comprised of a resource and an instance. A resource can be a mesh, a particle system, a light, or any other 3D object. In order to be visible resources must be attached to an instance using [method instance_set_base]. The instance must also be attached to the scenario using [method instance_set_scenario] in order to be visible. RenderingServer methods that don't have a prefix are usually 3D-specific (but not always).
		[b]2D:[/b] In 2D, all visible objects are some form of canvas item. In order to be visible, a canvas item needs to be the child of a canvas attached to a viewport, or it needs to be the child of another canvas item that is eventually attached to the canvas. 2D-specific RenderingServer methods generally start with [code]canvas_*[/code].
		[b]Headless mode:[/b] Starting the engine with the [code]--headless[/code] [url=$DOCS_URL/tutorials/editor/command_line_tutorial.html]command line argument[/url] disables all rendering and window management functions. Most functions from [RenderingServer] will return dummy values in this case.
	</description>
	<tutorials>
		<link title="Optimization using Servers">$DOCS_URL/tutorials/performance/using_servers.html</link>
	</tutorials>
	<methods>
		<method name="bake_render_uv2">
			<return type="Image[]" />
			<param index="0" name="base" type="RID" />
			<param index="1" name="material_overrides" type="RID[]" />
			<param index="2" name="image_size" type="Vector2i" />
			<description>
				Bakes the material data of the Mesh passed in the [param base] parameter with optional [param material_overrides] to a set of [Image]s of size [param image_size]. Returns an array of [Image]s containing material properties as specified in [enum BakeChannels].
			</description>
		</method>
		<method name="call_on_render_thread">
			<return type="void" />
			<param index="0" name="callable" type="Callable" />
			<description>
				As the RenderingServer actual logic may run on an separate thread, accessing its internals from the main (or any other) thread will result in errors. To make it easier to run code that can safely access the rendering internals (such as [RenderingDevice] and similar RD classes), push a callable via this function so it will be executed on the render thread.
			</description>
		</method>
		<method name="camera_attributes_create">
			<return type="RID" />
			<description>
				Creates a camera attributes object and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]camera_attributes_[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				[b]Note:[/b] The equivalent resource is [CameraAttributes].
			</description>
		</method>
		<method name="camera_attributes_set_auto_exposure">
			<return type="void" />
			<param index="0" name="camera_attributes" type="RID" />
			<param index="1" name="enable" type="bool" />
			<param index="2" name="min_sensitivity" type="float" />
			<param index="3" name="max_sensitivity" type="float" />
			<param index="4" name="speed" type="float" />
			<param index="5" name="scale" type="float" />
			<description>
				Sets the parameters to use with the auto-exposure effect. These parameters take on the same meaning as their counterparts in [CameraAttributes] and [CameraAttributesPractical].
			</description>
		</method>
		<method name="camera_attributes_set_dof_blur">
			<return type="void" />
			<param index="0" name="camera_attributes" type="RID" />
			<param index="1" name="far_enable" type="bool" />
			<param index="2" name="far_distance" type="float" />
			<param index="3" name="far_transition" type="float" />
			<param index="4" name="near_enable" type="bool" />
			<param index="5" name="near_distance" type="float" />
			<param index="6" name="near_transition" type="float" />
			<param index="7" name="amount" type="float" />
			<description>
				Sets the parameters to use with the DOF blur effect. These parameters take on the same meaning as their counterparts in [CameraAttributesPractical].
			</description>
		</method>
		<method name="camera_attributes_set_dof_blur_bokeh_shape">
			<return type="void" />
			<param index="0" name="shape" type="int" enum="RenderingServer.DOFBokehShape" />
			<description>
				Sets the shape of the DOF bokeh pattern. Different shapes may be used to achieve artistic effect, or to meet performance targets. For more detail on available options see [enum DOFBokehShape].
			</description>
		</method>
		<method name="camera_attributes_set_dof_blur_quality">
			<return type="void" />
			<param index="0" name="quality" type="int" enum="RenderingServer.DOFBlurQuality" />
			<param index="1" name="use_jitter" type="bool" />
			<description>
				Sets the quality level of the DOF blur effect to one of the options in [enum DOFBlurQuality]. [param use_jitter] can be used to jitter samples taken during the blur pass to hide artifacts at the cost of looking more fuzzy.
			</description>
		</method>
		<method name="camera_attributes_set_exposure">
			<return type="void" />
			<param index="0" name="camera_attributes" type="RID" />
			<param index="1" name="multiplier" type="float" />
			<param index="2" name="normalization" type="float" />
			<description>
				Sets the exposure values that will be used by the renderers. The normalization amount is used to bake a given Exposure Value (EV) into rendering calculations to reduce the dynamic range of the scene.
				The normalization factor can be calculated from exposure value (EV100) as follows:
				[codeblock]
				func get_exposure_normalization(ev100: float):
				    return 1.0 / (pow(2.0, ev100) * 1.2)
				[/codeblock]
				The exposure value can be calculated from aperture (in f-stops), shutter speed (in seconds), and sensitivity (in ISO) as follows:
				[codeblock]
				func get_exposure(aperture: float, shutter_speed: float, sensitivity: float):
				    return log((aperture * aperture) / shutter_speed * (100.0 / sensitivity)) / log(2)
				[/codeblock]
			</description>
		</method>
		<method name="camera_create">
			<return type="RID" />
			<description>
				Creates a 3D camera and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]camera_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				[b]Note:[/b] The equivalent node is [Camera3D].
			</description>
		</method>
		<method name="camera_set_camera_attributes">
			<return type="void" />
			<param index="0" name="camera" type="RID" />
			<param index="1" name="effects" type="RID" />
			<description>
				Sets the camera_attributes created with [method camera_attributes_create] to the given camera.
			</description>
		</method>
		<method name="camera_set_compositor">
			<return type="void" />
			<param index="0" name="camera" type="RID" />
			<param index="1" name="compositor" type="RID" />
			<description>
				Sets the compositor used by this camera. Equivalent to [member Camera3D.compositor].
			</description>
		</method>
		<method name="camera_set_cull_mask">
			<return type="void" />
			<param index="0" name="camera" type="RID" />
			<param index="1" name="layers" type="int" />
			<description>
				Sets the cull mask associated with this camera. The cull mask describes which 3D layers are rendered by this camera. Equivalent to [member Camera3D.cull_mask].
			</description>
		</method>
		<method name="camera_set_environment">
			<return type="void" />
			<param index="0" name="camera" type="RID" />
			<param index="1" name="env" type="RID" />
			<description>
				Sets the environment used by this camera. Equivalent to [member Camera3D.environment].
			</description>
		</method>
		<method name="camera_set_frustum">
			<return type="void" />
			<param index="0" name="camera" type="RID" />
			<param index="1" name="size" type="float" />
			<param index="2" name="offset" type="Vector2" />
			<param index="3" name="z_near" type="float" />
			<param index="4" name="z_far" type="float" />
			<description>
				Sets camera to use frustum projection. This mode allows adjusting the [param offset] argument to create "tilted frustum" effects.
			</description>
		</method>
		<method name="camera_set_orthogonal">
			<return type="void" />
			<param index="0" name="camera" type="RID" />
			<param index="1" name="size" type="float" />
			<param index="2" name="z_near" type="float" />
			<param index="3" name="z_far" type="float" />
			<description>
				Sets camera to use orthogonal projection, also known as orthographic projection. Objects remain the same size on the screen no matter how far away they are.
			</description>
		</method>
		<method name="camera_set_perspective">
			<return type="void" />
			<param index="0" name="camera" type="RID" />
			<param index="1" name="fovy_degrees" type="float" />
			<param index="2" name="z_near" type="float" />
			<param index="3" name="z_far" type="float" />
			<description>
				Sets camera to use perspective projection. Objects on the screen becomes smaller when they are far away.
			</description>
		</method>
		<method name="camera_set_transform">
			<return type="void" />
			<param index="0" name="camera" type="RID" />
			<param index="1" name="transform" type="Transform3D" />
			<description>
				Sets [Transform3D] of camera.
			</description>
		</method>
		<method name="camera_set_use_vertical_aspect">
			<return type="void" />
			<param index="0" name="camera" type="RID" />
			<param index="1" name="enable" type="bool" />
			<description>
				If [code]true[/code], preserves the horizontal aspect ratio which is equivalent to [constant Camera3D.KEEP_WIDTH]. If [code]false[/code], preserves the vertical aspect ratio which is equivalent to [constant Camera3D.KEEP_HEIGHT].
			</description>
		</method>
		<method name="canvas_create">
			<return type="RID" />
			<description>
				Creates a canvas and returns the assigned [RID]. It can be accessed with the RID that is returned. This RID will be used in all [code]canvas_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				Canvas has no [Resource] or [Node] equivalent.
			</description>
		</method>
		<method name="canvas_item_add_animation_slice">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="animation_length" type="float" />
			<param index="2" name="slice_begin" type="float" />
			<param index="3" name="slice_end" type="float" />
			<param index="4" name="offset" type="float" default="0.0" />
			<description>
				Subsequent drawing commands will be ignored unless they fall within the specified animation slice. This is a faster way to implement animations that loop on background rather than redrawing constantly.
			</description>
		</method>
		<method name="canvas_item_add_circle">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="pos" type="Vector2" />
			<param index="2" name="radius" type="float" />
			<param index="3" name="color" type="Color" />
			<param index="4" name="antialiased" type="bool" default="false" />
			<description>
				Draws a circle on the [CanvasItem] pointed to by the [param item] [RID]. See also [method CanvasItem.draw_circle].
			</description>
		</method>
		<method name="canvas_item_add_clip_ignore">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="ignore" type="bool" />
			<description>
				If [param ignore] is [code]true[/code], ignore clipping on items drawn with this canvas item until this is called again with [param ignore] set to [code]false[/code].
			</description>
		</method>
		<method name="canvas_item_add_lcd_texture_rect_region">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="rect" type="Rect2" />
			<param index="2" name="texture" type="RID" />
			<param index="3" name="src_rect" type="Rect2" />
			<param index="4" name="modulate" type="Color" />
			<description>
				See also [method CanvasItem.draw_lcd_texture_rect_region].
			</description>
		</method>
		<method name="canvas_item_add_line">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="from" type="Vector2" />
			<param index="2" name="to" type="Vector2" />
			<param index="3" name="color" type="Color" />
			<param index="4" name="width" type="float" default="-1.0" />
			<param index="5" name="antialiased" type="bool" default="false" />
			<description>
				Draws a line on the [CanvasItem] pointed to by the [param item] [RID]. See also [method CanvasItem.draw_line].
			</description>
		</method>
		<method name="canvas_item_add_mesh">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="mesh" type="RID" />
			<param index="2" name="transform" type="Transform2D" default="Transform2D(1, 0, 0, 1, 0, 0)" />
			<param index="3" name="modulate" type="Color" default="Color(1, 1, 1, 1)" />
			<param index="4" name="texture" type="RID" default="RID()" />
			<description>
				Draws a mesh created with [method mesh_create] with given [param transform], [param modulate] color, and [param texture]. This is used internally by [MeshInstance2D].
			</description>
		</method>
		<method name="canvas_item_add_msdf_texture_rect_region">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="rect" type="Rect2" />
			<param index="2" name="texture" type="RID" />
			<param index="3" name="src_rect" type="Rect2" />
			<param index="4" name="modulate" type="Color" default="Color(1, 1, 1, 1)" />
			<param index="5" name="outline_size" type="int" default="0" />
			<param index="6" name="px_range" type="float" default="1.0" />
			<param index="7" name="scale" type="float" default="1.0" />
			<description>
				See also [method CanvasItem.draw_msdf_texture_rect_region].
			</description>
		</method>
		<method name="canvas_item_add_multiline">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="points" type="PackedVector2Array" />
			<param index="2" name="colors" type="PackedColorArray" />
			<param index="3" name="width" type="float" default="-1.0" />
			<param index="4" name="antialiased" type="bool" default="false" />
			<description>
				Draws a 2D multiline on the [CanvasItem] pointed to by the [param item] [RID]. See also [method CanvasItem.draw_multiline] and [method CanvasItem.draw_multiline_colors].
			</description>
		</method>
		<method name="canvas_item_add_multimesh">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="mesh" type="RID" />
			<param index="2" name="texture" type="RID" default="RID()" />
			<description>
				Draws a 2D [MultiMesh] on the [CanvasItem] pointed to by the [param item] [RID]. See also [method CanvasItem.draw_multimesh].
			</description>
		</method>
		<method name="canvas_item_add_nine_patch">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="rect" type="Rect2" />
			<param index="2" name="source" type="Rect2" />
			<param index="3" name="texture" type="RID" />
			<param index="4" name="topleft" type="Vector2" />
			<param index="5" name="bottomright" type="Vector2" />
			<param index="6" name="x_axis_mode" type="int" enum="RenderingServer.NinePatchAxisMode" default="0" />
			<param index="7" name="y_axis_mode" type="int" enum="RenderingServer.NinePatchAxisMode" default="0" />
			<param index="8" name="draw_center" type="bool" default="true" />
			<param index="9" name="modulate" type="Color" default="Color(1, 1, 1, 1)" />
			<description>
				Draws a nine-patch rectangle on the [CanvasItem] pointed to by the [param item] [RID].
			</description>
		</method>
		<method name="canvas_item_add_particles">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="particles" type="RID" />
			<param index="2" name="texture" type="RID" />
			<description>
				Draws particles on the [CanvasItem] pointed to by the [param item] [RID].
			</description>
		</method>
		<method name="canvas_item_add_polygon">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="points" type="PackedVector2Array" />
			<param index="2" name="colors" type="PackedColorArray" />
			<param index="3" name="uvs" type="PackedVector2Array" default="PackedVector2Array()" />
			<param index="4" name="texture" type="RID" default="RID()" />
			<description>
				Draws a 2D polygon on the [CanvasItem] pointed to by the [param item] [RID]. If you need more flexibility (such as being able to use bones), use [method canvas_item_add_triangle_array] instead. See also [method CanvasItem.draw_polygon].
				[b]Note:[/b] If you frequently redraw the same polygon with a large number of vertices, consider pre-calculating the triangulation with [method Geometry2D.triangulate_polygon] and using [method CanvasItem.draw_mesh], [method CanvasItem.draw_multimesh], or [method canvas_item_add_triangle_array].
			</description>
		</method>
		<method name="canvas_item_add_polyline">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="points" type="PackedVector2Array" />
			<param index="2" name="colors" type="PackedColorArray" />
			<param index="3" name="width" type="float" default="-1.0" />
			<param index="4" name="antialiased" type="bool" default="false" />
			<description>
				Draws a 2D polyline on the [CanvasItem] pointed to by the [param item] [RID]. See also [method CanvasItem.draw_polyline] and [method CanvasItem.draw_polyline_colors].
			</description>
		</method>
		<method name="canvas_item_add_primitive">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="points" type="PackedVector2Array" />
			<param index="2" name="colors" type="PackedColorArray" />
			<param index="3" name="uvs" type="PackedVector2Array" />
			<param index="4" name="texture" type="RID" />
			<description>
				Draws a 2D primitive on the [CanvasItem] pointed to by the [param item] [RID]. See also [method CanvasItem.draw_primitive].
			</description>
		</method>
		<method name="canvas_item_add_rect">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="rect" type="Rect2" />
			<param index="2" name="color" type="Color" />
			<param index="3" name="antialiased" type="bool" default="false" />
			<description>
				Draws a rectangle on the [CanvasItem] pointed to by the [param item] [RID]. See also [method CanvasItem.draw_rect].
			</description>
		</method>
		<method name="canvas_item_add_set_transform">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="transform" type="Transform2D" />
			<description>
				Sets a [Transform2D] that will be used to transform subsequent canvas item commands.
			</description>
		</method>
		<method name="canvas_item_add_texture_rect">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="rect" type="Rect2" />
			<param index="2" name="texture" type="RID" />
			<param index="3" name="tile" type="bool" default="false" />
			<param index="4" name="modulate" type="Color" default="Color(1, 1, 1, 1)" />
			<param index="5" name="transpose" type="bool" default="false" />
			<description>
				Draws a 2D textured rectangle on the [CanvasItem] pointed to by the [param item] [RID]. See also [method CanvasItem.draw_texture_rect] and [method Texture2D.draw_rect].
			</description>
		</method>
		<method name="canvas_item_add_texture_rect_region">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="rect" type="Rect2" />
			<param index="2" name="texture" type="RID" />
			<param index="3" name="src_rect" type="Rect2" />
			<param index="4" name="modulate" type="Color" default="Color(1, 1, 1, 1)" />
			<param index="5" name="transpose" type="bool" default="false" />
			<param index="6" name="clip_uv" type="bool" default="true" />
			<description>
				Draws the specified region of a 2D textured rectangle on the [CanvasItem] pointed to by the [param item] [RID]. See also [method CanvasItem.draw_texture_rect_region] and [method Texture2D.draw_rect_region].
			</description>
		</method>
		<method name="canvas_item_add_triangle_array">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="indices" type="PackedInt32Array" />
			<param index="2" name="points" type="PackedVector2Array" />
			<param index="3" name="colors" type="PackedColorArray" />
			<param index="4" name="uvs" type="PackedVector2Array" default="PackedVector2Array()" />
			<param index="5" name="bones" type="PackedInt32Array" default="PackedInt32Array()" />
			<param index="6" name="weights" type="PackedFloat32Array" default="PackedFloat32Array()" />
			<param index="7" name="texture" type="RID" default="RID()" />
			<param index="8" name="count" type="int" default="-1" />
			<description>
				Draws a triangle array on the [CanvasItem] pointed to by the [param item] [RID]. This is internally used by [Line2D] and [StyleBoxFlat] for rendering. [method canvas_item_add_triangle_array] is highly flexible, but more complex to use than [method canvas_item_add_polygon].
				[b]Note:[/b] [param count] is unused and can be left unspecified.
			</description>
		</method>
		<method name="canvas_item_attach_skeleton">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="skeleton" type="RID" />
			<description>
				Attaches a skeleton to the [CanvasItem]. Removes the previous skeleton.
			</description>
		</method>
		<method name="canvas_item_clear">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<description>
				Clears the [CanvasItem] and removes all commands in it.
			</description>
		</method>
		<method name="canvas_item_create">
			<return type="RID" />
			<description>
				Creates a new CanvasItem instance and returns its [RID]. It can be accessed with the RID that is returned. This RID will be used in all [code]canvas_item_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				[b]Note:[/b] The equivalent node is [CanvasItem].
			</description>
		</method>
		<method name="canvas_item_get_instance_shader_parameter" qualifiers="const">
			<return type="Variant" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="parameter" type="StringName" />
			<description>
				Returns the value of the per-instance shader uniform from the specified canvas item instance. Equivalent to [method CanvasItem.get_instance_shader_parameter].
			</description>
		</method>
		<method name="canvas_item_get_instance_shader_parameter_default_value" qualifiers="const">
			<return type="Variant" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="parameter" type="StringName" />
			<description>
				Returns the default value of the per-instance shader uniform from the specified canvas item instance. Equivalent to [method CanvasItem.get_instance_shader_parameter].
			</description>
		</method>
		<method name="canvas_item_get_instance_shader_parameter_list" qualifiers="const">
			<return type="Dictionary[]" />
			<param index="0" name="instance" type="RID" />
			<description>
				Returns a dictionary of per-instance shader uniform names of the per-instance shader uniform from the specified canvas item instance.
				The returned dictionary is in PropertyInfo format, with the keys [code]name[/code], [code]class_name[/code], [code]type[/code], [code]hint[/code], [code]hint_string[/code], and [code]usage[/code].
			</description>
		</method>
		<method name="canvas_item_reset_physics_interpolation">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<description>
				Prevents physics interpolation for the current physics tick.
				This is useful when moving a canvas item to a new location, to give an instantaneous change rather than interpolation from the previous location.
			</description>
		</method>
		<method name="canvas_item_set_canvas_group_mode">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="mode" type="int" enum="RenderingServer.CanvasGroupMode" />
			<param index="2" name="clear_margin" type="float" default="5.0" />
			<param index="3" name="fit_empty" type="bool" default="false" />
			<param index="4" name="fit_margin" type="float" default="0.0" />
			<param index="5" name="blur_mipmaps" type="bool" default="false" />
			<description>
				Sets the canvas group mode used during 2D rendering for the canvas item specified by the [param item] RID. For faster but more limited clipping, use [method canvas_item_set_clip] instead.
				[b]Note:[/b] The equivalent node functionality is found in [CanvasGroup] and [member CanvasItem.clip_children].
			</description>
		</method>
		<method name="canvas_item_set_clip">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="clip" type="bool" />
			<description>
				If [param clip] is [code]true[/code], makes the canvas item specified by the [param item] RID not draw anything outside of its rect's coordinates. This clipping is fast, but works only with axis-aligned rectangles. This means that rotation is ignored by the clipping rectangle. For more advanced clipping shapes, use [method canvas_item_set_canvas_group_mode] instead.
				[b]Note:[/b] The equivalent node functionality is found in [member Label.clip_text], [RichTextLabel] (always enabled) and more.
			</description>
		</method>
		<method name="canvas_item_set_copy_to_backbuffer">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="enabled" type="bool" />
			<param index="2" name="rect" type="Rect2" />
			<description>
				Sets the [CanvasItem] to copy a rect to the backbuffer.
			</description>
		</method>
		<method name="canvas_item_set_custom_rect">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="use_custom_rect" type="bool" />
			<param index="2" name="rect" type="Rect2" default="Rect2(0, 0, 0, 0)" />
			<description>
				If [param use_custom_rect] is [code]true[/code], sets the custom visibility rectangle (used for culling) to [param rect] for the canvas item specified by [param item]. Setting a custom visibility rect can reduce CPU load when drawing lots of 2D instances. If [param use_custom_rect] is [code]false[/code], automatically computes a visibility rectangle based on the canvas item's draw commands.
			</description>
		</method>
		<method name="canvas_item_set_default_texture_filter">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="filter" type="int" enum="RenderingServer.CanvasItemTextureFilter" />
			<description>
				Sets the default texture filter mode for the canvas item specified by the [param item] RID. Equivalent to [member CanvasItem.texture_filter].
			</description>
		</method>
		<method name="canvas_item_set_default_texture_repeat">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="repeat" type="int" enum="RenderingServer.CanvasItemTextureRepeat" />
			<description>
				Sets the default texture repeat mode for the canvas item specified by the [param item] RID. Equivalent to [member CanvasItem.texture_repeat].
			</description>
		</method>
		<method name="canvas_item_set_distance_field_mode">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="enabled" type="bool" />
			<description>
				If [param enabled] is [code]true[/code], enables multichannel signed distance field rendering mode for the canvas item specified by the [param item] RID. This is meant to be used for font rendering, or with specially generated images using [url=https://github.com/Chlumsky/msdfgen]msdfgen[/url].
			</description>
		</method>
		<method name="canvas_item_set_draw_behind_parent">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="enabled" type="bool" />
			<description>
				If [param enabled] is [code]true[/code], draws the canvas item specified by the [param item] RID behind its parent. Equivalent to [member CanvasItem.show_behind_parent].
			</description>
		</method>
		<method name="canvas_item_set_draw_index">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="index" type="int" />
			<description>
				Sets the index for the [CanvasItem].
			</description>
		</method>
		<method name="canvas_item_set_instance_shader_parameter">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="parameter" type="StringName" />
			<param index="2" name="value" type="Variant" />
			<description>
				Sets the per-instance shader uniform on the specified canvas item instance. Equivalent to [method CanvasItem.set_instance_shader_parameter].
			</description>
		</method>
		<method name="canvas_item_set_interpolated">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="interpolated" type="bool" />
			<description>
				If [param interpolated] is [code]true[/code], turns on physics interpolation for the canvas item.
			</description>
		</method>
		<method name="canvas_item_set_light_mask">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="mask" type="int" />
			<description>
				Sets the light [param mask] for the canvas item specified by the [param item] RID. Equivalent to [member CanvasItem.light_mask].
			</description>
		</method>
		<method name="canvas_item_set_material">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="material" type="RID" />
			<description>
				Sets a new [param material] to the canvas item specified by the [param item] RID. Equivalent to [member CanvasItem.material].
			</description>
		</method>
		<method name="canvas_item_set_modulate">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="color" type="Color" />
			<description>
				Multiplies the color of the canvas item specified by the [param item] RID, while affecting its children. See also [method canvas_item_set_self_modulate]. Equivalent to [member CanvasItem.modulate].
			</description>
		</method>
		<method name="canvas_item_set_parent">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="parent" type="RID" />
			<description>
				Sets a parent [CanvasItem] to the [CanvasItem]. The item will inherit transform, modulation and visibility from its parent, like [CanvasItem] nodes in the scene tree.
			</description>
		</method>
		<method name="canvas_item_set_self_modulate">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="color" type="Color" />
			<description>
				Multiplies the color of the canvas item specified by the [param item] RID, without affecting its children. See also [method canvas_item_set_modulate]. Equivalent to [member CanvasItem.self_modulate].
			</description>
		</method>
		<method name="canvas_item_set_sort_children_by_y">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="enabled" type="bool" />
			<description>
				If [param enabled] is [code]true[/code], child nodes with the lowest Y position are drawn before those with a higher Y position. Y-sorting only affects children that inherit from the canvas item specified by the [param item] RID, not the canvas item itself. Equivalent to [member CanvasItem.y_sort_enabled].
			</description>
		</method>
		<method name="canvas_item_set_transform">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="transform" type="Transform2D" />
			<description>
				Sets the [param transform] of the canvas item specified by the [param item] RID. This affects where and how the item will be drawn. Child canvas items' transforms are multiplied by their parent's transform. Equivalent to [member Node2D.transform].
			</description>
		</method>
		<method name="canvas_item_set_use_parent_material">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="enabled" type="bool" />
			<description>
				Sets if the [CanvasItem] uses its parent's material.
			</description>
		</method>
		<method name="canvas_item_set_visibility_layer">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="visibility_layer" type="int" />
			<description>
				Sets the rendering visibility layer associated with this [CanvasItem]. Only [Viewport] nodes with a matching rendering mask will render this [CanvasItem].
			</description>
		</method>
		<method name="canvas_item_set_visibility_notifier">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="enable" type="bool" />
			<param index="2" name="area" type="Rect2" />
			<param index="3" name="enter_callable" type="Callable" />
			<param index="4" name="exit_callable" type="Callable" />
			<description>
				Sets the given [CanvasItem] as visibility notifier. [param area] defines the area of detecting visibility. [param enter_callable] is called when the [CanvasItem] enters the screen, [param exit_callable] is called when the [CanvasItem] exits the screen. If [param enable] is [code]false[/code], the item will no longer function as notifier.
				This method can be used to manually mimic [VisibleOnScreenNotifier2D].
			</description>
		</method>
		<method name="canvas_item_set_visible">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="visible" type="bool" />
			<description>
				Sets the visibility of the [CanvasItem].
			</description>
		</method>
		<method name="canvas_item_set_z_as_relative_to_parent">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="enabled" type="bool" />
			<description>
				If this is enabled, the Z index of the parent will be added to the children's Z index.
			</description>
		</method>
		<method name="canvas_item_set_z_index">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="z_index" type="int" />
			<description>
				Sets the [CanvasItem]'s Z index, i.e. its draw order (lower indexes are drawn first).
			</description>
		</method>
		<method name="canvas_item_transform_physics_interpolation">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="transform" type="Transform2D" />
			<description>
				Transforms both the current and previous stored transform for a canvas item.
				This allows transforming a canvas item without creating a "glitch" in the interpolation, which is particularly useful for large worlds utilizing a shifting origin.
			</description>
		</method>
		<method name="canvas_light_attach_to_canvas">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="canvas" type="RID" />
			<description>
				Attaches the canvas light to the canvas. Removes it from its previous canvas.
			</description>
		</method>
		<method name="canvas_light_create">
			<return type="RID" />
			<description>
				Creates a canvas light and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]canvas_light_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				[b]Note:[/b] The equivalent node is [Light2D].
			</description>
		</method>
		<method name="canvas_light_occluder_attach_to_canvas">
			<return type="void" />
			<param index="0" name="occluder" type="RID" />
			<param index="1" name="canvas" type="RID" />
			<description>
				Attaches a light occluder to the canvas. Removes it from its previous canvas.
			</description>
		</method>
		<method name="canvas_light_occluder_create">
			<return type="RID" />
			<description>
				Creates a light occluder and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]canvas_light_occluder_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				[b]Note:[/b] The equivalent node is [LightOccluder2D].
			</description>
		</method>
		<method name="canvas_light_occluder_reset_physics_interpolation">
			<return type="void" />
			<param index="0" name="occluder" type="RID" />
			<description>
				Prevents physics interpolation for the current physics tick.
				This is useful when moving an occluder to a new location, to give an instantaneous change rather than interpolation from the previous location.
			</description>
		</method>
		<method name="canvas_light_occluder_set_as_sdf_collision">
			<return type="void" />
			<param index="0" name="occluder" type="RID" />
			<param index="1" name="enable" type="bool" />
			<description>
			</description>
		</method>
		<method name="canvas_light_occluder_set_enabled">
			<return type="void" />
			<param index="0" name="occluder" type="RID" />
			<param index="1" name="enabled" type="bool" />
			<description>
				Enables or disables light occluder.
			</description>
		</method>
		<method name="canvas_light_occluder_set_interpolated">
			<return type="void" />
			<param index="0" name="occluder" type="RID" />
			<param index="1" name="interpolated" type="bool" />
			<description>
				If [param interpolated] is [code]true[/code], turns on physics interpolation for the light occluder.
			</description>
		</method>
		<method name="canvas_light_occluder_set_light_mask">
			<return type="void" />
			<param index="0" name="occluder" type="RID" />
			<param index="1" name="mask" type="int" />
			<description>
				The light mask. See [LightOccluder2D] for more information on light masks.
			</description>
		</method>
		<method name="canvas_light_occluder_set_polygon">
			<return type="void" />
			<param index="0" name="occluder" type="RID" />
			<param index="1" name="polygon" type="RID" />
			<description>
				Sets a light occluder's polygon.
			</description>
		</method>
		<method name="canvas_light_occluder_set_transform">
			<return type="void" />
			<param index="0" name="occluder" type="RID" />
			<param index="1" name="transform" type="Transform2D" />
			<description>
				Sets a light occluder's [Transform2D].
			</description>
		</method>
		<method name="canvas_light_occluder_transform_physics_interpolation">
			<return type="void" />
			<param index="0" name="occluder" type="RID" />
			<param index="1" name="transform" type="Transform2D" />
			<description>
				Transforms both the current and previous stored transform for a light occluder.
				This allows transforming an occluder without creating a "glitch" in the interpolation, which is particularly useful for large worlds utilizing a shifting origin.
			</description>
		</method>
		<method name="canvas_light_reset_physics_interpolation">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<description>
				Prevents physics interpolation for the current physics tick.
				This is useful when moving a canvas item to a new location, to give an instantaneous change rather than interpolation from the previous location.
			</description>
		</method>
		<method name="canvas_light_set_blend_mode">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="mode" type="int" enum="RenderingServer.CanvasLightBlendMode" />
			<description>
				Sets the blend mode for the given canvas light. See [enum CanvasLightBlendMode] for options. Equivalent to [member Light2D.blend_mode].
			</description>
		</method>
		<method name="canvas_light_set_color">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="color" type="Color" />
			<description>
				Sets the color for a light.
			</description>
		</method>
		<method name="canvas_light_set_enabled">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="enabled" type="bool" />
			<description>
				Enables or disables a canvas light.
			</description>
		</method>
		<method name="canvas_light_set_energy">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="energy" type="float" />
			<description>
				Sets a canvas light's energy.
			</description>
		</method>
		<method name="canvas_light_set_height">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="height" type="float" />
			<description>
				Sets a canvas light's height.
			</description>
		</method>
		<method name="canvas_light_set_interpolated">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="interpolated" type="bool" />
			<description>
				If [param interpolated] is [code]true[/code], turns on physics interpolation for the canvas light.
			</description>
		</method>
		<method name="canvas_light_set_item_cull_mask">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="mask" type="int" />
			<description>
				The light mask. See [LightOccluder2D] for more information on light masks.
			</description>
		</method>
		<method name="canvas_light_set_item_shadow_cull_mask">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="mask" type="int" />
			<description>
				The binary mask used to determine which layers this canvas light's shadows affects. See [LightOccluder2D] for more information on light masks.
			</description>
		</method>
		<method name="canvas_light_set_layer_range">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="min_layer" type="int" />
			<param index="2" name="max_layer" type="int" />
			<description>
				The layer range that gets rendered with this light.
			</description>
		</method>
		<method name="canvas_light_set_mode">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="mode" type="int" enum="RenderingServer.CanvasLightMode" />
			<description>
				The mode of the light, see [enum CanvasLightMode] constants.
			</description>
		</method>
		<method name="canvas_light_set_shadow_color">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="color" type="Color" />
			<description>
				Sets the color of the canvas light's shadow.
			</description>
		</method>
		<method name="canvas_light_set_shadow_enabled">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="enabled" type="bool" />
			<description>
				Enables or disables the canvas light's shadow.
			</description>
		</method>
		<method name="canvas_light_set_shadow_filter">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="filter" type="int" enum="RenderingServer.CanvasLightShadowFilter" />
			<description>
				Sets the canvas light's shadow's filter, see [enum CanvasLightShadowFilter] constants.
			</description>
		</method>
		<method name="canvas_light_set_shadow_smooth">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="smooth" type="float" />
			<description>
				Smoothens the shadow. The lower, the smoother.
			</description>
		</method>
		<method name="canvas_light_set_texture">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="texture" type="RID" />
			<description>
				Sets the texture to be used by a [PointLight2D]. Equivalent to [member PointLight2D.texture].
			</description>
		</method>
		<method name="canvas_light_set_texture_offset">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="offset" type="Vector2" />
			<description>
				Sets the offset of a [PointLight2D]'s texture. Equivalent to [member PointLight2D.offset].
			</description>
		</method>
		<method name="canvas_light_set_texture_scale">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="scale" type="float" />
			<description>
				Sets the scale factor of a [PointLight2D]'s texture. Equivalent to [member PointLight2D.texture_scale].
			</description>
		</method>
		<method name="canvas_light_set_transform">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="transform" type="Transform2D" />
			<description>
				Sets the canvas light's [Transform2D].
			</description>
		</method>
		<method name="canvas_light_set_z_range">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="min_z" type="int" />
			<param index="2" name="max_z" type="int" />
			<description>
				Sets the Z range of objects that will be affected by this light. Equivalent to [member Light2D.range_z_min] and [member Light2D.range_z_max].
			</description>
		</method>
		<method name="canvas_light_transform_physics_interpolation">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="transform" type="Transform2D" />
			<description>
				Transforms both the current and previous stored transform for a canvas light.
				This allows transforming a light without creating a "glitch" in the interpolation, which is particularly useful for large worlds utilizing a shifting origin.
			</description>
		</method>
		<method name="canvas_occluder_polygon_create">
			<return type="RID" />
			<description>
				Creates a new light occluder polygon and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]canvas_occluder_polygon_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				[b]Note:[/b] The equivalent resource is [OccluderPolygon2D].
			</description>
		</method>
		<method name="canvas_occluder_polygon_set_cull_mode">
			<return type="void" />
			<param index="0" name="occluder_polygon" type="RID" />
			<param index="1" name="mode" type="int" enum="RenderingServer.CanvasOccluderPolygonCullMode" />
			<description>
				Sets an occluder polygons cull mode. See [enum CanvasOccluderPolygonCullMode] constants.
			</description>
		</method>
		<method name="canvas_occluder_polygon_set_shape">
			<return type="void" />
			<param index="0" name="occluder_polygon" type="RID" />
			<param index="1" name="shape" type="PackedVector2Array" />
			<param index="2" name="closed" type="bool" />
			<description>
				Sets the shape of the occluder polygon.
			</description>
		</method>
		<method name="canvas_set_disable_scale">
			<return type="void" />
			<param index="0" name="disable" type="bool" />
			<description>
			</description>
		</method>
		<method name="canvas_set_item_mirroring">
			<return type="void" />
			<param index="0" name="canvas" type="RID" />
			<param index="1" name="item" type="RID" />
			<param index="2" name="mirroring" type="Vector2" />
			<description>
				A copy of the canvas item will be drawn with a local offset of the [param mirroring].
				[b]Note:[/b] This is equivalent to calling [method canvas_set_item_repeat] like [code]canvas_set_item_repeat(item, mirroring, 1)[/code], with an additional check ensuring [param canvas] is a parent of [param item].
			</description>
		</method>
		<method name="canvas_set_item_repeat">
			<return type="void" />
			<param index="0" name="item" type="RID" />
			<param index="1" name="repeat_size" type="Vector2" />
			<param index="2" name="repeat_times" type="int" />
			<description>
				A copy of the canvas item will be drawn with a local offset of the [param repeat_size] by the number of times of the [param repeat_times]. As the [param repeat_times] increases, the copies will spread away from the origin texture.
			</description>
		</method>
		<method name="canvas_set_modulate">
			<return type="void" />
			<param index="0" name="canvas" type="RID" />
			<param index="1" name="color" type="Color" />
			<description>
				Modulates all colors in the given canvas.
			</description>
		</method>
		<method name="canvas_set_shadow_texture_size">
			<return type="void" />
			<param index="0" name="size" type="int" />
			<description>
				Sets the [member ProjectSettings.rendering/2d/shadow_atlas/size] to use for [Light2D] shadow rendering (in pixels). The value is rounded up to the nearest power of 2.
			</description>
		</method>
		<method name="canvas_texture_create">
			<return type="RID" />
			<description>
				Creates a canvas texture and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]canvas_texture_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method. See also [method texture_2d_create].
				[b]Note:[/b] The equivalent resource is [CanvasTexture] and is only meant to be used in 2D rendering, not 3D.
			</description>
		</method>
		<method name="canvas_texture_set_channel">
			<return type="void" />
			<param index="0" name="canvas_texture" type="RID" />
			<param index="1" name="channel" type="int" enum="RenderingServer.CanvasTextureChannel" />
			<param index="2" name="texture" type="RID" />
			<description>
				Sets the [param channel]'s [param texture] for the canvas texture specified by the [param canvas_texture] RID. Equivalent to [member CanvasTexture.diffuse_texture], [member CanvasTexture.normal_texture] and [member CanvasTexture.specular_texture].
			</description>
		</method>
		<method name="canvas_texture_set_shading_parameters">
			<return type="void" />
			<param index="0" name="canvas_texture" type="RID" />
			<param index="1" name="base_color" type="Color" />
			<param index="2" name="shininess" type="float" />
			<description>
				Sets the [param base_color] and [param shininess] to use for the canvas texture specified by the [param canvas_texture] RID. Equivalent to [member CanvasTexture.specular_color] and [member CanvasTexture.specular_shininess].
			</description>
		</method>
		<method name="canvas_texture_set_texture_filter">
			<return type="void" />
			<param index="0" name="canvas_texture" type="RID" />
			<param index="1" name="filter" type="int" enum="RenderingServer.CanvasItemTextureFilter" />
			<description>
				Sets the texture [param filter] mode to use for the canvas texture specified by the [param canvas_texture] RID.
			</description>
		</method>
		<method name="canvas_texture_set_texture_repeat">
			<return type="void" />
			<param index="0" name="canvas_texture" type="RID" />
			<param index="1" name="repeat" type="int" enum="RenderingServer.CanvasItemTextureRepeat" />
			<description>
				Sets the texture [param repeat] mode to use for the canvas texture specified by the [param canvas_texture] RID.
			</description>
		</method>
		<method name="compositor_create">
			<return type="RID" />
			<description>
				Creates a new compositor and adds it to the RenderingServer. It can be accessed with the RID that is returned.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
			</description>
		</method>
		<method name="compositor_effect_create">
			<return type="RID" />
			<description>
				Creates a new rendering effect and adds it to the RenderingServer. It can be accessed with the RID that is returned.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
			</description>
		</method>
		<method name="compositor_effect_set_callback">
			<return type="void" />
			<param index="0" name="effect" type="RID" />
			<param index="1" name="callback_type" type="int" enum="RenderingServer.CompositorEffectCallbackType" />
			<param index="2" name="callback" type="Callable" />
			<description>
				Sets the callback type ([param callback_type]) and callback method([param callback]) for this rendering effect.
			</description>
		</method>
		<method name="compositor_effect_set_enabled">
			<return type="void" />
			<param index="0" name="effect" type="RID" />
			<param index="1" name="enabled" type="bool" />
			<description>
				Enables/disables this rendering effect.
			</description>
		</method>
		<method name="compositor_effect_set_flag">
			<return type="void" />
			<param index="0" name="effect" type="RID" />
			<param index="1" name="flag" type="int" enum="RenderingServer.CompositorEffectFlags" />
			<param index="2" name="set" type="bool" />
			<description>
				Sets the flag ([param flag]) for this rendering effect to [code]true[/code] or [code]false[/code] ([param set]).
			</description>
		</method>
		<method name="compositor_set_compositor_effects">
			<return type="void" />
			<param index="0" name="compositor" type="RID" />
			<param index="1" name="effects" type="RID[]" />
			<description>
				Sets the compositor effects for the specified compositor RID. [param effects] should be an array containing RIDs created with [method compositor_effect_create].
			</description>
		</method>
		<method name="create_local_rendering_device" qualifiers="const">
			<return type="RenderingDevice" />
			<description>
				Creates a RenderingDevice that can be used to do draw and compute operations on a separate thread. Cannot draw to the screen nor share data with the global RenderingDevice.
				[b]Note:[/b] When using the OpenGL rendering driver or when running in headless mode, this function always returns [code]null[/code].
			</description>
		</method>
		<method name="debug_canvas_item_get_rect">
			<return type="Rect2" />
			<param index="0" name="item" type="RID" />
			<description>
				Returns the bounding rectangle for a canvas item in local space, as calculated by the renderer. This bound is used internally for culling.
				[b]Warning:[/b] This function is intended for debugging in the editor, and will pass through and return a zero [Rect2] in exported projects.
			</description>
		</method>
		<method name="decal_create">
			<return type="RID" />
			<description>
				Creates a decal and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]decal_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				To place in a scene, attach this decal to an instance using [method instance_set_base] using the returned RID.
				[b]Note:[/b] The equivalent node is [Decal].
			</description>
		</method>
		<method name="decal_set_albedo_mix">
			<return type="void" />
			<param index="0" name="decal" type="RID" />
			<param index="1" name="albedo_mix" type="float" />
			<description>
				Sets the [param albedo_mix] in the decal specified by the [param decal] RID. Equivalent to [member Decal.albedo_mix].
			</description>
		</method>
		<method name="decal_set_cull_mask">
			<return type="void" />
			<param index="0" name="decal" type="RID" />
			<param index="1" name="mask" type="int" />
			<description>
				Sets the cull [param mask] in the decal specified by the [param decal] RID. Equivalent to [member Decal.cull_mask].
			</description>
		</method>
		<method name="decal_set_distance_fade" keywords="decal_set_lod">
			<return type="void" />
			<param index="0" name="decal" type="RID" />
			<param index="1" name="enabled" type="bool" />
			<param index="2" name="begin" type="float" />
			<param index="3" name="length" type="float" />
			<description>
				Sets the distance fade parameters in the decal specified by the [param decal] RID. Equivalent to [member Decal.distance_fade_enabled], [member Decal.distance_fade_begin] and [member Decal.distance_fade_length].
			</description>
		</method>
		<method name="decal_set_emission_energy">
			<return type="void" />
			<param index="0" name="decal" type="RID" />
			<param index="1" name="energy" type="float" />
			<description>
				Sets the emission [param energy] in the decal specified by the [param decal] RID. Equivalent to [member Decal.emission_energy].
			</description>
		</method>
		<method name="decal_set_fade">
			<return type="void" />
			<param index="0" name="decal" type="RID" />
			<param index="1" name="above" type="float" />
			<param index="2" name="below" type="float" />
			<description>
				Sets the upper fade ([param above]) and lower fade ([param below]) in the decal specified by the [param decal] RID. Equivalent to [member Decal.upper_fade] and [member Decal.lower_fade].
			</description>
		</method>
		<method name="decal_set_modulate">
			<return type="void" />
			<param index="0" name="decal" type="RID" />
			<param index="1" name="color" type="Color" />
			<description>
				Sets the color multiplier in the decal specified by the [param decal] RID to [param color]. Equivalent to [member Decal.modulate].
			</description>
		</method>
		<method name="decal_set_normal_fade">
			<return type="void" />
			<param index="0" name="decal" type="RID" />
			<param index="1" name="fade" type="float" />
			<description>
				Sets the normal [param fade] in the decal specified by the [param decal] RID. Equivalent to [member Decal.normal_fade].
			</description>
		</method>
		<method name="decal_set_size">
			<return type="void" />
			<param index="0" name="decal" type="RID" />
			<param index="1" name="size" type="Vector3" />
			<description>
				Sets the [param size] of the decal specified by the [param decal] RID. Equivalent to [member Decal.size].
			</description>
		</method>
		<method name="decal_set_texture">
			<return type="void" />
			<param index="0" name="decal" type="RID" />
			<param index="1" name="type" type="int" enum="RenderingServer.DecalTexture" />
			<param index="2" name="texture" type="RID" />
			<description>
				Sets the [param texture] in the given texture [param type] slot for the specified decal. Equivalent to [method Decal.set_texture].
			</description>
		</method>
		<method name="decals_set_filter">
			<return type="void" />
			<param index="0" name="filter" type="int" enum="RenderingServer.DecalFilter" />
			<description>
				Sets the texture [param filter] mode to use when rendering decals. This parameter is global and cannot be set on a per-decal basis.
			</description>
		</method>
		<method name="directional_light_create">
			<return type="RID" />
			<description>
				Creates a directional light and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID can be used in most [code]light_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				To place in a scene, attach this directional light to an instance using [method instance_set_base] using the returned RID.
				[b]Note:[/b] The equivalent node is [DirectionalLight3D].
			</description>
		</method>
		<method name="directional_shadow_atlas_set_size">
			<return type="void" />
			<param index="0" name="size" type="int" />
			<param index="1" name="is_16bits" type="bool" />
			<description>
				Sets the [param size] of the directional light shadows in 3D. See also [member ProjectSettings.rendering/lights_and_shadows/directional_shadow/size]. This parameter is global and cannot be set on a per-viewport basis.
			</description>
		</method>
		<method name="directional_soft_shadow_filter_set_quality">
			<return type="void" />
			<param index="0" name="quality" type="int" enum="RenderingServer.ShadowQuality" />
			<description>
				Sets the filter [param quality] for directional light shadows in 3D. See also [member ProjectSettings.rendering/lights_and_shadows/directional_shadow/soft_shadow_filter_quality]. This parameter is global and cannot be set on a per-viewport basis.
			</description>
		</method>
		<method name="environment_bake_panorama">
			<return type="Image" />
			<param index="0" name="environment" type="RID" />
			<param index="1" name="bake_irradiance" type="bool" />
			<param index="2" name="size" type="Vector2i" />
			<description>
				Generates and returns an [Image] containing the radiance map for the specified [param environment] RID's sky. This supports built-in sky material and custom sky shaders. If [param bake_irradiance] is [code]true[/code], the irradiance map is saved instead of the radiance map. The radiance map is used to render reflected light, while the irradiance map is used to render ambient light. See also [method sky_bake_panorama].
				[b]Note:[/b] The image is saved in linear color space without any tonemapping performed, which means it will look too dark if viewed directly in an image editor.
				[b]Note:[/b] [param size] should be a 2:1 aspect ratio for the generated panorama to have square pixels. For radiance maps, there is no point in using a height greater than [member Sky.radiance_size], as it won't increase detail. Irradiance maps only contain low-frequency data, so there is usually no point in going past a size of 128×64 pixels when saving an irradiance map.
			</description>
		</method>
		<method name="environment_create">
			<return type="RID" />
			<description>
				Creates an environment and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]environment_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				[b]Note:[/b] The equivalent resource is [Environment].
			</description>
		</method>
		<method name="environment_glow_set_use_bicubic_upscale">
			<return type="void" />
			<param index="0" name="enable" type="bool" />
			<description>
				If [param enable] is [code]true[/code], enables bicubic upscaling for glow which improves quality at the cost of performance. Equivalent to [member ProjectSettings.rendering/environment/glow/upscale_mode].
				[b]Note:[/b] This setting is only effective when using the Forward+ or Mobile rendering methods, as Compatibility uses a different glow implementation.
			</description>
		</method>
		<method name="environment_set_adjustment">
			<return type="void" />
			<param index="0" name="env" type="RID" />
			<param index="1" name="enable" type="bool" />
			<param index="2" name="brightness" type="float" />
			<param index="3" name="contrast" type="float" />
			<param index="4" name="saturation" type="float" />
			<param index="5" name="use_1d_color_correction" type="bool" />
			<param index="6" name="color_correction" type="RID" />
			<description>
				Sets the values to be used with the "adjustments" post-process effect. See [Environment] for more details.
			</description>
		</method>
		<method name="environment_set_ambient_light">
			<return type="void" />
			<param index="0" name="env" type="RID" />
			<param index="1" name="color" type="Color" />
			<param index="2" name="ambient" type="int" enum="RenderingServer.EnvironmentAmbientSource" default="0" />
			<param index="3" name="energy" type="float" default="1.0" />
			<param index="4" name="sky_contribution" type="float" default="0.0" />
			<param index="5" name="reflection_source" type="int" enum="RenderingServer.EnvironmentReflectionSource" default="0" />
			<description>
				Sets the values to be used for ambient light rendering. See [Environment] for more details.
			</description>
		</method>
		<method name="environment_set_background">
			<return type="void" />
			<param index="0" name="env" type="RID" />
			<param index="1" name="bg" type="int" enum="RenderingServer.EnvironmentBG" />
			<description>
				Sets the environment's background mode. Equivalent to [member Environment.background_mode].
			</description>
		</method>
		<method name="environment_set_bg_color">
			<return type="void" />
			<param index="0" name="env" type="RID" />
			<param index="1" name="color" type="Color" />
			<description>
				Color displayed for clear areas of the scene. Only effective if using the [constant ENV_BG_COLOR] background mode.
			</description>
		</method>
		<method name="environment_set_bg_energy">
			<return type="void" />
			<param index="0" name="env" type="RID" />
			<param index="1" name="multiplier" type="float" />
			<param index="2" name="exposure_value" type="float" />
			<description>
				Sets the intensity of the background color.
			</description>
		</method>
		<method name="environment_set_camera_id">
			<return type="void" />
			<param index="0" name="env" type="RID" />
			<param index="1" name="id" type="int" />
			<description>
				Sets the camera ID to be used as environment background.
			</description>
		</method>
		<method name="environment_set_canvas_max_layer">
			<return type="void" />
			<param index="0" name="env" type="RID" />
			<param index="1" name="max_layer" type="int" />
			<description>
				Sets the maximum layer to use if using Canvas background mode.
			</description>
		</method>
		<method name="environment_set_fog">
			<return type="void" />
			<param index="0" name="env" type="RID" />
			<param index="1" name="enable" type="bool" />
			<param index="2" name="light_color" type="Color" />
			<param index="3" name="light_energy" type="float" />
			<param index="4" name="sun_scatter" type="float" />
			<param index="5" name="density" type="float" />
			<param index="6" name="height" type="float" />
			<param index="7" name="height_density" type="float" />
			<param index="8" name="aerial_perspective" type="float" />
			<param index="9" name="sky_affect" type="float" />
			<param index="10" name="fog_mode" type="int" enum="RenderingServer.EnvironmentFogMode" default="0" />
			<description>
				Configures fog for the specified environment RID. See [code]fog_*[/code] properties in [Environment] for more information.
			</description>
		</method>
		<method name="environment_set_fog_depth">
			<return type="void" />
			<param index="0" name="env" type="RID" />
			<param index="1" name="curve" type="float" />
			<param index="2" name="begin" type="float" />
			<param index="3" name="end" type="float" />
			<description>
				Configures fog depth for the specified environment RID. Only has an effect when the fog mode of the environment is [constant ENV_FOG_MODE_DEPTH]. See [code]fog_depth_*[/code] properties in [Environment] for more information.
			</description>
		</method>
		<method name="environment_set_glow">
			<return type="void" />
			<param index="0" name="env" type="RID" />
			<param index="1" name="enable" type="bool" />
			<param index="2" name="levels" type="PackedFloat32Array" />
			<param index="3" name="intensity" type="float" />
			<param index="4" name="strength" type="float" />
			<param index="5" name="mix" type="float" />
			<param index="6" name="bloom_threshold" type="float" />
			<param index="7" name="blend_mode" type="int" enum="RenderingServer.EnvironmentGlowBlendMode" />
			<param index="8" name="hdr_bleed_threshold" type="float" />
			<param index="9" name="hdr_bleed_scale" type="float" />
			<param index="10" name="hdr_luminance_cap" type="float" />
			<param index="11" name="glow_map_strength" type="float" />
			<param index="12" name="glow_map" type="RID" />
			<description>
				Configures glow for the specified environment RID. See [code]glow_*[/code] properties in [Environment] for more information.
			</description>
		</method>
		<method name="environment_set_sdfgi">
			<return type="void" />
			<param index="0" name="env" type="RID" />
			<param index="1" name="enable" type="bool" />
			<param index="2" name="cascades" type="int" />
			<param index="3" name="min_cell_size" type="float" />
			<param index="4" name="y_scale" type="int" enum="RenderingServer.EnvironmentSDFGIYScale" />
			<param index="5" name="use_occlusion" type="bool" />
			<param index="6" name="bounce_feedback" type="float" />
			<param index="7" name="read_sky" type="bool" />
			<param index="8" name="energy" type="float" />
			<param index="9" name="normal_bias" type="float" />
			<param index="10" name="probe_bias" type="float" />
			<description>
				Configures signed distance field global illumination for the specified environment RID. See [code]sdfgi_*[/code] properties in [Environment] for more information.
			</description>
		</method>
		<method name="environment_set_sdfgi_frames_to_converge">
			<return type="void" />
			<param index="0" name="frames" type="int" enum="RenderingServer.EnvironmentSDFGIFramesToConverge" />
			<description>
				Sets the number of frames to use for converging signed distance field global illumination. Equivalent to [member ProjectSettings.rendering/global_illumination/sdfgi/frames_to_converge].
			</description>
		</method>
		<method name="environment_set_sdfgi_frames_to_update_light">
			<return type="void" />
			<param index="0" name="frames" type="int" enum="RenderingServer.EnvironmentSDFGIFramesToUpdateLight" />
			<description>
				Sets the update speed for dynamic lights' indirect lighting when computing signed distance field global illumination. Equivalent to [member ProjectSettings.rendering/global_illumination/sdfgi/frames_to_update_lights].
			</description>
		</method>
		<method name="environment_set_sdfgi_ray_count">
			<return type="void" />
			<param index="0" name="ray_count" type="int" enum="RenderingServer.EnvironmentSDFGIRayCount" />
			<description>
				Sets the number of rays to throw per frame when computing signed distance field global illumination. Equivalent to [member ProjectSettings.rendering/global_illumination/sdfgi/probe_ray_count].
			</description>
		</method>
		<method name="environment_set_sky">
			<return type="void" />
			<param index="0" name="env" type="RID" />
			<param index="1" name="sky" type="RID" />
			<description>
				Sets the [Sky] to be used as the environment's background when using [i]BGMode[/i] sky. Equivalent to [member Environment.sky].
			</description>
		</method>
		<method name="environment_set_sky_custom_fov">
			<return type="void" />
			<param index="0" name="env" type="RID" />
			<param index="1" name="scale" type="float" />
			<description>
				Sets a custom field of view for the background [Sky]. Equivalent to [member Environment.sky_custom_fov].
			</description>
		</method>
		<method name="environment_set_sky_orientation">
			<return type="void" />
			<param index="0" name="env" type="RID" />
			<param index="1" name="orientation" type="Basis" />
			<description>
				Sets the rotation of the background [Sky] expressed as a [Basis]. Equivalent to [member Environment.sky_rotation], where the rotation vector is used to construct the [Basis].
			</description>
		</method>
		<method name="environment_set_ssao">
			<return type="void" />
			<param index="0" name="env" type="RID" />
			<param index="1" name="enable" type="bool" />
			<param index="2" name="radius" type="float" />
			<param index="3" name="intensity" type="float" />
			<param index="4" name="power" type="float" />
			<param index="5" name="detail" type="float" />
			<param index="6" name="horizon" type="float" />
			<param index="7" name="sharpness" type="float" />
			<param index="8" name="light_affect" type="float" />
			<param index="9" name="ao_channel_affect" type="float" />
			<description>
				Sets the variables to be used with the screen-space ambient occlusion (SSAO) post-process effect. See [Environment] for more details.
			</description>
		</method>
		<method name="environment_set_ssao_quality">
			<return type="void" />
			<param index="0" name="quality" type="int" enum="RenderingServer.EnvironmentSSAOQuality" />
			<param index="1" name="half_size" type="bool" />
			<param index="2" name="adaptive_target" type="float" />
			<param index="3" name="blur_passes" type="int" />
			<param index="4" name="fadeout_from" type="float" />
			<param index="5" name="fadeout_to" type="float" />
			<description>
				Sets the quality level of the screen-space ambient occlusion (SSAO) post-process effect. See [Environment] for more details.
			</description>
		</method>
		<method name="environment_set_ssil_quality">
			<return type="void" />
			<param index="0" name="quality" type="int" enum="RenderingServer.EnvironmentSSILQuality" />
			<param index="1" name="half_size" type="bool" />
			<param index="2" name="adaptive_target" type="float" />
			<param index="3" name="blur_passes" type="int" />
			<param index="4" name="fadeout_from" type="float" />
			<param index="5" name="fadeout_to" type="float" />
			<description>
				Sets the quality level of the screen-space indirect lighting (SSIL) post-process effect. See [Environment] for more details.
			</description>
		</method>
		<method name="environment_set_ssr">
			<return type="void" />
			<param index="0" name="env" type="RID" />
			<param index="1" name="enable" type="bool" />
			<param index="2" name="max_steps" type="int" />
			<param index="3" name="fade_in" type="float" />
			<param index="4" name="fade_out" type="float" />
			<param index="5" name="depth_tolerance" type="float" />
			<description>
				Sets the variables to be used with the screen-space reflections (SSR) post-process effect. See [Environment] for more details.
			</description>
		</method>
		<method name="environment_set_ssr_roughness_quality">
			<return type="void" />
			<param index="0" name="quality" type="int" enum="RenderingServer.EnvironmentSSRRoughnessQuality" />
			<description>
			</description>
		</method>
		<method name="environment_set_tonemap">
			<return type="void" />
			<param index="0" name="env" type="RID" />
			<param index="1" name="tone_mapper" type="int" enum="RenderingServer.EnvironmentToneMapper" />
			<param index="2" name="exposure" type="float" />
			<param index="3" name="white" type="float" />
			<description>
				Sets the variables to be used with the "tonemap" post-process effect. See [Environment] for more details.
			</description>
		</method>
		<method name="environment_set_volumetric_fog">
			<return type="void" />
			<param index="0" name="env" type="RID" />
			<param index="1" name="enable" type="bool" />
			<param index="2" name="density" type="float" />
			<param index="3" name="albedo" type="Color" />
			<param index="4" name="emission" type="Color" />
			<param index="5" name="emission_energy" type="float" />
			<param index="6" name="anisotropy" type="float" />
			<param index="7" name="length" type="float" />
			<param index="8" name="p_detail_spread" type="float" />
			<param index="9" name="gi_inject" type="float" />
			<param index="10" name="temporal_reprojection" type="bool" />
			<param index="11" name="temporal_reprojection_amount" type="float" />
			<param index="12" name="ambient_inject" type="float" />
			<param index="13" name="sky_affect" type="float" />
			<description>
				Sets the variables to be used with the volumetric fog post-process effect. See [Environment] for more details.
			</description>
		</method>
		<method name="environment_set_volumetric_fog_filter_active">
			<return type="void" />
			<param index="0" name="active" type="bool" />
			<description>
				Enables filtering of the volumetric fog scattering buffer. This results in much smoother volumes with very few under-sampling artifacts.
			</description>
		</method>
		<method name="environment_set_volumetric_fog_volume_size">
			<return type="void" />
			<param index="0" name="size" type="int" />
			<param index="1" name="depth" type="int" />
			<description>
				Sets the resolution of the volumetric fog's froxel buffer. [param size] is modified by the screen's aspect ratio and then used to set the width and height of the buffer. While [param depth] is directly used to set the depth of the buffer.
			</description>
		</method>
		<method name="fog_volume_create">
			<return type="RID" />
			<description>
				Creates a new fog volume and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]fog_volume_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				[b]Note:[/b] The equivalent node is [FogVolume].
			</description>
		</method>
		<method name="fog_volume_set_material">
			<return type="void" />
			<param index="0" name="fog_volume" type="RID" />
			<param index="1" name="material" type="RID" />
			<description>
				Sets the [Material] of the fog volume. Can be either a [FogMaterial] or a custom [ShaderMaterial].
			</description>
		</method>
		<method name="fog_volume_set_shape">
			<return type="void" />
			<param index="0" name="fog_volume" type="RID" />
			<param index="1" name="shape" type="int" enum="RenderingServer.FogVolumeShape" />
			<description>
				Sets the shape of the fog volume to either [constant RenderingServer.FOG_VOLUME_SHAPE_ELLIPSOID], [constant RenderingServer.FOG_VOLUME_SHAPE_CONE], [constant RenderingServer.FOG_VOLUME_SHAPE_CYLINDER], [constant RenderingServer.FOG_VOLUME_SHAPE_BOX] or [constant RenderingServer.FOG_VOLUME_SHAPE_WORLD].
			</description>
		</method>
		<method name="fog_volume_set_size">
			<return type="void" />
			<param index="0" name="fog_volume" type="RID" />
			<param index="1" name="size" type="Vector3" />
			<description>
				Sets the size of the fog volume when shape is [constant RenderingServer.FOG_VOLUME_SHAPE_ELLIPSOID], [constant RenderingServer.FOG_VOLUME_SHAPE_CONE], [constant RenderingServer.FOG_VOLUME_SHAPE_CYLINDER] or [constant RenderingServer.FOG_VOLUME_SHAPE_BOX].
			</description>
		</method>
		<method name="force_draw">
			<return type="void" />
			<param index="0" name="swap_buffers" type="bool" default="true" />
			<param index="1" name="frame_step" type="float" default="0.0" />
			<description>
				Forces redrawing of all viewports at once. Must be called from the main thread.
			</description>
		</method>
		<method name="force_sync">
			<return type="void" />
			<description>
				Forces a synchronization between the CPU and GPU, which may be required in certain cases. Only call this when needed, as CPU-GPU synchronization has a performance cost.
			</description>
		</method>
		<method name="free_rid">
			<return type="void" />
			<param index="0" name="rid" type="RID" />
			<description>
				Tries to free an object in the RenderingServer. To avoid memory leaks, this should be called after using an object as memory management does not occur automatically when using RenderingServer directly.
			</description>
		</method>
		<method name="get_current_rendering_driver_name" qualifiers="const">
			<return type="String" />
			<description>
				Returns the name of the current rendering driver. This can be [code]vulkan[/code], [code]d3d12[/code], [code]metal[/code], [code]opengl3[/code], [code]opengl3_es[/code], or [code]opengl3_angle[/code]. See also [method get_current_rendering_method].
				The rendering driver is determined by [member ProjectSettings.rendering/rendering_device/driver], the [code]--rendering-driver[/code] command line argument that overrides this project setting, or an automatic fallback that is applied depending on the hardware.
			</description>
		</method>
		<method name="get_current_rendering_method" qualifiers="const">
			<return type="String" />
			<description>
				Returns the name of the current rendering method. This can be [code]forward_plus[/code], [code]mobile[/code], or [code]gl_compatibility[/code]. See also [method get_current_rendering_driver_name].
				The rendering method is determined by [member ProjectSettings.rendering/renderer/rendering_method], the [code]--rendering-method[/code] command line argument that overrides this project setting, or an automatic fallback that is applied depending on the hardware.
			</description>
		</method>
		<method name="get_default_clear_color">
			<return type="Color" />
			<description>
				Returns the default clear color which is used when a specific clear color has not been selected. See also [method set_default_clear_color].
			</description>
		</method>
		<method name="get_frame_setup_time_cpu" qualifiers="const">
			<return type="float" />
			<description>
				Returns the time taken to setup rendering on the CPU in milliseconds. This value is shared across all viewports and does [i]not[/i] require [method viewport_set_measure_render_time] to be enabled on a viewport to be queried. See also [method viewport_get_measured_render_time_cpu].
			</description>
		</method>
		<method name="get_rendering_device" qualifiers="const">
			<return type="RenderingDevice" />
			<description>
				Returns the global RenderingDevice.
				[b]Note:[/b] When using the OpenGL rendering driver or when running in headless mode, this function always returns [code]null[/code].
			</description>
		</method>
		<method name="get_rendering_info">
			<return type="int" />
			<param index="0" name="info" type="int" enum="RenderingServer.RenderingInfo" />
			<description>
				Returns a statistic about the rendering engine which can be used for performance profiling. See [enum RenderingServer.RenderingInfo] for a list of values that can be queried. See also [method viewport_get_render_info], which returns information specific to a viewport.
				[b]Note:[/b] Only 3D rendering is currently taken into account by some of these values, such as the number of draw calls.
				[b]Note:[/b] Rendering information is not available until at least 2 frames have been rendered by the engine. If rendering information is not available, [method get_rendering_info] returns [code]0[/code]. To print rendering information in [code]_ready()[/code] successfully, use the following:
				[codeblock]
				func _ready():
				    for _i in 2:
				        await get_tree().process_frame

				    print(RenderingServer.get_rendering_info(RENDERING_INFO_TOTAL_DRAW_CALLS_IN_FRAME))
				[/codeblock]
			</description>
		</method>
		<method name="get_shader_parameter_list" qualifiers="const">
			<return type="Dictionary[]" />
			<param index="0" name="shader" type="RID" />
			<description>
				Returns the parameters of a shader.
			</description>
		</method>
		<method name="get_test_cube">
			<return type="RID" />
			<description>
				Returns the RID of the test cube. This mesh will be created and returned on the first call to [method get_test_cube], then it will be cached for subsequent calls. See also [method make_sphere_mesh].
			</description>
		</method>
		<method name="get_test_texture">
			<return type="RID" />
			<description>
				Returns the RID of a 256×256 texture with a testing pattern on it (in [constant Image.FORMAT_RGB8] format). This texture will be created and returned on the first call to [method get_test_texture], then it will be cached for subsequent calls. See also [method get_white_texture].
				[b]Example:[/b] Get the test texture and apply it to a [Sprite2D] node:
				[codeblock]
				var texture_rid = RenderingServer.get_test_texture()
				var texture = ImageTexture.create_from_image(RenderingServer.texture_2d_get(texture_rid))
				$Sprite2D.texture = texture
				[/codeblock]
			</description>
		</method>
		<method name="get_video_adapter_api_version" qualifiers="const">
			<return type="String" />
			<description>
				Returns the version of the graphics video adapter [i]currently in use[/i] (e.g. "1.2.189" for Vulkan, "3.3.0 NVIDIA 510.60.02" for OpenGL). This version may be different from the actual latest version supported by the hardware, as Redot may not always request the latest version. See also [method OS.get_video_adapter_driver_info].
				[b]Note:[/b] When running a headless or server binary, this function returns an empty string.
			</description>
		</method>
		<method name="get_video_adapter_name" qualifiers="const">
			<return type="String" />
			<description>
				Returns the name of the video adapter (e.g. "GeForce GTX 1080/PCIe/SSE2").
				[b]Note:[/b] When running a headless or server binary, this function returns an empty string.
				[b]Note:[/b] On the web platform, some browsers such as Firefox may report a different, fixed GPU name such as "GeForce GTX 980" (regardless of the user's actual GPU model). This is done to make fingerprinting more difficult.
			</description>
		</method>
		<method name="get_video_adapter_type" qualifiers="const">
			<return type="int" enum="RenderingDevice.DeviceType" />
			<description>
				Returns the type of the video adapter. Since dedicated graphics cards from a given generation will [i]usually[/i] be significantly faster than integrated graphics made in the same generation, the device type can be used as a basis for automatic graphics settings adjustment. However, this is not always true, so make sure to provide users with a way to manually override graphics settings.
				[b]Note:[/b] When using the OpenGL rendering driver or when running in headless mode, this function always returns [constant RenderingDevice.DEVICE_TYPE_OTHER].
			</description>
		</method>
		<method name="get_video_adapter_vendor" qualifiers="const">
			<return type="String" />
			<description>
				Returns the vendor of the video adapter (e.g. "NVIDIA Corporation").
				[b]Note:[/b] When running a headless or server binary, this function returns an empty string.
			</description>
		</method>
		<method name="get_white_texture">
			<return type="RID" />
			<description>
				Returns the ID of a 4×4 white texture (in [constant Image.FORMAT_RGB8] format). This texture will be created and returned on the first call to [method get_white_texture], then it will be cached for subsequent calls. See also [method get_test_texture].
				[b]Example:[/b] Get the white texture and apply it to a [Sprite2D] node:
				[codeblock]
				var texture_rid = RenderingServer.get_white_texture()
				var texture = ImageTexture.create_from_image(RenderingServer.texture_2d_get(texture_rid))
				$Sprite2D.texture = texture
				[/codeblock]
			</description>
		</method>
		<method name="gi_set_use_half_resolution">
			<return type="void" />
			<param index="0" name="half_resolution" type="bool" />
			<description>
				If [param half_resolution] is [code]true[/code], renders [VoxelGI] and SDFGI ([member Environment.sdfgi_enabled]) buffers at halved resolution on each axis (e.g. 960×540 when the viewport size is 1920×1080). This improves performance significantly when VoxelGI or SDFGI is enabled, at the cost of artifacts that may be visible on polygon edges. The loss in quality becomes less noticeable as the viewport resolution increases. [LightmapGI] rendering is not affected by this setting. Equivalent to [member ProjectSettings.rendering/global_illumination/gi/use_half_resolution].
			</description>
		</method>
		<method name="global_shader_parameter_add">
			<return type="void" />
			<param index="0" name="name" type="StringName" />
			<param index="1" name="type" type="int" enum="RenderingServer.GlobalShaderParameterType" />
			<param index="2" name="default_value" type="Variant" />
			<description>
				Creates a new global shader uniform.
				[b]Note:[/b] Global shader parameter names are case-sensitive.
			</description>
		</method>
		<method name="global_shader_parameter_get" qualifiers="const">
			<return type="Variant" />
			<param index="0" name="name" type="StringName" />
			<description>
				Returns the value of the global shader uniform specified by [param name].
				[b]Note:[/b] [method global_shader_parameter_get] has a large performance penalty as the rendering thread needs to synchronize with the calling thread, which is slow. Do not use this method during gameplay to avoid stuttering. If you need to read values in a script after setting them, consider creating an autoload where you store the values you need to query at the same time you're setting them as global parameters.
			</description>
		</method>
		<method name="global_shader_parameter_get_list" qualifiers="const">
			<return type="StringName[]" />
			<description>
				Returns the list of global shader uniform names.
				[b]Note:[/b] [method global_shader_parameter_get] has a large performance penalty as the rendering thread needs to synchronize with the calling thread, which is slow. Do not use this method during gameplay to avoid stuttering. If you need to read values in a script after setting them, consider creating an autoload where you store the values you need to query at the same time you're setting them as global parameters.
			</description>
		</method>
		<method name="global_shader_parameter_get_type" qualifiers="const">
			<return type="int" enum="RenderingServer.GlobalShaderParameterType" />
			<param index="0" name="name" type="StringName" />
			<description>
				Returns the type associated to the global shader uniform specified by [param name].
				[b]Note:[/b] [method global_shader_parameter_get] has a large performance penalty as the rendering thread needs to synchronize with the calling thread, which is slow. Do not use this method during gameplay to avoid stuttering. If you need to read values in a script after setting them, consider creating an autoload where you store the values you need to query at the same time you're setting them as global parameters.
			</description>
		</method>
		<method name="global_shader_parameter_remove">
			<return type="void" />
			<param index="0" name="name" type="StringName" />
			<description>
				Removes the global shader uniform specified by [param name].
			</description>
		</method>
		<method name="global_shader_parameter_set">
			<return type="void" />
			<param index="0" name="name" type="StringName" />
			<param index="1" name="value" type="Variant" />
			<description>
				Sets the global shader uniform [param name] to [param value].
			</description>
		</method>
		<method name="global_shader_parameter_set_override">
			<return type="void" />
			<param index="0" name="name" type="StringName" />
			<param index="1" name="value" type="Variant" />
			<description>
				Overrides the global shader uniform [param name] with [param value]. Equivalent to the [ShaderGlobalsOverride] node.
			</description>
		</method>
		<method name="has_changed" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if changes have been made to the RenderingServer's data. [method force_draw] is usually called if this happens.
			</description>
		</method>
		<method name="has_feature" qualifiers="const" deprecated="This method has not been used since Redot 3.0.">
			<return type="bool" />
			<param index="0" name="feature" type="int" enum="RenderingServer.Features" />
			<description>
				This method does nothing and always returns [code]false[/code].
			</description>
		</method>
		<method name="has_os_feature" qualifiers="const">
			<return type="bool" />
			<param index="0" name="feature" type="String" />
			<description>
				Returns [code]true[/code] if the OS supports a certain [param feature]. Features might be [code]s3tc[/code], [code]etc[/code], and [code]etc2[/code].
			</description>
		</method>
		<method name="instance_attach_object_instance_id">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="id" type="int" />
			<description>
				Attaches a unique Object ID to instance. Object ID must be attached to instance for proper culling with [method instances_cull_aabb], [method instances_cull_convex], and [method instances_cull_ray].
			</description>
		</method>
		<method name="instance_attach_skeleton">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="skeleton" type="RID" />
			<description>
				Attaches a skeleton to an instance. Removes the previous skeleton from the instance.
			</description>
		</method>
		<method name="instance_create">
			<return type="RID" />
			<description>
				Creates a visual instance and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]instance_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				An instance is a way of placing a 3D object in the scenario. Objects like particles, meshes, reflection probes and decals need to be associated with an instance to be visible in the scenario using [method instance_set_base].
				[b]Note:[/b] The equivalent node is [VisualInstance3D].
			</description>
		</method>
		<method name="instance_create2">
			<return type="RID" />
			<param index="0" name="base" type="RID" />
			<param index="1" name="scenario" type="RID" />
			<description>
				Creates a visual instance, adds it to the RenderingServer, and sets both base and scenario. It can be accessed with the RID that is returned. This RID will be used in all [code]instance_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method. This is a shorthand for using [method instance_create] and setting the base and scenario manually.
			</description>
		</method>
		<method name="instance_geometry_get_shader_parameter" qualifiers="const">
			<return type="Variant" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="parameter" type="StringName" />
			<description>
				Returns the value of the per-instance shader uniform from the specified 3D geometry instance. Equivalent to [method GeometryInstance3D.get_instance_shader_parameter].
				[b]Note:[/b] Per-instance shader parameter names are case-sensitive.
			</description>
		</method>
		<method name="instance_geometry_get_shader_parameter_default_value" qualifiers="const">
			<return type="Variant" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="parameter" type="StringName" />
			<description>
				Returns the default value of the per-instance shader uniform from the specified 3D geometry instance. Equivalent to [method GeometryInstance3D.get_instance_shader_parameter].
			</description>
		</method>
		<method name="instance_geometry_get_shader_parameter_list" qualifiers="const">
			<return type="Dictionary[]" />
			<param index="0" name="instance" type="RID" />
			<description>
				Returns a dictionary of per-instance shader uniform names of the per-instance shader uniform from the specified 3D geometry instance. The returned dictionary is in PropertyInfo format, with the keys [code]name[/code], [code]class_name[/code], [code]type[/code], [code]hint[/code], [code]hint_string[/code] and [code]usage[/code]. Equivalent to [method GeometryInstance3D.get_instance_shader_parameter].
			</description>
		</method>
		<method name="instance_geometry_set_cast_shadows_setting">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="shadow_casting_setting" type="int" enum="RenderingServer.ShadowCastingSetting" />
			<description>
				Sets the shadow casting setting to one of [enum ShadowCastingSetting]. Equivalent to [member GeometryInstance3D.cast_shadow].
			</description>
		</method>
		<method name="instance_geometry_set_flag">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="flag" type="int" enum="RenderingServer.InstanceFlags" />
			<param index="2" name="enabled" type="bool" />
			<description>
				Sets the flag for a given [enum InstanceFlags]. See [enum InstanceFlags] for more details.
			</description>
		</method>
		<method name="instance_geometry_set_lightmap">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="lightmap" type="RID" />
			<param index="2" name="lightmap_uv_scale" type="Rect2" />
			<param index="3" name="lightmap_slice" type="int" />
			<description>
				Sets the lightmap GI instance to use for the specified 3D geometry instance. The lightmap UV scale for the specified instance (equivalent to [member GeometryInstance3D.gi_lightmap_scale]) and lightmap atlas slice must also be specified.
			</description>
		</method>
		<method name="instance_geometry_set_lod_bias">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="lod_bias" type="float" />
			<description>
				Sets the level of detail bias to use when rendering the specified 3D geometry instance. Higher values result in higher detail from further away. Equivalent to [member GeometryInstance3D.lod_bias].
			</description>
		</method>
		<method name="instance_geometry_set_material_overlay">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="material" type="RID" />
			<description>
				Sets a material that will be rendered for all surfaces on top of active materials for the mesh associated with this instance. Equivalent to [member GeometryInstance3D.material_overlay].
			</description>
		</method>
		<method name="instance_geometry_set_material_override">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="material" type="RID" />
			<description>
				Sets a material that will override the material for all surfaces on the mesh associated with this instance. Equivalent to [member GeometryInstance3D.material_override].
			</description>
		</method>
		<method name="instance_geometry_set_shader_parameter">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="parameter" type="StringName" />
			<param index="2" name="value" type="Variant" />
			<description>
				Sets the per-instance shader uniform on the specified 3D geometry instance. Equivalent to [method GeometryInstance3D.set_instance_shader_parameter].
			</description>
		</method>
		<method name="instance_geometry_set_transparency">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="transparency" type="float" />
			<description>
				Sets the transparency for the given geometry instance. Equivalent to [member GeometryInstance3D.transparency].
				A transparency of [code]0.0[/code] is fully opaque, while [code]1.0[/code] is fully transparent. Values greater than [code]0.0[/code] (exclusive) will force the geometry's materials to go through the transparent pipeline, which is slower to render and can exhibit rendering issues due to incorrect transparency sorting. However, unlike using a transparent material, setting [param transparency] to a value greater than [code]0.0[/code] (exclusive) will [i]not[/i] disable shadow rendering.
				In spatial shaders, [code]1.0 - transparency[/code] is set as the default value of the [code]ALPHA[/code] built-in.
				[b]Note:[/b] [param transparency] is clamped between [code]0.0[/code] and [code]1.0[/code], so this property cannot be used to make transparent materials more opaque than they originally are.
			</description>
		</method>
		<method name="instance_geometry_set_visibility_range">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="min" type="float" />
			<param index="2" name="max" type="float" />
			<param index="3" name="min_margin" type="float" />
			<param index="4" name="max_margin" type="float" />
			<param index="5" name="fade_mode" type="int" enum="RenderingServer.VisibilityRangeFadeMode" />
			<description>
				Sets the visibility range values for the given geometry instance. Equivalent to [member GeometryInstance3D.visibility_range_begin] and related properties.
			</description>
		</method>
		<method name="instance_set_base">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="base" type="RID" />
			<description>
				Sets the base of the instance. A base can be any of the 3D objects that are created in the RenderingServer that can be displayed. For example, any of the light types, mesh, multimesh, particle system, reflection probe, decal, lightmap, voxel GI and visibility notifiers are all types that can be set as the base of an instance in order to be displayed in the scenario.
			</description>
		</method>
		<method name="instance_set_blend_shape_weight">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="shape" type="int" />
			<param index="2" name="weight" type="float" />
			<description>
				Sets the weight for a given blend shape associated with this instance.
			</description>
		</method>
		<method name="instance_set_custom_aabb">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="aabb" type="AABB" />
			<description>
				Sets a custom AABB to use when culling objects from the view frustum. Equivalent to setting [member GeometryInstance3D.custom_aabb].
			</description>
		</method>
		<method name="instance_set_extra_visibility_margin">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="margin" type="float" />
			<description>
				Sets a margin to increase the size of the AABB when culling objects from the view frustum. This allows you to avoid culling objects that fall outside the view frustum. Equivalent to [member GeometryInstance3D.extra_cull_margin].
			</description>
		</method>
		<method name="instance_set_ignore_culling">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="enabled" type="bool" />
			<description>
				If [code]true[/code], ignores both frustum and occlusion culling on the specified 3D geometry instance. This is not the same as [member GeometryInstance3D.ignore_occlusion_culling], which only ignores occlusion culling and leaves frustum culling intact.
			</description>
		</method>
		<method name="instance_set_layer_mask">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="mask" type="int" />
			<description>
				Sets the render layers that this instance will be drawn to. Equivalent to [member VisualInstance3D.layers].
			</description>
		</method>
		<method name="instance_set_pivot_data">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="sorting_offset" type="float" />
			<param index="2" name="use_aabb_center" type="bool" />
			<description>
				Sets the sorting offset and switches between using the bounding box or instance origin for depth sorting.
			</description>
		</method>
		<method name="instance_set_scenario">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="scenario" type="RID" />
			<description>
				Sets the scenario that the instance is in. The scenario is the 3D world that the objects will be displayed in.
			</description>
		</method>
		<method name="instance_set_surface_override_material">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="surface" type="int" />
			<param index="2" name="material" type="RID" />
			<description>
				Sets the override material of a specific surface. Equivalent to [method MeshInstance3D.set_surface_override_material].
			</description>
		</method>
		<method name="instance_set_transform">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="transform" type="Transform3D" />
			<description>
				Sets the world space transform of the instance. Equivalent to [member Node3D.global_transform].
			</description>
		</method>
		<method name="instance_set_visibility_parent">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="parent" type="RID" />
			<description>
				Sets the visibility parent for the given instance. Equivalent to [member Node3D.visibility_parent].
			</description>
		</method>
		<method name="instance_set_visible">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<param index="1" name="visible" type="bool" />
			<description>
				Sets whether an instance is drawn or not. Equivalent to [member Node3D.visible].
			</description>
		</method>
		<method name="instance_teleport">
			<return type="void" />
			<param index="0" name="instance" type="RID" />
			<description>
				Resets motion vectors and other interpolated values. Use this [i]after[/i] teleporting a mesh from one position to another to avoid ghosting artifacts.
			</description>
		</method>
		<method name="instances_cull_aabb" qualifiers="const">
			<return type="PackedInt64Array" />
			<param index="0" name="aabb" type="AABB" />
			<param index="1" name="scenario" type="RID" default="RID()" />
			<description>
				Returns an array of object IDs intersecting with the provided AABB. Only 3D nodes that inherit from [VisualInstance3D] are considered, such as [MeshInstance3D] or [DirectionalLight3D]. Use [method @GlobalScope.instance_from_id] to obtain the actual nodes. A scenario RID must be provided, which is available in the [World3D] you want to query. This forces an update for all resources queued to update.
				[b]Warning:[/b] This function is primarily intended for editor usage. For in-game use cases, prefer physics collision.
			</description>
		</method>
		<method name="instances_cull_convex" qualifiers="const">
			<return type="PackedInt64Array" />
			<param index="0" name="convex" type="Plane[]" />
			<param index="1" name="scenario" type="RID" default="RID()" />
			<description>
				Returns an array of object IDs intersecting with the provided convex shape. Only 3D nodes that inherit from [VisualInstance3D] are considered, such as [MeshInstance3D] or [DirectionalLight3D]. Use [method @GlobalScope.instance_from_id] to obtain the actual nodes. A scenario RID must be provided, which is available in the [World3D] you want to query. This forces an update for all resources queued to update.
				[b]Warning:[/b] This function is primarily intended for editor usage. For in-game use cases, prefer physics collision.
			</description>
		</method>
		<method name="instances_cull_ray" qualifiers="const">
			<return type="PackedInt64Array" />
			<param index="0" name="from" type="Vector3" />
			<param index="1" name="to" type="Vector3" />
			<param index="2" name="scenario" type="RID" default="RID()" />
			<description>
				Returns an array of object IDs intersecting with the provided 3D ray. Only 3D nodes that inherit from [VisualInstance3D] are considered, such as [MeshInstance3D] or [DirectionalLight3D]. Use [method @GlobalScope.instance_from_id] to obtain the actual nodes. A scenario RID must be provided, which is available in the [World3D] you want to query. This forces an update for all resources queued to update.
				[b]Warning:[/b] This function is primarily intended for editor usage. For in-game use cases, prefer physics collision.
			</description>
		</method>
		<method name="is_on_render_thread">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if our code is currently executing on the rendering thread.
			</description>
		</method>
		<method name="light_directional_set_blend_splits">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="enable" type="bool" />
			<description>
				If [code]true[/code], this directional light will blend between shadow map splits resulting in a smoother transition between them. Equivalent to [member DirectionalLight3D.directional_shadow_blend_splits].
			</description>
		</method>
		<method name="light_directional_set_shadow_mode">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="mode" type="int" enum="RenderingServer.LightDirectionalShadowMode" />
			<description>
				Sets the shadow mode for this directional light. Equivalent to [member DirectionalLight3D.directional_shadow_mode]. See [enum LightDirectionalShadowMode] for options.
			</description>
		</method>
		<method name="light_directional_set_sky_mode">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="mode" type="int" enum="RenderingServer.LightDirectionalSkyMode" />
			<description>
				If [code]true[/code], this light will not be used for anything except sky shaders. Use this for lights that impact your sky shader that you may want to hide from affecting the rest of the scene. For example, you may want to enable this when the sun in your sky shader falls below the horizon.
			</description>
		</method>
		<method name="light_omni_set_shadow_mode">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="mode" type="int" enum="RenderingServer.LightOmniShadowMode" />
			<description>
				Sets whether to use a dual paraboloid or a cubemap for the shadow map. Dual paraboloid is faster but may suffer from artifacts. Equivalent to [member OmniLight3D.omni_shadow_mode].
			</description>
		</method>
		<method name="light_projectors_set_filter">
			<return type="void" />
			<param index="0" name="filter" type="int" enum="RenderingServer.LightProjectorFilter" />
			<description>
				Sets the texture filter mode to use when rendering light projectors. This parameter is global and cannot be set on a per-light basis.
			</description>
		</method>
		<method name="light_set_bake_mode">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="bake_mode" type="int" enum="RenderingServer.LightBakeMode" />
			<description>
				Sets the bake mode to use for the specified 3D light. Equivalent to [member Light3D.light_bake_mode].
			</description>
		</method>
		<method name="light_set_color">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="color" type="Color" />
			<description>
				Sets the color of the light. Equivalent to [member Light3D.light_color].
			</description>
		</method>
		<method name="light_set_cull_mask">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="mask" type="int" />
			<description>
				Sets the cull mask for this 3D light. Lights only affect objects in the selected layers. Equivalent to [member Light3D.light_cull_mask].
			</description>
		</method>
		<method name="light_set_distance_fade" keywords="light_set_lod">
			<return type="void" />
			<param index="0" name="decal" type="RID" />
			<param index="1" name="enabled" type="bool" />
			<param index="2" name="begin" type="float" />
			<param index="3" name="shadow" type="float" />
			<param index="4" name="length" type="float" />
			<description>
				Sets the distance fade for this 3D light. This acts as a form of level of detail (LOD) and can be used to improve performance. Equivalent to [member Light3D.distance_fade_enabled], [member Light3D.distance_fade_begin], [member Light3D.distance_fade_shadow], and [member Light3D.distance_fade_length].
			</description>
		</method>
		<method name="light_set_max_sdfgi_cascade">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="cascade" type="int" />
			<description>
				Sets the maximum SDFGI cascade in which the 3D light's indirect lighting is rendered. Higher values allow the light to be rendered in SDFGI further away from the camera.
			</description>
		</method>
		<method name="light_set_negative">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="enable" type="bool" />
			<description>
				If [code]true[/code], the 3D light will subtract light instead of adding light. Equivalent to [member Light3D.light_negative].
			</description>
		</method>
		<method name="light_set_param">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="param" type="int" enum="RenderingServer.LightParam" />
			<param index="2" name="value" type="float" />
			<description>
				Sets the specified 3D light parameter. See [enum LightParam] for options. Equivalent to [method Light3D.set_param].
			</description>
		</method>
		<method name="light_set_projector" keywords="light_set_cookie">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="texture" type="RID" />
			<description>
				Sets the projector texture to use for the specified 3D light. Equivalent to [member Light3D.light_projector].
			</description>
		</method>
		<method name="light_set_reverse_cull_face_mode">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="enabled" type="bool" />
			<description>
				If [code]true[/code], reverses the backface culling of the mesh. This can be useful when you have a flat mesh that has a light behind it. If you need to cast a shadow on both sides of the mesh, set the mesh to use double-sided shadows with [method instance_geometry_set_cast_shadows_setting]. Equivalent to [member Light3D.shadow_reverse_cull_face].
			</description>
		</method>
		<method name="light_set_shadow">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="enabled" type="bool" />
			<description>
				If [code]true[/code], light will cast shadows. Equivalent to [member Light3D.shadow_enabled].
			</description>
		</method>
		<method name="light_set_shadow_caster_mask">
			<return type="void" />
			<param index="0" name="light" type="RID" />
			<param index="1" name="mask" type="int" />
			<description>
				Sets the shadow caster mask for this 3D light. Shadows will only be cast using objects in the selected layers. Equivalent to [member Light3D.shadow_caster_mask].
			</description>
		</method>
		<method name="lightmap_create">
			<return type="RID" />
			<description>
				Creates a new lightmap global illumination instance and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]lightmap_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				[b]Note:[/b] The equivalent node is [LightmapGI].
			</description>
		</method>
		<method name="lightmap_get_probe_capture_bsp_tree" qualifiers="const">
			<return type="PackedInt32Array" />
			<param index="0" name="lightmap" type="RID" />
			<description>
			</description>
		</method>
		<method name="lightmap_get_probe_capture_points" qualifiers="const">
			<return type="PackedVector3Array" />
			<param index="0" name="lightmap" type="RID" />
			<description>
			</description>
		</method>
		<method name="lightmap_get_probe_capture_sh" qualifiers="const">
			<return type="PackedColorArray" />
			<param index="0" name="lightmap" type="RID" />
			<description>
			</description>
		</method>
		<method name="lightmap_get_probe_capture_tetrahedra" qualifiers="const">
			<return type="PackedInt32Array" />
			<param index="0" name="lightmap" type="RID" />
			<description>
			</description>
		</method>
		<method name="lightmap_set_baked_exposure_normalization">
			<return type="void" />
			<param index="0" name="lightmap" type="RID" />
			<param index="1" name="baked_exposure" type="float" />
			<description>
				Used to inform the renderer what exposure normalization value was used while baking the lightmap. This value will be used and modulated at run time to ensure that the lightmap maintains a consistent level of exposure even if the scene-wide exposure normalization is changed at run time. For more information see [method camera_attributes_set_exposure].
			</description>
		</method>
		<method name="lightmap_set_probe_bounds">
			<return type="void" />
			<param index="0" name="lightmap" type="RID" />
			<param index="1" name="bounds" type="AABB" />
			<description>
			</description>
		</method>
		<method name="lightmap_set_probe_capture_data">
			<return type="void" />
			<param index="0" name="lightmap" type="RID" />
			<param index="1" name="points" type="PackedVector3Array" />
			<param index="2" name="point_sh" type="PackedColorArray" />
			<param index="3" name="tetrahedra" type="PackedInt32Array" />
			<param index="4" name="bsp_tree" type="PackedInt32Array" />
			<description>
			</description>
		</method>
		<method name="lightmap_set_probe_capture_update_speed">
			<return type="void" />
			<param index="0" name="speed" type="float" />
			<description>
			</description>
		</method>
		<method name="lightmap_set_probe_interior">
			<return type="void" />
			<param index="0" name="lightmap" type="RID" />
			<param index="1" name="interior" type="bool" />
			<description>
			</description>
		</method>
		<method name="lightmap_set_textures">
			<return type="void" />
			<param index="0" name="lightmap" type="RID" />
			<param index="1" name="light" type="RID" />
			<param index="2" name="uses_sh" type="bool" />
			<description>
				Set the textures on the given [param lightmap] GI instance to the texture array pointed to by the [param light] RID. If the lightmap texture was baked with [member LightmapGI.directional] set to [code]true[/code], then [param uses_sh] must also be [code]true[/code].
			</description>
		</method>
		<method name="lightmaps_set_bicubic_filter">
			<return type="void" />
			<param index="0" name="enable" type="bool" />
			<description>
				Toggles whether a bicubic filter should be used when lightmaps are sampled. This smoothens their appearance at a performance cost.
			</description>
		</method>
		<method name="make_sphere_mesh">
			<return type="RID" />
			<param index="0" name="latitudes" type="int" />
			<param index="1" name="longitudes" type="int" />
			<param index="2" name="radius" type="float" />
			<description>
				Returns a mesh of a sphere with the given number of horizontal subdivisions, vertical subdivisions and radius. See also [method get_test_cube].
			</description>
		</method>
		<method name="material_create">
			<return type="RID" />
			<description>
				Creates an empty material and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]material_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				[b]Note:[/b] The equivalent resource is [Material].
			</description>
		</method>
		<method name="material_get_param" qualifiers="const">
			<return type="Variant" />
			<param index="0" name="material" type="RID" />
			<param index="1" name="parameter" type="StringName" />
			<description>
				Returns the value of a certain material's parameter.
			</description>
		</method>
		<method name="material_set_next_pass">
			<return type="void" />
			<param index="0" name="material" type="RID" />
			<param index="1" name="next_material" type="RID" />
			<description>
				Sets an object's next material.
			</description>
		</method>
		<method name="material_set_param">
			<return type="void" />
			<param index="0" name="material" type="RID" />
			<param index="1" name="parameter" type="StringName" />
			<param index="2" name="value" type="Variant" />
			<description>
				Sets a material's parameter.
			</description>
		</method>
		<method name="material_set_render_priority">
			<return type="void" />
			<param index="0" name="material" type="RID" />
			<param index="1" name="priority" type="int" />
			<description>
				Sets a material's render priority.
			</description>
		</method>
		<method name="material_set_shader">
			<return type="void" />
			<param index="0" name="shader_material" type="RID" />
			<param index="1" name="shader" type="RID" />
			<description>
				Sets a shader material's shader.
			</description>
		</method>
		<method name="mesh_add_surface">
			<return type="void" />
			<param index="0" name="mesh" type="RID" />
			<param index="1" name="surface" type="Dictionary" />
			<description>
			</description>
		</method>
		<method name="mesh_add_surface_from_arrays">
			<return type="void" />
			<param index="0" name="mesh" type="RID" />
			<param index="1" name="primitive" type="int" enum="RenderingServer.PrimitiveType" />
			<param index="2" name="arrays" type="Array" />
			<param index="3" name="blend_shapes" type="Array" default="[]" />
			<param index="4" name="lods" type="Dictionary" default="{}" />
			<param index="5" name="compress_format" type="int" enum="RenderingServer.ArrayFormat" is_bitfield="true" default="0" />
			<description>
			</description>
		</method>
		<method name="mesh_clear">
			<return type="void" />
			<param index="0" name="mesh" type="RID" />
			<description>
				Removes all surfaces from a mesh.
			</description>
		</method>
		<method name="mesh_create">
			<return type="RID" />
			<description>
				Creates a new mesh and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]mesh_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				To place in a scene, attach this mesh to an instance using [method instance_set_base] using the returned RID.
				[b]Note:[/b] The equivalent resource is [Mesh].
			</description>
		</method>
		<method name="mesh_create_from_surfaces">
			<return type="RID" />
			<param index="0" name="surfaces" type="Dictionary[]" />
			<param index="1" name="blend_shape_count" type="int" default="0" />
			<description>
			</description>
		</method>
		<method name="mesh_get_blend_shape_count" qualifiers="const">
			<return type="int" />
			<param index="0" name="mesh" type="RID" />
			<description>
				Returns a mesh's blend shape count.
			</description>
		</method>
		<method name="mesh_get_blend_shape_mode" qualifiers="const">
			<return type="int" enum="RenderingServer.BlendShapeMode" />
			<param index="0" name="mesh" type="RID" />
			<description>
				Returns a mesh's blend shape mode.
			</description>
		</method>
		<method name="mesh_get_custom_aabb" qualifiers="const">
			<return type="AABB" />
			<param index="0" name="mesh" type="RID" />
			<description>
				Returns a mesh's custom aabb.
			</description>
		</method>
		<method name="mesh_get_surface">
			<return type="Dictionary" />
			<param index="0" name="mesh" type="RID" />
			<param index="1" name="surface" type="int" />
			<description>
			</description>
		</method>
		<method name="mesh_get_surface_count" qualifiers="const">
			<return type="int" />
			<param index="0" name="mesh" type="RID" />
			<description>
				Returns a mesh's number of surfaces.
			</description>
		</method>
		<method name="mesh_set_blend_shape_mode">
			<return type="void" />
			<param index="0" name="mesh" type="RID" />
			<param index="1" name="mode" type="int" enum="RenderingServer.BlendShapeMode" />
			<description>
				Sets a mesh's blend shape mode.
			</description>
		</method>
		<method name="mesh_set_custom_aabb">
			<return type="void" />
			<param index="0" name="mesh" type="RID" />
			<param index="1" name="aabb" type="AABB" />
			<description>
				Sets a mesh's custom aabb.
			</description>
		</method>
		<method name="mesh_set_shadow_mesh">
			<return type="void" />
			<param index="0" name="mesh" type="RID" />
			<param index="1" name="shadow_mesh" type="RID" />
			<description>
			</description>
		</method>
		<method name="mesh_surface_get_arrays" qualifiers="const">
			<return type="Array" />
			<param index="0" name="mesh" type="RID" />
			<param index="1" name="surface" type="int" />
			<description>
				Returns a mesh's surface's buffer arrays.
			</description>
		</method>
		<method name="mesh_surface_get_blend_shape_arrays" qualifiers="const">
			<return type="Array[]" />
			<param index="0" name="mesh" type="RID" />
			<param index="1" name="surface" type="int" />
			<description>
				Returns a mesh's surface's arrays for blend shapes.
			</description>
		</method>
		<method name="mesh_surface_get_format_attribute_stride" qualifiers="const">
			<return type="int" />
			<param index="0" name="format" type="int" enum="RenderingServer.ArrayFormat" is_bitfield="true" />
			<param index="1" name="vertex_count" type="int" />
			<description>
				Returns the stride of the attribute buffer for a mesh with given [param format].
			</description>
		</method>
		<method name="mesh_surface_get_format_normal_tangent_stride" qualifiers="const">
			<return type="int" />
			<param index="0" name="format" type="int" enum="RenderingServer.ArrayFormat" is_bitfield="true" />
			<param index="1" name="vertex_count" type="int" />
			<description>
				Returns the stride of the combined normals and tangents for a mesh with given [param format]. Note importantly that, while normals and tangents are in the vertex buffer with vertices, they are only interleaved with each other and so have a different stride than vertex positions.
			</description>
		</method>
		<method name="mesh_surface_get_format_offset" qualifiers="const">
			<return type="int" />
			<param index="0" name="format" type="int" enum="RenderingServer.ArrayFormat" is_bitfield="true" />
			<param index="1" name="vertex_count" type="int" />
			<param index="2" name="array_index" type="int" />
			<description>
				Returns the offset of a given attribute by [param array_index] in the start of its respective buffer.
			</description>
		</method>
		<method name="mesh_surface_get_format_skin_stride" qualifiers="const">
			<return type="int" />
			<param index="0" name="format" type="int" enum="RenderingServer.ArrayFormat" is_bitfield="true" />
			<param index="1" name="vertex_count" type="int" />
			<description>
				Returns the stride of the skin buffer for a mesh with given [param format].
			</description>
		</method>
		<method name="mesh_surface_get_format_vertex_stride" qualifiers="const">
			<return type="int" />
			<param index="0" name="format" type="int" enum="RenderingServer.ArrayFormat" is_bitfield="true" />
			<param index="1" name="vertex_count" type="int" />
			<description>
				Returns the stride of the vertex positions for a mesh with given [param format]. Note importantly that vertex positions are stored consecutively and are not interleaved with the other attributes in the vertex buffer (normals and tangents).
			</description>
		</method>
		<method name="mesh_surface_get_material" qualifiers="const">
			<return type="RID" />
			<param index="0" name="mesh" type="RID" />
			<param index="1" name="surface" type="int" />
			<description>
				Returns a mesh's surface's material.
			</description>
		</method>
		<method name="mesh_surface_remove">
			<return type="void" />
			<param index="0" name="mesh" type="RID" />
			<param index="1" name="surface" type="int" />
			<description>
				Removes the surface at the given index from the Mesh, shifting surfaces with higher index down by one.
			</description>
		</method>
		<method name="mesh_surface_set_material">
			<return type="void" />
			<param index="0" name="mesh" type="RID" />
			<param index="1" name="surface" type="int" />
			<param index="2" name="material" type="RID" />
			<description>
				Sets a mesh's surface's material.
			</description>
		</method>
		<method name="mesh_surface_update_attribute_region">
			<return type="void" />
			<param index="0" name="mesh" type="RID" />
			<param index="1" name="surface" type="int" />
			<param index="2" name="offset" type="int" />
			<param index="3" name="data" type="PackedByteArray" />
			<description>
			</description>
		</method>
		<method name="mesh_surface_update_skin_region">
			<return type="void" />
			<param index="0" name="mesh" type="RID" />
			<param index="1" name="surface" type="int" />
			<param index="2" name="offset" type="int" />
			<param index="3" name="data" type="PackedByteArray" />
			<description>
			</description>
		</method>
		<method name="mesh_surface_update_vertex_region">
			<return type="void" />
			<param index="0" name="mesh" type="RID" />
			<param index="1" name="surface" type="int" />
			<param index="2" name="offset" type="int" />
			<param index="3" name="data" type="PackedByteArray" />
			<description>
			</description>
		</method>
		<method name="multimesh_allocate_data">
			<return type="void" />
			<param index="0" name="multimesh" type="RID" />
			<param index="1" name="instances" type="int" />
			<param index="2" name="transform_format" type="int" enum="RenderingServer.MultimeshTransformFormat" />
			<param index="3" name="color_format" type="bool" default="false" />
			<param index="4" name="custom_data_format" type="bool" default="false" />
			<param index="5" name="use_indirect" type="bool" default="false" />
			<description>
			</description>
		</method>
		<method name="multimesh_create">
			<return type="RID" />
			<description>
				Creates a new multimesh on the RenderingServer and returns an [RID] handle. This RID will be used in all [code]multimesh_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				To place in a scene, attach this multimesh to an instance using [method instance_set_base] using the returned RID.
				[b]Note:[/b] The equivalent resource is [MultiMesh].
			</description>
		</method>
		<method name="multimesh_get_aabb" qualifiers="const">
			<return type="AABB" />
			<param index="0" name="multimesh" type="RID" />
			<description>
				Calculates and returns the axis-aligned bounding box that encloses all instances within the multimesh.
			</description>
		</method>
		<method name="multimesh_get_buffer" qualifiers="const">
			<return type="PackedFloat32Array" />
			<param index="0" name="multimesh" type="RID" />
			<description>
				Returns the MultiMesh data (such as instance transforms, colors, etc.). See [method multimesh_set_buffer] for details on the returned data.
				[b]Note:[/b] If the buffer is in the engine's internal cache, it will have to be fetched from GPU memory and possibly decompressed. This means [method multimesh_get_buffer] is potentially a slow operation and should be avoided whenever possible.
			</description>
		</method>
		<method name="multimesh_get_buffer_rd_rid" qualifiers="const">
			<return type="RID" />
			<param index="0" name="multimesh" type="RID" />
			<description>
				Returns the [RenderingDevice] [RID] handle of the [MultiMesh], which can be used as any other buffer on the Rendering Device.
			</description>
		</method>
		<method name="multimesh_get_command_buffer_rd_rid" qualifiers="const">
			<return type="RID" />
			<param index="0" name="multimesh" type="RID" />
			<description>
				Returns the [RenderingDevice] [RID] handle of the [MultiMesh] command buffer. This [RID] is only valid if [code]use_indirect[/code] is set to [code]true[/code] when allocating data through [method multimesh_allocate_data]. It can be used to directly modify the instance count via buffer.
				The data structure is dependent on both how many surfaces the mesh contains and whether it is indexed or not, the buffer has 5 integers in it, with the last unused if the mesh is not indexed.
				Each of the values in the buffer correspond to these options:
				[codeblock lang=text]
				Indexed:
				  0 - indexCount;
				  1 - instanceCount;
				  2 - firstIndex;
				  3 - vertexOffset;
				  4 - firstInstance;
				Non Indexed:
				  0 - vertexCount;
				  1 - instanceCount;
				  2 - firstVertex;
				  3 - firstInstance;
				  4 - unused;
				[/codeblock]
			</description>
		</method>
		<method name="multimesh_get_custom_aabb" qualifiers="const">
			<return type="AABB" />
			<param index="0" name="multimesh" type="RID" />
			<description>
				Returns the custom AABB defined for this MultiMesh resource.
			</description>
		</method>
		<method name="multimesh_get_instance_count" qualifiers="const">
			<return type="int" />
			<param index="0" name="multimesh" type="RID" />
			<description>
				Returns the number of instances allocated for this multimesh.
			</description>
		</method>
		<method name="multimesh_get_mesh" qualifiers="const">
			<return type="RID" />
			<param index="0" name="multimesh" type="RID" />
			<description>
				Returns the RID of the mesh that will be used in drawing this multimesh.
			</description>
		</method>
		<method name="multimesh_get_visible_instances" qualifiers="const">
			<return type="int" />
			<param index="0" name="multimesh" type="RID" />
			<description>
				Returns the number of visible instances for this multimesh.
			</description>
		</method>
		<method name="multimesh_instance_get_color" qualifiers="const">
			<return type="Color" />
			<param index="0" name="multimesh" type="RID" />
			<param index="1" name="index" type="int" />
			<description>
				Returns the color by which the specified instance will be modulated.
			</description>
		</method>
		<method name="multimesh_instance_get_custom_data" qualifiers="const">
			<return type="Color" />
			<param index="0" name="multimesh" type="RID" />
			<param index="1" name="index" type="int" />
			<description>
				Returns the custom data associated with the specified instance.
			</description>
		</method>
		<method name="multimesh_instance_get_transform" qualifiers="const">
			<return type="Transform3D" />
			<param index="0" name="multimesh" type="RID" />
			<param index="1" name="index" type="int" />
			<description>
				Returns the [Transform3D] of the specified instance.
			</description>
		</method>
		<method name="multimesh_instance_get_transform_2d" qualifiers="const">
			<return type="Transform2D" />
			<param index="0" name="multimesh" type="RID" />
			<param index="1" name="index" type="int" />
			<description>
				Returns the [Transform2D] of the specified instance. For use when the multimesh is set to use 2D transforms.
			</description>
		</method>
		<method name="multimesh_instance_reset_physics_interpolation">
			<return type="void" />
			<param index="0" name="multimesh" type="RID" />
			<param index="1" name="index" type="int" />
			<description>
				Prevents physics interpolation for the specified instance during the current physics tick.
				This is useful when moving an instance to a new location, to give an instantaneous change rather than interpolation from the previous location.
			</description>
		</method>
		<method name="multimesh_instance_set_color">
			<return type="void" />
			<param index="0" name="multimesh" type="RID" />
			<param index="1" name="index" type="int" />
			<param index="2" name="color" type="Color" />
			<description>
				Sets the color by which this instance will be modulated. Equivalent to [method MultiMesh.set_instance_color].
			</description>
		</method>
		<method name="multimesh_instance_set_custom_data">
			<return type="void" />
			<param index="0" name="multimesh" type="RID" />
			<param index="1" name="index" type="int" />
			<param index="2" name="custom_data" type="Color" />
			<description>
				Sets the custom data for this instance. Custom data is passed as a [Color], but is interpreted as a [code]vec4[/code] in the shader. Equivalent to [method MultiMesh.set_instance_custom_data].
			</description>
		</method>
		<method name="multimesh_instance_set_transform">
			<return type="void" />
			<param index="0" name="multimesh" type="RID" />
			<param index="1" name="index" type="int" />
			<param index="2" name="transform" type="Transform3D" />
			<description>
				Sets the [Transform3D] for this instance. Equivalent to [method MultiMesh.set_instance_transform].
			</description>
		</method>
		<method name="multimesh_instance_set_transform_2d">
			<return type="void" />
			<param index="0" name="multimesh" type="RID" />
			<param index="1" name="index" type="int" />
			<param index="2" name="transform" type="Transform2D" />
			<description>
				Sets the [Transform2D] for this instance. For use when multimesh is used in 2D. Equivalent to [method MultiMesh.set_instance_transform_2d].
			</description>
		</method>
		<method name="multimesh_set_buffer">
			<return type="void" />
			<param index="0" name="multimesh" type="RID" />
			<param index="1" name="buffer" type="PackedFloat32Array" />
			<description>
				Set the entire data to use for drawing the [param multimesh] at once to [param buffer] (such as instance transforms and colors). [param buffer]'s size must match the number of instances multiplied by the per-instance data size (which depends on the enabled MultiMesh fields). Otherwise, an error message is printed and nothing is rendered. See also [method multimesh_get_buffer].
				The per-instance data size and expected data order is:
				[codeblock lang=text]
				2D:
				  - Position: 8 floats (8 floats for Transform2D)
				  - Position + Vertex color: 12 floats (8 floats for Transform2D, 4 floats for Color)
				  - Position + Custom data: 12 floats (8 floats for Transform2D, 4 floats of custom data)
				  - Position + Vertex color + Custom data: 16 floats (8 floats for Transform2D, 4 floats for Color, 4 floats of custom data)
				3D:
				  - Position: 12 floats (12 floats for Transform3D)
				  - Position + Vertex color: 16 floats (12 floats for Transform3D, 4 floats for Color)
				  - Position + Custom data: 16 floats (12 floats for Transform3D, 4 floats of custom data)
				  - Position + Vertex color + Custom data: 20 floats (12 floats for Transform3D, 4 floats for Color, 4 floats of custom data)
				[/codeblock]
				Instance transforms are in row-major order. Specifically:
				- For [Transform2D] the float-order is: [code](x.x, y.x, padding_float, origin.x, x.y, y.y, padding_float, origin.y)[/code].
				- For [Transform3D] the float-order is: [code](basis.x.x, basis.y.x, basis.z.x, origin.x, basis.x.y, basis.y.y, basis.z.y, origin.y, basis.x.z, basis.y.z, basis.z.z, origin.z)[/code].
			</description>
		</method>
		<method name="multimesh_set_buffer_interpolated">
			<return type="void" />
			<param index="0" name="multimesh" type="RID" />
			<param index="1" name="buffer" type="PackedFloat32Array" />
			<param index="2" name="buffer_previous" type="PackedFloat32Array" />
			<description>
				Alternative version of [method multimesh_set_buffer] for use with physics interpolation.
				Takes both an array of current data and an array of data for the previous physics tick.
			</description>
		</method>
		<method name="multimesh_set_custom_aabb">
			<return type="void" />
			<param index="0" name="multimesh" type="RID" />
			<param index="1" name="aabb" type="AABB" />
			<description>
				Sets the custom AABB for this MultiMesh resource.
			</description>
		</method>
		<method name="multimesh_set_mesh">
			<return type="void" />
			<param index="0" name="multimesh" type="RID" />
			<param index="1" name="mesh" type="RID" />
			<description>
				Sets the mesh to be drawn by the multimesh. Equivalent to [member MultiMesh.mesh].
			</description>
		</method>
		<method name="multimesh_set_physics_interpolated">
			<return type="void" />
			<param index="0" name="multimesh" type="RID" />
			<param index="1" name="interpolated" type="bool" />
			<description>
				Turns on and off physics interpolation for this MultiMesh resource.
			</description>
		</method>
		<method name="multimesh_set_physics_interpolation_quality">
			<return type="void" />
			<param index="0" name="multimesh" type="RID" />
			<param index="1" name="quality" type="int" enum="RenderingServer.MultimeshPhysicsInterpolationQuality" />
			<description>
				Sets the physics interpolation quality for the [MultiMesh].
				A value of [constant MULTIMESH_INTERP_QUALITY_FAST] gives fast but low quality interpolation, a value of [constant MULTIMESH_INTERP_QUALITY_HIGH] gives slower but higher quality interpolation.
			</description>
		</method>
		<method name="multimesh_set_visible_instances">
			<return type="void" />
			<param index="0" name="multimesh" type="RID" />
			<param index="1" name="visible" type="int" />
			<description>
				Sets the number of instances visible at a given time. If -1, all instances that have been allocated are drawn. Equivalent to [member MultiMesh.visible_instance_count].
			</description>
		</method>
		<method name="occluder_create">
			<return type="RID" />
			<description>
				Creates an occluder instance and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]occluder_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				[b]Note:[/b] The equivalent resource is [Occluder3D] (not to be confused with the [OccluderInstance3D] node).
			</description>
		</method>
		<method name="occluder_set_mesh">
			<return type="void" />
			<param index="0" name="occluder" type="RID" />
			<param index="1" name="vertices" type="PackedVector3Array" />
			<param index="2" name="indices" type="PackedInt32Array" />
			<description>
				Sets the mesh data for the given occluder RID, which controls the shape of the occlusion culling that will be performed.
			</description>
		</method>
		<method name="omni_light_create">
			<return type="RID" />
			<description>
				Creates a new omni light and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID can be used in most [code]light_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				To place in a scene, attach this omni light to an instance using [method instance_set_base] using the returned RID.
				[b]Note:[/b] The equivalent node is [OmniLight3D].
			</description>
		</method>
		<method name="particles_collision_create">
			<return type="RID" />
			<description>
				Creates a new 3D GPU particle collision or attractor and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID can be used in most [code]particles_collision_*[/code] RenderingServer functions.
				[b]Note:[/b] The equivalent nodes are [GPUParticlesCollision3D] and [GPUParticlesAttractor3D].
			</description>
		</method>
		<method name="particles_collision_height_field_update">
			<return type="void" />
			<param index="0" name="particles_collision" type="RID" />
			<description>
				Requests an update for the 3D GPU particle collision heightfield. This may be automatically called by the 3D GPU particle collision heightfield depending on its [member GPUParticlesCollisionHeightField3D.update_mode].
			</description>
		</method>
		<method name="particles_collision_set_attractor_attenuation">
			<return type="void" />
			<param index="0" name="particles_collision" type="RID" />
			<param index="1" name="curve" type="float" />
			<description>
				Sets the attenuation [param curve] for the 3D GPU particles attractor specified by the [param particles_collision] RID. Only used for attractors, not colliders. Equivalent to [member GPUParticlesAttractor3D.attenuation].
			</description>
		</method>
		<method name="particles_collision_set_attractor_directionality">
			<return type="void" />
			<param index="0" name="particles_collision" type="RID" />
			<param index="1" name="amount" type="float" />
			<description>
				Sets the directionality [param amount] for the 3D GPU particles attractor specified by the [param particles_collision] RID. Only used for attractors, not colliders. Equivalent to [member GPUParticlesAttractor3D.directionality].
			</description>
		</method>
		<method name="particles_collision_set_attractor_strength">
			<return type="void" />
			<param index="0" name="particles_collision" type="RID" />
			<param index="1" name="strength" type="float" />
			<description>
				Sets the [param strength] for the 3D GPU particles attractor specified by the [param particles_collision] RID. Only used for attractors, not colliders. Equivalent to [member GPUParticlesAttractor3D.strength].
			</description>
		</method>
		<method name="particles_collision_set_box_extents">
			<return type="void" />
			<param index="0" name="particles_collision" type="RID" />
			<param index="1" name="extents" type="Vector3" />
			<description>
				Sets the [param extents] for the 3D GPU particles collision by the [param particles_collision] RID. Equivalent to [member GPUParticlesCollisionBox3D.size], [member GPUParticlesCollisionSDF3D.size], [member GPUParticlesCollisionHeightField3D.size], [member GPUParticlesAttractorBox3D.size] or [member GPUParticlesAttractorVectorField3D.size] depending on the [param particles_collision] type.
			</description>
		</method>
		<method name="particles_collision_set_collision_type">
			<return type="void" />
			<param index="0" name="particles_collision" type="RID" />
			<param index="1" name="type" type="int" enum="RenderingServer.ParticlesCollisionType" />
			<description>
				Sets the collision or attractor shape [param type] for the 3D GPU particles collision or attractor specified by the [param particles_collision] RID.
			</description>
		</method>
		<method name="particles_collision_set_cull_mask">
			<return type="void" />
			<param index="0" name="particles_collision" type="RID" />
			<param index="1" name="mask" type="int" />
			<description>
				Sets the cull [param mask] for the 3D GPU particles collision or attractor specified by the [param particles_collision] RID. Equivalent to [member GPUParticlesCollision3D.cull_mask] or [member GPUParticlesAttractor3D.cull_mask] depending on the [param particles_collision] type.
			</description>
		</method>
		<method name="particles_collision_set_field_texture">
			<return type="void" />
			<param index="0" name="particles_collision" type="RID" />
			<param index="1" name="texture" type="RID" />
			<description>
				Sets the signed distance field [param texture] for the 3D GPU particles collision specified by the [param particles_collision] RID. Equivalent to [member GPUParticlesCollisionSDF3D.texture] or [member GPUParticlesAttractorVectorField3D.texture] depending on the [param particles_collision] type.
			</description>
		</method>
		<method name="particles_collision_set_height_field_mask">
			<return type="void" />
			<param index="0" name="particles_collision" type="RID" />
			<param index="1" name="mask" type="int" />
			<description>
				Sets the heightfield [param mask] for the 3D GPU particles heightfield collision specified by the [param particles_collision] RID. Equivalent to [member GPUParticlesCollisionHeightField3D.heightfield_mask].
			</description>
		</method>
		<method name="particles_collision_set_height_field_resolution">
			<return type="void" />
			<param index="0" name="particles_collision" type="RID" />
			<param index="1" name="resolution" type="int" enum="RenderingServer.ParticlesCollisionHeightfieldResolution" />
			<description>
				Sets the heightmap [param resolution] for the 3D GPU particles heightfield collision specified by the [param particles_collision] RID. Equivalent to [member GPUParticlesCollisionHeightField3D.resolution].
			</description>
		</method>
		<method name="particles_collision_set_sphere_radius">
			<return type="void" />
			<param index="0" name="particles_collision" type="RID" />
			<param index="1" name="radius" type="float" />
			<description>
				Sets the [param radius] for the 3D GPU particles sphere collision or attractor specified by the [param particles_collision] RID. Equivalent to [member GPUParticlesCollisionSphere3D.radius] or [member GPUParticlesAttractorSphere3D.radius] depending on the [param particles_collision] type.
			</description>
		</method>
		<method name="particles_create">
			<return type="RID" />
			<description>
				Creates a GPU-based particle system and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]particles_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				To place in a scene, attach these particles to an instance using [method instance_set_base] using the returned RID.
				[b]Note:[/b] The equivalent nodes are [GPUParticles2D] and [GPUParticles3D].
				[b]Note:[/b] All [code]particles_*[/code] methods only apply to GPU-based particles, not CPU-based particles. [CPUParticles2D] and [CPUParticles3D] do not have equivalent RenderingServer functions available, as these use [MultiMeshInstance2D] and [MultiMeshInstance3D] under the hood (see [code]multimesh_*[/code] methods).
			</description>
		</method>
		<method name="particles_emit">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="transform" type="Transform3D" />
			<param index="2" name="velocity" type="Vector3" />
			<param index="3" name="color" type="Color" />
			<param index="4" name="custom" type="Color" />
			<param index="5" name="emit_flags" type="int" />
			<description>
				Manually emits particles from the [param particles] instance.
			</description>
		</method>
		<method name="particles_get_current_aabb">
			<return type="AABB" />
			<param index="0" name="particles" type="RID" />
			<description>
				Calculates and returns the axis-aligned bounding box that contains all the particles. Equivalent to [method GPUParticles3D.capture_aabb].
			</description>
		</method>
		<method name="particles_get_emitting">
			<return type="bool" />
			<param index="0" name="particles" type="RID" />
			<description>
				Returns [code]true[/code] if particles are currently set to emitting.
			</description>
		</method>
		<method name="particles_is_inactive">
			<return type="bool" />
			<param index="0" name="particles" type="RID" />
			<description>
				Returns [code]true[/code] if particles are not emitting and particles are set to inactive.
			</description>
		</method>
		<method name="particles_request_process">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<description>
				Add particle system to list of particle systems that need to be updated. Update will take place on the next frame, or on the next call to [method instances_cull_aabb], [method instances_cull_convex], or [method instances_cull_ray].
			</description>
		</method>
		<method name="particles_request_process_time">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="time" type="float" />
			<description>
				Requests particles to process for extra process time during a single frame.
			</description>
		</method>
		<method name="particles_restart">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<description>
				Reset the particles on the next update. Equivalent to [method GPUParticles3D.restart].
			</description>
		</method>
		<method name="particles_set_amount">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="amount" type="int" />
			<description>
				Sets the number of particles to be drawn and allocates the memory for them. Equivalent to [member GPUParticles3D.amount].
			</description>
		</method>
		<method name="particles_set_amount_ratio">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="ratio" type="float" />
			<description>
				Sets the amount ratio for particles to be emitted. Equivalent to [member GPUParticles3D.amount_ratio].
			</description>
		</method>
		<method name="particles_set_collision_base_size">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="size" type="float" />
			<description>
			</description>
		</method>
		<method name="particles_set_custom_aabb">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="aabb" type="AABB" />
			<description>
				Sets a custom axis-aligned bounding box for the particle system. Equivalent to [member GPUParticles3D.visibility_aabb].
			</description>
		</method>
		<method name="particles_set_draw_order">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="order" type="int" enum="RenderingServer.ParticlesDrawOrder" />
			<description>
				Sets the draw order of the particles to one of the named enums from [enum ParticlesDrawOrder]. See [enum ParticlesDrawOrder] for options. Equivalent to [member GPUParticles3D.draw_order].
			</description>
		</method>
		<method name="particles_set_draw_pass_mesh">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="pass" type="int" />
			<param index="2" name="mesh" type="RID" />
			<description>
				Sets the mesh to be used for the specified draw pass. Equivalent to [member GPUParticles3D.draw_pass_1], [member GPUParticles3D.draw_pass_2], [member GPUParticles3D.draw_pass_3], and [member GPUParticles3D.draw_pass_4].
			</description>
		</method>
		<method name="particles_set_draw_passes">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="count" type="int" />
			<description>
				Sets the number of draw passes to use. Equivalent to [member GPUParticles3D.draw_passes].
			</description>
		</method>
		<method name="particles_set_emission_transform">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="transform" type="Transform3D" />
			<description>
				Sets the [Transform3D] that will be used by the particles when they first emit.
			</description>
		</method>
		<method name="particles_set_emitter_velocity">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="velocity" type="Vector3" />
			<description>
				Sets the velocity of a particle node, that will be used by [member ParticleProcessMaterial.inherit_velocity_ratio].
			</description>
		</method>
		<method name="particles_set_emitting">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="emitting" type="bool" />
			<description>
				If [code]true[/code], particles will emit over time. Setting to [code]false[/code] does not reset the particles, but only stops their emission. Equivalent to [member GPUParticles3D.emitting].
			</description>
		</method>
		<method name="particles_set_explosiveness_ratio">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="ratio" type="float" />
			<description>
				Sets the explosiveness ratio. Equivalent to [member GPUParticles3D.explosiveness].
			</description>
		</method>
		<method name="particles_set_fixed_fps">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="fps" type="int" />
			<description>
				Sets the frame rate that the particle system rendering will be fixed to. Equivalent to [member GPUParticles3D.fixed_fps].
			</description>
		</method>
		<method name="particles_set_fractional_delta">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="enable" type="bool" />
			<description>
				If [code]true[/code], uses fractional delta which smooths the movement of the particles. Equivalent to [member GPUParticles3D.fract_delta].
			</description>
		</method>
		<method name="particles_set_interp_to_end">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="factor" type="float" />
			<description>
				Sets the value that informs a [ParticleProcessMaterial] to rush all particles towards the end of their lifetime.
			</description>
		</method>
		<method name="particles_set_interpolate">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="enable" type="bool" />
			<description>
			</description>
		</method>
		<method name="particles_set_lifetime">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="lifetime" type="float" />
			<description>
				Sets the lifetime of each particle in the system. Equivalent to [member GPUParticles3D.lifetime].
			</description>
		</method>
		<method name="particles_set_mode">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="mode" type="int" enum="RenderingServer.ParticlesMode" />
			<description>
				Sets whether the GPU particles specified by the [param particles] RID should be rendered in 2D or 3D according to [param mode].
			</description>
		</method>
		<method name="particles_set_one_shot">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="one_shot" type="bool" />
			<description>
				If [code]true[/code], particles will emit once and then stop. Equivalent to [member GPUParticles3D.one_shot].
			</description>
		</method>
		<method name="particles_set_pre_process_time">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="time" type="float" />
			<description>
				Sets the preprocess time for the particles' animation. This lets you delay starting an animation until after the particles have begun emitting. Equivalent to [member GPUParticles3D.preprocess].
			</description>
		</method>
		<method name="particles_set_process_material">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="material" type="RID" />
			<description>
				Sets the material for processing the particles.
				[b]Note:[/b] This is not the material used to draw the materials. Equivalent to [member GPUParticles3D.process_material].
			</description>
		</method>
		<method name="particles_set_randomness_ratio">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="ratio" type="float" />
			<description>
				Sets the emission randomness ratio. This randomizes the emission of particles within their phase. Equivalent to [member GPUParticles3D.randomness].
			</description>
		</method>
		<method name="particles_set_speed_scale">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="scale" type="float" />
			<description>
				Sets the speed scale of the particle system. Equivalent to [member GPUParticles3D.speed_scale].
			</description>
		</method>
		<method name="particles_set_subemitter">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="subemitter_particles" type="RID" />
			<description>
			</description>
		</method>
		<method name="particles_set_trail_bind_poses">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="bind_poses" type="Transform3D[]" />
			<description>
			</description>
		</method>
		<method name="particles_set_trails">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="enable" type="bool" />
			<param index="2" name="length_sec" type="float" />
			<description>
				If [param enable] is [code]true[/code], enables trails for the [param particles] with the specified [param length_sec] in seconds. Equivalent to [member GPUParticles3D.trail_enabled] and [member GPUParticles3D.trail_lifetime].
			</description>
		</method>
		<method name="particles_set_transform_align">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="align" type="int" enum="RenderingServer.ParticlesTransformAlign" />
			<description>
			</description>
		</method>
		<method name="particles_set_use_local_coordinates">
			<return type="void" />
			<param index="0" name="particles" type="RID" />
			<param index="1" name="enable" type="bool" />
			<description>
				If [code]true[/code], particles use local coordinates. If [code]false[/code] they use global coordinates. Equivalent to [member GPUParticles3D.local_coords].
			</description>
		</method>
		<method name="positional_soft_shadow_filter_set_quality">
			<return type="void" />
			<param index="0" name="quality" type="int" enum="RenderingServer.ShadowQuality" />
			<description>
				Sets the filter quality for omni and spot light shadows in 3D. See also [member ProjectSettings.rendering/lights_and_shadows/positional_shadow/soft_shadow_filter_quality]. This parameter is global and cannot be set on a per-viewport basis.
			</description>
		</method>
		<method name="reflection_probe_create">
			<return type="RID" />
			<description>
				Creates a reflection probe and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]reflection_probe_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				To place in a scene, attach this reflection probe to an instance using [method instance_set_base] using the returned RID.
				[b]Note:[/b] The equivalent node is [ReflectionProbe].
			</description>
		</method>
		<method name="reflection_probe_set_ambient_color">
			<return type="void" />
			<param index="0" name="probe" type="RID" />
			<param index="1" name="color" type="Color" />
			<description>
				Sets the reflection probe's custom ambient light color. Equivalent to [member ReflectionProbe.ambient_color].
			</description>
		</method>
		<method name="reflection_probe_set_ambient_energy">
			<return type="void" />
			<param index="0" name="probe" type="RID" />
			<param index="1" name="energy" type="float" />
			<description>
				Sets the reflection probe's custom ambient light energy. Equivalent to [member ReflectionProbe.ambient_color_energy].
			</description>
		</method>
		<method name="reflection_probe_set_ambient_mode">
			<return type="void" />
			<param index="0" name="probe" type="RID" />
			<param index="1" name="mode" type="int" enum="RenderingServer.ReflectionProbeAmbientMode" />
			<description>
				Sets the reflection probe's ambient light mode. Equivalent to [member ReflectionProbe.ambient_mode].
			</description>
		</method>
		<method name="reflection_probe_set_as_interior">
			<return type="void" />
			<param index="0" name="probe" type="RID" />
			<param index="1" name="enable" type="bool" />
			<description>
				If [code]true[/code], reflections will ignore sky contribution. Equivalent to [member ReflectionProbe.interior].
			</description>
		</method>
		<method name="reflection_probe_set_blend_distance">
			<return type="void" />
			<param index="0" name="probe" type="RID" />
			<param index="1" name="blend_distance" type="float" />
			<description>
				Sets the distance in meters over which a probe blends into the scene.
			</description>
		</method>
		<method name="reflection_probe_set_cull_mask">
			<return type="void" />
			<param index="0" name="probe" type="RID" />
			<param index="1" name="layers" type="int" />
			<description>
				Sets the render cull mask for this reflection probe. Only instances with a matching layer will be reflected by this probe. Equivalent to [member ReflectionProbe.cull_mask].
			</description>
		</method>
		<method name="reflection_probe_set_enable_box_projection">
			<return type="void" />
			<param index="0" name="probe" type="RID" />
			<param index="1" name="enable" type="bool" />
			<description>
				If [code]true[/code], uses box projection. This can make reflections look more correct in certain situations. Equivalent to [member ReflectionProbe.box_projection].
			</description>
		</method>
		<method name="reflection_probe_set_enable_shadows">
			<return type="void" />
			<param index="0" name="probe" type="RID" />
			<param index="1" name="enable" type="bool" />
			<description>
				If [code]true[/code], computes shadows in the reflection probe. This makes the reflection much slower to compute. Equivalent to [member ReflectionProbe.enable_shadows].
			</description>
		</method>
		<method name="reflection_probe_set_intensity">
			<return type="void" />
			<param index="0" name="probe" type="RID" />
			<param index="1" name="intensity" type="float" />
			<description>
				Sets the intensity of the reflection probe. Intensity modulates the strength of the reflection. Equivalent to [member ReflectionProbe.intensity].
			</description>
		</method>
		<method name="reflection_probe_set_max_distance">
			<return type="void" />
			<param index="0" name="probe" type="RID" />
			<param index="1" name="distance" type="float" />
			<description>
				Sets the max distance away from the probe an object can be before it is culled. Equivalent to [member ReflectionProbe.max_distance].
			</description>
		</method>
		<method name="reflection_probe_set_mesh_lod_threshold">
			<return type="void" />
			<param index="0" name="probe" type="RID" />
			<param index="1" name="pixels" type="float" />
			<description>
				Sets the mesh level of detail to use in the reflection probe rendering. Higher values will use less detailed versions of meshes that have LOD variations generated, which can improve performance. Equivalent to [member ReflectionProbe.mesh_lod_threshold].
			</description>
		</method>
		<method name="reflection_probe_set_origin_offset">
			<return type="void" />
			<param index="0" name="probe" type="RID" />
			<param index="1" name="offset" type="Vector3" />
			<description>
				Sets the origin offset to be used when this reflection probe is in box project mode. Equivalent to [member ReflectionProbe.origin_offset].
			</description>
		</method>
		<method name="reflection_probe_set_reflection_mask">
			<return type="void" />
			<param index="0" name="probe" type="RID" />
			<param index="1" name="layers" type="int" />
			<description>
				Sets the render reflection mask for this reflection probe. Only instances with a matching layer will have reflections applied from this probe. Equivalent to [member ReflectionProbe.reflection_mask].
			</description>
		</method>
		<method name="reflection_probe_set_resolution">
			<return type="void" />
			<param index="0" name="probe" type="RID" />
			<param index="1" name="resolution" type="int" />
			<description>
				Sets the resolution to use when rendering the specified reflection probe. The [param resolution] is specified for each cubemap face: for instance, specifying [code]512[/code] will allocate 6 faces of 512×512 each (plus mipmaps for roughness levels).
			</description>
		</method>
		<method name="reflection_probe_set_size">
			<return type="void" />
			<param index="0" name="probe" type="RID" />
			<param index="1" name="size" type="Vector3" />
			<description>
				Sets the size of the area that the reflection probe will capture. Equivalent to [member ReflectionProbe.size].
			</description>
		</method>
		<method name="reflection_probe_set_update_mode">
			<return type="void" />
			<param index="0" name="probe" type="RID" />
			<param index="1" name="mode" type="int" enum="RenderingServer.ReflectionProbeUpdateMode" />
			<description>
				Sets how often the reflection probe updates. Can either be once or every frame. See [enum ReflectionProbeUpdateMode] for options.
			</description>
		</method>
		<method name="request_frame_drawn_callback">
			<return type="void" />
			<param index="0" name="callable" type="Callable" />
			<description>
				Schedules a callback to the given callable after a frame has been drawn.
			</description>
		</method>
		<method name="scenario_create">
			<return type="RID" />
			<description>
				Creates a scenario and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]scenario_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				The scenario is the 3D world that all the visual instances exist in.
			</description>
		</method>
		<method name="scenario_set_camera_attributes">
			<return type="void" />
			<param index="0" name="scenario" type="RID" />
			<param index="1" name="effects" type="RID" />
			<description>
				Sets the camera attributes ([param effects]) that will be used with this scenario. See also [CameraAttributes].
			</description>
		</method>
		<method name="scenario_set_compositor">
			<return type="void" />
			<param index="0" name="scenario" type="RID" />
			<param index="1" name="compositor" type="RID" />
			<description>
				Sets the compositor ([param compositor]) that will be used with this scenario. See also [Compositor].
			</description>
		</method>
		<method name="scenario_set_environment">
			<return type="void" />
			<param index="0" name="scenario" type="RID" />
			<param index="1" name="environment" type="RID" />
			<description>
				Sets the environment that will be used with this scenario. See also [Environment].
			</description>
		</method>
		<method name="scenario_set_fallback_environment">
			<return type="void" />
			<param index="0" name="scenario" type="RID" />
			<param index="1" name="environment" type="RID" />
			<description>
				Sets the fallback environment to be used by this scenario. The fallback environment is used if no environment is set. Internally, this is used by the editor to provide a default environment.
			</description>
		</method>
		<method name="screen_space_roughness_limiter_set_active">
			<return type="void" />
			<param index="0" name="enable" type="bool" />
			<param index="1" name="amount" type="float" />
			<param index="2" name="limit" type="float" />
			<description>
				Sets the screen-space roughness limiter parameters, such as whether it should be enabled and its thresholds. Equivalent to [member ProjectSettings.rendering/anti_aliasing/screen_space_roughness_limiter/enabled], [member ProjectSettings.rendering/anti_aliasing/screen_space_roughness_limiter/amount] and [member ProjectSettings.rendering/anti_aliasing/screen_space_roughness_limiter/limit].
			</description>
		</method>
		<method name="set_boot_image">
			<return type="void" />
			<param index="0" name="image" type="Image" />
			<param index="1" name="color" type="Color" />
			<param index="2" name="scale" type="bool" />
			<param index="3" name="use_filter" type="bool" default="true" />
			<description>
				Sets a boot image. The color defines the background color. If [param scale] is [code]true[/code], the image will be scaled to fit the screen size. If [param use_filter] is [code]true[/code], the image will be scaled with linear interpolation. If [param use_filter] is [code]false[/code], the image will be scaled with nearest-neighbor interpolation.
			</description>
		</method>
		<method name="set_debug_generate_wireframes">
			<return type="void" />
			<param index="0" name="generate" type="bool" />
			<description>
				If [param generate] is [code]true[/code], generates debug wireframes for all meshes that are loaded when using the Compatibility renderer. By default, the engine does not generate debug wireframes at runtime, since they slow down loading of assets and take up VRAM.
				[b]Note:[/b] You must call this method before loading any meshes when using the Compatibility renderer, otherwise wireframes will not be used.
			</description>
		</method>
		<method name="set_default_clear_color">
			<return type="void" />
			<param index="0" name="color" type="Color" />
			<description>
				Sets the default clear color which is used when a specific clear color has not been selected. See also [method get_default_clear_color].
			</description>
		</method>
		<method name="shader_create">
			<return type="RID" />
			<description>
				Creates an empty shader and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]shader_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				[b]Note:[/b] The equivalent resource is [Shader].
			</description>
		</method>
		<method name="shader_get_code" qualifiers="const">
			<return type="String" />
			<param index="0" name="shader" type="RID" />
			<description>
				Returns a shader's source code as a string.
			</description>
		</method>
		<method name="shader_get_default_texture_parameter" qualifiers="const">
			<return type="RID" />
			<param index="0" name="shader" type="RID" />
			<param index="1" name="name" type="StringName" />
			<param index="2" name="index" type="int" default="0" />
			<description>
				Returns a default texture from a shader searched by name.
				[b]Note:[/b] If the sampler array is used use [param index] to access the specified texture.
			</description>
		</method>
		<method name="shader_get_parameter_default" qualifiers="const">
			<return type="Variant" />
			<param index="0" name="shader" type="RID" />
			<param index="1" name="name" type="StringName" />
			<description>
				Returns the default value for the specified shader uniform. This is usually the value written in the shader source code.
			</description>
		</method>
		<method name="shader_set_code">
			<return type="void" />
			<param index="0" name="shader" type="RID" />
			<param index="1" name="code" type="String" />
			<description>
				Sets the shader's source code (which triggers recompilation after being changed).
			</description>
		</method>
		<method name="shader_set_default_texture_parameter">
			<return type="void" />
			<param index="0" name="shader" type="RID" />
			<param index="1" name="name" type="StringName" />
			<param index="2" name="texture" type="RID" />
			<param index="3" name="index" type="int" default="0" />
			<description>
				Sets a shader's default texture. Overwrites the texture given by name.
				[b]Note:[/b] If the sampler array is used use [param index] to access the specified texture.
			</description>
		</method>
		<method name="shader_set_path_hint">
			<return type="void" />
			<param index="0" name="shader" type="RID" />
			<param index="1" name="path" type="String" />
			<description>
				Sets the path hint for the specified shader. This should generally match the [Shader] resource's [member Resource.resource_path].
			</description>
		</method>
		<method name="skeleton_allocate_data">
			<return type="void" />
			<param index="0" name="skeleton" type="RID" />
			<param index="1" name="bones" type="int" />
			<param index="2" name="is_2d_skeleton" type="bool" default="false" />
			<description>
			</description>
		</method>
		<method name="skeleton_bone_get_transform" qualifiers="const">
			<return type="Transform3D" />
			<param index="0" name="skeleton" type="RID" />
			<param index="1" name="bone" type="int" />
			<description>
				Returns the [Transform3D] set for a specific bone of this skeleton.
			</description>
		</method>
		<method name="skeleton_bone_get_transform_2d" qualifiers="const">
			<return type="Transform2D" />
			<param index="0" name="skeleton" type="RID" />
			<param index="1" name="bone" type="int" />
			<description>
				Returns the [Transform2D] set for a specific bone of this skeleton.
			</description>
		</method>
		<method name="skeleton_bone_set_transform">
			<return type="void" />
			<param index="0" name="skeleton" type="RID" />
			<param index="1" name="bone" type="int" />
			<param index="2" name="transform" type="Transform3D" />
			<description>
				Sets the [Transform3D] for a specific bone of this skeleton.
			</description>
		</method>
		<method name="skeleton_bone_set_transform_2d">
			<return type="void" />
			<param index="0" name="skeleton" type="RID" />
			<param index="1" name="bone" type="int" />
			<param index="2" name="transform" type="Transform2D" />
			<description>
				Sets the [Transform2D] for a specific bone of this skeleton.
			</description>
		</method>
		<method name="skeleton_create">
			<return type="RID" />
			<description>
				Creates a skeleton and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]skeleton_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
			</description>
		</method>
		<method name="skeleton_get_bone_count" qualifiers="const">
			<return type="int" />
			<param index="0" name="skeleton" type="RID" />
			<description>
				Returns the number of bones allocated for this skeleton.
			</description>
		</method>
		<method name="skeleton_set_base_transform_2d">
			<return type="void" />
			<param index="0" name="skeleton" type="RID" />
			<param index="1" name="base_transform" type="Transform2D" />
			<description>
			</description>
		</method>
		<method name="sky_bake_panorama">
			<return type="Image" />
			<param index="0" name="sky" type="RID" />
			<param index="1" name="energy" type="float" />
			<param index="2" name="bake_irradiance" type="bool" />
			<param index="3" name="size" type="Vector2i" />
			<description>
				Generates and returns an [Image] containing the radiance map for the specified [param sky] RID. This supports built-in sky material and custom sky shaders. If [param bake_irradiance] is [code]true[/code], the irradiance map is saved instead of the radiance map. The radiance map is used to render reflected light, while the irradiance map is used to render ambient light. See also [method environment_bake_panorama].
				[b]Note:[/b] The image is saved in linear color space without any tonemapping performed, which means it will look too dark if viewed directly in an image editor. [param energy] values above [code]1.0[/code] can be used to brighten the resulting image.
				[b]Note:[/b] [param size] should be a 2:1 aspect ratio for the generated panorama to have square pixels. For radiance maps, there is no point in using a height greater than [member Sky.radiance_size], as it won't increase detail. Irradiance maps only contain low-frequency data, so there is usually no point in going past a size of 128×64 pixels when saving an irradiance map.
			</description>
		</method>
		<method name="sky_create">
			<return type="RID" />
			<description>
				Creates an empty sky and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]sky_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
			</description>
		</method>
		<method name="sky_set_material">
			<return type="void" />
			<param index="0" name="sky" type="RID" />
			<param index="1" name="material" type="RID" />
			<description>
				Sets the material that the sky uses to render the background, ambient and reflection maps.
			</description>
		</method>
		<method name="sky_set_mode">
			<return type="void" />
			<param index="0" name="sky" type="RID" />
			<param index="1" name="mode" type="int" enum="RenderingServer.SkyMode" />
			<description>
				Sets the process [param mode] of the sky specified by the [param sky] RID. Equivalent to [member Sky.process_mode].
			</description>
		</method>
		<method name="sky_set_radiance_size">
			<return type="void" />
			<param index="0" name="sky" type="RID" />
			<param index="1" name="radiance_size" type="int" />
			<description>
				Sets the [param radiance_size] of the sky specified by the [param sky] RID (in pixels). Equivalent to [member Sky.radiance_size].
			</description>
		</method>
		<method name="spot_light_create">
			<return type="RID" />
			<description>
				Creates a spot light and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID can be used in most [code]light_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				To place in a scene, attach this spot light to an instance using [method instance_set_base] using the returned RID.
			</description>
		</method>
		<method name="sub_surface_scattering_set_quality">
			<return type="void" />
			<param index="0" name="quality" type="int" enum="RenderingServer.SubSurfaceScatteringQuality" />
			<description>
				Sets [member ProjectSettings.rendering/environment/subsurface_scattering/subsurface_scattering_quality] to use when rendering materials that have subsurface scattering enabled.
			</description>
		</method>
		<method name="sub_surface_scattering_set_scale">
			<return type="void" />
			<param index="0" name="scale" type="float" />
			<param index="1" name="depth_scale" type="float" />
			<description>
				Sets the [member ProjectSettings.rendering/environment/subsurface_scattering/subsurface_scattering_scale] and [member ProjectSettings.rendering/environment/subsurface_scattering/subsurface_scattering_depth_scale] to use when rendering materials that have subsurface scattering enabled.
			</description>
		</method>
		<method name="texture_2d_create">
			<return type="RID" />
			<param index="0" name="image" type="Image" />
			<description>
				Creates a 2-dimensional texture and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]texture_2d_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				[b]Note:[/b] The equivalent resource is [Texture2D].
				[b]Note:[/b] Not to be confused with [method RenderingDevice.texture_create], which creates the graphics API's own texture type as opposed to the Redot-specific [Texture2D] resource.
			</description>
		</method>
		<method name="texture_2d_get" qualifiers="const">
			<return type="Image" />
			<param index="0" name="texture" type="RID" />
			<description>
				Returns an [Image] instance from the given [param texture] [RID].
				[b]Example:[/b] Get the test texture from [method get_test_texture] and apply it to a [Sprite2D] node:
				[codeblock]
				var texture_rid = RenderingServer.get_test_texture()
				var texture = ImageTexture.create_from_image(RenderingServer.texture_2d_get(texture_rid))
				$Sprite2D.texture = texture
				[/codeblock]
			</description>
		</method>
		<method name="texture_2d_layer_get" qualifiers="const">
			<return type="Image" />
			<param index="0" name="texture" type="RID" />
			<param index="1" name="layer" type="int" />
			<description>
				Returns an [Image] instance from the given [param texture] [RID] and [param layer].
			</description>
		</method>
		<method name="texture_2d_layered_create">
			<return type="RID" />
			<param index="0" name="layers" type="Image[]" />
			<param index="1" name="layered_type" type="int" enum="RenderingServer.TextureLayeredType" />
			<description>
				Creates a 2-dimensional layered texture and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]texture_2d_layered_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				[b]Note:[/b] The equivalent resource is [TextureLayered].
			</description>
		</method>
		<method name="texture_2d_layered_placeholder_create">
			<return type="RID" />
			<param index="0" name="layered_type" type="int" enum="RenderingServer.TextureLayeredType" />
			<description>
				Creates a placeholder for a 2-dimensional layered texture and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]texture_2d_layered_*[/code] RenderingServer functions, although it does nothing when used. See also [method texture_2d_placeholder_create].
				[b]Note:[/b] The equivalent resource is [PlaceholderTextureLayered].
			</description>
		</method>
		<method name="texture_2d_placeholder_create">
			<return type="RID" />
			<description>
				Creates a placeholder for a 2-dimensional layered texture and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]texture_2d_layered_*[/code] RenderingServer functions, although it does nothing when used. See also [method texture_2d_layered_placeholder_create].
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				[b]Note:[/b] The equivalent resource is [PlaceholderTexture2D].
			</description>
		</method>
		<method name="texture_2d_update">
			<return type="void" />
			<param index="0" name="texture" type="RID" />
			<param index="1" name="image" type="Image" />
			<param index="2" name="layer" type="int" />
			<description>
				Updates the texture specified by the [param texture] [RID] with the data in [param image]. A [param layer] must also be specified, which should be [code]0[/code] when updating a single-layer texture ([Texture2D]).
				[b]Note:[/b] The [param image] must have the same width, height and format as the current [param texture] data. Otherwise, an error will be printed and the original texture won't be modified. If you need to use different width, height or format, use [method texture_replace] instead.
			</description>
		</method>
		<method name="texture_3d_create">
			<return type="RID" />
			<param index="0" name="format" type="int" enum="Image.Format" />
			<param index="1" name="width" type="int" />
			<param index="2" name="height" type="int" />
			<param index="3" name="depth" type="int" />
			<param index="4" name="mipmaps" type="bool" />
			<param index="5" name="data" type="Image[]" />
			<description>
				[b]Note:[/b] The equivalent resource is [Texture3D].
			</description>
		</method>
		<method name="texture_3d_get" qualifiers="const">
			<return type="Image[]" />
			<param index="0" name="texture" type="RID" />
			<description>
				Returns 3D texture data as an array of [Image]s for the specified texture [RID].
			</description>
		</method>
		<method name="texture_3d_placeholder_create">
			<return type="RID" />
			<description>
				Creates a placeholder for a 3-dimensional texture and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]texture_3d_*[/code] RenderingServer functions, although it does nothing when used.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				[b]Note:[/b] The equivalent resource is [PlaceholderTexture3D].
			</description>
		</method>
		<method name="texture_3d_update">
			<return type="void" />
			<param index="0" name="texture" type="RID" />
			<param index="1" name="data" type="Image[]" />
			<description>
				Updates the texture specified by the [param texture] [RID]'s data with the data in [param data]. All the texture's layers must be replaced at once.
				[b]Note:[/b] The [param texture] must have the same width, height, depth and format as the current texture data. Otherwise, an error will be printed and the original texture won't be modified. If you need to use different width, height, depth or format, use [method texture_replace] instead.
			</description>
		</method>
		<method name="texture_create_from_native_handle">
			<return type="RID" />
			<param index="0" name="type" type="int" enum="RenderingServer.TextureType" />
			<param index="1" name="format" type="int" enum="Image.Format" />
			<param index="2" name="native_handle" type="int" />
			<param index="3" name="width" type="int" />
			<param index="4" name="height" type="int" />
			<param index="5" name="depth" type="int" />
			<param index="6" name="layers" type="int" default="1" />
			<param index="7" name="layered_type" type="int" enum="RenderingServer.TextureLayeredType" default="0" />
			<description>
				Creates a texture based on a native handle that was created outside of Redot's renderer.
				[b]Note:[/b] If using only the rendering device renderer, it's recommend to use [method RenderingDevice.texture_create_from_extension] together with [method RenderingServer.texture_rd_create], rather than this method. It will give you much more control over the texture's format and usage.
			</description>
		</method>
		<method name="texture_get_format" qualifiers="const">
			<return type="int" enum="Image.Format" />
			<param index="0" name="texture" type="RID" />
			<description>
				Returns the format for the texture.
			</description>
		</method>
		<method name="texture_get_native_handle" qualifiers="const">
			<return type="int" />
			<param index="0" name="texture" type="RID" />
			<param index="1" name="srgb" type="bool" default="false" />
			<description>
				Returns the internal graphics handle for this texture object. For use when communicating with third-party APIs mostly with GDExtension.
				[b]Note:[/b] This function returns a [code]uint64_t[/code] which internally maps to a [code]GLuint[/code] (OpenGL) or [code]VkImage[/code] (Vulkan).
			</description>
		</method>
		<method name="texture_get_path" qualifiers="const">
			<return type="String" />
			<param index="0" name="texture" type="RID" />
			<description>
			</description>
		</method>
		<method name="texture_get_rd_texture" qualifiers="const">
			<return type="RID" />
			<param index="0" name="texture" type="RID" />
			<param index="1" name="srgb" type="bool" default="false" />
			<description>
				Returns a texture [RID] that can be used with [RenderingDevice].
			</description>
		</method>
		<method name="texture_proxy_create" deprecated="ProxyTexture was removed in Redot 4.">
			<return type="RID" />
			<param index="0" name="base" type="RID" />
			<description>
				This method does nothing and always returns an invalid [RID].
			</description>
		</method>
		<method name="texture_proxy_update" deprecated="ProxyTexture was removed in Redot 4.">
			<return type="void" />
			<param index="0" name="texture" type="RID" />
			<param index="1" name="proxy_to" type="RID" />
			<description>
				This method does nothing.
			</description>
		</method>
		<method name="texture_rd_create">
			<return type="RID" />
			<param index="0" name="rd_texture" type="RID" />
			<param index="1" name="layer_type" type="int" enum="RenderingServer.TextureLayeredType" default="0" />
			<description>
				Creates a new texture object based on a texture created directly on the [RenderingDevice]. If the texture contains layers, [param layer_type] is used to define the layer type.
			</description>
		</method>
		<method name="texture_replace">
			<return type="void" />
			<param index="0" name="texture" type="RID" />
			<param index="1" name="by_texture" type="RID" />
			<description>
				Replaces [param texture]'s texture data by the texture specified by the [param by_texture] RID, without changing [param texture]'s RID.
			</description>
		</method>
		<method name="texture_set_force_redraw_if_visible">
			<return type="void" />
			<param index="0" name="texture" type="RID" />
			<param index="1" name="enable" type="bool" />
			<description>
			</description>
		</method>
		<method name="texture_set_path">
			<return type="void" />
			<param index="0" name="texture" type="RID" />
			<param index="1" name="path" type="String" />
			<description>
			</description>
		</method>
		<method name="texture_set_size_override">
			<return type="void" />
			<param index="0" name="texture" type="RID" />
			<param index="1" name="width" type="int" />
			<param index="2" name="height" type="int" />
			<description>
			</description>
		</method>
		<method name="viewport_attach_camera">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="camera" type="RID" />
			<description>
				Sets a viewport's camera.
			</description>
		</method>
		<method name="viewport_attach_canvas">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="canvas" type="RID" />
			<description>
				Sets a viewport's canvas.
			</description>
		</method>
		<method name="viewport_attach_to_screen">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="rect" type="Rect2" default="Rect2(0, 0, 0, 0)" />
			<param index="2" name="screen" type="int" default="0" />
			<description>
				Copies the viewport to a region of the screen specified by [param rect]. If [method viewport_set_render_direct_to_screen] is [code]true[/code], then the viewport does not use a framebuffer and the contents of the viewport are rendered directly to screen. However, note that the root viewport is drawn last, therefore it will draw over the screen. Accordingly, you must set the root viewport to an area that does not cover the area that you have attached this viewport to.
				For example, you can set the root viewport to not render at all with the following code:
				[codeblocks]
				[gdscript]
				func _ready():
				    RenderingServer.viewport_attach_to_screen(get_viewport().get_viewport_rid(), Rect2())
				    RenderingServer.viewport_attach_to_screen($Viewport.get_viewport_rid(), Rect2(0, 0, 600, 600))
				[/gdscript]
				[/codeblocks]
				Using this can result in significant optimization, especially on lower-end devices. However, it comes at the cost of having to manage your viewports manually. For further optimization, see [method viewport_set_render_direct_to_screen].
			</description>
		</method>
		<method name="viewport_create">
			<return type="RID" />
			<description>
				Creates an empty viewport and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]viewport_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				[b]Note:[/b] The equivalent node is [Viewport].
			</description>
		</method>
		<method name="viewport_get_measured_render_time_cpu" qualifiers="const">
			<return type="float" />
			<param index="0" name="viewport" type="RID" />
			<description>
				Returns the CPU time taken to render the last frame in milliseconds. This [i]only[/i] includes time spent in rendering-related operations; scripts' [code]_process[/code] functions and other engine subsystems are not included in this readout. To get a complete readout of CPU time spent to render the scene, sum the render times of all viewports that are drawn every frame plus [method get_frame_setup_time_cpu]. Unlike [method Engine.get_frames_per_second], this method will accurately reflect CPU utilization even if framerate is capped via V-Sync or [member Engine.max_fps]. See also [method viewport_get_measured_render_time_gpu].
				[b]Note:[/b] Requires measurements to be enabled on the specified [param viewport] using [method viewport_set_measure_render_time]. Otherwise, this method returns [code]0.0[/code].
			</description>
		</method>
		<method name="viewport_get_measured_render_time_gpu" qualifiers="const">
			<return type="float" />
			<param index="0" name="viewport" type="RID" />
			<description>
				Returns the GPU time taken to render the last frame in milliseconds. To get a complete readout of GPU time spent to render the scene, sum the render times of all viewports that are drawn every frame. Unlike [method Engine.get_frames_per_second], this method accurately reflects GPU utilization even if framerate is capped via V-Sync or [member Engine.max_fps]. See also [method viewport_get_measured_render_time_cpu].
				[b]Note:[/b] Requires measurements to be enabled on the specified [param viewport] using [method viewport_set_measure_render_time]. Otherwise, this method returns [code]0.0[/code].
				[b]Note:[/b] When GPU utilization is low enough during a certain period of time, GPUs will decrease their power state (which in turn decreases core and memory clock speeds). This can cause the reported GPU time to increase if GPU utilization is kept low enough by a framerate cap (compared to what it would be at the GPU's highest power state). Keep this in mind when benchmarking using [method viewport_get_measured_render_time_gpu]. This behavior can be overridden in the graphics driver settings at the cost of higher power usage.
			</description>
		</method>
		<method name="viewport_get_render_info">
			<return type="int" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="type" type="int" enum="RenderingServer.ViewportRenderInfoType" />
			<param index="2" name="info" type="int" enum="RenderingServer.ViewportRenderInfo" />
			<description>
				Returns a statistic about the rendering engine which can be used for performance profiling. This is separated into render pass [param type]s, each of them having the same [param info]s you can query (different passes will return different values). See [enum RenderingServer.ViewportRenderInfoType] for a list of render pass types and [enum RenderingServer.ViewportRenderInfo] for a list of information that can be queried.
				See also [method get_rendering_info], which returns global information across all viewports.
				[b]Note:[/b] Viewport rendering information is not available until at least 2 frames have been rendered by the engine. If rendering information is not available, [method viewport_get_render_info] returns [code]0[/code]. To print rendering information in [code]_ready()[/code] successfully, use the following:
				[codeblock]
				func _ready():
				    for _i in 2:
				        await get_tree().process_frame

				    print(
				            RenderingServer.viewport_get_render_info(get_viewport().get_viewport_rid(),
				            RenderingServer.VIEWPORT_RENDER_INFO_TYPE_VISIBLE,
				            RenderingServer.VIEWPORT_RENDER_INFO_DRAW_CALLS_IN_FRAME)
				    )
				[/codeblock]
			</description>
		</method>
		<method name="viewport_get_render_target" qualifiers="const">
			<return type="RID" />
			<param index="0" name="viewport" type="RID" />
			<description>
				Returns the render target for the viewport.
			</description>
		</method>
		<method name="viewport_get_texture" qualifiers="const">
			<return type="RID" />
			<param index="0" name="viewport" type="RID" />
			<description>
				Returns the viewport's last rendered frame.
			</description>
		</method>
		<method name="viewport_get_update_mode" qualifiers="const">
			<return type="int" enum="RenderingServer.ViewportUpdateMode" />
			<param index="0" name="viewport" type="RID" />
			<description>
				Returns the viewport's update mode. See [enum ViewportUpdateMode] constants for options.
				[b]Warning:[/b] Calling this from any thread other than the rendering thread will be detrimental to performance.
			</description>
		</method>
		<method name="viewport_remove_canvas">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="canvas" type="RID" />
			<description>
				Detaches a viewport from a canvas.
			</description>
		</method>
		<method name="viewport_set_active">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="active" type="bool" />
			<description>
				If [code]true[/code], sets the viewport active, else sets it inactive.
			</description>
		</method>
		<method name="viewport_set_anisotropic_filtering_level">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="anisotropic_filtering_level" type="int" enum="RenderingServer.ViewportAnisotropicFiltering" />
			<description>
				Sets the maximum number of samples to take when using anisotropic filtering on textures (as a power of two). A higher sample count will result in sharper textures at oblique angles, but is more expensive to compute. A value of [code]0[/code] forcibly disables anisotropic filtering, even on materials where it is enabled.
				The anisotropic filtering level also affects decals and light projectors if they are configured to use anisotropic filtering. See [member ProjectSettings.rendering/textures/decals/filter] and [member ProjectSettings.rendering/textures/light_projectors/filter].
				[b]Note:[/b] In 3D, for this setting to have an effect, set [member BaseMaterial3D.texture_filter] to [constant BaseMaterial3D.TEXTURE_FILTER_LINEAR_WITH_MIPMAPS_ANISOTROPIC] or [constant BaseMaterial3D.TEXTURE_FILTER_NEAREST_WITH_MIPMAPS_ANISOTROPIC] on materials.
				[b]Note:[/b] In 2D, for this setting to have an effect, set [member CanvasItem.texture_filter] to [constant CanvasItem.TEXTURE_FILTER_LINEAR_WITH_MIPMAPS_ANISOTROPIC] or [constant CanvasItem.TEXTURE_FILTER_NEAREST_WITH_MIPMAPS_ANISOTROPIC] on the [CanvasItem] node displaying the texture (or in [CanvasTexture]). However, anisotropic filtering is rarely useful in 2D, so only enable it for textures in 2D if it makes a meaningful visual difference.
			</description>
		</method>
		<method name="viewport_set_canvas_cull_mask">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="canvas_cull_mask" type="int" />
			<description>
				Sets the rendering mask associated with this [Viewport]. Only [CanvasItem] nodes with a matching rendering visibility layer will be rendered by this [Viewport].
			</description>
		</method>
		<method name="viewport_set_canvas_stacking">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="canvas" type="RID" />
			<param index="2" name="layer" type="int" />
			<param index="3" name="sublayer" type="int" />
			<description>
				Sets the stacking order for a viewport's canvas.
				[param layer] is the actual canvas layer, while [param sublayer] specifies the stacking order of the canvas among those in the same layer.
			</description>
		</method>
		<method name="viewport_set_canvas_transform">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="canvas" type="RID" />
			<param index="2" name="offset" type="Transform2D" />
			<description>
				Sets the transformation of a viewport's canvas.
			</description>
		</method>
		<method name="viewport_set_clear_mode">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="clear_mode" type="int" enum="RenderingServer.ViewportClearMode" />
			<description>
				Sets the clear mode of a viewport. See [enum ViewportClearMode] for options.
			</description>
		</method>
		<method name="viewport_set_debug_draw">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="draw" type="int" enum="RenderingServer.ViewportDebugDraw" />
			<description>
				Sets the debug draw mode of a viewport. See [enum ViewportDebugDraw] for options.
			</description>
		</method>
		<method name="viewport_set_default_canvas_item_texture_filter">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="filter" type="int" enum="RenderingServer.CanvasItemTextureFilter" />
			<description>
				Sets the default texture filtering mode for the specified [param viewport] RID. See [enum CanvasItemTextureFilter] for options.
			</description>
		</method>
		<method name="viewport_set_default_canvas_item_texture_repeat">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="repeat" type="int" enum="RenderingServer.CanvasItemTextureRepeat" />
			<description>
				Sets the default texture repeat mode for the specified [param viewport] RID. See [enum CanvasItemTextureRepeat] for options.
			</description>
		</method>
		<method name="viewport_set_disable_2d">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="disable" type="bool" />
			<description>
				If [code]true[/code], the viewport's canvas (i.e. 2D and GUI elements) is not rendered.
			</description>
		</method>
		<method name="viewport_set_disable_3d">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="disable" type="bool" />
			<description>
				If [code]true[/code], the viewport's 3D elements are not rendered.
			</description>
		</method>
		<method name="viewport_set_environment_mode">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="mode" type="int" enum="RenderingServer.ViewportEnvironmentMode" />
			<description>
				Sets the viewport's environment mode which allows enabling or disabling rendering of 3D environment over 2D canvas. When disabled, 2D will not be affected by the environment. When enabled, 2D will be affected by the environment if the environment background mode is [constant ENV_BG_CANVAS]. The default behavior is to inherit the setting from the viewport's parent. If the topmost parent is also set to [constant VIEWPORT_ENVIRONMENT_INHERIT], then the behavior will be the same as if it was set to [constant VIEWPORT_ENVIRONMENT_ENABLED].
			</description>
		</method>
		<method name="viewport_set_fsr_sharpness">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="sharpness" type="float" />
			<description>
				Determines how sharp the upscaled image will be when using the FSR upscaling mode. Sharpness halves with every whole number. Values go from 0.0 (sharpest) to 2.0. Values above 2.0 won't make a visible difference.
			</description>
		</method>
		<method name="viewport_set_global_canvas_transform">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="transform" type="Transform2D" />
			<description>
				Sets the viewport's global transformation matrix.
			</description>
		</method>
		<method name="viewport_set_measure_render_time">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="enable" type="bool" />
			<description>
				Sets the measurement for the given [param viewport] RID (obtained using [method Viewport.get_viewport_rid]). Once enabled, [method viewport_get_measured_render_time_cpu] and [method viewport_get_measured_render_time_gpu] will return values greater than [code]0.0[/code] when queried with the given [param viewport].
			</description>
		</method>
		<method name="viewport_set_msaa_2d">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="msaa" type="int" enum="RenderingServer.ViewportMSAA" />
			<description>
				Sets the multisample antialiasing mode for 2D/Canvas on the specified [param viewport] RID. See [enum ViewportMSAA] for options. Equivalent to [member ProjectSettings.rendering/anti_aliasing/quality/msaa_2d] or [member Viewport.msaa_2d].
			</description>
		</method>
		<method name="viewport_set_msaa_3d">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="msaa" type="int" enum="RenderingServer.ViewportMSAA" />
			<description>
				Sets the multisample antialiasing mode for 3D on the specified [param viewport] RID. See [enum ViewportMSAA] for options. Equivalent to [member ProjectSettings.rendering/anti_aliasing/quality/msaa_3d] or [member Viewport.msaa_3d].
			</description>
		</method>
		<method name="viewport_set_occlusion_culling_build_quality">
			<return type="void" />
			<param index="0" name="quality" type="int" enum="RenderingServer.ViewportOcclusionCullingBuildQuality" />
			<description>
				Sets the [member ProjectSettings.rendering/occlusion_culling/bvh_build_quality] to use for occlusion culling. This parameter is global and cannot be set on a per-viewport basis.
			</description>
		</method>
		<method name="viewport_set_occlusion_rays_per_thread">
			<return type="void" />
			<param index="0" name="rays_per_thread" type="int" />
			<description>
				Sets the [member ProjectSettings.rendering/occlusion_culling/occlusion_rays_per_thread] to use for occlusion culling. This parameter is global and cannot be set on a per-viewport basis.
			</description>
		</method>
		<method name="viewport_set_parent_viewport">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="parent_viewport" type="RID" />
			<description>
				Sets the viewport's parent to the viewport specified by the [param parent_viewport] RID.
			</description>
		</method>
		<method name="viewport_set_positional_shadow_atlas_quadrant_subdivision">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="quadrant" type="int" />
			<param index="2" name="subdivision" type="int" />
			<description>
				Sets the number of subdivisions to use in the specified shadow atlas [param quadrant] for omni and spot shadows. See also [method Viewport.set_positional_shadow_atlas_quadrant_subdiv].
			</description>
		</method>
		<method name="viewport_set_positional_shadow_atlas_size">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="size" type="int" />
			<param index="2" name="use_16_bits" type="bool" default="false" />
			<description>
				Sets the [param size] of the shadow atlas's images (used for omni and spot lights) on the viewport specified by the [param viewport] RID. The value is rounded up to the nearest power of 2. If [param use_16_bits] is [code]true[/code], use 16 bits for the omni/spot shadow depth map. Enabling this results in shadows having less precision and may result in shadow acne, but can lead to performance improvements on some devices.
				[b]Note:[/b] If this is set to [code]0[/code], no positional shadows will be visible at all. This can improve performance significantly on low-end systems by reducing both the CPU and GPU load (as fewer draw calls are needed to draw the scene without shadows).
			</description>
		</method>
		<method name="viewport_set_render_direct_to_screen">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="enabled" type="bool" />
			<description>
				If [code]true[/code], render the contents of the viewport directly to screen. This allows a low-level optimization where you can skip drawing a viewport to the root viewport. While this optimization can result in a significant increase in speed (especially on older devices), it comes at a cost of usability. When this is enabled, you cannot read from the viewport or from the screen_texture. You also lose the benefit of certain window settings, such as the various stretch modes. Another consequence to be aware of is that in 2D the rendering happens in window coordinates, so if you have a viewport that is double the size of the window, and you set this, then only the portion that fits within the window will be drawn, no automatic scaling is possible, even if your game scene is significantly larger than the window size.
			</description>
		</method>
		<method name="viewport_set_scaling_3d_mode">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="scaling_3d_mode" type="int" enum="RenderingServer.ViewportScaling3DMode" />
			<description>
				Sets the 3D resolution scaling mode. Bilinear scaling renders at different resolution to either undersample or supersample the viewport. FidelityFX Super Resolution 1.0, abbreviated to FSR, is an upscaling technology that produces high quality images at fast framerates by using a spatially aware upscaling algorithm. FSR is slightly more expensive than bilinear, but it produces significantly higher image quality. FSR should be used where possible.
			</description>
		</method>
		<method name="viewport_set_scaling_3d_scale">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="scale" type="float" />
			<description>
				Scales the 3D render buffer based on the viewport size uses an image filter specified in [enum ViewportScaling3DMode] to scale the output image to the full viewport size. Values lower than [code]1.0[/code] can be used to speed up 3D rendering at the cost of quality (undersampling). Values greater than [code]1.0[/code] are only valid for bilinear mode and can be used to improve 3D rendering quality at a high performance cost (supersampling). See also [enum ViewportMSAA] for multi-sample antialiasing, which is significantly cheaper but only smoothens the edges of polygons.
				When using FSR upscaling, AMD recommends exposing the following values as preset options to users "Ultra Quality: 0.77", "Quality: 0.67", "Balanced: 0.59", "Performance: 0.5" instead of exposing the entire scale.
			</description>
		</method>
		<method name="viewport_set_scenario">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="scenario" type="RID" />
			<description>
				Sets a viewport's scenario. The scenario contains information about environment information, reflection atlas, etc.
			</description>
		</method>
		<method name="viewport_set_screen_space_aa">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="mode" type="int" enum="RenderingServer.ViewportScreenSpaceAA" />
			<description>
				Sets the viewport's screen-space antialiasing mode. Equivalent to [member ProjectSettings.rendering/anti_aliasing/quality/screen_space_aa] or [member Viewport.screen_space_aa].
			</description>
		</method>
		<method name="viewport_set_sdf_oversize_and_scale">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="oversize" type="int" enum="RenderingServer.ViewportSDFOversize" />
			<param index="2" name="scale" type="int" enum="RenderingServer.ViewportSDFScale" />
			<description>
				Sets the viewport's 2D signed distance field [member ProjectSettings.rendering/2d/sdf/oversize] and [member ProjectSettings.rendering/2d/sdf/scale]. This is used when sampling the signed distance field in [CanvasItem] shaders as well as [GPUParticles2D] collision. This is [i]not[/i] used by SDFGI in 3D rendering.
			</description>
		</method>
		<method name="viewport_set_size">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="width" type="int" />
			<param index="2" name="height" type="int" />
			<description>
				Sets the viewport's width and height in pixels.
			</description>
		</method>
		<method name="viewport_set_snap_2d_transforms_to_pixel">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="enabled" type="bool" />
			<description>
				If [code]true[/code], canvas item transforms (i.e. origin position) are snapped to the nearest pixel when rendering. This can lead to a crisper appearance at the cost of less smooth movement, especially when [Camera2D] smoothing is enabled. Equivalent to [member ProjectSettings.rendering/2d/snap/snap_2d_transforms_to_pixel].
			</description>
		</method>
		<method name="viewport_set_snap_2d_vertices_to_pixel">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="enabled" type="bool" />
			<description>
				If [code]true[/code], canvas item vertices (i.e. polygon points) are snapped to the nearest pixel when rendering. This can lead to a crisper appearance at the cost of less smooth movement, especially when [Camera2D] smoothing is enabled. Equivalent to [member ProjectSettings.rendering/2d/snap/snap_2d_vertices_to_pixel].
			</description>
		</method>
		<method name="viewport_set_texture_mipmap_bias">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="mipmap_bias" type="float" />
			<description>
				Affects the final texture sharpness by reading from a lower or higher mipmap (also called "texture LOD bias"). Negative values make mipmapped textures sharper but grainier when viewed at a distance, while positive values make mipmapped textures blurrier (even when up close). To get sharper textures at a distance without introducing too much graininess, set this between [code]-0.75[/code] and [code]0.0[/code]. Enabling temporal antialiasing ([member ProjectSettings.rendering/anti_aliasing/quality/use_taa]) can help reduce the graininess visible when using negative mipmap bias.
				[b]Note:[/b] When the 3D scaling mode is set to FSR 1.0, this value is used to adjust the automatic mipmap bias which is calculated internally based on the scale factor. The formula for this is [code]-log2(1.0 / scale) + mipmap_bias[/code].
			</description>
		</method>
		<method name="viewport_set_transparent_background">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="enabled" type="bool" />
			<description>
				If [code]true[/code], the viewport renders its background as transparent.
			</description>
		</method>
		<method name="viewport_set_update_mode">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="update_mode" type="int" enum="RenderingServer.ViewportUpdateMode" />
			<description>
				Sets when the viewport should be updated. See [enum ViewportUpdateMode] constants for options.
			</description>
		</method>
		<method name="viewport_set_use_debanding">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="enable" type="bool" />
			<description>
				If [code]true[/code], enables debanding on the specified viewport. Equivalent to [member ProjectSettings.rendering/anti_aliasing/quality/use_debanding] or [member Viewport.use_debanding].
			</description>
		</method>
		<method name="viewport_set_use_hdr_2d">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="enabled" type="bool" />
			<description>
				If [code]true[/code], 2D rendering will use a high dynamic range (HDR) format framebuffer matching the bit depth of the 3D framebuffer. When using the Forward+ renderer this will be an [code]RGBA16[/code] framebuffer, while when using the Mobile renderer it will be an [code]RGB10_A2[/code] framebuffer. Additionally, 2D rendering will take place in linear color space and will be converted to sRGB space immediately before blitting to the screen (if the Viewport is attached to the screen). Practically speaking, this means that the end result of the Viewport will not be clamped into the [code]0-1[/code] range and can be used in 3D rendering without color space adjustments. This allows 2D rendering to take advantage of effects requiring high dynamic range (e.g. 2D glow) as well as substantially improves the appearance of effects requiring highly detailed gradients. This setting has the same effect as [member Viewport.use_hdr_2d].
				[b]Note:[/b] This setting will have no effect when using the Compatibility renderer, which always renders in low dynamic range for performance reasons.
			</description>
		</method>
		<method name="viewport_set_use_occlusion_culling">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="enable" type="bool" />
			<description>
				If [code]true[/code], enables occlusion culling on the specified viewport. Equivalent to [member ProjectSettings.rendering/occlusion_culling/use_occlusion_culling].
			</description>
		</method>
		<method name="viewport_set_use_taa">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="enable" type="bool" />
			<description>
				If [code]true[/code], use temporal antialiasing. Equivalent to [member ProjectSettings.rendering/anti_aliasing/quality/use_taa] or [member Viewport.use_taa].
			</description>
		</method>
		<method name="viewport_set_use_xr">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="use_xr" type="bool" />
			<description>
				If [code]true[/code], the viewport uses augmented or virtual reality technologies. See [XRInterface].
			</description>
		</method>
		<method name="viewport_set_vrs_mode">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="mode" type="int" enum="RenderingServer.ViewportVRSMode" />
			<description>
				Sets the Variable Rate Shading (VRS) mode for the viewport. If the GPU does not support VRS, this property is ignored. Equivalent to [member ProjectSettings.rendering/vrs/mode].
			</description>
		</method>
		<method name="viewport_set_vrs_texture">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="texture" type="RID" />
			<description>
				The texture to use when the VRS mode is set to [constant RenderingServer.VIEWPORT_VRS_TEXTURE]. Equivalent to [member ProjectSettings.rendering/vrs/texture].
			</description>
		</method>
		<method name="viewport_set_vrs_update_mode">
			<return type="void" />
			<param index="0" name="viewport" type="RID" />
			<param index="1" name="mode" type="int" enum="RenderingServer.ViewportVRSUpdateMode" />
			<description>
				Sets the update mode for Variable Rate Shading (VRS) for the viewport. VRS requires the input texture to be converted to the format usable by the VRS method supported by the hardware. The update mode defines how often this happens. If the GPU does not support VRS, or VRS is not enabled, this property is ignored.
				If set to [constant RenderingServer.VIEWPORT_VRS_UPDATE_ONCE], the input texture is copied once and the mode is changed to [constant RenderingServer.VIEWPORT_VRS_UPDATE_DISABLED].
			</description>
		</method>
		<method name="visibility_notifier_create">
			<return type="RID" />
			<description>
				Creates a new 3D visibility notifier object and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]visibility_notifier_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				To place in a scene, attach this notifier to an instance using [method instance_set_base] using the returned RID.
				[b]Note:[/b] The equivalent node is [VisibleOnScreenNotifier3D].
			</description>
		</method>
		<method name="visibility_notifier_set_aabb">
			<return type="void" />
			<param index="0" name="notifier" type="RID" />
			<param index="1" name="aabb" type="AABB" />
			<description>
			</description>
		</method>
		<method name="visibility_notifier_set_callbacks">
			<return type="void" />
			<param index="0" name="notifier" type="RID" />
			<param index="1" name="enter_callable" type="Callable" />
			<param index="2" name="exit_callable" type="Callable" />
			<description>
			</description>
		</method>
		<method name="voxel_gi_allocate_data">
			<return type="void" />
			<param index="0" name="voxel_gi" type="RID" />
			<param index="1" name="to_cell_xform" type="Transform3D" />
			<param index="2" name="aabb" type="AABB" />
			<param index="3" name="octree_size" type="Vector3i" />
			<param index="4" name="octree_cells" type="PackedByteArray" />
			<param index="5" name="data_cells" type="PackedByteArray" />
			<param index="6" name="distance_field" type="PackedByteArray" />
			<param index="7" name="level_counts" type="PackedInt32Array" />
			<description>
			</description>
		</method>
		<method name="voxel_gi_create">
			<return type="RID" />
			<description>
				Creates a new voxel-based global illumination object and adds it to the RenderingServer. It can be accessed with the RID that is returned. This RID will be used in all [code]voxel_gi_*[/code] RenderingServer functions.
				Once finished with your RID, you will want to free the RID using the RenderingServer's [method free_rid] method.
				[b]Note:[/b] The equivalent node is [VoxelGI].
			</description>
		</method>
		<method name="voxel_gi_get_data_cells" qualifiers="const">
			<return type="PackedByteArray" />
			<param index="0" name="voxel_gi" type="RID" />
			<description>
			</description>
		</method>
		<method name="voxel_gi_get_distance_field" qualifiers="const">
			<return type="PackedByteArray" />
			<param index="0" name="voxel_gi" type="RID" />
			<description>
			</description>
		</method>
		<method name="voxel_gi_get_level_counts" qualifiers="const">
			<return type="PackedInt32Array" />
			<param index="0" name="voxel_gi" type="RID" />
			<description>
			</description>
		</method>
		<method name="voxel_gi_get_octree_cells" qualifiers="const">
			<return type="PackedByteArray" />
			<param index="0" name="voxel_gi" type="RID" />
			<description>
			</description>
		</method>
		<method name="voxel_gi_get_octree_size" qualifiers="const">
			<return type="Vector3i" />
			<param index="0" name="voxel_gi" type="RID" />
			<description>
			</description>
		</method>
		<method name="voxel_gi_get_to_cell_xform" qualifiers="const">
			<return type="Transform3D" />
			<param index="0" name="voxel_gi" type="RID" />
			<description>
			</description>
		</method>
		<method name="voxel_gi_set_baked_exposure_normalization">
			<return type="void" />
			<param index="0" name="voxel_gi" type="RID" />
			<param index="1" name="baked_exposure" type="float" />
			<description>
				Used to inform the renderer what exposure normalization value was used while baking the voxel gi. This value will be used and modulated at run time to ensure that the voxel gi maintains a consistent level of exposure even if the scene-wide exposure normalization is changed at run time. For more information see [method camera_attributes_set_exposure].
			</description>
		</method>
		<method name="voxel_gi_set_bias">
			<return type="void" />
			<param index="0" name="voxel_gi" type="RID" />
			<param index="1" name="bias" type="float" />
			<description>
				Sets the [member VoxelGIData.bias] value to use on the specified [param voxel_gi]'s [RID].
			</description>
		</method>
		<method name="voxel_gi_set_dynamic_range">
			<return type="void" />
			<param index="0" name="voxel_gi" type="RID" />
			<param index="1" name="range" type="float" />
			<description>
				Sets the [member VoxelGIData.dynamic_range] value to use on the specified [param voxel_gi]'s [RID].
			</description>
		</method>
		<method name="voxel_gi_set_energy">
			<return type="void" />
			<param index="0" name="voxel_gi" type="RID" />
			<param index="1" name="energy" type="float" />
			<description>
				Sets the [member VoxelGIData.energy] value to use on the specified [param voxel_gi]'s [RID].
			</description>
		</method>
		<method name="voxel_gi_set_interior">
			<return type="void" />
			<param index="0" name="voxel_gi" type="RID" />
			<param index="1" name="enable" type="bool" />
			<description>
				Sets the [member VoxelGIData.interior] value to use on the specified [param voxel_gi]'s [RID].
			</description>
		</method>
		<method name="voxel_gi_set_normal_bias">
			<return type="void" />
			<param index="0" name="voxel_gi" type="RID" />
			<param index="1" name="bias" type="float" />
			<description>
				Sets the [member VoxelGIData.normal_bias] value to use on the specified [param voxel_gi]'s [RID].
			</description>
		</method>
		<method name="voxel_gi_set_propagation">
			<return type="void" />
			<param index="0" name="voxel_gi" type="RID" />
			<param index="1" name="amount" type="float" />
			<description>
				Sets the [member VoxelGIData.propagation] value to use on the specified [param voxel_gi]'s [RID].
			</description>
		</method>
		<method name="voxel_gi_set_quality">
			<return type="void" />
			<param index="0" name="quality" type="int" enum="RenderingServer.VoxelGIQuality" />
			<description>
				Sets the [member ProjectSettings.rendering/global_illumination/voxel_gi/quality] value to use when rendering. This parameter is global and cannot be set on a per-VoxelGI basis.
			</description>
		</method>
		<method name="voxel_gi_set_use_two_bounces">
			<return type="void" />
			<param index="0" name="voxel_gi" type="RID" />
			<param index="1" name="enable" type="bool" />
			<description>
				Sets the [member VoxelGIData.use_two_bounces] value to use on the specified [param voxel_gi]'s [RID].
			</description>
		</method>
	</methods>
	<members>
		<member name="render_loop_enabled" type="bool" setter="set_render_loop_enabled" getter="is_render_loop_enabled">
			If [code]false[/code], disables rendering completely, but the engine logic is still being processed. You can call [method force_draw] to draw a frame even with rendering disabled.
		</member>
	</members>
	<signals>
		<signal name="frame_post_draw">
			<description>
				Emitted at the end of the frame, after the RenderingServer has finished updating all the Viewports.
			</description>
		</signal>
		<signal name="frame_pre_draw">
			<description>
				Emitted at the beginning of the frame, before the RenderingServer updates all the Viewports.
			</description>
		</signal>
	</signals>
	<constants>
		<constant name="NO_INDEX_ARRAY" value="-1">
			Marks an error that shows that the index array is empty.
		</constant>
		<constant name="ARRAY_WEIGHTS_SIZE" value="4">
			Number of weights/bones per vertex.
		</constant>
		<constant name="CANVAS_ITEM_Z_MIN" value="-4096">
			The minimum Z-layer for canvas items.
		</constant>
		<constant name="CANVAS_ITEM_Z_MAX" value="4096">
			The maximum Z-layer for canvas items.
		</constant>
		<constant name="MAX_GLOW_LEVELS" value="7">
			The maximum number of glow levels that can be used with the glow post-processing effect.
		</constant>
		<constant name="MAX_CURSORS" value="8" deprecated="This constant is not used by the engine.">
		</constant>
		<constant name="MAX_2D_DIRECTIONAL_LIGHTS" value="8">
			The maximum number of directional lights that can be rendered at a given time in 2D.
		</constant>
		<constant name="MAX_MESH_SURFACES" value="256">
			The maximum number of surfaces a mesh can have.
		</constant>
		<constant name="TEXTURE_TYPE_2D" value="0" enum="TextureType">
			2D texture.
		</constant>
		<constant name="TEXTURE_TYPE_LAYERED" value="1" enum="TextureType">
			Layered texture.
		</constant>
		<constant name="TEXTURE_TYPE_3D" value="2" enum="TextureType">
			3D texture.
		</constant>
		<constant name="TEXTURE_LAYERED_2D_ARRAY" value="0" enum="TextureLayeredType">
			Array of 2-dimensional textures (see [Texture2DArray]).
		</constant>
		<constant name="TEXTURE_LAYERED_CUBEMAP" value="1" enum="TextureLayeredType">
			Cubemap texture (see [Cubemap]).
		</constant>
		<constant name="TEXTURE_LAYERED_CUBEMAP_ARRAY" value="2" enum="TextureLayeredType">
			Array of cubemap textures (see [CubemapArray]).
		</constant>
		<constant name="CUBEMAP_LAYER_LEFT" value="0" enum="CubeMapLayer">
			Left face of a [Cubemap].
		</constant>
		<constant name="CUBEMAP_LAYER_RIGHT" value="1" enum="CubeMapLayer">
			Right face of a [Cubemap].
		</constant>
		<constant name="CUBEMAP_LAYER_BOTTOM" value="2" enum="CubeMapLayer">
			Bottom face of a [Cubemap].
		</constant>
		<constant name="CUBEMAP_LAYER_TOP" value="3" enum="CubeMapLayer">
			Top face of a [Cubemap].
		</constant>
		<constant name="CUBEMAP_LAYER_FRONT" value="4" enum="CubeMapLayer">
			Front face of a [Cubemap].
		</constant>
		<constant name="CUBEMAP_LAYER_BACK" value="5" enum="CubeMapLayer">
			Back face of a [Cubemap].
		</constant>
		<constant name="SHADER_SPATIAL" value="0" enum="ShaderMode">
			Shader is a 3D shader.
		</constant>
		<constant name="SHADER_CANVAS_ITEM" value="1" enum="ShaderMode">
			Shader is a 2D shader.
		</constant>
		<constant name="SHADER_PARTICLES" value="2" enum="ShaderMode">
			Shader is a particle shader (can be used in both 2D and 3D).
		</constant>
		<constant name="SHADER_SKY" value="3" enum="ShaderMode">
			Shader is a 3D sky shader.
		</constant>
		<constant name="SHADER_FOG" value="4" enum="ShaderMode">
			Shader is a 3D fog shader.
		</constant>
		<constant name="SHADER_MAX" value="5" enum="ShaderMode">
			Represents the size of the [enum ShaderMode] enum.
		</constant>
		<constant name="MATERIAL_RENDER_PRIORITY_MIN" value="-128">
			The minimum renderpriority of all materials.
		</constant>
		<constant name="MATERIAL_RENDER_PRIORITY_MAX" value="127">
			The maximum renderpriority of all materials.
		</constant>
		<constant name="ARRAY_VERTEX" value="0" enum="ArrayType">
			Array is a vertex position array.
		</constant>
		<constant name="ARRAY_NORMAL" value="1" enum="ArrayType">
			Array is a normal array.
		</constant>
		<constant name="ARRAY_TANGENT" value="2" enum="ArrayType">
			Array is a tangent array.
		</constant>
		<constant name="ARRAY_COLOR" value="3" enum="ArrayType">
			Array is a vertex color array.
		</constant>
		<constant name="ARRAY_TEX_UV" value="4" enum="ArrayType">
			Array is a UV coordinates array.
		</constant>
		<constant name="ARRAY_TEX_UV2" value="5" enum="ArrayType">
			Array is a UV coordinates array for the second set of UV coordinates.
		</constant>
		<constant name="ARRAY_CUSTOM0" value="6" enum="ArrayType">
			Array is a custom data array for the first set of custom data.
		</constant>
		<constant name="ARRAY_CUSTOM1" value="7" enum="ArrayType">
			Array is a custom data array for the second set of custom data.
		</constant>
		<constant name="ARRAY_CUSTOM2" value="8" enum="ArrayType">
			Array is a custom data array for the third set of custom data.
		</constant>
		<constant name="ARRAY_CUSTOM3" value="9" enum="ArrayType">
			Array is a custom data array for the fourth set of custom data.
		</constant>
		<constant name="ARRAY_BONES" value="10" enum="ArrayType">
			Array contains bone information.
		</constant>
		<constant name="ARRAY_WEIGHTS" value="11" enum="ArrayType">
			Array is weight information.
		</constant>
		<constant name="ARRAY_INDEX" value="12" enum="ArrayType">
			Array is an index array.
		</constant>
		<constant name="ARRAY_MAX" value="13" enum="ArrayType">
			Represents the size of the [enum ArrayType] enum.
		</constant>
		<constant name="ARRAY_CUSTOM_COUNT" value="4">
			The number of custom data arrays available ([constant ARRAY_CUSTOM0], [constant ARRAY_CUSTOM1], [constant ARRAY_CUSTOM2], [constant ARRAY_CUSTOM3]).
		</constant>
		<constant name="ARRAY_CUSTOM_RGBA8_UNORM" value="0" enum="ArrayCustomFormat">
			Custom data array contains 8-bit-per-channel red/green/blue/alpha color data. Values are normalized, unsigned floating-point in the [code][0.0, 1.0][/code] range.
		</constant>
		<constant name="ARRAY_CUSTOM_RGBA8_SNORM" value="1" enum="ArrayCustomFormat">
			Custom data array contains 8-bit-per-channel red/green/blue/alpha color data. Values are normalized, signed floating-point in the [code][-1.0, 1.0][/code] range.
		</constant>
		<constant name="ARRAY_CUSTOM_RG_HALF" value="2" enum="ArrayCustomFormat">
			Custom data array contains 16-bit-per-channel red/green color data. Values are floating-point in half precision.
		</constant>
		<constant name="ARRAY_CUSTOM_RGBA_HALF" value="3" enum="ArrayCustomFormat">
			Custom data array contains 16-bit-per-channel red/green/blue/alpha color data. Values are floating-point in half precision.
		</constant>
		<constant name="ARRAY_CUSTOM_R_FLOAT" value="4" enum="ArrayCustomFormat">
			Custom data array contains 32-bit-per-channel red color data. Values are floating-point in single precision.
		</constant>
		<constant name="ARRAY_CUSTOM_RG_FLOAT" value="5" enum="ArrayCustomFormat">
			Custom data array contains 32-bit-per-channel red/green color data. Values are floating-point in single precision.
		</constant>
		<constant name="ARRAY_CUSTOM_RGB_FLOAT" value="6" enum="ArrayCustomFormat">
			Custom data array contains 32-bit-per-channel red/green/blue color data. Values are floating-point in single precision.
		</constant>
		<constant name="ARRAY_CUSTOM_RGBA_FLOAT" value="7" enum="ArrayCustomFormat">
			Custom data array contains 32-bit-per-channel red/green/blue/alpha color data. Values are floating-point in single precision.
		</constant>
		<constant name="ARRAY_CUSTOM_MAX" value="8" enum="ArrayCustomFormat">
			Represents the size of the [enum ArrayCustomFormat] enum.
		</constant>
		<constant name="ARRAY_FORMAT_VERTEX" value="1" enum="ArrayFormat" is_bitfield="true">
			Flag used to mark a vertex position array.
		</constant>
		<constant name="ARRAY_FORMAT_NORMAL" value="2" enum="ArrayFormat" is_bitfield="true">
			Flag used to mark a normal array.
		</constant>
		<constant name="ARRAY_FORMAT_TANGENT" value="4" enum="ArrayFormat" is_bitfield="true">
			Flag used to mark a tangent array.
		</constant>
		<constant name="ARRAY_FORMAT_COLOR" value="8" enum="ArrayFormat" is_bitfield="true">
			Flag used to mark a vertex color array.
		</constant>
		<constant name="ARRAY_FORMAT_TEX_UV" value="16" enum="ArrayFormat" is_bitfield="true">
			Flag used to mark a UV coordinates array.
		</constant>
		<constant name="ARRAY_FORMAT_TEX_UV2" value="32" enum="ArrayFormat" is_bitfield="true">
			Flag used to mark a UV coordinates array for the second UV coordinates.
		</constant>
		<constant name="ARRAY_FORMAT_CUSTOM0" value="64" enum="ArrayFormat" is_bitfield="true">
			Flag used to mark an array of custom per-vertex data for the first set of custom data.
		</constant>
		<constant name="ARRAY_FORMAT_CUSTOM1" value="128" enum="ArrayFormat" is_bitfield="true">
			Flag used to mark an array of custom per-vertex data for the second set of custom data.
		</constant>
		<constant name="ARRAY_FORMAT_CUSTOM2" value="256" enum="ArrayFormat" is_bitfield="true">
			Flag used to mark an array of custom per-vertex data for the third set of custom data.
		</constant>
		<constant name="ARRAY_FORMAT_CUSTOM3" value="512" enum="ArrayFormat" is_bitfield="true">
			Flag used to mark an array of custom per-vertex data for the fourth set of custom data.
		</constant>
		<constant name="ARRAY_FORMAT_BONES" value="1024" enum="ArrayFormat" is_bitfield="true">
			Flag used to mark a bone information array.
		</constant>
		<constant name="ARRAY_FORMAT_WEIGHTS" value="2048" enum="ArrayFormat" is_bitfield="true">
			Flag used to mark a weights array.
		</constant>
		<constant name="ARRAY_FORMAT_INDEX" value="4096" enum="ArrayFormat" is_bitfield="true">
			Flag used to mark an index array.
		</constant>
		<constant name="ARRAY_FORMAT_BLEND_SHAPE_MASK" value="7" enum="ArrayFormat" is_bitfield="true">
			Mask of mesh channels permitted in blend shapes.
		</constant>
		<constant name="ARRAY_FORMAT_CUSTOM_BASE" value="13" enum="ArrayFormat" is_bitfield="true">
			Shift of first custom channel.
		</constant>
		<constant name="ARRAY_FORMAT_CUSTOM_BITS" value="3" enum="ArrayFormat" is_bitfield="true">
			Number of format bits per custom channel. See [enum ArrayCustomFormat].
		</constant>
		<constant name="ARRAY_FORMAT_CUSTOM0_SHIFT" value="13" enum="ArrayFormat" is_bitfield="true">
			Amount to shift [enum ArrayCustomFormat] for custom channel index 0.
		</constant>
		<constant name="ARRAY_FORMAT_CUSTOM1_SHIFT" value="16" enum="ArrayFormat" is_bitfield="true">
			Amount to shift [enum ArrayCustomFormat] for custom channel index 1.
		</constant>
		<constant name="ARRAY_FORMAT_CUSTOM2_SHIFT" value="19" enum="ArrayFormat" is_bitfield="true">
			Amount to shift [enum ArrayCustomFormat] for custom channel index 2.
		</constant>
		<constant name="ARRAY_FORMAT_CUSTOM3_SHIFT" value="22" enum="ArrayFormat" is_bitfield="true">
			Amount to shift [enum ArrayCustomFormat] for custom channel index 3.
		</constant>
		<constant name="ARRAY_FORMAT_CUSTOM_MASK" value="7" enum="ArrayFormat" is_bitfield="true">
			Mask of custom format bits per custom channel. Must be shifted by one of the SHIFT constants. See [enum ArrayCustomFormat].
		</constant>
		<constant name="ARRAY_COMPRESS_FLAGS_BASE" value="25" enum="ArrayFormat" is_bitfield="true">
			Shift of first compress flag. Compress flags should be passed to [method ArrayMesh.add_surface_from_arrays] and [method SurfaceTool.commit].
		</constant>
		<constant name="ARRAY_FLAG_USE_2D_VERTICES" value="33554432" enum="ArrayFormat" is_bitfield="true">
			Flag used to mark that the array contains 2D vertices.
		</constant>
		<constant name="ARRAY_FLAG_USE_DYNAMIC_UPDATE" value="67108864" enum="ArrayFormat" is_bitfield="true">
			Flag used to mark that the mesh data will use [code]GL_DYNAMIC_DRAW[/code] on GLES. Unused on Vulkan.
		</constant>
		<constant name="ARRAY_FLAG_USE_8_BONE_WEIGHTS" value="134217728" enum="ArrayFormat" is_bitfield="true">
			Flag used to mark that the array uses 8 bone weights instead of 4.
		</constant>
		<constant name="ARRAY_FLAG_USES_EMPTY_VERTEX_ARRAY" value="268435456" enum="ArrayFormat" is_bitfield="true">
			Flag used to mark that the mesh does not have a vertex array and instead will infer vertex positions in the shader using indices and other information.
		</constant>
		<constant name="ARRAY_FLAG_COMPRESS_ATTRIBUTES" value="536870912" enum="ArrayFormat" is_bitfield="true">
			Flag used to mark that a mesh is using compressed attributes (vertices, normals, tangents, UVs). When this form of compression is enabled, vertex positions will be packed into an RGBA16UNORM attribute and scaled in the vertex shader. The normal and tangent will be packed into an RG16UNORM representing an axis, and a 16-bit float stored in the A-channel of the vertex. UVs will use 16-bit normalized floats instead of full 32-bit signed floats. When using this compression mode you must use either vertices, normals, and tangents or only vertices. You cannot use normals without tangents. Importers will automatically enable this compression if they can.
		</constant>
		<constant name="ARRAY_FLAG_FORMAT_VERSION_BASE" value="35" enum="ArrayFormat" is_bitfield="true">
			Flag used to mark the start of the bits used to store the mesh version.
		</constant>
		<constant name="ARRAY_FLAG_FORMAT_VERSION_SHIFT" value="35" enum="ArrayFormat" is_bitfield="true">
			Flag used to shift a mesh format int to bring the version into the lowest digits.
		</constant>
		<constant name="ARRAY_FLAG_FORMAT_VERSION_1" value="0" enum="ArrayFormat" is_bitfield="true">
			Flag used to record the format used by prior mesh versions before the introduction of a version.
		</constant>
		<constant name="ARRAY_FLAG_FORMAT_VERSION_2" value="34359738368" enum="ArrayFormat" is_bitfield="true">
			Flag used to record the second iteration of the mesh version flag. The primary difference between this and [constant ARRAY_FLAG_FORMAT_VERSION_1] is that this version supports [constant ARRAY_FLAG_COMPRESS_ATTRIBUTES] and in this version vertex positions are de-interleaved from normals and tangents.
		</constant>
		<constant name="ARRAY_FLAG_FORMAT_CURRENT_VERSION" value="34359738368" enum="ArrayFormat" is_bitfield="true">
			Flag used to record the current version that the engine expects. Currently this is the same as [constant ARRAY_FLAG_FORMAT_VERSION_2].
		</constant>
		<constant name="ARRAY_FLAG_FORMAT_VERSION_MASK" value="255" enum="ArrayFormat" is_bitfield="true">
			Flag used to isolate the bits used for mesh version after using [constant ARRAY_FLAG_FORMAT_VERSION_SHIFT] to shift them into place.
		</constant>
		<constant name="PRIMITIVE_POINTS" value="0" enum="PrimitiveType">
			Primitive to draw consists of points.
		</constant>
		<constant name="PRIMITIVE_LINES" value="1" enum="PrimitiveType">
			Primitive to draw consists of lines.
		</constant>
		<constant name="PRIMITIVE_LINE_STRIP" value="2" enum="PrimitiveType">
			Primitive to draw consists of a line strip from start to end.
		</constant>
		<constant name="PRIMITIVE_TRIANGLES" value="3" enum="PrimitiveType">
			Primitive to draw consists of triangles.
		</constant>
		<constant name="PRIMITIVE_TRIANGLE_STRIP" value="4" enum="PrimitiveType">
			Primitive to draw consists of a triangle strip (the last 3 vertices are always combined to make a triangle).
		</constant>
		<constant name="PRIMITIVE_MAX" value="5" enum="PrimitiveType">
			Represents the size of the [enum PrimitiveType] enum.
		</constant>
		<constant name="BLEND_SHAPE_MODE_NORMALIZED" value="0" enum="BlendShapeMode">
			Blend shapes are normalized.
		</constant>
		<constant name="BLEND_SHAPE_MODE_RELATIVE" value="1" enum="BlendShapeMode">
			Blend shapes are relative to base weight.
		</constant>
		<constant name="MULTIMESH_TRANSFORM_2D" value="0" enum="MultimeshTransformFormat">
			Use [Transform2D] to store MultiMesh transform.
		</constant>
		<constant name="MULTIMESH_TRANSFORM_3D" value="1" enum="MultimeshTransformFormat">
			Use [Transform3D] to store MultiMesh transform.
		</constant>
		<constant name="MULTIMESH_INTERP_QUALITY_FAST" value="0" enum="MultimeshPhysicsInterpolationQuality">
			MultiMesh physics interpolation favors speed over quality.
		</constant>
		<constant name="MULTIMESH_INTERP_QUALITY_HIGH" value="1" enum="MultimeshPhysicsInterpolationQuality">
			MultiMesh physics interpolation favors quality over speed.
		</constant>
		<constant name="LIGHT_PROJECTOR_FILTER_NEAREST" value="0" enum="LightProjectorFilter">
			Nearest-neighbor filter for light projectors (use for pixel art light projectors). No mipmaps are used for rendering, which means light projectors at a distance will look sharp but grainy. This has roughly the same performance cost as using mipmaps.
		</constant>
		<constant name="LIGHT_PROJECTOR_FILTER_LINEAR" value="1" enum="LightProjectorFilter">
			Linear filter for light projectors (use for non-pixel art light projectors). No mipmaps are used for rendering, which means light projectors at a distance will look smooth but blurry. This has roughly the same performance cost as using mipmaps.
		</constant>
		<constant name="LIGHT_PROJECTOR_FILTER_NEAREST_MIPMAPS" value="2" enum="LightProjectorFilter">
			Nearest-neighbor filter for light projectors (use for pixel art light projectors). Isotropic mipmaps are used for rendering, which means light projectors at a distance will look smooth but blurry. This has roughly the same performance cost as not using mipmaps.
		</constant>
		<constant name="LIGHT_PROJECTOR_FILTER_LINEAR_MIPMAPS" value="3" enum="LightProjectorFilter">
			Linear filter for light projectors (use for non-pixel art light projectors). Isotropic mipmaps are used for rendering, which means light projectors at a distance will look smooth but blurry. This has roughly the same performance cost as not using mipmaps.
		</constant>
		<constant name="LIGHT_PROJECTOR_FILTER_NEAREST_MIPMAPS_ANISOTROPIC" value="4" enum="LightProjectorFilter">
			Nearest-neighbor filter for light projectors (use for pixel art light projectors). Anisotropic mipmaps are used for rendering, which means light projectors at a distance will look smooth and sharp when viewed from oblique angles. This looks better compared to isotropic mipmaps, but is slower. The level of anisotropic filtering is defined by [member ProjectSettings.rendering/textures/default_filters/anisotropic_filtering_level].
		</constant>
		<constant name="LIGHT_PROJECTOR_FILTER_LINEAR_MIPMAPS_ANISOTROPIC" value="5" enum="LightProjectorFilter">
			Linear filter for light projectors (use for non-pixel art light projectors). Anisotropic mipmaps are used for rendering, which means light projectors at a distance will look smooth and sharp when viewed from oblique angles. This looks better compared to isotropic mipmaps, but is slower. The level of anisotropic filtering is defined by [member ProjectSettings.rendering/textures/default_filters/anisotropic_filtering_level].
		</constant>
		<constant name="LIGHT_DIRECTIONAL" value="0" enum="LightType">
			Directional (sun/moon) light (see [DirectionalLight3D]).
		</constant>
		<constant name="LIGHT_OMNI" value="1" enum="LightType">
			Omni light (see [OmniLight3D]).
		</constant>
		<constant name="LIGHT_SPOT" value="2" enum="LightType">
			Spot light (see [SpotLight3D]).
		</constant>
		<constant name="LIGHT_PARAM_ENERGY" value="0" enum="LightParam">
			The light's energy multiplier.
		</constant>
		<constant name="LIGHT_PARAM_INDIRECT_ENERGY" value="1" enum="LightParam">
			The light's indirect energy multiplier (final indirect energy is [constant LIGHT_PARAM_ENERGY] * [constant LIGHT_PARAM_INDIRECT_ENERGY]).
		</constant>
		<constant name="LIGHT_PARAM_VOLUMETRIC_FOG_ENERGY" value="2" enum="LightParam">
			The light's volumetric fog energy multiplier (final volumetric fog energy is [constant LIGHT_PARAM_ENERGY] * [constant LIGHT_PARAM_VOLUMETRIC_FOG_ENERGY]).
		</constant>
		<constant name="LIGHT_PARAM_SPECULAR" value="3" enum="LightParam">
			The light's influence on specularity.
		</constant>
		<constant name="LIGHT_PARAM_RANGE" value="4" enum="LightParam">
			The light's range.
		</constant>
		<constant name="LIGHT_PARAM_SIZE" value="5" enum="LightParam">
			The size of the light when using spot light or omni light. The angular size of the light when using directional light.
		</constant>
		<constant name="LIGHT_PARAM_ATTENUATION" value="6" enum="LightParam">
			The light's attenuation.
		</constant>
		<constant name="LIGHT_PARAM_SPOT_ANGLE" value="7" enum="LightParam">
			The spotlight's angle.
		</constant>
		<constant name="LIGHT_PARAM_SPOT_ATTENUATION" value="8" enum="LightParam">
			The spotlight's attenuation.
		</constant>
		<constant name="LIGHT_PARAM_SHADOW_MAX_DISTANCE" value="9" enum="LightParam">
			The maximum distance for shadow splits. Increasing this value will make directional shadows visible from further away, at the cost of lower overall shadow detail and performance (since more objects need to be included in the directional shadow rendering).
		</constant>
		<constant name="LIGHT_PARAM_SHADOW_SPLIT_1_OFFSET" value="10" enum="LightParam">
			Proportion of shadow atlas occupied by the first split.
		</constant>
		<constant name="LIGHT_PARAM_SHADOW_SPLIT_2_OFFSET" value="11" enum="LightParam">
			Proportion of shadow atlas occupied by the second split.
		</constant>
		<constant name="LIGHT_PARAM_SHADOW_SPLIT_3_OFFSET" value="12" enum="LightParam">
			Proportion of shadow atlas occupied by the third split. The fourth split occupies the rest.
		</constant>
		<constant name="LIGHT_PARAM_SHADOW_FADE_START" value="13" enum="LightParam">
			Proportion of shadow max distance where the shadow will start to fade out.
		</constant>
		<constant name="LIGHT_PARAM_SHADOW_NORMAL_BIAS" value="14" enum="LightParam">
			Normal bias used to offset shadow lookup by object normal. Can be used to fix self-shadowing artifacts.
		</constant>
		<constant name="LIGHT_PARAM_SHADOW_BIAS" value="15" enum="LightParam">
			Bias for the shadow lookup to fix self-shadowing artifacts.
		</constant>
		<constant name="LIGHT_PARAM_SHADOW_PANCAKE_SIZE" value="16" enum="LightParam">
			Sets the size of the directional shadow pancake. The pancake offsets the start of the shadow's camera frustum to provide a higher effective depth resolution for the shadow. However, a high pancake size can cause artifacts in the shadows of large objects that are close to the edge of the frustum. Reducing the pancake size can help. Setting the size to [code]0[/code] turns off the pancaking effect.
		</constant>
		<constant name="LIGHT_PARAM_SHADOW_OPACITY" value="17" enum="LightParam">
			The light's shadow opacity. Values lower than [code]1.0[/code] make the light appear through shadows. This can be used to fake global illumination at a low performance cost.
		</constant>
		<constant name="LIGHT_PARAM_SHADOW_BLUR" value="18" enum="LightParam">
			Blurs the edges of the shadow. Can be used to hide pixel artifacts in low resolution shadow maps. A high value can make shadows appear grainy and can cause other unwanted artifacts. Try to keep as near default as possible.
		</constant>
		<constant name="LIGHT_PARAM_TRANSMITTANCE_BIAS" value="19" enum="LightParam">
		</constant>
		<constant name="LIGHT_PARAM_INTENSITY" value="20" enum="LightParam">
			Constant representing the intensity of the light, measured in Lumens when dealing with a [SpotLight3D] or [OmniLight3D], or measured in Lux with a [DirectionalLight3D]. Only used when [member ProjectSettings.rendering/lights_and_shadows/use_physical_light_units] is [code]true[/code].
		</constant>
		<constant name="LIGHT_PARAM_MAX" value="21" enum="LightParam">
			Represents the size of the [enum LightParam] enum.
		</constant>
		<constant name="LIGHT_BAKE_DISABLED" value="0" enum="LightBakeMode">
			Light is ignored when baking. This is the fastest mode, but the light will be taken into account when baking global illumination. This mode should generally be used for dynamic lights that change quickly, as the effect of global illumination is less noticeable on those lights.
		</constant>
		<constant name="LIGHT_BAKE_STATIC" value="1" enum="LightBakeMode">
			Light is taken into account in static baking ([VoxelGI], [LightmapGI], SDFGI ([member Environment.sdfgi_enabled])). The light can be moved around or modified, but its global illumination will not update in real-time. This is suitable for subtle changes (such as flickering torches), but generally not large changes such as toggling a light on and off.
		</constant>
		<constant name="LIGHT_BAKE_DYNAMIC" value="2" enum="LightBakeMode">
			Light is taken into account in dynamic baking ([VoxelGI] and SDFGI ([member Environment.sdfgi_enabled]) only). The light can be moved around or modified with global illumination updating in real-time. The light's global illumination appearance will be slightly different compared to [constant LIGHT_BAKE_STATIC]. This has a greater performance cost compared to [constant LIGHT_BAKE_STATIC]. When using SDFGI, the update speed of dynamic lights is affected by [member ProjectSettings.rendering/global_illumination/sdfgi/frames_to_update_lights].
		</constant>
		<constant name="LIGHT_OMNI_SHADOW_DUAL_PARABOLOID" value="0" enum="LightOmniShadowMode">
			Use a dual paraboloid shadow map for omni lights.
		</constant>
		<constant name="LIGHT_OMNI_SHADOW_CUBE" value="1" enum="LightOmniShadowMode">
			Use a cubemap shadow map for omni lights. Slower but better quality than dual paraboloid.
		</constant>
		<constant name="LIGHT_DIRECTIONAL_SHADOW_ORTHOGONAL" value="0" enum="LightDirectionalShadowMode">
			Use orthogonal shadow projection for directional light.
		</constant>
		<constant name="LIGHT_DIRECTIONAL_SHADOW_PARALLEL_2_SPLITS" value="1" enum="LightDirectionalShadowMode">
			Use 2 splits for shadow projection when using directional light.
		</constant>
		<constant name="LIGHT_DIRECTIONAL_SHADOW_PARALLEL_4_SPLITS" value="2" enum="LightDirectionalShadowMode">
			Use 4 splits for shadow projection when using directional light.
		</constant>
		<constant name="LIGHT_DIRECTIONAL_SKY_MODE_LIGHT_AND_SKY" value="0" enum="LightDirectionalSkyMode">
			Use DirectionalLight3D in both sky rendering and scene lighting.
		</constant>
		<constant name="LIGHT_DIRECTIONAL_SKY_MODE_LIGHT_ONLY" value="1" enum="LightDirectionalSkyMode">
			Only use DirectionalLight3D in scene lighting.
		</constant>
		<constant name="LIGHT_DIRECTIONAL_SKY_MODE_SKY_ONLY" value="2" enum="LightDirectionalSkyMode">
			Only use DirectionalLight3D in sky rendering.
		</constant>
		<constant name="SHADOW_QUALITY_HARD" value="0" enum="ShadowQuality">
			Lowest shadow filtering quality (fastest). Soft shadows are not available with this quality setting, which means the [member Light3D.shadow_blur] property is ignored if [member Light3D.light_size] and [member Light3D.light_angular_distance] is [code]0.0[/code].
			[b]Note:[/b] The variable shadow blur performed by [member Light3D.light_size] and [member Light3D.light_angular_distance] is still effective when using hard shadow filtering. In this case, [member Light3D.shadow_blur] [i]is[/i] taken into account. However, the results will not be blurred, instead the blur amount is treated as a maximum radius for the penumbra.
		</constant>
		<constant name="SHADOW_QUALITY_SOFT_VERY_LOW" value="1" enum="ShadowQuality">
			Very low shadow filtering quality (faster). When using this quality setting, [member Light3D.shadow_blur] is automatically multiplied by 0.75× to avoid introducing too much noise. This division only applies to lights whose [member Light3D.light_size] or [member Light3D.light_angular_distance] is [code]0.0[/code]).
		</constant>
		<constant name="SHADOW_QUALITY_SOFT_LOW" value="2" enum="ShadowQuality">
			Low shadow filtering quality (fast).
		</constant>
		<constant name="SHADOW_QUALITY_SOFT_MEDIUM" value="3" enum="ShadowQuality">
			Medium low shadow filtering quality (average).
		</constant>
		<constant name="SHADOW_QUALITY_SOFT_HIGH" value="4" enum="ShadowQuality">
			High low shadow filtering quality (slow). When using this quality setting, [member Light3D.shadow_blur] is automatically multiplied by 1.5× to better make use of the high sample count. This increased blur also improves the stability of dynamic object shadows. This multiplier only applies to lights whose [member Light3D.light_size] or [member Light3D.light_angular_distance] is [code]0.0[/code]).
		</constant>
		<constant name="SHADOW_QUALITY_SOFT_ULTRA" value="5" enum="ShadowQuality">
			Highest low shadow filtering quality (slowest). When using this quality setting, [member Light3D.shadow_blur] is automatically multiplied by 2× to better make use of the high sample count. This increased blur also improves the stability of dynamic object shadows. This multiplier only applies to lights whose [member Light3D.light_size] or [member Light3D.light_angular_distance] is [code]0.0[/code]).
		</constant>
		<constant name="SHADOW_QUALITY_MAX" value="6" enum="ShadowQuality">
			Represents the size of the [enum ShadowQuality] enum.
		</constant>
		<constant name="REFLECTION_PROBE_UPDATE_ONCE" value="0" enum="ReflectionProbeUpdateMode">
			Reflection probe will update reflections once and then stop.
		</constant>
		<constant name="REFLECTION_PROBE_UPDATE_ALWAYS" value="1" enum="ReflectionProbeUpdateMode">
			Reflection probe will update each frame. This mode is necessary to capture moving objects.
		</constant>
		<constant name="REFLECTION_PROBE_AMBIENT_DISABLED" value="0" enum="ReflectionProbeAmbientMode">
			Do not apply any ambient lighting inside the reflection probe's box defined by its size.
		</constant>
		<constant name="REFLECTION_PROBE_AMBIENT_ENVIRONMENT" value="1" enum="ReflectionProbeAmbientMode">
			Apply automatically-sourced environment lighting inside the reflection probe's box defined by its size.
		</constant>
		<constant name="REFLECTION_PROBE_AMBIENT_COLOR" value="2" enum="ReflectionProbeAmbientMode">
			Apply custom ambient lighting inside the reflection probe's box defined by its size. See [method reflection_probe_set_ambient_color] and [method reflection_probe_set_ambient_energy].
		</constant>
		<constant name="DECAL_TEXTURE_ALBEDO" value="0" enum="DecalTexture">
			Albedo texture slot in a decal ([member Decal.texture_albedo]).
		</constant>
		<constant name="DECAL_TEXTURE_NORMAL" value="1" enum="DecalTexture">
			Normal map texture slot in a decal ([member Decal.texture_normal]).
		</constant>
		<constant name="DECAL_TEXTURE_ORM" value="2" enum="DecalTexture">
			Occlusion/Roughness/Metallic texture slot in a decal ([member Decal.texture_orm]).
		</constant>
		<constant name="DECAL_TEXTURE_EMISSION" value="3" enum="DecalTexture">
			Emission texture slot in a decal ([member Decal.texture_emission]).
		</constant>
		<constant name="DECAL_TEXTURE_MAX" value="4" enum="DecalTexture">
			Represents the size of the [enum DecalTexture] enum.
		</constant>
		<constant name="DECAL_FILTER_NEAREST" value="0" enum="DecalFilter">
			Nearest-neighbor filter for decals (use for pixel art decals). No mipmaps are used for rendering, which means decals at a distance will look sharp but grainy. This has roughly the same performance cost as using mipmaps.
		</constant>
		<constant name="DECAL_FILTER_LINEAR" value="1" enum="DecalFilter">
			Linear filter for decals (use for non-pixel art decals). No mipmaps are used for rendering, which means decals at a distance will look smooth but blurry. This has roughly the same performance cost as using mipmaps.
		</constant>
		<constant name="DECAL_FILTER_NEAREST_MIPMAPS" value="2" enum="DecalFilter">
			Nearest-neighbor filter for decals (use for pixel art decals). Isotropic mipmaps are used for rendering, which means decals at a distance will look smooth but blurry. This has roughly the same performance cost as not using mipmaps.
		</constant>
		<constant name="DECAL_FILTER_LINEAR_MIPMAPS" value="3" enum="DecalFilter">
			Linear filter for decals (use for non-pixel art decals). Isotropic mipmaps are used for rendering, which means decals at a distance will look smooth but blurry. This has roughly the same performance cost as not using mipmaps.
		</constant>
		<constant name="DECAL_FILTER_NEAREST_MIPMAPS_ANISOTROPIC" value="4" enum="DecalFilter">
			Nearest-neighbor filter for decals (use for pixel art decals). Anisotropic mipmaps are used for rendering, which means decals at a distance will look smooth and sharp when viewed from oblique angles. This looks better compared to isotropic mipmaps, but is slower. The level of anisotropic filtering is defined by [member ProjectSettings.rendering/textures/default_filters/anisotropic_filtering_level].
		</constant>
		<constant name="DECAL_FILTER_LINEAR_MIPMAPS_ANISOTROPIC" value="5" enum="DecalFilter">
			Linear filter for decals (use for non-pixel art decals). Anisotropic mipmaps are used for rendering, which means decals at a distance will look smooth and sharp when viewed from oblique angles. This looks better compared to isotropic mipmaps, but is slower. The level of anisotropic filtering is defined by [member ProjectSettings.rendering/textures/default_filters/anisotropic_filtering_level].
		</constant>
		<constant name="VOXEL_GI_QUALITY_LOW" value="0" enum="VoxelGIQuality">
			Low [VoxelGI] rendering quality using 4 cones.
		</constant>
		<constant name="VOXEL_GI_QUALITY_HIGH" value="1" enum="VoxelGIQuality">
			High [VoxelGI] rendering quality using 6 cones.
		</constant>
		<constant name="PARTICLES_MODE_2D" value="0" enum="ParticlesMode">
			2D particles.
		</constant>
		<constant name="PARTICLES_MODE_3D" value="1" enum="ParticlesMode">
			3D particles.
		</constant>
		<constant name="PARTICLES_TRANSFORM_ALIGN_DISABLED" value="0" enum="ParticlesTransformAlign">
		</constant>
		<constant name="PARTICLES_TRANSFORM_ALIGN_Z_BILLBOARD" value="1" enum="ParticlesTransformAlign">
		</constant>
		<constant name="PARTICLES_TRANSFORM_ALIGN_Y_TO_VELOCITY" value="2" enum="ParticlesTransformAlign">
		</constant>
		<constant name="PARTICLES_TRANSFORM_ALIGN_Z_BILLBOARD_Y_TO_VELOCITY" value="3" enum="ParticlesTransformAlign">
		</constant>
		<constant name="PARTICLES_EMIT_FLAG_POSITION" value="1">
		</constant>
		<constant name="PARTICLES_EMIT_FLAG_ROTATION_SCALE" value="2">
		</constant>
		<constant name="PARTICLES_EMIT_FLAG_VELOCITY" value="4">
		</constant>
		<constant name="PARTICLES_EMIT_FLAG_COLOR" value="8">
		</constant>
		<constant name="PARTICLES_EMIT_FLAG_CUSTOM" value="16">
		</constant>
		<constant name="PARTICLES_DRAW_ORDER_INDEX" value="0" enum="ParticlesDrawOrder">
			Draw particles in the order that they appear in the particles array.
		</constant>
		<constant name="PARTICLES_DRAW_ORDER_LIFETIME" value="1" enum="ParticlesDrawOrder">
			Sort particles based on their lifetime. In other words, the particle with the highest lifetime is drawn at the front.
		</constant>
		<constant name="PARTICLES_DRAW_ORDER_REVERSE_LIFETIME" value="2" enum="ParticlesDrawOrder">
			Sort particles based on the inverse of their lifetime. In other words, the particle with the lowest lifetime is drawn at the front.
		</constant>
		<constant name="PARTICLES_DRAW_ORDER_VIEW_DEPTH" value="3" enum="ParticlesDrawOrder">
			Sort particles based on their distance to the camera.
		</constant>
		<constant name="PARTICLES_COLLISION_TYPE_SPHERE_ATTRACT" value="0" enum="ParticlesCollisionType">
		</constant>
		<constant name="PARTICLES_COLLISION_TYPE_BOX_ATTRACT" value="1" enum="ParticlesCollisionType">
		</constant>
		<constant name="PARTICLES_COLLISION_TYPE_VECTOR_FIELD_ATTRACT" value="2" enum="ParticlesCollisionType">
		</constant>
		<constant name="PARTICLES_COLLISION_TYPE_SPHERE_COLLIDE" value="3" enum="ParticlesCollisionType">
		</constant>
		<constant name="PARTICLES_COLLISION_TYPE_BOX_COLLIDE" value="4" enum="ParticlesCollisionType">
		</constant>
		<constant name="PARTICLES_COLLISION_TYPE_SDF_COLLIDE" value="5" enum="ParticlesCollisionType">
		</constant>
		<constant name="PARTICLES_COLLISION_TYPE_HEIGHTFIELD_COLLIDE" value="6" enum="ParticlesCollisionType">
		</constant>
		<constant name="PARTICLES_COLLISION_HEIGHTFIELD_RESOLUTION_256" value="0" enum="ParticlesCollisionHeightfieldResolution">
		</constant>
		<constant name="PARTICLES_COLLISION_HEIGHTFIELD_RESOLUTION_512" value="1" enum="ParticlesCollisionHeightfieldResolution">
		</constant>
		<constant name="PARTICLES_COLLISION_HEIGHTFIELD_RESOLUTION_1024" value="2" enum="ParticlesCollisionHeightfieldResolution">
		</constant>
		<constant name="PARTICLES_COLLISION_HEIGHTFIELD_RESOLUTION_2048" value="3" enum="ParticlesCollisionHeightfieldResolution">
		</constant>
		<constant name="PARTICLES_COLLISION_HEIGHTFIELD_RESOLUTION_4096" value="4" enum="ParticlesCollisionHeightfieldResolution">
		</constant>
		<constant name="PARTICLES_COLLISION_HEIGHTFIELD_RESOLUTION_8192" value="5" enum="ParticlesCollisionHeightfieldResolution">
		</constant>
		<constant name="PARTICLES_COLLISION_HEIGHTFIELD_RESOLUTION_MAX" value="6" enum="ParticlesCollisionHeightfieldResolution">
			Represents the size of the [enum ParticlesCollisionHeightfieldResolution] enum.
		</constant>
		<constant name="FOG_VOLUME_SHAPE_ELLIPSOID" value="0" enum="FogVolumeShape">
			[FogVolume] will be shaped like an ellipsoid (stretched sphere).
		</constant>
		<constant name="FOG_VOLUME_SHAPE_CONE" value="1" enum="FogVolumeShape">
			[FogVolume] will be shaped like a cone pointing upwards (in local coordinates). The cone's angle is set automatically to fill the size. The cone will be adjusted to fit within the size. Rotate the [FogVolume] node to reorient the cone. Non-uniform scaling via size is not supported (scale the [FogVolume] node instead).
		</constant>
		<constant name="FOG_VOLUME_SHAPE_CYLINDER" value="2" enum="FogVolumeShape">
			[FogVolume] will be shaped like an upright cylinder (in local coordinates). Rotate the [FogVolume] node to reorient the cylinder. The cylinder will be adjusted to fit within the size. Non-uniform scaling via size is not supported (scale the [FogVolume] node instead).
		</constant>
		<constant name="FOG_VOLUME_SHAPE_BOX" value="3" enum="FogVolumeShape">
			[FogVolume] will be shaped like a box.
		</constant>
		<constant name="FOG_VOLUME_SHAPE_WORLD" value="4" enum="FogVolumeShape">
			[FogVolume] will have no shape, will cover the whole world and will not be culled.
		</constant>
		<constant name="FOG_VOLUME_SHAPE_MAX" value="5" enum="FogVolumeShape">
			Represents the size of the [enum FogVolumeShape] enum.
		</constant>
		<constant name="VIEWPORT_SCALING_3D_MODE_BILINEAR" value="0" enum="ViewportScaling3DMode">
			Use bilinear scaling for the viewport's 3D buffer. The amount of scaling can be set using [member Viewport.scaling_3d_scale]. Values less than [code]1.0[/code] will result in undersampling while values greater than [code]1.0[/code] will result in supersampling. A value of [code]1.0[/code] disables scaling.
		</constant>
		<constant name="VIEWPORT_SCALING_3D_MODE_FSR" value="1" enum="ViewportScaling3DMode">
			Use AMD FidelityFX Super Resolution 1.0 upscaling for the viewport's 3D buffer. The amount of scaling can be set using [member Viewport.scaling_3d_scale]. Values less than [code]1.0[/code] will result in the viewport being upscaled using FSR. Values greater than [code]1.0[/code] are not supported and bilinear downsampling will be used instead. A value of [code]1.0[/code] disables scaling.
		</constant>
		<constant name="VIEWPORT_SCALING_3D_MODE_FSR2" value="2" enum="ViewportScaling3DMode">
			Use AMD FidelityFX Super Resolution 2.2 upscaling for the viewport's 3D buffer. The amount of scaling can be set using [member Viewport.scaling_3d_scale]. Values less than [code]1.0[/code] will result in the viewport being upscaled using FSR2. Values greater than [code]1.0[/code] are not supported and bilinear downsampling will be used instead. A value of [code]1.0[/code] will use FSR2 at native resolution as a TAA solution.
		</constant>
		<constant name="VIEWPORT_SCALING_3D_MODE_METALFX_SPATIAL" value="3" enum="ViewportScaling3DMode">
			Use MetalFX spatial upscaling for the viewport's 3D buffer. The amount of scaling can be set using [member Viewport.scaling_3d_scale]. Values less than [code]1.0[/code] will result in the viewport being upscaled using MetalFX. Values greater than [code]1.0[/code] are not supported and bilinear downsampling will be used instead. A value of [code]1.0[/code] disables scaling.
			[b]Note:[/b] Only supported when the Metal rendering driver is in use, which limits this scaling mode to macOS and iOS.
		</constant>
		<constant name="VIEWPORT_SCALING_3D_MODE_METALFX_TEMPORAL" value="4" enum="ViewportScaling3DMode">
			Use MetalFX temporal upscaling for the viewport's 3D buffer. The amount of scaling can be set using [member Viewport.scaling_3d_scale]. Values less than [code]1.0[/code] will result in the viewport being upscaled using MetalFX. Values greater than [code]1.0[/code] are not supported and bilinear downsampling will be used instead. A value of [code]1.0[/code] will use MetalFX at native resolution as a TAA solution.
			[b]Note:[/b] Only supported when the Metal rendering driver is in use, which limits this scaling mode to macOS and iOS.
		</constant>
		<constant name="VIEWPORT_SCALING_3D_MODE_MAX" value="5" enum="ViewportScaling3DMode">
			Represents the size of the [enum ViewportScaling3DMode] enum.
		</constant>
		<constant name="VIEWPORT_UPDATE_DISABLED" value="0" enum="ViewportUpdateMode">
			Do not update the viewport's render target.
		</constant>
		<constant name="VIEWPORT_UPDATE_ONCE" value="1" enum="ViewportUpdateMode">
			Update the viewport's render target once, then switch to [constant VIEWPORT_UPDATE_DISABLED].
		</constant>
		<constant name="VIEWPORT_UPDATE_WHEN_VISIBLE" value="2" enum="ViewportUpdateMode">
			Update the viewport's render target only when it is visible. This is the default value.
		</constant>
		<constant name="VIEWPORT_UPDATE_WHEN_PARENT_VISIBLE" value="3" enum="ViewportUpdateMode">
			Update the viewport's render target only when its parent is visible.
		</constant>
		<constant name="VIEWPORT_UPDATE_ALWAYS" value="4" enum="ViewportUpdateMode">
			Always update the viewport's render target.
		</constant>
		<constant name="VIEWPORT_CLEAR_ALWAYS" value="0" enum="ViewportClearMode">
			Always clear the viewport's render target before drawing.
		</constant>
		<constant name="VIEWPORT_CLEAR_NEVER" value="1" enum="ViewportClearMode">
			Never clear the viewport's render target.
		</constant>
		<constant name="VIEWPORT_CLEAR_ONLY_NEXT_FRAME" value="2" enum="ViewportClearMode">
			Clear the viewport's render target on the next frame, then switch to [constant VIEWPORT_CLEAR_NEVER].
		</constant>
		<constant name="VIEWPORT_ENVIRONMENT_DISABLED" value="0" enum="ViewportEnvironmentMode">
			Disable rendering of 3D environment over 2D canvas.
		</constant>
		<constant name="VIEWPORT_ENVIRONMENT_ENABLED" value="1" enum="ViewportEnvironmentMode">
			Enable rendering of 3D environment over 2D canvas.
		</constant>
		<constant name="VIEWPORT_ENVIRONMENT_INHERIT" value="2" enum="ViewportEnvironmentMode">
			Inherit enable/disable value from parent. If the topmost parent is also set to [constant VIEWPORT_ENVIRONMENT_INHERIT], then this has the same behavior as [constant VIEWPORT_ENVIRONMENT_ENABLED].
		</constant>
		<constant name="VIEWPORT_ENVIRONMENT_MAX" value="3" enum="ViewportEnvironmentMode">
			Represents the size of the [enum ViewportEnvironmentMode] enum.
		</constant>
		<constant name="VIEWPORT_SDF_OVERSIZE_100_PERCENT" value="0" enum="ViewportSDFOversize">
			Do not oversize the 2D signed distance field. Occluders may disappear when touching the viewport's edges, and [GPUParticles3D] collision may stop working earlier than intended. This has the lowest GPU requirements.
		</constant>
		<constant name="VIEWPORT_SDF_OVERSIZE_120_PERCENT" value="1" enum="ViewportSDFOversize">
			2D signed distance field covers 20% of the viewport's size outside the viewport on each side (top, right, bottom, left).
		</constant>
		<constant name="VIEWPORT_SDF_OVERSIZE_150_PERCENT" value="2" enum="ViewportSDFOversize">
			2D signed distance field covers 50% of the viewport's size outside the viewport on each side (top, right, bottom, left).
		</constant>
		<constant name="VIEWPORT_SDF_OVERSIZE_200_PERCENT" value="3" enum="ViewportSDFOversize">
			2D signed distance field covers 100% of the viewport's size outside the viewport on each side (top, right, bottom, left). This has the highest GPU requirements.
		</constant>
		<constant name="VIEWPORT_SDF_OVERSIZE_MAX" value="4" enum="ViewportSDFOversize">
			Represents the size of the [enum ViewportSDFOversize] enum.
		</constant>
		<constant name="VIEWPORT_SDF_SCALE_100_PERCENT" value="0" enum="ViewportSDFScale">
			Full resolution 2D signed distance field scale. This has the highest GPU requirements.
		</constant>
		<constant name="VIEWPORT_SDF_SCALE_50_PERCENT" value="1" enum="ViewportSDFScale">
			Half resolution 2D signed distance field scale on each axis (25% of the viewport pixel count).
		</constant>
		<constant name="VIEWPORT_SDF_SCALE_25_PERCENT" value="2" enum="ViewportSDFScale">
			Quarter resolution 2D signed distance field scale on each axis (6.25% of the viewport pixel count). This has the lowest GPU requirements.
		</constant>
		<constant name="VIEWPORT_SDF_SCALE_MAX" value="3" enum="ViewportSDFScale">
			Represents the size of the [enum ViewportSDFScale] enum.
		</constant>
		<constant name="VIEWPORT_MSAA_DISABLED" value="0" enum="ViewportMSAA">
			Multisample antialiasing for 3D is disabled. This is the default value, and also the fastest setting.
		</constant>
		<constant name="VIEWPORT_MSAA_2X" value="1" enum="ViewportMSAA">
			Multisample antialiasing uses 2 samples per pixel for 3D. This has a moderate impact on performance.
		</constant>
		<constant name="VIEWPORT_MSAA_4X" value="2" enum="ViewportMSAA">
			Multisample antialiasing uses 4 samples per pixel for 3D. This has a high impact on performance.
		</constant>
		<constant name="VIEWPORT_MSAA_8X" value="3" enum="ViewportMSAA">
			Multisample antialiasing uses 8 samples per pixel for 3D. This has a very high impact on performance. Likely unsupported on low-end and older hardware.
		</constant>
		<constant name="VIEWPORT_MSAA_MAX" value="4" enum="ViewportMSAA">
			Represents the size of the [enum ViewportMSAA] enum.
		</constant>
		<constant name="VIEWPORT_ANISOTROPY_DISABLED" value="0" enum="ViewportAnisotropicFiltering">
			Anisotropic filtering is disabled.
		</constant>
		<constant name="VIEWPORT_ANISOTROPY_2X" value="1" enum="ViewportAnisotropicFiltering">
			Use 2× anisotropic filtering.
		</constant>
		<constant name="VIEWPORT_ANISOTROPY_4X" value="2" enum="ViewportAnisotropicFiltering">
			Use 4× anisotropic filtering. This is the default value.
		</constant>
		<constant name="VIEWPORT_ANISOTROPY_8X" value="3" enum="ViewportAnisotropicFiltering">
			Use 8× anisotropic filtering.
		</constant>
		<constant name="VIEWPORT_ANISOTROPY_16X" value="4" enum="ViewportAnisotropicFiltering">
			Use 16× anisotropic filtering.
		</constant>
		<constant name="VIEWPORT_ANISOTROPY_MAX" value="5" enum="ViewportAnisotropicFiltering">
			Represents the size of the [enum ViewportAnisotropicFiltering] enum.
		</constant>
		<constant name="VIEWPORT_SCREEN_SPACE_AA_DISABLED" value="0" enum="ViewportScreenSpaceAA">
			Do not perform any antialiasing in the full screen post-process.
		</constant>
		<constant name="VIEWPORT_SCREEN_SPACE_AA_FXAA" value="1" enum="ViewportScreenSpaceAA">
			Use fast approximate antialiasing. FXAA is a popular screen-space antialiasing method, which is fast but will make the image look blurry, especially at lower resolutions. It can still work relatively well at large resolutions such as 1440p and 4K.
		</constant>
		<constant name="VIEWPORT_SCREEN_SPACE_AA_MAX" value="2" enum="ViewportScreenSpaceAA">
			Represents the size of the [enum ViewportScreenSpaceAA] enum.
		</constant>
		<constant name="VIEWPORT_OCCLUSION_BUILD_QUALITY_LOW" value="0" enum="ViewportOcclusionCullingBuildQuality">
			Low occlusion culling BVH build quality (as defined by Embree). Results in the lowest CPU usage, but least effective culling.
		</constant>
		<constant name="VIEWPORT_OCCLUSION_BUILD_QUALITY_MEDIUM" value="1" enum="ViewportOcclusionCullingBuildQuality">
			Medium occlusion culling BVH build quality (as defined by Embree).
		</constant>
		<constant name="VIEWPORT_OCCLUSION_BUILD_QUALITY_HIGH" value="2" enum="ViewportOcclusionCullingBuildQuality">
			High occlusion culling BVH build quality (as defined by Embree). Results in the highest CPU usage, but most effective culling.
		</constant>
		<constant name="VIEWPORT_RENDER_INFO_OBJECTS_IN_FRAME" value="0" enum="ViewportRenderInfo">
			Number of objects drawn in a single frame.
		</constant>
		<constant name="VIEWPORT_RENDER_INFO_PRIMITIVES_IN_FRAME" value="1" enum="ViewportRenderInfo">
			Number of points, lines, or triangles drawn in a single frame.
		</constant>
		<constant name="VIEWPORT_RENDER_INFO_DRAW_CALLS_IN_FRAME" value="2" enum="ViewportRenderInfo">
			Number of draw calls during this frame.
		</constant>
		<constant name="VIEWPORT_RENDER_INFO_MAX" value="3" enum="ViewportRenderInfo">
			Represents the size of the [enum ViewportRenderInfo] enum.
		</constant>
		<constant name="VIEWPORT_RENDER_INFO_TYPE_VISIBLE" value="0" enum="ViewportRenderInfoType">
			Visible render pass (excluding shadows).
		</constant>
		<constant name="VIEWPORT_RENDER_INFO_TYPE_SHADOW" value="1" enum="ViewportRenderInfoType">
			Shadow render pass. Objects will be rendered several times depending on the number of amounts of lights with shadows and the number of directional shadow splits.
		</constant>
		<constant name="VIEWPORT_RENDER_INFO_TYPE_CANVAS" value="2" enum="ViewportRenderInfoType">
			Canvas item rendering. This includes all 2D rendering.
		</constant>
		<constant name="VIEWPORT_RENDER_INFO_TYPE_MAX" value="3" enum="ViewportRenderInfoType">
			Represents the size of the [enum ViewportRenderInfoType] enum.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_DISABLED" value="0" enum="ViewportDebugDraw">
			Debug draw is disabled. Default setting.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_UNSHADED" value="1" enum="ViewportDebugDraw">
			Objects are displayed without light information.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_LIGHTING" value="2" enum="ViewportDebugDraw">
			Objects are displayed with only light information.
			[b]Note:[/b] When using this debug draw mode, custom shaders are ignored since all materials in the scene temporarily use a debug material. This means the result from custom shader functions (such as vertex displacement) won't be visible anymore when using this debug draw mode.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_OVERDRAW" value="3" enum="ViewportDebugDraw">
			Objects are displayed semi-transparent with additive blending so you can see where they are drawing over top of one another. A higher overdraw (represented by brighter colors) means you are wasting performance on drawing pixels that are being hidden behind others.
			[b]Note:[/b] When using this debug draw mode, custom shaders are ignored since all materials in the scene temporarily use a debug material. This means the result from custom shader functions (such as vertex displacement) won't be visible anymore when using this debug draw mode.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_WIREFRAME" value="4" enum="ViewportDebugDraw">
			Debug draw draws objects in wireframe.
			[b]Note:[/b] [method set_debug_generate_wireframes] must be called before loading any meshes for wireframes to be visible when using the Compatibility renderer.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_NORMAL_BUFFER" value="5" enum="ViewportDebugDraw">
			Normal buffer is drawn instead of regular scene so you can see the per-pixel normals that will be used by post-processing effects.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_VOXEL_GI_ALBEDO" value="6" enum="ViewportDebugDraw">
			Objects are displayed with only the albedo value from [VoxelGI]s. Requires at least one visible [VoxelGI] node that has been baked to have a visible effect.
			[b]Note:[/b] Only supported when using the Forward+ rendering method.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_VOXEL_GI_LIGHTING" value="7" enum="ViewportDebugDraw">
			Objects are displayed with only the lighting value from [VoxelGI]s. Requires at least one visible [VoxelGI] node that has been baked to have a visible effect.
			[b]Note:[/b] Only supported when using the Forward+ rendering method.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_VOXEL_GI_EMISSION" value="8" enum="ViewportDebugDraw">
			Objects are displayed with only the emission color from [VoxelGI]s. Requires at least one visible [VoxelGI] node that has been baked to have a visible effect.
			[b]Note:[/b] Only supported when using the Forward+ rendering method.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_SHADOW_ATLAS" value="9" enum="ViewportDebugDraw">
			Draws the shadow atlas that stores shadows from [OmniLight3D]s and [SpotLight3D]s in the upper left quadrant of the [Viewport].
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_DIRECTIONAL_SHADOW_ATLAS" value="10" enum="ViewportDebugDraw">
			Draws the shadow atlas that stores shadows from [DirectionalLight3D]s in the upper left quadrant of the [Viewport].
			The slice of the camera frustum related to the shadow map cascade is superimposed to visualize coverage. The color of each slice matches the colors used for [constant VIEWPORT_DEBUG_DRAW_PSSM_SPLITS]. When shadow cascades are blended the overlap is taken into account when drawing the frustum slices.
			The last cascade shows all frustum slices to illustrate the coverage of all slices.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_SCENE_LUMINANCE" value="11" enum="ViewportDebugDraw">
			Draws the estimated scene luminance. This is a 1×1 texture that is generated when autoexposure is enabled to control the scene's exposure.
			[b]Note:[/b] Only supported when using the Forward+ or Mobile rendering methods.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_SSAO" value="12" enum="ViewportDebugDraw">
			Draws the screen space ambient occlusion texture instead of the scene so that you can clearly see how it is affecting objects. In order for this display mode to work, you must have [member Environment.ssao_enabled] set in your [WorldEnvironment].
			[b]Note:[/b] Only supported when using the Forward+ rendering method.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_SSIL" value="13" enum="ViewportDebugDraw">
			Draws the screen space indirect lighting texture instead of the scene so that you can clearly see how it is affecting objects. In order for this display mode to work, you must have [member Environment.ssil_enabled] set in your [WorldEnvironment].
			[b]Note:[/b] Only supported when using the Forward+ rendering method.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_PSSM_SPLITS" value="14" enum="ViewportDebugDraw">
			Colors each PSSM split for the [DirectionalLight3D]s in the scene a different color so you can see where the splits are. In order (from closest to furthest from the camera), they are colored red, green, blue, and yellow.
			[b]Note:[/b] When using this debug draw mode, custom shaders are ignored since all materials in the scene temporarily use a debug material. This means the result from custom shader functions (such as vertex displacement) won't be visible anymore when using this debug draw mode.
			[b]Note:[/b] Only supported when using the Forward+ or Mobile rendering methods.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_DECAL_ATLAS" value="15" enum="ViewportDebugDraw">
			Draws the decal atlas that stores decal textures from [Decal]s.
			[b]Note:[/b] Only supported when using the Forward+ or Mobile rendering methods.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_SDFGI" value="16" enum="ViewportDebugDraw">
			Draws SDFGI cascade data. This is the data structure that is used to bounce lighting against and create reflections.
			[b]Note:[/b] Only supported when using the Forward+ rendering method.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_SDFGI_PROBES" value="17" enum="ViewportDebugDraw">
			Draws SDFGI probe data. This is the data structure that is used to give indirect lighting dynamic objects moving within the scene.
			[b]Note:[/b] Only supported when using the Forward+ rendering method.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_GI_BUFFER" value="18" enum="ViewportDebugDraw">
			Draws the global illumination buffer from [VoxelGI] or SDFGI. Requires [VoxelGI] (at least one visible baked VoxelGI node) or SDFGI ([member Environment.sdfgi_enabled]) to be enabled to have a visible effect.
			[b]Note:[/b] Only supported when using the Forward+ rendering method.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_DISABLE_LOD" value="19" enum="ViewportDebugDraw">
			Disable mesh LOD. All meshes are drawn with full detail, which can be used to compare performance.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_CLUSTER_OMNI_LIGHTS" value="20" enum="ViewportDebugDraw">
			Draws the [OmniLight3D] cluster. Clustering determines where lights are positioned in screen-space, which allows the engine to only process these portions of the screen for lighting.
			[b]Note:[/b] Only supported when using the Forward+ rendering method.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_CLUSTER_SPOT_LIGHTS" value="21" enum="ViewportDebugDraw">
			Draws the [SpotLight3D] cluster. Clustering determines where lights are positioned in screen-space, which allows the engine to only process these portions of the screen for lighting.
			[b]Note:[/b] Only supported when using the Forward+ rendering method.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_CLUSTER_DECALS" value="22" enum="ViewportDebugDraw">
			Draws the [Decal] cluster. Clustering determines where decals are positioned in screen-space, which allows the engine to only process these portions of the screen for decals.
			[b]Note:[/b] Only supported when using the Forward+ rendering method.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_CLUSTER_REFLECTION_PROBES" value="23" enum="ViewportDebugDraw">
			Draws the [ReflectionProbe] cluster. Clustering determines where reflection probes are positioned in screen-space, which allows the engine to only process these portions of the screen for reflection probes.
			[b]Note:[/b] Only supported when using the Forward+ rendering method.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_OCCLUDERS" value="24" enum="ViewportDebugDraw">
			Draws the occlusion culling buffer. This low-resolution occlusion culling buffer is rasterized on the CPU and is used to check whether instances are occluded by other objects.
			[b]Note:[/b] Only supported when using the Forward+ or Mobile rendering methods.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_MOTION_VECTORS" value="25" enum="ViewportDebugDraw">
			Draws the motion vectors buffer. This is used by temporal antialiasing to correct for motion that occurs during gameplay.
			[b]Note:[/b] Only supported when using the Forward+ rendering method.
		</constant>
		<constant name="VIEWPORT_DEBUG_DRAW_INTERNAL_BUFFER" value="26" enum="ViewportDebugDraw">
			Internal buffer is drawn instead of regular scene so you can see the per-pixel output that will be used by post-processing effects.
			[b]Note:[/b] Only supported when using the Forward+ or Mobile rendering methods.
		</constant>
		<constant name="VIEWPORT_VRS_DISABLED" value="0" enum="ViewportVRSMode">
			Variable rate shading is disabled.
		</constant>
		<constant name="VIEWPORT_VRS_TEXTURE" value="1" enum="ViewportVRSMode">
			Variable rate shading uses a texture. Note, for stereoscopic use a texture atlas with a texture for each view.
		</constant>
		<constant name="VIEWPORT_VRS_XR" value="2" enum="ViewportVRSMode">
			Variable rate shading texture is supplied by the primary [XRInterface]. Note that this may override the update mode.
		</constant>
		<constant name="VIEWPORT_VRS_MAX" value="3" enum="ViewportVRSMode">
			Represents the size of the [enum ViewportVRSMode] enum.
		</constant>
		<constant name="VIEWPORT_VRS_UPDATE_DISABLED" value="0" enum="ViewportVRSUpdateMode">
			The input texture for variable rate shading will not be processed.
		</constant>
		<constant name="VIEWPORT_VRS_UPDATE_ONCE" value="1" enum="ViewportVRSUpdateMode">
			The input texture for variable rate shading will be processed once.
		</constant>
		<constant name="VIEWPORT_VRS_UPDATE_ALWAYS" value="2" enum="ViewportVRSUpdateMode">
			The input texture for variable rate shading will be processed each frame.
		</constant>
		<constant name="VIEWPORT_VRS_UPDATE_MAX" value="3" enum="ViewportVRSUpdateMode">
			Represents the size of the [enum ViewportVRSUpdateMode] enum.
		</constant>
		<constant name="SKY_MODE_AUTOMATIC" value="0" enum="SkyMode">
			Automatically selects the appropriate process mode based on your sky shader. If your shader uses [code]TIME[/code] or [code]POSITION[/code], this will use [constant SKY_MODE_REALTIME]. If your shader uses any of the [code]LIGHT_*[/code] variables or any custom uniforms, this uses [constant SKY_MODE_INCREMENTAL]. Otherwise, this defaults to [constant SKY_MODE_QUALITY].
		</constant>
		<constant name="SKY_MODE_QUALITY" value="1" enum="SkyMode">
			Uses high quality importance sampling to process the radiance map. In general, this results in much higher quality than [constant SKY_MODE_REALTIME] but takes much longer to generate. This should not be used if you plan on changing the sky at runtime. If you are finding that the reflection is not blurry enough and is showing sparkles or fireflies, try increasing [member ProjectSettings.rendering/reflections/sky_reflections/ggx_samples].
		</constant>
		<constant name="SKY_MODE_INCREMENTAL" value="2" enum="SkyMode">
			Uses the same high quality importance sampling to process the radiance map as [constant SKY_MODE_QUALITY], but updates over several frames. The number of frames is determined by [member ProjectSettings.rendering/reflections/sky_reflections/roughness_layers]. Use this when you need highest quality radiance maps, but have a sky that updates slowly.
		</constant>
		<constant name="SKY_MODE_REALTIME" value="3" enum="SkyMode">
			Uses the fast filtering algorithm to process the radiance map. In general this results in lower quality, but substantially faster run times. If you need better quality, but still need to update the sky every frame, consider turning on [member ProjectSettings.rendering/reflections/sky_reflections/fast_filter_high_quality].
			[b]Note:[/b] The fast filtering algorithm is limited to 256×256 cubemaps, so [method sky_set_radiance_size] must be set to [code]256[/code]. Otherwise, a warning is printed and the overridden radiance size is ignored.
		</constant>
		<constant name="COMPOSITOR_EFFECT_FLAG_ACCESS_RESOLVED_COLOR" value="1" enum="CompositorEffectFlags">
			The rendering effect requires the color buffer to be resolved if MSAA is enabled.
		</constant>
		<constant name="COMPOSITOR_EFFECT_FLAG_ACCESS_RESOLVED_DEPTH" value="2" enum="CompositorEffectFlags">
			The rendering effect requires the depth buffer to be resolved if MSAA is enabled.
		</constant>
		<constant name="COMPOSITOR_EFFECT_FLAG_NEEDS_MOTION_VECTORS" value="4" enum="CompositorEffectFlags">
			The rendering effect requires motion vectors to be produced.
		</constant>
		<constant name="COMPOSITOR_EFFECT_FLAG_NEEDS_ROUGHNESS" value="8" enum="CompositorEffectFlags">
			The rendering effect requires normals and roughness g-buffer to be produced (Forward+ only).
		</constant>
		<constant name="COMPOSITOR_EFFECT_FLAG_NEEDS_SEPARATE_SPECULAR" value="16" enum="CompositorEffectFlags">
			The rendering effect requires specular data to be separated out (Forward+ only).
		</constant>
		<constant name="COMPOSITOR_EFFECT_CALLBACK_TYPE_PRE_OPAQUE" value="0" enum="CompositorEffectCallbackType">
			The callback is called before our opaque rendering pass, but after depth prepass (if applicable).
		</constant>
		<constant name="COMPOSITOR_EFFECT_CALLBACK_TYPE_POST_OPAQUE" value="1" enum="CompositorEffectCallbackType">
			The callback is called after our opaque rendering pass, but before our sky is rendered.
		</constant>
		<constant name="COMPOSITOR_EFFECT_CALLBACK_TYPE_POST_SKY" value="2" enum="CompositorEffectCallbackType">
			The callback is called after our sky is rendered, but before our back buffers are created (and if enabled, before subsurface scattering and/or screen space reflections).
		</constant>
		<constant name="COMPOSITOR_EFFECT_CALLBACK_TYPE_PRE_TRANSPARENT" value="3" enum="CompositorEffectCallbackType">
			The callback is called before our transparent rendering pass, but after our sky is rendered and we've created our back buffers.
		</constant>
		<constant name="COMPOSITOR_EFFECT_CALLBACK_TYPE_POST_TRANSPARENT" value="4" enum="CompositorEffectCallbackType">
			The callback is called after our transparent rendering pass, but before any built-in post-processing effects and output to our render target.
		</constant>
		<constant name="COMPOSITOR_EFFECT_CALLBACK_TYPE_ANY" value="-1" enum="CompositorEffectCallbackType">
		</constant>
		<constant name="ENV_BG_CLEAR_COLOR" value="0" enum="EnvironmentBG">
			Use the clear color as background.
		</constant>
		<constant name="ENV_BG_COLOR" value="1" enum="EnvironmentBG">
			Use a specified color as the background.
		</constant>
		<constant name="ENV_BG_SKY" value="2" enum="EnvironmentBG">
			Use a sky resource for the background.
		</constant>
		<constant name="ENV_BG_CANVAS" value="3" enum="EnvironmentBG">
			Use a specified canvas layer as the background. This can be useful for instantiating a 2D scene in a 3D world.
		</constant>
		<constant name="ENV_BG_KEEP" value="4" enum="EnvironmentBG">
			Do not clear the background, use whatever was rendered last frame as the background.
		</constant>
		<constant name="ENV_BG_CAMERA_FEED" value="5" enum="EnvironmentBG">
			Displays a camera feed in the background.
		</constant>
		<constant name="ENV_BG_MAX" value="6" enum="EnvironmentBG">
			Represents the size of the [enum EnvironmentBG] enum.
		</constant>
		<constant name="ENV_AMBIENT_SOURCE_BG" value="0" enum="EnvironmentAmbientSource">
			Gather ambient light from whichever source is specified as the background.
		</constant>
		<constant name="ENV_AMBIENT_SOURCE_DISABLED" value="1" enum="EnvironmentAmbientSource">
			Disable ambient light.
		</constant>
		<constant name="ENV_AMBIENT_SOURCE_COLOR" value="2" enum="EnvironmentAmbientSource">
			Specify a specific [Color] for ambient light.
		</constant>
		<constant name="ENV_AMBIENT_SOURCE_SKY" value="3" enum="EnvironmentAmbientSource">
			Gather ambient light from the [Sky] regardless of what the background is.
		</constant>
		<constant name="ENV_REFLECTION_SOURCE_BG" value="0" enum="EnvironmentReflectionSource">
			Use the background for reflections.
		</constant>
		<constant name="ENV_REFLECTION_SOURCE_DISABLED" value="1" enum="EnvironmentReflectionSource">
			Disable reflections.
		</constant>
		<constant name="ENV_REFLECTION_SOURCE_SKY" value="2" enum="EnvironmentReflectionSource">
			Use the [Sky] for reflections regardless of what the background is.
		</constant>
		<constant name="ENV_GLOW_BLEND_MODE_ADDITIVE" value="0" enum="EnvironmentGlowBlendMode">
			Additive glow blending mode. Mostly used for particles, glows (bloom), lens flare, bright sources.
		</constant>
		<constant name="ENV_GLOW_BLEND_MODE_SCREEN" value="1" enum="EnvironmentGlowBlendMode">
			Screen glow blending mode. Increases brightness, used frequently with bloom.
		</constant>
		<constant name="ENV_GLOW_BLEND_MODE_SOFTLIGHT" value="2" enum="EnvironmentGlowBlendMode">
			Soft light glow blending mode. Modifies contrast, exposes shadows and highlights (vivid bloom).
		</constant>
		<constant name="ENV_GLOW_BLEND_MODE_REPLACE" value="3" enum="EnvironmentGlowBlendMode">
			Replace glow blending mode. Replaces all pixels' color by the glow value. This can be used to simulate a full-screen blur effect by tweaking the glow parameters to match the original image's brightness.
		</constant>
		<constant name="ENV_GLOW_BLEND_MODE_MIX" value="4" enum="EnvironmentGlowBlendMode">
			Mixes the glow with the underlying color to avoid increasing brightness as much while still maintaining a glow effect.
		</constant>
		<constant name="ENV_FOG_MODE_EXPONENTIAL" value="0" enum="EnvironmentFogMode">
			Use a physically-based fog model defined primarily by fog density.
		</constant>
		<constant name="ENV_FOG_MODE_DEPTH" value="1" enum="EnvironmentFogMode">
			Use a simple fog model defined by start and end positions and a custom curve. While not physically accurate, this model can be useful when you need more artistic control.
		</constant>
		<constant name="ENV_TONE_MAPPER_LINEAR" value="0" enum="EnvironmentToneMapper">
			Does not modify color data, resulting in a linear tonemapping curve which unnaturally clips bright values, causing bright lighting to look blown out. The simplest and fastest tonemapper.
		</constant>
		<constant name="ENV_TONE_MAPPER_REINHARD" value="1" enum="EnvironmentToneMapper">
			A simple tonemapping curve that rolls off bright values to prevent clipping. This results in an image that can appear dull and low contrast. Slower than [constant ENV_TONE_MAPPER_LINEAR].
			[b]Note:[/b] When [member Environment.tonemap_white] is left at the default value of [code]1.0[/code], [constant ENV_TONE_MAPPER_REINHARD] produces an identical image to [constant ENV_TONE_MAPPER_LINEAR].
		</constant>
		<constant name="ENV_TONE_MAPPER_FILMIC" value="2" enum="EnvironmentToneMapper">
			Uses a film-like tonemapping curve to prevent clipping of bright values and provide better contrast than [constant ENV_TONE_MAPPER_REINHARD]. Slightly slower than [constant ENV_TONE_MAPPER_REINHARD].
		</constant>
		<constant name="ENV_TONE_MAPPER_ACES" value="3" enum="EnvironmentToneMapper">
			Uses a high-contrast film-like tonemapping curve and desaturates bright values for a more realistic appearance. Slightly slower than [constant ENV_TONE_MAPPER_FILMIC].
			[b]Note:[/b] This tonemapping operator is called "ACES Fitted" in Redot 3.x.
		</constant>
		<constant name="ENV_TONE_MAPPER_AGX" value="4" enum="EnvironmentToneMapper">
			Uses a film-like tonemapping curve and desaturates bright values for a more realistic appearance. Better than other tonemappers at maintaining the hue of colors as they become brighter. The slowest tonemapping option.
			[b]Note:[/b] [member Environment.tonemap_white] is fixed at a value of [code]16.29[/code], which makes [constant ENV_TONE_MAPPER_AGX] unsuitable for use with the Mobile rendering method.
		</constant>
		<constant name="ENV_SSR_ROUGHNESS_QUALITY_DISABLED" value="0" enum="EnvironmentSSRRoughnessQuality">
			Lowest quality of roughness filter for screen-space reflections. Rough materials will not have blurrier screen-space reflections compared to smooth (non-rough) materials. This is the fastest option.
		</constant>
		<constant name="ENV_SSR_ROUGHNESS_QUALITY_LOW" value="1" enum="EnvironmentSSRRoughnessQuality">
			Low quality of roughness filter for screen-space reflections.
		</constant>
		<constant name="ENV_SSR_ROUGHNESS_QUALITY_MEDIUM" value="2" enum="EnvironmentSSRRoughnessQuality">
			Medium quality of roughness filter for screen-space reflections.
		</constant>
		<constant name="ENV_SSR_ROUGHNESS_QUALITY_HIGH" value="3" enum="EnvironmentSSRRoughnessQuality">
			High quality of roughness filter for screen-space reflections. This is the slowest option.
		</constant>
		<constant name="ENV_SSAO_QUALITY_VERY_LOW" value="0" enum="EnvironmentSSAOQuality">
			Lowest quality of screen-space ambient occlusion.
		</constant>
		<constant name="ENV_SSAO_QUALITY_LOW" value="1" enum="EnvironmentSSAOQuality">
			Low quality screen-space ambient occlusion.
		</constant>
		<constant name="ENV_SSAO_QUALITY_MEDIUM" value="2" enum="EnvironmentSSAOQuality">
			Medium quality screen-space ambient occlusion.
		</constant>
		<constant name="ENV_SSAO_QUALITY_HIGH" value="3" enum="EnvironmentSSAOQuality">
			High quality screen-space ambient occlusion.
		</constant>
		<constant name="ENV_SSAO_QUALITY_ULTRA" value="4" enum="EnvironmentSSAOQuality">
			Highest quality screen-space ambient occlusion. Uses the adaptive target setting which can be dynamically adjusted to smoothly balance performance and visual quality.
		</constant>
		<constant name="ENV_SSIL_QUALITY_VERY_LOW" value="0" enum="EnvironmentSSILQuality">
			Lowest quality of screen-space indirect lighting.
		</constant>
		<constant name="ENV_SSIL_QUALITY_LOW" value="1" enum="EnvironmentSSILQuality">
			Low quality screen-space indirect lighting.
		</constant>
		<constant name="ENV_SSIL_QUALITY_MEDIUM" value="2" enum="EnvironmentSSILQuality">
			High quality screen-space indirect lighting.
		</constant>
		<constant name="ENV_SSIL_QUALITY_HIGH" value="3" enum="EnvironmentSSILQuality">
			High quality screen-space indirect lighting.
		</constant>
		<constant name="ENV_SSIL_QUALITY_ULTRA" value="4" enum="EnvironmentSSILQuality">
			Highest quality screen-space indirect lighting. Uses the adaptive target setting which can be dynamically adjusted to smoothly balance performance and visual quality.
		</constant>
		<constant name="ENV_SDFGI_Y_SCALE_50_PERCENT" value="0" enum="EnvironmentSDFGIYScale">
			Use 50% scale for SDFGI on the Y (vertical) axis. SDFGI cells will be twice as short as they are wide. This allows providing increased GI detail and reduced light leaking with thin floors and ceilings. This is usually the best choice for scenes that don't feature much verticality.
		</constant>
		<constant name="ENV_SDFGI_Y_SCALE_75_PERCENT" value="1" enum="EnvironmentSDFGIYScale">
			Use 75% scale for SDFGI on the Y (vertical) axis. This is a balance between the 50% and 100% SDFGI Y scales.
		</constant>
		<constant name="ENV_SDFGI_Y_SCALE_100_PERCENT" value="2" enum="EnvironmentSDFGIYScale">
			Use 100% scale for SDFGI on the Y (vertical) axis. SDFGI cells will be as tall as they are wide. This is usually the best choice for highly vertical scenes. The downside is that light leaking may become more noticeable with thin floors and ceilings.
		</constant>
		<constant name="ENV_SDFGI_RAY_COUNT_4" value="0" enum="EnvironmentSDFGIRayCount">
			Throw 4 rays per frame when converging SDFGI. This has the lowest GPU requirements, but creates the most noisy result.
		</constant>
		<constant name="ENV_SDFGI_RAY_COUNT_8" value="1" enum="EnvironmentSDFGIRayCount">
			Throw 8 rays per frame when converging SDFGI.
		</constant>
		<constant name="ENV_SDFGI_RAY_COUNT_16" value="2" enum="EnvironmentSDFGIRayCount">
			Throw 16 rays per frame when converging SDFGI.
		</constant>
		<constant name="ENV_SDFGI_RAY_COUNT_32" value="3" enum="EnvironmentSDFGIRayCount">
			Throw 32 rays per frame when converging SDFGI.
		</constant>
		<constant name="ENV_SDFGI_RAY_COUNT_64" value="4" enum="EnvironmentSDFGIRayCount">
			Throw 64 rays per frame when converging SDFGI.
		</constant>
		<constant name="ENV_SDFGI_RAY_COUNT_96" value="5" enum="EnvironmentSDFGIRayCount">
			Throw 96 rays per frame when converging SDFGI. This has high GPU requirements.
		</constant>
		<constant name="ENV_SDFGI_RAY_COUNT_128" value="6" enum="EnvironmentSDFGIRayCount">
			Throw 128 rays per frame when converging SDFGI. This has very high GPU requirements, but creates the least noisy result.
		</constant>
		<constant name="ENV_SDFGI_RAY_COUNT_MAX" value="7" enum="EnvironmentSDFGIRayCount">
			Represents the size of the [enum EnvironmentSDFGIRayCount] enum.
		</constant>
		<constant name="ENV_SDFGI_CONVERGE_IN_5_FRAMES" value="0" enum="EnvironmentSDFGIFramesToConverge">
			Converge SDFGI over 5 frames. This is the most responsive, but creates the most noisy result with a given ray count.
		</constant>
		<constant name="ENV_SDFGI_CONVERGE_IN_10_FRAMES" value="1" enum="EnvironmentSDFGIFramesToConverge">
			Configure SDFGI to fully converge over 10 frames.
		</constant>
		<constant name="ENV_SDFGI_CONVERGE_IN_15_FRAMES" value="2" enum="EnvironmentSDFGIFramesToConverge">
			Configure SDFGI to fully converge over 15 frames.
		</constant>
		<constant name="ENV_SDFGI_CONVERGE_IN_20_FRAMES" value="3" enum="EnvironmentSDFGIFramesToConverge">
			Configure SDFGI to fully converge over 20 frames.
		</constant>
		<constant name="ENV_SDFGI_CONVERGE_IN_25_FRAMES" value="4" enum="EnvironmentSDFGIFramesToConverge">
			Configure SDFGI to fully converge over 25 frames.
		</constant>
		<constant name="ENV_SDFGI_CONVERGE_IN_30_FRAMES" value="5" enum="EnvironmentSDFGIFramesToConverge">
			Configure SDFGI to fully converge over 30 frames. This is the least responsive, but creates the least noisy result with a given ray count.
		</constant>
		<constant name="ENV_SDFGI_CONVERGE_MAX" value="6" enum="EnvironmentSDFGIFramesToConverge">
			Represents the size of the [enum EnvironmentSDFGIFramesToConverge] enum.
		</constant>
		<constant name="ENV_SDFGI_UPDATE_LIGHT_IN_1_FRAME" value="0" enum="EnvironmentSDFGIFramesToUpdateLight">
			Update indirect light from dynamic lights in SDFGI over 1 frame. This is the most responsive, but has the highest GPU requirements.
		</constant>
		<constant name="ENV_SDFGI_UPDATE_LIGHT_IN_2_FRAMES" value="1" enum="EnvironmentSDFGIFramesToUpdateLight">
			Update indirect light from dynamic lights in SDFGI over 2 frames.
		</constant>
		<constant name="ENV_SDFGI_UPDATE_LIGHT_IN_4_FRAMES" value="2" enum="EnvironmentSDFGIFramesToUpdateLight">
			Update indirect light from dynamic lights in SDFGI over 4 frames.
		</constant>
		<constant name="ENV_SDFGI_UPDATE_LIGHT_IN_8_FRAMES" value="3" enum="EnvironmentSDFGIFramesToUpdateLight">
			Update indirect light from dynamic lights in SDFGI over 8 frames.
		</constant>
		<constant name="ENV_SDFGI_UPDATE_LIGHT_IN_16_FRAMES" value="4" enum="EnvironmentSDFGIFramesToUpdateLight">
			Update indirect light from dynamic lights in SDFGI over 16 frames. This is the least responsive, but has the lowest GPU requirements.
		</constant>
		<constant name="ENV_SDFGI_UPDATE_LIGHT_MAX" value="5" enum="EnvironmentSDFGIFramesToUpdateLight">
			Represents the size of the [enum EnvironmentSDFGIFramesToUpdateLight] enum.
		</constant>
		<constant name="SUB_SURFACE_SCATTERING_QUALITY_DISABLED" value="0" enum="SubSurfaceScatteringQuality">
			Disables subsurface scattering entirely, even on materials that have [member BaseMaterial3D.subsurf_scatter_enabled] set to [code]true[/code]. This has the lowest GPU requirements.
		</constant>
		<constant name="SUB_SURFACE_SCATTERING_QUALITY_LOW" value="1" enum="SubSurfaceScatteringQuality">
			Low subsurface scattering quality.
		</constant>
		<constant name="SUB_SURFACE_SCATTERING_QUALITY_MEDIUM" value="2" enum="SubSurfaceScatteringQuality">
			Medium subsurface scattering quality.
		</constant>
		<constant name="SUB_SURFACE_SCATTERING_QUALITY_HIGH" value="3" enum="SubSurfaceScatteringQuality">
			High subsurface scattering quality. This has the highest GPU requirements.
		</constant>
		<constant name="DOF_BOKEH_BOX" value="0" enum="DOFBokehShape">
			Calculate the DOF blur using a box filter. The fastest option, but results in obvious lines in blur pattern.
		</constant>
		<constant name="DOF_BOKEH_HEXAGON" value="1" enum="DOFBokehShape">
			Calculates DOF blur using a hexagon shaped filter.
		</constant>
		<constant name="DOF_BOKEH_CIRCLE" value="2" enum="DOFBokehShape">
			Calculates DOF blur using a circle shaped filter. Best quality and most realistic, but slowest. Use only for areas where a lot of performance can be dedicated to post-processing (e.g. cutscenes).
		</constant>
		<constant name="DOF_BLUR_QUALITY_VERY_LOW" value="0" enum="DOFBlurQuality">
			Lowest quality DOF blur. This is the fastest setting, but you may be able to see filtering artifacts.
		</constant>
		<constant name="DOF_BLUR_QUALITY_LOW" value="1" enum="DOFBlurQuality">
			Low quality DOF blur.
		</constant>
		<constant name="DOF_BLUR_QUALITY_MEDIUM" value="2" enum="DOFBlurQuality">
			Medium quality DOF blur.
		</constant>
		<constant name="DOF_BLUR_QUALITY_HIGH" value="3" enum="DOFBlurQuality">
			Highest quality DOF blur. Results in the smoothest looking blur by taking the most samples, but is also significantly slower.
		</constant>
		<constant name="INSTANCE_NONE" value="0" enum="InstanceType">
			The instance does not have a type.
		</constant>
		<constant name="INSTANCE_MESH" value="1" enum="InstanceType">
			The instance is a mesh.
		</constant>
		<constant name="INSTANCE_MULTIMESH" value="2" enum="InstanceType">
			The instance is a multimesh.
		</constant>
		<constant name="INSTANCE_PARTICLES" value="3" enum="InstanceType">
			The instance is a particle emitter.
		</constant>
		<constant name="INSTANCE_PARTICLES_COLLISION" value="4" enum="InstanceType">
			The instance is a GPUParticles collision shape.
		</constant>
		<constant name="INSTANCE_LIGHT" value="5" enum="InstanceType">
			The instance is a light.
		</constant>
		<constant name="INSTANCE_REFLECTION_PROBE" value="6" enum="InstanceType">
			The instance is a reflection probe.
		</constant>
		<constant name="INSTANCE_DECAL" value="7" enum="InstanceType">
			The instance is a decal.
		</constant>
		<constant name="INSTANCE_VOXEL_GI" value="8" enum="InstanceType">
			The instance is a VoxelGI.
		</constant>
		<constant name="INSTANCE_LIGHTMAP" value="9" enum="InstanceType">
			The instance is a lightmap.
		</constant>
		<constant name="INSTANCE_OCCLUDER" value="10" enum="InstanceType">
			The instance is an occlusion culling occluder.
		</constant>
		<constant name="INSTANCE_VISIBLITY_NOTIFIER" value="11" enum="InstanceType">
			The instance is a visible on-screen notifier.
		</constant>
		<constant name="INSTANCE_FOG_VOLUME" value="12" enum="InstanceType">
			The instance is a fog volume.
		</constant>
		<constant name="INSTANCE_MAX" value="13" enum="InstanceType">
			Represents the size of the [enum InstanceType] enum.
		</constant>
		<constant name="INSTANCE_GEOMETRY_MASK" value="14" enum="InstanceType">
			A combination of the flags of geometry instances (mesh, multimesh, immediate and particles).
		</constant>
		<constant name="INSTANCE_FLAG_USE_BAKED_LIGHT" value="0" enum="InstanceFlags">
			Allows the instance to be used in baked lighting.
		</constant>
		<constant name="INSTANCE_FLAG_USE_DYNAMIC_GI" value="1" enum="InstanceFlags">
			Allows the instance to be used with dynamic global illumination.
		</constant>
		<constant name="INSTANCE_FLAG_DRAW_NEXT_FRAME_IF_VISIBLE" value="2" enum="InstanceFlags">
			When set, manually requests to draw geometry on next frame.
		</constant>
		<constant name="INSTANCE_FLAG_IGNORE_OCCLUSION_CULLING" value="3" enum="InstanceFlags">
			Always draw, even if the instance would be culled by occlusion culling. Does not affect view frustum culling.
		</constant>
		<constant name="INSTANCE_FLAG_MAX" value="4" enum="InstanceFlags">
			Represents the size of the [enum InstanceFlags] enum.
		</constant>
		<constant name="SHADOW_CASTING_SETTING_OFF" value="0" enum="ShadowCastingSetting">
			Disable shadows from this instance.
		</constant>
		<constant name="SHADOW_CASTING_SETTING_ON" value="1" enum="ShadowCastingSetting">
			Cast shadows from this instance.
		</constant>
		<constant name="SHADOW_CASTING_SETTING_DOUBLE_SIDED" value="2" enum="ShadowCastingSetting">
			Disable backface culling when rendering the shadow of the object. This is slightly slower but may result in more correct shadows.
		</constant>
		<constant name="SHADOW_CASTING_SETTING_SHADOWS_ONLY" value="3" enum="ShadowCastingSetting">
			Only render the shadows from the object. The object itself will not be drawn.
		</constant>
		<constant name="VISIBILITY_RANGE_FADE_DISABLED" value="0" enum="VisibilityRangeFadeMode" keywords="LOD_FADE_DISABLED">
			Disable visibility range fading for the given instance.
		</constant>
		<constant name="VISIBILITY_RANGE_FADE_SELF" value="1" enum="VisibilityRangeFadeMode" keywords="LOD_FADE_SELF">
			Fade-out the given instance when it approaches its visibility range limits.
		</constant>
		<constant name="VISIBILITY_RANGE_FADE_DEPENDENCIES" value="2" enum="VisibilityRangeFadeMode" keywords="LOD_FADE_DEPENDENCIES">
			Fade-in the given instance's dependencies when reaching its visibility range limits.
		</constant>
		<constant name="BAKE_CHANNEL_ALBEDO_ALPHA" value="0" enum="BakeChannels">
			Index of [Image] in array of [Image]s returned by [method bake_render_uv2]. Image uses [constant Image.FORMAT_RGBA8] and contains albedo color in the [code].rgb[/code] channels and alpha in the [code].a[/code] channel.
		</constant>
		<constant name="BAKE_CHANNEL_NORMAL" value="1" enum="BakeChannels">
			Index of [Image] in array of [Image]s returned by [method bake_render_uv2]. Image uses [constant Image.FORMAT_RGBA8] and contains the per-pixel normal of the object in the [code].rgb[/code] channels and nothing in the [code].a[/code] channel. The per-pixel normal is encoded as [code]normal * 0.5 + 0.5[/code].
		</constant>
		<constant name="BAKE_CHANNEL_ORM" value="2" enum="BakeChannels">
			Index of [Image] in array of [Image]s returned by [method bake_render_uv2]. Image uses [constant Image.FORMAT_RGBA8] and contains ambient occlusion (from material and decals only) in the [code].r[/code] channel, roughness in the [code].g[/code] channel, metallic in the [code].b[/code] channel and sub surface scattering amount in the [code].a[/code] channel.
		</constant>
		<constant name="BAKE_CHANNEL_EMISSION" value="3" enum="BakeChannels">
			Index of [Image] in array of [Image]s returned by [method bake_render_uv2]. Image uses [constant Image.FORMAT_RGBAH] and contains emission color in the [code].rgb[/code] channels and nothing in the [code].a[/code] channel.
		</constant>
		<constant name="CANVAS_TEXTURE_CHANNEL_DIFFUSE" value="0" enum="CanvasTextureChannel">
			Diffuse canvas texture ([member CanvasTexture.diffuse_texture]).
		</constant>
		<constant name="CANVAS_TEXTURE_CHANNEL_NORMAL" value="1" enum="CanvasTextureChannel">
			Normal map canvas texture ([member CanvasTexture.normal_texture]).
		</constant>
		<constant name="CANVAS_TEXTURE_CHANNEL_SPECULAR" value="2" enum="CanvasTextureChannel">
			Specular map canvas texture ([member CanvasTexture.specular_texture]).
		</constant>
		<constant name="NINE_PATCH_STRETCH" value="0" enum="NinePatchAxisMode">
			The nine patch gets stretched where needed.
		</constant>
		<constant name="NINE_PATCH_TILE" value="1" enum="NinePatchAxisMode">
			The nine patch gets filled with tiles where needed.
		</constant>
		<constant name="NINE_PATCH_TILE_FIT" value="2" enum="NinePatchAxisMode">
			The nine patch gets filled with tiles where needed and stretches them a bit if needed.
		</constant>
		<constant name="CANVAS_ITEM_TEXTURE_FILTER_DEFAULT" value="0" enum="CanvasItemTextureFilter">
			Uses the default filter mode for this [Viewport].
		</constant>
		<constant name="CANVAS_ITEM_TEXTURE_FILTER_NEAREST" value="1" enum="CanvasItemTextureFilter">
			The texture filter reads from the nearest pixel only. This makes the texture look pixelated from up close, and grainy from a distance (due to mipmaps not being sampled).
		</constant>
		<constant name="CANVAS_ITEM_TEXTURE_FILTER_LINEAR" value="2" enum="CanvasItemTextureFilter">
			The texture filter blends between the nearest 4 pixels. This makes the texture look smooth from up close, and grainy from a distance (due to mipmaps not being sampled).
		</constant>
		<constant name="CANVAS_ITEM_TEXTURE_FILTER_NEAREST_WITH_MIPMAPS" value="3" enum="CanvasItemTextureFilter">
			The texture filter reads from the nearest pixel and blends between the nearest 2 mipmaps (or uses the nearest mipmap if [member ProjectSettings.rendering/textures/default_filters/use_nearest_mipmap_filter] is [code]true[/code]). This makes the texture look pixelated from up close, and smooth from a distance.
			Use this for non-pixel art textures that may be viewed at a low scale (e.g. due to [Camera2D] zoom or sprite scaling), as mipmaps are important to smooth out pixels that are smaller than on-screen pixels.
		</constant>
		<constant name="CANVAS_ITEM_TEXTURE_FILTER_LINEAR_WITH_MIPMAPS" value="4" enum="CanvasItemTextureFilter">
			The texture filter blends between the nearest 4 pixels and between the nearest 2 mipmaps (or uses the nearest mipmap if [member ProjectSettings.rendering/textures/default_filters/use_nearest_mipmap_filter] is [code]true[/code]). This makes the texture look smooth from up close, and smooth from a distance.
			Use this for non-pixel art textures that may be viewed at a low scale (e.g. due to [Camera2D] zoom or sprite scaling), as mipmaps are important to smooth out pixels that are smaller than on-screen pixels.
		</constant>
		<constant name="CANVAS_ITEM_TEXTURE_FILTER_NEAREST_WITH_MIPMAPS_ANISOTROPIC" value="5" enum="CanvasItemTextureFilter">
			The texture filter reads from the nearest pixel and blends between 2 mipmaps (or uses the nearest mipmap if [member ProjectSettings.rendering/textures/default_filters/use_nearest_mipmap_filter] is [code]true[/code]) based on the angle between the surface and the camera view. This makes the texture look pixelated from up close, and smooth from a distance. Anisotropic filtering improves texture quality on surfaces that are almost in line with the camera, but is slightly slower. The anisotropic filtering level can be changed by adjusting [member ProjectSettings.rendering/textures/default_filters/anisotropic_filtering_level].
			[b]Note:[/b] This texture filter is rarely useful in 2D projects. [constant CANVAS_ITEM_TEXTURE_FILTER_NEAREST_WITH_MIPMAPS] is usually more appropriate in this case.
		</constant>
		<constant name="CANVAS_ITEM_TEXTURE_FILTER_LINEAR_WITH_MIPMAPS_ANISOTROPIC" value="6" enum="CanvasItemTextureFilter">
			The texture filter blends between the nearest 4 pixels and blends between 2 mipmaps (or uses the nearest mipmap if [member ProjectSettings.rendering/textures/default_filters/use_nearest_mipmap_filter] is [code]true[/code]) based on the angle between the surface and the camera view. This makes the texture look smooth from up close, and smooth from a distance. Anisotropic filtering improves texture quality on surfaces that are almost in line with the camera, but is slightly slower. The anisotropic filtering level can be changed by adjusting [member ProjectSettings.rendering/textures/default_filters/anisotropic_filtering_level].
			[b]Note:[/b] This texture filter is rarely useful in 2D projects. [constant CANVAS_ITEM_TEXTURE_FILTER_LINEAR_WITH_MIPMAPS] is usually more appropriate in this case.
		</constant>
		<constant name="CANVAS_ITEM_TEXTURE_FILTER_MAX" value="7" enum="CanvasItemTextureFilter">
			Max value for [enum CanvasItemTextureFilter] enum.
		</constant>
		<constant name="CANVAS_ITEM_TEXTURE_REPEAT_DEFAULT" value="0" enum="CanvasItemTextureRepeat">
			Uses the default repeat mode for this [Viewport].
		</constant>
		<constant name="CANVAS_ITEM_TEXTURE_REPEAT_DISABLED" value="1" enum="CanvasItemTextureRepeat">
			Disables textures repeating. Instead, when reading UVs outside the 0-1 range, the value will be clamped to the edge of the texture, resulting in a stretched out look at the borders of the texture.
		</constant>
		<constant name="CANVAS_ITEM_TEXTURE_REPEAT_ENABLED" value="2" enum="CanvasItemTextureRepeat">
			Enables the texture to repeat when UV coordinates are outside the 0-1 range. If using one of the linear filtering modes, this can result in artifacts at the edges of a texture when the sampler filters across the edges of the texture.
		</constant>
		<constant name="CANVAS_ITEM_TEXTURE_REPEAT_MIRROR" value="3" enum="CanvasItemTextureRepeat">
			Flip the texture when repeating so that the edge lines up instead of abruptly changing.
		</constant>
		<constant name="CANVAS_ITEM_TEXTURE_REPEAT_MAX" value="4" enum="CanvasItemTextureRepeat">
			Max value for [enum CanvasItemTextureRepeat] enum.
		</constant>
		<constant name="CANVAS_GROUP_MODE_DISABLED" value="0" enum="CanvasGroupMode">
			Child draws over parent and is not clipped.
		</constant>
		<constant name="CANVAS_GROUP_MODE_CLIP_ONLY" value="1" enum="CanvasGroupMode">
			Parent is used for the purposes of clipping only. Child is clipped to the parent's visible area, parent is not drawn.
		</constant>
		<constant name="CANVAS_GROUP_MODE_CLIP_AND_DRAW" value="2" enum="CanvasGroupMode">
			Parent is used for clipping child, but parent is also drawn underneath child as normal before clipping child to its visible area.
		</constant>
		<constant name="CANVAS_GROUP_MODE_TRANSPARENT" value="3" enum="CanvasGroupMode">
		</constant>
		<constant name="CANVAS_LIGHT_MODE_POINT" value="0" enum="CanvasLightMode">
			2D point light (see [PointLight2D]).
		</constant>
		<constant name="CANVAS_LIGHT_MODE_DIRECTIONAL" value="1" enum="CanvasLightMode">
			2D directional (sun/moon) light (see [DirectionalLight2D]).
		</constant>
		<constant name="CANVAS_LIGHT_BLEND_MODE_ADD" value="0" enum="CanvasLightBlendMode">
			Adds light color additive to the canvas.
		</constant>
		<constant name="CANVAS_LIGHT_BLEND_MODE_SUB" value="1" enum="CanvasLightBlendMode">
			Adds light color subtractive to the canvas.
		</constant>
		<constant name="CANVAS_LIGHT_BLEND_MODE_MIX" value="2" enum="CanvasLightBlendMode">
			The light adds color depending on transparency.
		</constant>
		<constant name="CANVAS_LIGHT_FILTER_NONE" value="0" enum="CanvasLightShadowFilter">
			Do not apply a filter to canvas light shadows.
		</constant>
		<constant name="CANVAS_LIGHT_FILTER_PCF5" value="1" enum="CanvasLightShadowFilter">
			Use PCF5 filtering to filter canvas light shadows.
		</constant>
		<constant name="CANVAS_LIGHT_FILTER_PCF13" value="2" enum="CanvasLightShadowFilter">
			Use PCF13 filtering to filter canvas light shadows.
		</constant>
		<constant name="CANVAS_LIGHT_FILTER_MAX" value="3" enum="CanvasLightShadowFilter">
			Max value of the [enum CanvasLightShadowFilter] enum.
		</constant>
		<constant name="CANVAS_OCCLUDER_POLYGON_CULL_DISABLED" value="0" enum="CanvasOccluderPolygonCullMode">
			Culling of the canvas occluder is disabled.
		</constant>
		<constant name="CANVAS_OCCLUDER_POLYGON_CULL_CLOCKWISE" value="1" enum="CanvasOccluderPolygonCullMode">
			Culling of the canvas occluder is clockwise.
		</constant>
		<constant name="CANVAS_OCCLUDER_POLYGON_CULL_COUNTER_CLOCKWISE" value="2" enum="CanvasOccluderPolygonCullMode">
			Culling of the canvas occluder is counterclockwise.
		</constant>
		<constant name="GLOBAL_VAR_TYPE_BOOL" value="0" enum="GlobalShaderParameterType">
			Boolean global shader parameter ([code]global uniform bool ...[/code]).
		</constant>
		<constant name="GLOBAL_VAR_TYPE_BVEC2" value="1" enum="GlobalShaderParameterType">
			2-dimensional boolean vector global shader parameter ([code]global uniform bvec2 ...[/code]).
		</constant>
		<constant name="GLOBAL_VAR_TYPE_BVEC3" value="2" enum="GlobalShaderParameterType">
			3-dimensional boolean vector global shader parameter ([code]global uniform bvec3 ...[/code]).
		</constant>
		<constant name="GLOBAL_VAR_TYPE_BVEC4" value="3" enum="GlobalShaderParameterType">
			4-dimensional boolean vector global shader parameter ([code]global uniform bvec4 ...[/code]).
		</constant>
		<constant name="GLOBAL_VAR_TYPE_INT" value="4" enum="GlobalShaderParameterType">
			Integer global shader parameter ([code]global uniform int ...[/code]).
		</constant>
		<constant name="GLOBAL_VAR_TYPE_IVEC2" value="5" enum="GlobalShaderParameterType">
			2-dimensional integer vector global shader parameter ([code]global uniform ivec2 ...[/code]).
		</constant>
		<constant name="GLOBAL_VAR_TYPE_IVEC3" value="6" enum="GlobalShaderParameterType">
			3-dimensional integer vector global shader parameter ([code]global uniform ivec3 ...[/code]).
		</constant>
		<constant name="GLOBAL_VAR_TYPE_IVEC4" value="7" enum="GlobalShaderParameterType">
			4-dimensional integer vector global shader parameter ([code]global uniform ivec4 ...[/code]).
		</constant>
		<constant name="GLOBAL_VAR_TYPE_RECT2I" value="8" enum="GlobalShaderParameterType">
			2-dimensional integer rectangle global shader parameter ([code]global uniform ivec4 ...[/code]). Equivalent to [constant GLOBAL_VAR_TYPE_IVEC4] in shader code, but exposed as a [Rect2i] in the editor UI.
		</constant>
		<constant name="GLOBAL_VAR_TYPE_UINT" value="9" enum="GlobalShaderParameterType">
			Unsigned integer global shader parameter ([code]global uniform uint ...[/code]).
		</constant>
		<constant name="GLOBAL_VAR_TYPE_UVEC2" value="10" enum="GlobalShaderParameterType">
			2-dimensional unsigned integer vector global shader parameter ([code]global uniform uvec2 ...[/code]).
		</constant>
		<constant name="GLOBAL_VAR_TYPE_UVEC3" value="11" enum="GlobalShaderParameterType">
			3-dimensional unsigned integer vector global shader parameter ([code]global uniform uvec3 ...[/code]).
		</constant>
		<constant name="GLOBAL_VAR_TYPE_UVEC4" value="12" enum="GlobalShaderParameterType">
			4-dimensional unsigned integer vector global shader parameter ([code]global uniform uvec4 ...[/code]).
		</constant>
		<constant name="GLOBAL_VAR_TYPE_FLOAT" value="13" enum="GlobalShaderParameterType">
			Single-precision floating-point global shader parameter ([code]global uniform float ...[/code]).
		</constant>
		<constant name="GLOBAL_VAR_TYPE_VEC2" value="14" enum="GlobalShaderParameterType">
			2-dimensional floating-point vector global shader parameter ([code]global uniform vec2 ...[/code]).
		</constant>
		<constant name="GLOBAL_VAR_TYPE_VEC3" value="15" enum="GlobalShaderParameterType">
			3-dimensional floating-point vector global shader parameter ([code]global uniform vec3 ...[/code]).
		</constant>
		<constant name="GLOBAL_VAR_TYPE_VEC4" value="16" enum="GlobalShaderParameterType">
			4-dimensional floating-point vector global shader parameter ([code]global uniform vec4 ...[/code]).
		</constant>
		<constant name="GLOBAL_VAR_TYPE_COLOR" value="17" enum="GlobalShaderParameterType">
			Color global shader parameter ([code]global uniform vec4 ...[/code]). Equivalent to [constant GLOBAL_VAR_TYPE_VEC4] in shader code, but exposed as a [Color] in the editor UI.
		</constant>
		<constant name="GLOBAL_VAR_TYPE_RECT2" value="18" enum="GlobalShaderParameterType">
			2-dimensional floating-point rectangle global shader parameter ([code]global uniform vec4 ...[/code]). Equivalent to [constant GLOBAL_VAR_TYPE_VEC4] in shader code, but exposed as a [Rect2] in the editor UI.
		</constant>
		<constant name="GLOBAL_VAR_TYPE_MAT2" value="19" enum="GlobalShaderParameterType">
			2×2 matrix global shader parameter ([code]global uniform mat2 ...[/code]). Exposed as a [PackedInt32Array] in the editor UI.
		</constant>
		<constant name="GLOBAL_VAR_TYPE_MAT3" value="20" enum="GlobalShaderParameterType">
			3×3 matrix global shader parameter ([code]global uniform mat3 ...[/code]). Exposed as a [Basis] in the editor UI.
		</constant>
		<constant name="GLOBAL_VAR_TYPE_MAT4" value="21" enum="GlobalShaderParameterType">
			4×4 matrix global shader parameter ([code]global uniform mat4 ...[/code]). Exposed as a [Projection] in the editor UI.
		</constant>
		<constant name="GLOBAL_VAR_TYPE_TRANSFORM_2D" value="22" enum="GlobalShaderParameterType">
			2-dimensional transform global shader parameter ([code]global uniform mat2x3 ...[/code]). Exposed as a [Transform2D] in the editor UI.
		</constant>
		<constant name="GLOBAL_VAR_TYPE_TRANSFORM" value="23" enum="GlobalShaderParameterType">
			3-dimensional transform global shader parameter ([code]global uniform mat3x4 ...[/code]). Exposed as a [Transform3D] in the editor UI.
		</constant>
		<constant name="GLOBAL_VAR_TYPE_SAMPLER2D" value="24" enum="GlobalShaderParameterType">
			2D sampler global shader parameter ([code]global uniform sampler2D ...[/code]). Exposed as a [Texture2D] in the editor UI.
		</constant>
		<constant name="GLOBAL_VAR_TYPE_SAMPLER2DARRAY" value="25" enum="GlobalShaderParameterType">
			2D sampler array global shader parameter ([code]global uniform sampler2DArray ...[/code]). Exposed as a [Texture2DArray] in the editor UI.
		</constant>
		<constant name="GLOBAL_VAR_TYPE_SAMPLER3D" value="26" enum="GlobalShaderParameterType">
			3D sampler global shader parameter ([code]global uniform sampler3D ...[/code]). Exposed as a [Texture3D] in the editor UI.
		</constant>
		<constant name="GLOBAL_VAR_TYPE_SAMPLERCUBE" value="27" enum="GlobalShaderParameterType">
			Cubemap sampler global shader parameter ([code]global uniform samplerCube ...[/code]). Exposed as a [Cubemap] in the editor UI.
		</constant>
		<constant name="GLOBAL_VAR_TYPE_SAMPLEREXT" value="28" enum="GlobalShaderParameterType">
			External sampler global shader parameter ([code]global uniform samplerExternalOES ...[/code]). Exposed as a [ExternalTexture] in the editor UI.
		</constant>
		<constant name="GLOBAL_VAR_TYPE_MAX" value="29" enum="GlobalShaderParameterType">
			Represents the size of the [enum GlobalShaderParameterType] enum.
		</constant>
		<constant name="RENDERING_INFO_TOTAL_OBJECTS_IN_FRAME" value="0" enum="RenderingInfo">
			Number of objects rendered in the current 3D scene. This varies depending on camera position and rotation.
		</constant>
		<constant name="RENDERING_INFO_TOTAL_PRIMITIVES_IN_FRAME" value="1" enum="RenderingInfo">
			Number of points, lines, or triangles rendered in the current 3D scene. This varies depending on camera position and rotation.
		</constant>
		<constant name="RENDERING_INFO_TOTAL_DRAW_CALLS_IN_FRAME" value="2" enum="RenderingInfo">
			Number of draw calls performed to render in the current 3D scene. This varies depending on camera position and rotation.
		</constant>
		<constant name="RENDERING_INFO_TEXTURE_MEM_USED" value="3" enum="RenderingInfo">
			Texture memory used (in bytes).
		</constant>
		<constant name="RENDERING_INFO_BUFFER_MEM_USED" value="4" enum="RenderingInfo">
			Buffer memory used (in bytes). This includes vertex data, uniform buffers, and many miscellaneous buffer types used internally.
		</constant>
		<constant name="RENDERING_INFO_VIDEO_MEM_USED" value="5" enum="RenderingInfo">
			Video memory used (in bytes). When using the Forward+ or Mobile renderers, this is always greater than the sum of [constant RENDERING_INFO_TEXTURE_MEM_USED] and [constant RENDERING_INFO_BUFFER_MEM_USED], since there is miscellaneous data not accounted for by those two metrics. When using the Compatibility renderer, this is equal to the sum of [constant RENDERING_INFO_TEXTURE_MEM_USED] and [constant RENDERING_INFO_BUFFER_MEM_USED].
		</constant>
		<constant name="RENDERING_INFO_PIPELINE_COMPILATIONS_CANVAS" value="6" enum="RenderingInfo">
			Number of pipeline compilations that were triggered by the 2D canvas renderer.
		</constant>
		<constant name="RENDERING_INFO_PIPELINE_COMPILATIONS_MESH" value="7" enum="RenderingInfo">
			Number of pipeline compilations that were triggered by loading meshes. These compilations will show up as longer loading times the first time a user runs the game and the pipeline is required.
		</constant>
		<constant name="RENDERING_INFO_PIPELINE_COMPILATIONS_SURFACE" value="8" enum="RenderingInfo">
			Number of pipeline compilations that were triggered by building the surface cache before rendering the scene. These compilations will show up as a stutter when loading an scene the first time a user runs the game and the pipeline is required.
		</constant>
		<constant name="RENDERING_INFO_PIPELINE_COMPILATIONS_DRAW" value="9" enum="RenderingInfo">
			Number of pipeline compilations that were triggered while drawing the scene. These compilations will show up as stutters during gameplay the first time a user runs the game and the pipeline is required.
		</constant>
		<constant name="RENDERING_INFO_PIPELINE_COMPILATIONS_SPECIALIZATION" value="10" enum="RenderingInfo">
			Number of pipeline compilations that were triggered to optimize the current scene. These compilations are done in the background and should not cause any stutters whatsoever.
		</constant>
		<constant name="PIPELINE_SOURCE_CANVAS" value="0" enum="PipelineSource">
			Pipeline compilation that was triggered by the 2D canvas renderer.
		</constant>
		<constant name="PIPELINE_SOURCE_MESH" value="1" enum="PipelineSource">
			Pipeline compilation that was triggered by loading a mesh.
		</constant>
		<constant name="PIPELINE_SOURCE_SURFACE" value="2" enum="PipelineSource">
			Pipeline compilation that was triggered by building the surface cache before rendering the scene.
		</constant>
		<constant name="PIPELINE_SOURCE_DRAW" value="3" enum="PipelineSource">
			Pipeline compilation that was triggered while drawing the scene.
		</constant>
		<constant name="PIPELINE_SOURCE_SPECIALIZATION" value="4" enum="PipelineSource">
			Pipeline compilation that was triggered to optimize the current scene.
		</constant>
		<constant name="PIPELINE_SOURCE_MAX" value="5" enum="PipelineSource">
			Represents the size of the [enum PipelineSource] enum.
		</constant>
		<constant name="FEATURE_SHADERS" value="0" enum="Features" deprecated="This constant has not been used since Redot 3.0.">
		</constant>
		<constant name="FEATURE_MULTITHREADED" value="1" enum="Features" deprecated="This constant has not been used since Redot 3.0.">
		</constant>
	</constants>
</class>
