<?xml version="1.0" encoding="UTF-8" ?>
<class name="VideoStreamPlayer" inherits="Control" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A control used for video playback.
	</brief_description>
	<description>
		A control used for playback of [VideoStream] resources.
		Supported video formats are [url=https://www.theora.org/]Ogg Theora[/url] ([code].ogv[/code], [VideoStreamTheora]) and any format exposed via a GDExtension plugin.
		[b]Warning:[/b] On Web, video playback [i]will[/i] perform poorly due to missing architecture-specific assembly optimizations.
	</description>
	<tutorials>
		<link title="Playing videos">$DOCS_URL/tutorials/animation/playing_videos.html</link>
	</tutorials>
	<methods>
		<method name="get_stream_length" qualifiers="const">
			<return type="float" />
			<description>
				The length of the current stream, in seconds.
				[b]Note:[/b] For [VideoStreamTheora] streams (the built-in format supported by Redot), this value will always be zero, as getting the stream length is not implemented yet. The feature may be supported by video formats implemented by a GDExtension add-on.
			</description>
		</method>
		<method name="get_stream_name" qualifiers="const">
			<return type="String" />
			<description>
				Returns the video stream's name, or [code]"&lt;No Stream&gt;"[/code] if no video stream is assigned.
			</description>
		</method>
		<method name="get_video_texture" qualifiers="const">
			<return type="Texture2D" />
			<description>
				Returns the current frame as a [Texture2D].
			</description>
		</method>
		<method name="is_playing" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if the video is playing.
				[b]Note:[/b] The video is still considered playing if paused during playback.
			</description>
		</method>
		<method name="play">
			<return type="void" />
			<description>
				Starts the video playback from the beginning. If the video is paused, this will not unpause the video.
			</description>
		</method>
		<method name="stop">
			<return type="void" />
			<description>
				Stops the video playback and sets the stream position to 0.
				[b]Note:[/b] Although the stream position will be set to 0, the first frame of the video stream won't become the current frame.
			</description>
		</method>
	</methods>
	<members>
		<member name="audio_track" type="int" setter="set_audio_track" getter="get_audio_track" default="0">
			The embedded audio track to play.
		</member>
		<member name="autoplay" type="bool" setter="set_autoplay" getter="has_autoplay" default="false">
			If [code]true[/code], playback starts when the scene loads.
		</member>
		<member name="buffering_msec" type="int" setter="set_buffering_msec" getter="get_buffering_msec" default="500">
			Amount of time in milliseconds to store in buffer while playing.
		</member>
		<member name="bus" type="StringName" setter="set_bus" getter="get_bus" default="&amp;&quot;Master&quot;">
			Audio bus to use for sound playback.
		</member>
		<member name="expand" type="bool" setter="set_expand" getter="has_expand" default="false">
			If [code]true[/code], the video scales to the control size. Otherwise, the control minimum size will be automatically adjusted to match the video stream's dimensions.
		</member>
		<member name="loop" type="bool" setter="set_loop" getter="has_loop" default="false">
			If [code]true[/code], the video restarts when it reaches its end.
		</member>
		<member name="paused" type="bool" setter="set_paused" getter="is_paused" default="false">
			If [code]true[/code], the video is paused.
		</member>
		<member name="stream" type="VideoStream" setter="set_stream" getter="get_stream">
			The assigned video stream. See description for supported formats.
		</member>
		<member name="stream_position" type="float" setter="set_stream_position" getter="get_stream_position">
			The current position of the stream, in seconds.
			[b]Note:[/b] Changing this value won't have any effect as seeking is not implemented yet, except in video formats implemented by a GDExtension add-on.
		</member>
		<member name="volume" type="float" setter="set_volume" getter="get_volume">
			Audio volume as a linear value.
		</member>
		<member name="volume_db" type="float" setter="set_volume_db" getter="get_volume_db" default="0.0">
			Audio volume in dB.
		</member>
	</members>
	<signals>
		<signal name="finished">
			<description>
				Emitted when playback is finished.
			</description>
		</signal>
	</signals>
</class>
