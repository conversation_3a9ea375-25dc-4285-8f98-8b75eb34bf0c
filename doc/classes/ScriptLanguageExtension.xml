<?xml version="1.0" encoding="UTF-8" ?>
<class name="ScriptLanguageExtension" inherits="ScriptLanguage" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
	</brief_description>
	<description>
	</description>
	<tutorials>
	</tutorials>
	<methods>
		<method name="_add_global_constant" qualifiers="virtual">
			<return type="void" />
			<param index="0" name="name" type="StringName" />
			<param index="1" name="value" type="Variant" />
			<description>
			</description>
		</method>
		<method name="_add_named_global_constant" qualifiers="virtual">
			<return type="void" />
			<param index="0" name="name" type="StringName" />
			<param index="1" name="value" type="Variant" />
			<description>
			</description>
		</method>
		<method name="_auto_indent_code" qualifiers="virtual const">
			<return type="String" />
			<param index="0" name="code" type="String" />
			<param index="1" name="from_line" type="int" />
			<param index="2" name="to_line" type="int" />
			<description>
			</description>
		</method>
		<method name="_can_inherit_from_file" qualifiers="virtual const">
			<return type="bool" />
			<description>
			</description>
		</method>
		<method name="_can_make_function" qualifiers="virtual const">
			<return type="bool" />
			<description>
			</description>
		</method>
		<method name="_complete_code" qualifiers="virtual const">
			<return type="Dictionary" />
			<param index="0" name="code" type="String" />
			<param index="1" name="path" type="String" />
			<param index="2" name="owner" type="Object" />
			<description>
			</description>
		</method>
		<method name="_create_script" qualifiers="virtual const">
			<return type="Object" />
			<description>
			</description>
		</method>
		<method name="_create_script_from_extension" qualifiers="virtual const">
			<return type="Object" />
			<param index="0" name="extension" type="String" />
			<description>
				Creates a new script object based on extension supported by this language.
				Also look at [method ScriptLanguageExtension._get_extensions].
			</description>
		</method>
		<method name="_debug_get_current_stack_info" qualifiers="virtual">
			<return type="Dictionary[]" />
			<description>
			</description>
		</method>
		<method name="_debug_get_error" qualifiers="virtual const">
			<return type="String" />
			<description>
			</description>
		</method>
		<method name="_debug_get_globals" qualifiers="virtual">
			<return type="Dictionary" />
			<param index="0" name="max_subitems" type="int" />
			<param index="1" name="max_depth" type="int" />
			<description>
			</description>
		</method>
		<method name="_debug_get_stack_level_count" qualifiers="virtual const">
			<return type="int" />
			<description>
			</description>
		</method>
		<method name="_debug_get_stack_level_function" qualifiers="virtual const">
			<return type="String" />
			<param index="0" name="level" type="int" />
			<description>
			</description>
		</method>
		<method name="_debug_get_stack_level_instance" qualifiers="virtual">
			<return type="void*" />
			<param index="0" name="level" type="int" />
			<description>
			</description>
		</method>
		<method name="_debug_get_stack_level_line" qualifiers="virtual const">
			<return type="int" />
			<param index="0" name="level" type="int" />
			<description>
			</description>
		</method>
		<method name="_debug_get_stack_level_locals" qualifiers="virtual">
			<return type="Dictionary" />
			<param index="0" name="level" type="int" />
			<param index="1" name="max_subitems" type="int" />
			<param index="2" name="max_depth" type="int" />
			<description>
			</description>
		</method>
		<method name="_debug_get_stack_level_members" qualifiers="virtual">
			<return type="Dictionary" />
			<param index="0" name="level" type="int" />
			<param index="1" name="max_subitems" type="int" />
			<param index="2" name="max_depth" type="int" />
			<description>
			</description>
		</method>
		<method name="_debug_get_stack_level_source" qualifiers="virtual const">
			<return type="String" />
			<param index="0" name="level" type="int" />
			<description>
				Returns the source associated with a given debug stack position.
			</description>
		</method>
		<method name="_debug_parse_stack_level_expression" qualifiers="virtual">
			<return type="String" />
			<param index="0" name="level" type="int" />
			<param index="1" name="expression" type="String" />
			<param index="2" name="max_subitems" type="int" />
			<param index="3" name="max_depth" type="int" />
			<description>
			</description>
		</method>
		<method name="_find_function" qualifiers="virtual const">
			<return type="int" />
			<param index="0" name="function" type="String" />
			<param index="1" name="code" type="String" />
			<description>
				Returns the line where the function is defined in the code, or [code]-1[/code] if the function is not present.
			</description>
		</method>
		<method name="_finish" qualifiers="virtual">
			<return type="void" />
			<description>
			</description>
		</method>
		<method name="_frame" qualifiers="virtual">
			<return type="void" />
			<description>
			</description>
		</method>
		<method name="_get_built_in_templates" qualifiers="virtual const">
			<return type="Dictionary[]" />
			<param index="0" name="object" type="StringName" />
			<description>
			</description>
		</method>
		<method name="_get_comment_delimiters" qualifiers="virtual const">
			<return type="PackedStringArray" />
			<description>
			</description>
		</method>
		<method name="_get_doc_comment_delimiters" qualifiers="virtual const">
			<return type="PackedStringArray" />
			<description>
			</description>
		</method>
		<method name="_get_extension" qualifiers="virtual const">
			<return type="String" />
			<description>
			</description>
		</method>
		<method name="_get_extensions" qualifiers="virtual const">
			<return type="PackedStringArray" />
			<description>
				Returns file extensions of scripts supported by this language.
			</description>
		</method>
		<method name="_get_global_class_name" qualifiers="virtual const">
			<return type="Dictionary" />
			<param index="0" name="path" type="String" />
			<description>
			</description>
		</method>
		<method name="_get_name" qualifiers="virtual const">
			<return type="String" />
			<description>
			</description>
		</method>
		<method name="_get_public_annotations" qualifiers="virtual const">
			<return type="Dictionary[]" />
			<description>
			</description>
		</method>
		<method name="_get_public_constants" qualifiers="virtual const">
			<return type="Dictionary" />
			<description>
			</description>
		</method>
		<method name="_get_public_functions" qualifiers="virtual const">
			<return type="Dictionary[]" />
			<description>
			</description>
		</method>
		<method name="_get_recognized_extensions" qualifiers="virtual const">
			<return type="PackedStringArray" />
			<description>
			</description>
		</method>
		<method name="_get_reserved_words" qualifiers="virtual const">
			<return type="PackedStringArray" />
			<description>
			</description>
		</method>
		<method name="_get_string_delimiters" qualifiers="virtual const">
			<return type="PackedStringArray" />
			<description>
			</description>
		</method>
		<method name="_get_type" qualifiers="virtual const">
			<return type="String" />
			<description>
			</description>
		</method>
		<method name="_get_type_from_extension" qualifiers="virtual const">
			<return type="String" />
			<param index="0" name="extension" type="String" />
			<description>
				Returns the script type of the given file extension.
				Also look at [method ScriptLanguageExtension._get_extensions].
			</description>
		</method>
		<method name="_handles_global_class_type" qualifiers="virtual const">
			<return type="bool" />
			<param index="0" name="type" type="String" />
			<description>
			</description>
		</method>
		<method name="_has_named_classes" qualifiers="virtual const" deprecated="This method is not called by the engine.">
			<return type="bool" />
			<description>
			</description>
		</method>
		<method name="_init" qualifiers="virtual">
			<return type="void" />
			<description>
			</description>
		</method>
		<method name="_is_control_flow_keyword" qualifiers="virtual const">
			<return type="bool" />
			<param index="0" name="keyword" type="String" />
			<description>
			</description>
		</method>
		<method name="_is_script_attachable" qualifiers="virtual const">
			<return type="bool" />
			<param index="0" name="extension" type="String" />
			<description>
				Determines if a script can be attached to objects based on the given file extension.
				Returns true if the script with the specified extension can be attached, false otherwise.
				Also look at [method ScriptLanguageExtension._get_extensions].
			</description>
		</method>
		<method name="_is_using_templates" qualifiers="virtual">
			<return type="bool" />
			<description>
			</description>
		</method>
		<method name="_lookup_code" qualifiers="virtual const">
			<return type="Dictionary" />
			<param index="0" name="code" type="String" />
			<param index="1" name="symbol" type="String" />
			<param index="2" name="path" type="String" />
			<param index="3" name="owner" type="Object" />
			<description>
			</description>
		</method>
		<method name="_make_function" qualifiers="virtual const">
			<return type="String" />
			<param index="0" name="class_name" type="String" />
			<param index="1" name="function_name" type="String" />
			<param index="2" name="function_args" type="PackedStringArray" />
			<description>
			</description>
		</method>
		<method name="_make_template" qualifiers="virtual const">
			<return type="Script" />
			<param index="0" name="template" type="String" />
			<param index="1" name="class_name" type="String" />
			<param index="2" name="base_class_name" type="String" />
			<description>
			</description>
		</method>
		<method name="_make_template_using_extension" qualifiers="virtual const">
			<return type="Script" />
			<param index="0" name="template" type="String" />
			<param index="1" name="class_name" type="String" />
			<param index="2" name="base_class_name" type="String" />
			<param index="3" name="extension" type="String" />
			<description>
			</description>
		</method>
		<method name="_open_in_external_editor" qualifiers="virtual">
			<return type="int" enum="Error" />
			<param index="0" name="script" type="Script" />
			<param index="1" name="line" type="int" />
			<param index="2" name="column" type="int" />
			<description>
			</description>
		</method>
		<method name="_overrides_external_editor" qualifiers="virtual">
			<return type="bool" />
			<description>
			</description>
		</method>
		<method name="_preferred_file_name_casing" qualifiers="virtual const">
			<return type="int" enum="ScriptLanguage.ScriptNameCasing" />
			<description>
			</description>
		</method>
		<method name="_profiling_get_accumulated_data" qualifiers="virtual">
			<return type="int" />
			<param index="0" name="info_array" type="ScriptLanguageExtensionProfilingInfo*" />
			<param index="1" name="info_max" type="int" />
			<description>
			</description>
		</method>
		<method name="_profiling_get_frame_data" qualifiers="virtual">
			<return type="int" />
			<param index="0" name="info_array" type="ScriptLanguageExtensionProfilingInfo*" />
			<param index="1" name="info_max" type="int" />
			<description>
			</description>
		</method>
		<method name="_profiling_set_save_native_calls" qualifiers="virtual">
			<return type="void" />
			<param index="0" name="enable" type="bool" />
			<description>
			</description>
		</method>
		<method name="_profiling_start" qualifiers="virtual">
			<return type="void" />
			<description>
			</description>
		</method>
		<method name="_profiling_stop" qualifiers="virtual">
			<return type="void" />
			<description>
			</description>
		</method>
		<method name="_reload_all_scripts" qualifiers="virtual">
			<return type="void" />
			<description>
			</description>
		</method>
		<method name="_reload_scripts" qualifiers="virtual">
			<return type="void" />
			<param index="0" name="scripts" type="Array" />
			<param index="1" name="soft_reload" type="bool" />
			<description>
			</description>
		</method>
		<method name="_reload_tool_script" qualifiers="virtual">
			<return type="void" />
			<param index="0" name="script" type="Script" />
			<param index="1" name="soft_reload" type="bool" />
			<description>
			</description>
		</method>
		<method name="_remove_named_global_constant" qualifiers="virtual">
			<return type="void" />
			<param index="0" name="name" type="StringName" />
			<description>
			</description>
		</method>
		<method name="_supports_builtin_mode" qualifiers="virtual const">
			<return type="bool" />
			<description>
			</description>
		</method>
		<method name="_supports_documentation" qualifiers="virtual const">
			<return type="bool" />
			<description>
			</description>
		</method>
		<method name="_thread_enter" qualifiers="virtual">
			<return type="void" />
			<description>
			</description>
		</method>
		<method name="_thread_exit" qualifiers="virtual">
			<return type="void" />
			<description>
			</description>
		</method>
		<method name="_validate" qualifiers="virtual const">
			<return type="Dictionary" />
			<param index="0" name="script" type="String" />
			<param index="1" name="path" type="String" />
			<param index="2" name="validate_functions" type="bool" />
			<param index="3" name="validate_errors" type="bool" />
			<param index="4" name="validate_warnings" type="bool" />
			<param index="5" name="validate_safe_lines" type="bool" />
			<description>
			</description>
		</method>
		<method name="_validate_path" qualifiers="virtual const">
			<return type="String" />
			<param index="0" name="path" type="String" />
			<description>
			</description>
		</method>
	</methods>
	<constants>
		<constant name="LOOKUP_RESULT_SCRIPT_LOCATION" value="0" enum="LookupResultType">
		</constant>
		<constant name="LOOKUP_RESULT_CLASS" value="1" enum="LookupResultType">
		</constant>
		<constant name="LOOKUP_RESULT_CLASS_CONSTANT" value="2" enum="LookupResultType">
		</constant>
		<constant name="LOOKUP_RESULT_CLASS_PROPERTY" value="3" enum="LookupResultType">
		</constant>
		<constant name="LOOKUP_RESULT_CLASS_METHOD" value="4" enum="LookupResultType">
		</constant>
		<constant name="LOOKUP_RESULT_CLASS_SIGNAL" value="5" enum="LookupResultType">
		</constant>
		<constant name="LOOKUP_RESULT_CLASS_ENUM" value="6" enum="LookupResultType">
		</constant>
		<constant name="LOOKUP_RESULT_CLASS_TBD_GLOBALSCOPE" value="7" enum="LookupResultType" deprecated="">
		</constant>
		<constant name="LOOKUP_RESULT_CLASS_ANNOTATION" value="8" enum="LookupResultType">
		</constant>
		<constant name="LOOKUP_RESULT_LOCAL_CONSTANT" value="9" enum="LookupResultType">
		</constant>
		<constant name="LOOKUP_RESULT_LOCAL_VARIABLE" value="10" enum="LookupResultType">
		</constant>
		<constant name="LOOKUP_RESULT_MAX" value="11" enum="LookupResultType">
		</constant>
		<constant name="LOCATION_LOCAL" value="0" enum="CodeCompletionLocation">
			The option is local to the location of the code completion query - e.g. a local variable. Subsequent value of location represent options from the outer class, the exact value represent how far they are (in terms of inner classes).
		</constant>
		<constant name="LOCATION_PARENT_MASK" value="256" enum="CodeCompletionLocation">
			The option is from the containing class or a parent class, relative to the location of the code completion query. Perform a bitwise OR with the class depth (e.g. [code]0[/code] for the local class, [code]1[/code] for the parent, [code]2[/code] for the grandparent, etc.) to store the depth of an option in the class or a parent class.
		</constant>
		<constant name="LOCATION_OTHER_USER_CODE" value="512" enum="CodeCompletionLocation">
			The option is from user code which is not local and not in a derived class (e.g. Autoload Singletons).
		</constant>
		<constant name="LOCATION_OTHER" value="1024" enum="CodeCompletionLocation">
			The option is from other engine code, not covered by the other enum constants - e.g. built-in classes.
		</constant>
		<constant name="CODE_COMPLETION_KIND_CLASS" value="0" enum="CodeCompletionKind">
		</constant>
		<constant name="CODE_COMPLETION_KIND_FUNCTION" value="1" enum="CodeCompletionKind">
		</constant>
		<constant name="CODE_COMPLETION_KIND_SIGNAL" value="2" enum="CodeCompletionKind">
		</constant>
		<constant name="CODE_COMPLETION_KIND_VARIABLE" value="3" enum="CodeCompletionKind">
		</constant>
		<constant name="CODE_COMPLETION_KIND_MEMBER" value="4" enum="CodeCompletionKind">
		</constant>
		<constant name="CODE_COMPLETION_KIND_ENUM" value="5" enum="CodeCompletionKind">
		</constant>
		<constant name="CODE_COMPLETION_KIND_CONSTANT" value="6" enum="CodeCompletionKind">
		</constant>
		<constant name="CODE_COMPLETION_KIND_NODE_PATH" value="7" enum="CodeCompletionKind">
		</constant>
		<constant name="CODE_COMPLETION_KIND_FILE_PATH" value="8" enum="CodeCompletionKind">
		</constant>
		<constant name="CODE_COMPLETION_KIND_PLAIN_TEXT" value="9" enum="CodeCompletionKind">
		</constant>
		<constant name="CODE_COMPLETION_KIND_MAX" value="10" enum="CodeCompletionKind">
		</constant>
	</constants>
</class>
