<?xml version="1.0" encoding="UTF-8" ?>
<class name="Quaternion" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A unit quaternion used for representing 3D rotations.
	</brief_description>
	<description>
		The [Quaternion] built-in [Variant] type is a 4D data structure that represents rotation in the form of a [url=https://en.wikipedia.org/wiki/Quaternions_and_spatial_rotation]Hamilton convention quaternion[/url]. Compared to the [Basis] type which can store both rotation and scale, quaternions can [i]only[/i] store rotation.
		A [Quaternion] is composed by 4 floating-point components: [member w], [member x], [member y], and [member z]. These components are very compact in memory, and because of this some operations are more efficient and less likely to cause floating-point errors. Methods such as [method get_angle], [method get_axis], and [method slerp] are faster than their [Basis] counterparts.
		For a great introduction to quaternions, see [url=https://www.youtube.com/watch?v=d4EgbgTm0Bg]this video by 3Blue1Brown[/url]. You do not need to know the math behind quaternions, as Redot provides several helper methods that handle it for you. These include [method slerp] and [method spherical_cubic_interpolate], as well as the [code]*[/code] operator.
		[b]Note:[/b] Quaternions must be normalized before being used for rotation (see [method normalized]).
		[b]Note:[/b] Similarly to [Vector2] and [Vector3], the components of a quaternion use 32-bit precision by default, unlike [float] which is always 64-bit. If double precision is needed, compile the engine with the option [code]precision=double[/code].
	</description>
	<tutorials>
		<link title="3Blue1Brown&apos;s video on Quaternions">https://www.youtube.com/watch?v=d4EgbgTm0Bg</link>
		<link title="Online Quaternion Visualization">https://quaternions.online/</link>
		<link title="Using 3D transforms">$DOCS_URL/tutorials/3d/using_transforms.html#interpolating-with-quaternions</link>
		<link title="Third Person Shooter (TPS) Demo">https://godotengine.org/asset-library/asset/2710</link>
		<link title="Advanced Quaternion Visualization">https://iwatake2222.github.io/rotation_master/rotation_master.html</link>
	</tutorials>
	<constructors>
		<constructor name="Quaternion">
			<return type="Quaternion" />
			<description>
				Constructs a [Quaternion] identical to [constant IDENTITY].
				[b]Note:[/b] In C#, this constructs a [Quaternion] with all of its components set to [code]0.0[/code].
			</description>
		</constructor>
		<constructor name="Quaternion">
			<return type="Quaternion" />
			<param index="0" name="from" type="Quaternion" />
			<description>
				Constructs a [Quaternion] as a copy of the given [Quaternion].
			</description>
		</constructor>
		<constructor name="Quaternion">
			<return type="Quaternion" />
			<param index="0" name="arc_from" type="Vector3" />
			<param index="1" name="arc_to" type="Vector3" />
			<description>
				Constructs a [Quaternion] representing the shortest arc between [param arc_from] and [param arc_to]. These can be imagined as two points intersecting a sphere's surface, with a radius of [code]1.0[/code].
			</description>
		</constructor>
		<constructor name="Quaternion">
			<return type="Quaternion" />
			<param index="0" name="axis" type="Vector3" />
			<param index="1" name="angle" type="float" />
			<description>
				Constructs a [Quaternion] representing rotation around the [param axis] by the given [param angle], in radians. The axis must be a normalized vector.
			</description>
		</constructor>
		<constructor name="Quaternion">
			<return type="Quaternion" />
			<param index="0" name="from" type="Basis" />
			<description>
				Constructs a [Quaternion] from the given rotation [Basis].
				This constructor is faster than [method Basis.get_rotation_quaternion], but the given basis must be [i]orthonormalized[/i] (see [method Basis.orthonormalized]). Otherwise, the constructor fails and returns [constant IDENTITY].
			</description>
		</constructor>
		<constructor name="Quaternion">
			<return type="Quaternion" />
			<param index="0" name="x" type="float" />
			<param index="1" name="y" type="float" />
			<param index="2" name="z" type="float" />
			<param index="3" name="w" type="float" />
			<description>
				Constructs a [Quaternion] defined by the given values.
				[b]Note:[/b] Only normalized quaternions represent rotation; if these values are not normalized, the new [Quaternion] will not be a valid rotation.
			</description>
		</constructor>
	</constructors>
	<methods>
		<method name="angle_to" qualifiers="const">
			<return type="float" />
			<param index="0" name="to" type="Quaternion" />
			<description>
				Returns the angle between this quaternion and [param to]. This is the magnitude of the angle you would need to rotate by to get from one to the other.
				[b]Note:[/b] The magnitude of the floating-point error for this method is abnormally high, so methods such as [code]is_zero_approx[/code] will not work reliably.
			</description>
		</method>
		<method name="dot" qualifiers="const">
			<return type="float" />
			<param index="0" name="with" type="Quaternion" />
			<description>
				Returns the dot product between this quaternion and [param with].
				This is equivalent to [code](quat.x * with.x) + (quat.y * with.y) + (quat.z * with.z) + (quat.w * with.w)[/code].
			</description>
		</method>
		<method name="exp" qualifiers="const">
			<return type="Quaternion" />
			<description>
				Returns the exponential of this quaternion. The rotation axis of the result is the normalized rotation axis of this quaternion, the angle of the result is the length of the vector part of this quaternion.
			</description>
		</method>
		<method name="from_euler" qualifiers="static">
			<return type="Quaternion" />
			<param index="0" name="euler" type="Vector3" />
			<description>
				Constructs a new [Quaternion] from the given [Vector3] of [url=https://en.wikipedia.org/wiki/Euler_angles]Euler angles[/url], in radians. This method always uses the YXZ convention ([constant EULER_ORDER_YXZ]).
			</description>
		</method>
		<method name="get_angle" qualifiers="const">
			<return type="float" />
			<description>
				Returns the angle of the rotation represented by this quaternion.
				[b]Note:[/b] The quaternion must be normalized.
			</description>
		</method>
		<method name="get_axis" qualifiers="const">
			<return type="Vector3" />
			<description>
				Returns the rotation axis of the rotation represented by this quaternion.
			</description>
		</method>
		<method name="get_euler" qualifiers="const">
			<return type="Vector3" />
			<param index="0" name="order" type="int" default="2" />
			<description>
				Returns this quaternion's rotation as a [Vector3] of [url=https://en.wikipedia.org/wiki/Euler_angles]Euler angles[/url], in radians.
				The order of each consecutive rotation can be changed with [param order] (see [enum EulerOrder] constants). By default, the YXZ convention is used ([constant EULER_ORDER_YXZ]): Z (roll) is calculated first, then X (pitch), and lastly Y (yaw). When using the opposite method [method from_euler], this order is reversed.
			</description>
		</method>
		<method name="inverse" qualifiers="const">
			<return type="Quaternion" />
			<description>
				Returns the inverse version of this quaternion, inverting the sign of every component except [member w].
			</description>
		</method>
		<method name="is_equal_approx" qualifiers="const">
			<return type="bool" />
			<param index="0" name="to" type="Quaternion" />
			<description>
				Returns [code]true[/code] if this quaternion and [param to] are approximately equal, by calling [method @GlobalScope.is_equal_approx] on each component.
			</description>
		</method>
		<method name="is_finite" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if this quaternion is finite, by calling [method @GlobalScope.is_finite] on each component.
			</description>
		</method>
		<method name="is_normalized" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if this quaternion is normalized. See also [method normalized].
			</description>
		</method>
		<method name="length" qualifiers="const" keywords="size">
			<return type="float" />
			<description>
				Returns this quaternion's length, also called magnitude.
			</description>
		</method>
		<method name="length_squared" qualifiers="const">
			<return type="float" />
			<description>
				Returns this quaternion's length, squared.
				[b]Note:[/b] This method is faster than [method length], so prefer it if you only need to compare quaternion lengths.
			</description>
		</method>
		<method name="log" qualifiers="const">
			<return type="Quaternion" />
			<description>
				Returns the logarithm of this quaternion. Multiplies this quaternion's rotation axis by its rotation angle, and stores the result in the returned quaternion's vector part ([member x], [member y], and [member z]). The returned quaternion's real part ([member w]) is always [code]0.0[/code].
			</description>
		</method>
		<method name="normalized" qualifiers="const">
			<return type="Quaternion" />
			<description>
				Returns a copy of this quaternion, normalized so that its length is [code]1.0[/code]. See also [method is_normalized].
			</description>
		</method>
		<method name="slerp" qualifiers="const" keywords="interpolate">
			<return type="Quaternion" />
			<param index="0" name="to" type="Quaternion" />
			<param index="1" name="weight" type="float" />
			<description>
				Performs a spherical-linear interpolation with the [param to] quaternion, given a [param weight] and returns the result. Both this quaternion and [param to] must be normalized.
			</description>
		</method>
		<method name="slerpni" qualifiers="const">
			<return type="Quaternion" />
			<param index="0" name="to" type="Quaternion" />
			<param index="1" name="weight" type="float" />
			<description>
				Performs a spherical-linear interpolation with the [param to] quaternion, given a [param weight] and returns the result. Unlike [method slerp], this method does not check if the rotation path is smaller than 90 degrees. Both this quaternion and [param to] must be normalized.
			</description>
		</method>
		<method name="spherical_cubic_interpolate" qualifiers="const">
			<return type="Quaternion" />
			<param index="0" name="b" type="Quaternion" />
			<param index="1" name="pre_a" type="Quaternion" />
			<param index="2" name="post_b" type="Quaternion" />
			<param index="3" name="weight" type="float" />
			<description>
				Performs a spherical cubic interpolation between quaternions [param pre_a], this vector, [param b], and [param post_b], by the given amount [param weight].
			</description>
		</method>
		<method name="spherical_cubic_interpolate_in_time" qualifiers="const">
			<return type="Quaternion" />
			<param index="0" name="b" type="Quaternion" />
			<param index="1" name="pre_a" type="Quaternion" />
			<param index="2" name="post_b" type="Quaternion" />
			<param index="3" name="weight" type="float" />
			<param index="4" name="b_t" type="float" />
			<param index="5" name="pre_a_t" type="float" />
			<param index="6" name="post_b_t" type="float" />
			<description>
				Performs a spherical cubic interpolation between quaternions [param pre_a], this vector, [param b], and [param post_b], by the given amount [param weight].
				It can perform smoother interpolation than [method spherical_cubic_interpolate] by the time values.
			</description>
		</method>
	</methods>
	<members>
		<member name="w" type="float" setter="" getter="" default="1.0">
			W component of the quaternion. This is the "real" part.
			[b]Note:[/b] Quaternion components should usually not be manipulated directly.
		</member>
		<member name="x" type="float" setter="" getter="" default="0.0">
			X component of the quaternion. This is the value along the "imaginary" [code]i[/code] axis.
			[b]Note:[/b] Quaternion components should usually not be manipulated directly.
		</member>
		<member name="y" type="float" setter="" getter="" default="0.0">
			Y component of the quaternion. This is the value along the "imaginary" [code]j[/code] axis.
			[b]Note:[/b] Quaternion components should usually not be manipulated directly.
		</member>
		<member name="z" type="float" setter="" getter="" default="0.0">
			Z component of the quaternion. This is the value along the "imaginary" [code]k[/code] axis.
			[b]Note:[/b] Quaternion components should usually not be manipulated directly.
		</member>
	</members>
	<constants>
		<constant name="IDENTITY" value="Quaternion(0, 0, 0, 1)">
			The identity quaternion, representing no rotation. This has the same rotation as [constant Basis.IDENTITY].
			If a [Vector3] is rotated (multiplied) by this quaternion, it does not change.
			[b]Note:[/b] In GDScript, this constant is equivalent to creating a [constructor Quaternion] without any arguments. It can be used to make your code clearer, and for consistency with C#.
		</constant>
	</constants>
	<operators>
		<operator name="operator !=">
			<return type="bool" />
			<param index="0" name="right" type="Quaternion" />
			<description>
				Returns [code]true[/code] if the components of both quaternions are not exactly equal.
				[b]Note:[/b] Due to floating-point precision errors, consider using [method is_equal_approx] instead, which is more reliable.
			</description>
		</operator>
		<operator name="operator *">
			<return type="Quaternion" />
			<param index="0" name="right" type="Quaternion" />
			<description>
				Composes (multiplies) two quaternions. This rotates the [param right] quaternion (the child) by this quaternion (the parent).
			</description>
		</operator>
		<operator name="operator *">
			<return type="Vector3" />
			<param index="0" name="right" type="Vector3" />
			<description>
				Rotates (multiplies) the [param right] vector by this quaternion, returning a [Vector3].
			</description>
		</operator>
		<operator name="operator *">
			<return type="Quaternion" />
			<param index="0" name="right" type="float" />
			<description>
				Multiplies each component of the [Quaternion] by the right [float] value.
				This operation is not meaningful on its own, but it can be used as a part of a larger expression.
			</description>
		</operator>
		<operator name="operator *">
			<return type="Quaternion" />
			<param index="0" name="right" type="int" />
			<description>
				Multiplies each component of the [Quaternion] by the right [int] value.
				This operation is not meaningful on its own, but it can be used as a part of a larger expression.
			</description>
		</operator>
		<operator name="operator +">
			<return type="Quaternion" />
			<param index="0" name="right" type="Quaternion" />
			<description>
				Adds each component of the left [Quaternion] to the right [Quaternion].
				This operation is not meaningful on its own, but it can be used as a part of a larger expression, such as approximating an intermediate rotation between two nearby rotations.
			</description>
		</operator>
		<operator name="operator -">
			<return type="Quaternion" />
			<param index="0" name="right" type="Quaternion" />
			<description>
				Subtracts each component of the left [Quaternion] by the right [Quaternion].
				This operation is not meaningful on its own, but it can be used as a part of a larger expression.
			</description>
		</operator>
		<operator name="operator /">
			<return type="Quaternion" />
			<param index="0" name="right" type="float" />
			<description>
				Divides each component of the [Quaternion] by the right [float] value.
				This operation is not meaningful on its own, but it can be used as a part of a larger expression.
			</description>
		</operator>
		<operator name="operator /">
			<return type="Quaternion" />
			<param index="0" name="right" type="int" />
			<description>
				Divides each component of the [Quaternion] by the right [int] value.
				This operation is not meaningful on its own, but it can be used as a part of a larger expression.
			</description>
		</operator>
		<operator name="operator ==">
			<return type="bool" />
			<param index="0" name="right" type="Quaternion" />
			<description>
				Returns [code]true[/code] if the components of both quaternions are exactly equal.
				[b]Note:[/b] Due to floating-point precision errors, consider using [method is_equal_approx] instead, which is more reliable.
			</description>
		</operator>
		<operator name="operator []">
			<return type="float" />
			<param index="0" name="index" type="int" />
			<description>
				Accesses each component of this quaternion by their index.
				Index [code]0[/code] is the same as [member x], index [code]1[/code] is the same as [member y], index [code]2[/code] is the same as [member z], and index [code]3[/code] is the same as [member w].
			</description>
		</operator>
		<operator name="operator unary+">
			<return type="Quaternion" />
			<description>
				Returns the same value as if the [code]+[/code] was not there. Unary [code]+[/code] does nothing, but sometimes it can make your code more readable.
			</description>
		</operator>
		<operator name="operator unary-">
			<return type="Quaternion" />
			<description>
				Returns the negative value of the [Quaternion]. This is the same as multiplying all components by [code]-1[/code]. This operation results in a quaternion that represents the same rotation.
			</description>
		</operator>
	</operators>
</class>
