<?xml version="1.0" encoding="UTF-8" ?>
<class name="TextMesh" inherits="PrimitiveMesh" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Generate an [PrimitiveMesh] from the text.
	</brief_description>
	<description>
		Generate an [PrimitiveMesh] from the text.
		TextMesh can be generated only when using dynamic fonts with vector glyph contours. Bitmap fonts (including bitmap data in the TrueType/OpenType containers, like color emoji fonts) are not supported.
		The UV layout is arranged in 4 horizontal strips, top to bottom: 40% of the height for the front face, 40% for the back face, 10% for the outer edges and 10% for the inner edges.
	</description>
	<tutorials>
		<link title="3D text">$DOCS_URL/tutorials/3d/3d_text.html</link>
	</tutorials>
	<members>
		<member name="autowrap_mode" type="int" setter="set_autowrap_mode" getter="get_autowrap_mode" enum="TextServer.AutowrapMode" default="0">
			If set to something other than [constant TextServer.AUTOWRAP_OFF], the text gets wrapped inside the node's bounding rectangle. If you resize the node, it will change its height automatically to show all the text. To see how each mode behaves, see [enum TextServer.AutowrapMode].
		</member>
		<member name="curve_step" type="float" setter="set_curve_step" getter="get_curve_step" default="0.5">
			Step (in pixels) used to approximate Bézier curves.
		</member>
		<member name="depth" type="float" setter="set_depth" getter="get_depth" default="0.05">
			Depths of the mesh, if set to [code]0.0[/code] only front surface, is generated, and UV layout is changed to use full texture for the front face only.
		</member>
		<member name="font" type="Font" setter="set_font" getter="get_font">
			Font configuration used to display text.
		</member>
		<member name="font_size" type="int" setter="set_font_size" getter="get_font_size" default="16">
			Font size of the [TextMesh]'s text.
		</member>
		<member name="horizontal_alignment" type="int" setter="set_horizontal_alignment" getter="get_horizontal_alignment" enum="HorizontalAlignment" default="1">
			Controls the text's horizontal alignment. Supports left, center, right, and fill, or justify. Set it to one of the [enum HorizontalAlignment] constants.
		</member>
		<member name="justification_flags" type="int" setter="set_justification_flags" getter="get_justification_flags" enum="TextServer.JustificationFlag" is_bitfield="true" default="163">
			Line fill alignment rules. See [enum TextServer.JustificationFlag] for more information.
		</member>
		<member name="language" type="String" setter="set_language" getter="get_language" default="&quot;&quot;">
			Language code used for text shaping algorithms, if left empty current locale is used instead.
		</member>
		<member name="line_spacing" type="float" setter="set_line_spacing" getter="get_line_spacing" default="0.0">
			Additional vertical spacing between lines (in pixels), spacing is added to line descent. This value can be negative.
		</member>
		<member name="offset" type="Vector2" setter="set_offset" getter="get_offset" default="Vector2(0, 0)">
			The text drawing offset (in pixels).
		</member>
		<member name="pixel_size" type="float" setter="set_pixel_size" getter="get_pixel_size" default="0.01">
			The size of one pixel's width on the text to scale it in 3D.
		</member>
		<member name="structured_text_bidi_override" type="int" setter="set_structured_text_bidi_override" getter="get_structured_text_bidi_override" enum="TextServer.StructuredTextParser" default="0">
			Set BiDi algorithm override for the structured text.
		</member>
		<member name="structured_text_bidi_override_options" type="Array" setter="set_structured_text_bidi_override_options" getter="get_structured_text_bidi_override_options" default="[]">
			Set additional options for BiDi override.
		</member>
		<member name="text" type="String" setter="set_text" getter="get_text" default="&quot;&quot;">
			The text to generate mesh from.
			[b]Note:[/b] Due to being a [Resource], it doesn't follow the rules of [member Node.auto_translate_mode]. If disabling translation is desired, it should be done manually with [method Object.set_message_translation].
		</member>
		<member name="text_direction" type="int" setter="set_text_direction" getter="get_text_direction" enum="TextServer.Direction" default="0">
			Base text writing direction.
		</member>
		<member name="uppercase" type="bool" setter="set_uppercase" getter="is_uppercase" default="false">
			If [code]true[/code], all the text displays as UPPERCASE.
		</member>
		<member name="vertical_alignment" type="int" setter="set_vertical_alignment" getter="get_vertical_alignment" enum="VerticalAlignment" default="1">
			Controls the text's vertical alignment. Supports top, center, bottom. Set it to one of the [enum VerticalAlignment] constants.
		</member>
		<member name="width" type="float" setter="set_width" getter="get_width" default="500.0">
			Text width (in pixels), used for fill alignment.
		</member>
	</members>
</class>
