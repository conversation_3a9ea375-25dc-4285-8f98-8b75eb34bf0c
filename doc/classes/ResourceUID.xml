<?xml version="1.0" encoding="UTF-8" ?>
<class name="ResourceUID" inherits="Object" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A singleton that manages the unique identifiers of all resources within a project.
	</brief_description>
	<description>
		Resource UIDs (Unique IDentifiers) allow the engine to keep references between resources intact, even if files are renamed or moved. They can be accessed with [code]uid://[/code].
		[ResourceUID] keeps track of all registered resource UIDs in a project, generates new UIDs, and converts between their string and integer representations.
	</description>
	<tutorials>
	</tutorials>
	<methods>
		<method name="add_id">
			<return type="void" />
			<param index="0" name="id" type="int" />
			<param index="1" name="path" type="String" />
			<description>
				Adds a new UID value which is mapped to the given resource path.
				Fails with an error if the UID already exists, so be sure to check [method has_id] beforehand, or use [method set_id] instead.
			</description>
		</method>
		<method name="create_id">
			<return type="int" />
			<description>
				Generates a random resource UID which is guaranteed to be unique within the list of currently loaded UIDs.
				In order for this UID to be registered, you must call [method add_id] or [method set_id].
			</description>
		</method>
		<method name="create_id_for_path">
			<return type="int" />
			<param index="0" name="path" type="String" />
			<description>
				Like [method create_id], but the UID is seeded with the provided [param path] and project name. UIDs generated for that path will be always the same within the current project.
			</description>
		</method>
		<method name="get_id_path" qualifiers="const">
			<return type="String" />
			<param index="0" name="id" type="int" />
			<description>
				Returns the path that the given UID value refers to.
				Fails with an error if the UID does not exist, so be sure to check [method has_id] beforehand.
			</description>
		</method>
		<method name="has_id" qualifiers="const">
			<return type="bool" />
			<param index="0" name="id" type="int" />
			<description>
				Returns whether the given UID value is known to the cache.
			</description>
		</method>
		<method name="id_to_text" qualifiers="const">
			<return type="String" />
			<param index="0" name="id" type="int" />
			<description>
				Converts the given UID to a [code]uid://[/code] string value.
			</description>
		</method>
		<method name="remove_id">
			<return type="void" />
			<param index="0" name="id" type="int" />
			<description>
				Removes a loaded UID value from the cache.
				Fails with an error if the UID does not exist, so be sure to check [method has_id] beforehand.
			</description>
		</method>
		<method name="set_id">
			<return type="void" />
			<param index="0" name="id" type="int" />
			<param index="1" name="path" type="String" />
			<description>
				Updates the resource path of an existing UID.
				Fails with an error if the UID does not exist, so be sure to check [method has_id] beforehand, or use [method add_id] instead.
			</description>
		</method>
		<method name="text_to_id" qualifiers="const">
			<return type="int" />
			<param index="0" name="text_id" type="String" />
			<description>
				Extracts the UID value from the given [code]uid://[/code] string.
			</description>
		</method>
	</methods>
	<constants>
		<constant name="INVALID_ID" value="-1">
			The value to use for an invalid UID, for example if the resource could not be loaded.
			Its text representation is [code]uid://&lt;invalid&gt;[/code].
		</constant>
	</constants>
</class>
