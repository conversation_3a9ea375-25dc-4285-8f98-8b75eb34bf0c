<?xml version="1.0" encoding="UTF-8" ?>
<class name="VisualShaderNodeIs" inherits="VisualShaderNode" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A boolean comparison operator to be used within the visual shader graph.
	</brief_description>
	<description>
		Returns the boolean result of the comparison between [code]INF[/code] or [code]NaN[/code] and a scalar parameter.
	</description>
	<tutorials>
	</tutorials>
	<members>
		<member name="function" type="int" setter="set_function" getter="get_function" enum="VisualShaderNodeIs.Function" default="0">
			The comparison function. See [enum Function] for options.
		</member>
	</members>
	<constants>
		<constant name="FUNC_IS_INF" value="0" enum="Function">
			Comparison with [code]INF[/code] (Infinity).
		</constant>
		<constant name="FUNC_IS_NAN" value="1" enum="Function">
			Comparison with [code]NaN[/code] (Not a Number; indicates invalid numeric results, such as division by zero).
		</constant>
		<constant name="FUNC_MAX" value="2" enum="Function">
			Represents the size of the [enum Function] enum.
		</constant>
	</constants>
</class>
