<?xml version="1.0" encoding="UTF-8" ?>
<class name="RichTextLabel" inherits="Control" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A control for displaying text that can contain different font styles, images, and basic formatting.
	</brief_description>
	<description>
		A control for displaying text that can contain custom fonts, images, and basic formatting. [RichTextLabel] manages these as an internal tag stack. It also adapts itself to given width/heights.
		[b]Note:[/b] [method newline], [method push_paragraph], [code]"\n"[/code], [code]"\r\n"[/code], [code]p[/code] tag, and alignment tags start a new paragraph. Each paragraph is processed independently, in its own BiDi context. If you want to force line wrapping within paragraph, any other line breaking character can be used, for example, Form Feed (U+000C), Next Line (U+0085), Line Separator (U+2028).
		[b]Note:[/b] Assignments to [member text] clear the tag stack and reconstruct it from the property's contents. Any edits made to [member text] will erase previous edits made from other manual sources such as [method append_text] and the [code]push_*[/code] / [method pop] methods.
		[b]Note:[/b] RichTextLabel doesn't support entangled BBCode tags. For example, instead of using [code skip-lint][b]bold[i]bold italic[/b]italic[/i][/code], use [code skip-lint][b]bold[i]bold italic[/i][/b][i]italic[/i][/code].
		[b]Note:[/b] [code]push_*/pop_*[/code] functions won't affect BBCode.
		[b]Note:[/b] Unlike [Label], [RichTextLabel] doesn't have a [i]property[/i] to horizontally align text to the center. Instead, enable [member bbcode_enabled] and surround the text in a [code skip-lint][center][/code] tag as follows: [code skip-lint][center]Example[/center][/code]. There is currently no built-in way to vertically align text either, but this can be emulated by relying on anchors/containers and the [member fit_content] property.
	</description>
	<tutorials>
		<link title="BBCode in RichTextLabel">$DOCS_URL/tutorials/ui/bbcode_in_richtextlabel.html</link>
		<link title="Rich Text Label with BBCode Demo">https://godotengine.org/asset-library/asset/2774</link>
		<link title="Operating System Testing Demo">https://godotengine.org/asset-library/asset/2789</link>
	</tutorials>
	<methods>
		<method name="add_image">
			<return type="void" />
			<param index="0" name="image" type="Texture2D" />
			<param index="1" name="width" type="int" default="0" />
			<param index="2" name="height" type="int" default="0" />
			<param index="3" name="color" type="Color" default="Color(1, 1, 1, 1)" />
			<param index="4" name="inline_align" type="int" enum="InlineAlignment" default="5" />
			<param index="5" name="region" type="Rect2" default="Rect2(0, 0, 0, 0)" />
			<param index="6" name="key" type="Variant" default="null" />
			<param index="7" name="pad" type="bool" default="false" />
			<param index="8" name="tooltip" type="String" default="&quot;&quot;" />
			<param index="9" name="size_in_percent" type="bool" default="false" />
			<param index="10" name="alt_text" type="String" default="&quot;&quot;" />
			<description>
				Adds an image's opening and closing tags to the tag stack, optionally providing a [param width] and [param height] to resize the image, a [param color] to tint the image and a [param region] to only use parts of the image.
				If [param width] or [param height] is set to 0, the image size will be adjusted in order to keep the original aspect ratio.
				If [param width] and [param height] are not set, but [param region] is, the region's rect will be used.
				[param key] is an optional identifier, that can be used to modify the image via [method update_image].
				If [param pad] is set, and the image is smaller than the size specified by [param width] and [param height], the image padding is added to match the size instead of upscaling.
				If [param size_in_percent] is set, [param width] and [param height] values are percentages of the control width instead of pixels.
				[param alt_text] is used as the image description for assistive apps.
			</description>
		</method>
		<method name="add_text">
			<return type="void" />
			<param index="0" name="text" type="String" />
			<description>
				Adds raw non-BBCode-parsed text to the tag stack.
			</description>
		</method>
		<method name="append_text">
			<return type="void" />
			<param index="0" name="bbcode" type="String" />
			<description>
				Parses [param bbcode] and adds tags to the tag stack as needed.
				[b]Note:[/b] Using this method, you can't close a tag that was opened in a previous [method append_text] call. This is done to improve performance, especially when updating large RichTextLabels since rebuilding the whole BBCode every time would be slower. If you absolutely need to close a tag in a future method call, append the [member text] instead of using [method append_text].
			</description>
		</method>
		<method name="clear">
			<return type="void" />
			<description>
				Clears the tag stack, causing the label to display nothing.
				[b]Note:[/b] This method does not affect [member text], and its contents will show again if the label is redrawn. However, setting [member text] to an empty [String] also clears the stack.
			</description>
		</method>
		<method name="deselect">
			<return type="void" />
			<description>
				Clears the current selection.
			</description>
		</method>
		<method name="get_character_line">
			<return type="int" />
			<param index="0" name="character" type="int" />
			<description>
				Returns the line number of the character position provided. Line and character numbers are both zero-indexed.
				[b]Note:[/b] If [member threaded] is enabled, this method returns a value for the loaded part of the document. Use [method is_finished] or [signal finished] to determine whether document is fully loaded.
			</description>
		</method>
		<method name="get_character_paragraph">
			<return type="int" />
			<param index="0" name="character" type="int" />
			<description>
				Returns the paragraph number of the character position provided. Paragraph and character numbers are both zero-indexed.
				[b]Note:[/b] If [member threaded] is enabled, this method returns a value for the loaded part of the document. Use [method is_finished] or [signal finished] to determine whether document is fully loaded.
			</description>
		</method>
		<method name="get_content_height" qualifiers="const">
			<return type="int" />
			<description>
				Returns the height of the content.
				[b]Note:[/b] If [member threaded] is enabled, this method returns a value for the loaded part of the document. Use [method is_finished] or [signal finished] to determine whether document is fully loaded.
			</description>
		</method>
		<method name="get_content_width" qualifiers="const">
			<return type="int" />
			<description>
				Returns the width of the content.
				[b]Note:[/b] If [member threaded] is enabled, this method returns a value for the loaded part of the document. Use [method is_finished] or [signal finished] to determine whether document is fully loaded.
			</description>
		</method>
		<method name="get_line_count" qualifiers="const">
			<return type="int" />
			<description>
				Returns the total number of lines in the text. Wrapped text is counted as multiple lines.
				[b]Note:[/b] If [member visible_characters_behavior] is set to [constant TextServer.VC_CHARS_BEFORE_SHAPING] only visible wrapped lines are counted.
				[b]Note:[/b] If [member threaded] is enabled, this method returns a value for the loaded part of the document. Use [method is_finished] or [signal finished] to determine whether document is fully loaded.
			</description>
		</method>
		<method name="get_line_height" qualifiers="const">
			<return type="int" />
			<param index="0" name="line" type="int" />
			<description>
				Returns the height of the line found at the provided index.
				[b]Note:[/b] If [member threaded] is enabled, this method returns a value for the loaded part of the document. Use [method is_finished] or [signal finished] to determine whether the document is fully loaded.
			</description>
		</method>
		<method name="get_line_offset">
			<return type="float" />
			<param index="0" name="line" type="int" />
			<description>
				Returns the vertical offset of the line found at the provided index.
				[b]Note:[/b] If [member threaded] is enabled, this method returns a value for the loaded part of the document. Use [method is_finished] or [signal finished] to determine whether document is fully loaded.
			</description>
		</method>
		<method name="get_line_range">
			<return type="Vector2i" />
			<param index="0" name="line" type="int" />
			<description>
				Returns the indexes of the first and last visible characters for the given [param line], as a [Vector2i].
				[b]Note:[/b] If [member visible_characters_behavior] is set to [constant TextServer.VC_CHARS_BEFORE_SHAPING] only visible wrapped lines are counted.
				[b]Note:[/b] If [member threaded] is enabled, this method returns a value for the loaded part of the document. Use [method is_finished] or [signal finished] to determine whether document is fully loaded.
			</description>
		</method>
		<method name="get_line_width" qualifiers="const">
			<return type="int" />
			<param index="0" name="line" type="int" />
			<description>
				Returns the width of the line found at the provided index.
				[b]Note:[/b] If [member threaded] is enabled, this method returns a value for the loaded part of the document. Use [method is_finished] or [signal finished] to determine whether the document is fully loaded.
			</description>
		</method>
		<method name="get_menu" qualifiers="const">
			<return type="PopupMenu" />
			<description>
				Returns the [PopupMenu] of this [RichTextLabel]. By default, this menu is displayed when right-clicking on the [RichTextLabel].
				You can add custom menu items or remove standard ones. Make sure your IDs don't conflict with the standard ones (see [enum MenuItems]). For example:
				[codeblocks]
				[gdscript]
				func _ready():
				    var menu = get_menu()
				    # Remove "Select All" item.
				    menu.remove_item(MENU_SELECT_ALL)
				    # Add custom items.
				    menu.add_separator()
				    menu.add_item("Duplicate Text", MENU_MAX + 1)
				    # Connect callback.
				    menu.id_pressed.connect(_on_item_pressed)

				func _on_item_pressed(id):
				    if id == MENU_MAX + 1:
				        add_text("\n" + get_parsed_text())
				[/gdscript]
				[csharp]
				public override void _Ready()
				{
				    var menu = GetMenu();
				    // Remove "Select All" item.
				    menu.RemoveItem(RichTextLabel.MenuItems.SelectAll);
				    // Add custom items.
				    menu.AddSeparator();
				    menu.AddItem("Duplicate Text", RichTextLabel.MenuItems.Max + 1);
				    // Add event handler.
				    menu.IdPressed += OnItemPressed;
				}

				public void OnItemPressed(int id)
				{
				    if (id == TextEdit.MenuItems.Max + 1)
				    {
				        AddText("\n" + GetParsedText());
				    }
				}
				[/csharp]
				[/codeblocks]
				[b]Warning:[/b] This is a required internal node, removing and freeing it may cause a crash. If you wish to hide it or any of its children, use their [member Window.visible] property.
			</description>
		</method>
		<method name="get_paragraph_count" qualifiers="const">
			<return type="int" />
			<description>
				Returns the total number of paragraphs (newlines or [code]p[/code] tags in the tag stack's text tags). Considers wrapped text as one paragraph.
			</description>
		</method>
		<method name="get_paragraph_offset">
			<return type="float" />
			<param index="0" name="paragraph" type="int" />
			<description>
				Returns the vertical offset of the paragraph found at the provided index.
				[b]Note:[/b] If [member threaded] is enabled, this method returns a value for the loaded part of the document. Use [method is_finished] or [signal finished] to determine whether document is fully loaded.
			</description>
		</method>
		<method name="get_parsed_text" qualifiers="const">
			<return type="String" />
			<description>
				Returns the text without BBCode mark-up.
			</description>
		</method>
		<method name="get_selected_text" qualifiers="const">
			<return type="String" />
			<description>
				Returns the current selection text. Does not include BBCodes.
			</description>
		</method>
		<method name="get_selection_from" qualifiers="const">
			<return type="int" />
			<description>
				Returns the current selection first character index if a selection is active, [code]-1[/code] otherwise. Does not include BBCodes.
			</description>
		</method>
		<method name="get_selection_line_offset" qualifiers="const">
			<return type="float" />
			<description>
				Returns the current selection vertical line offset if a selection is active, [code]-1.0[/code] otherwise.
			</description>
		</method>
		<method name="get_selection_to" qualifiers="const">
			<return type="int" />
			<description>
				Returns the current selection last character index if a selection is active, [code]-1[/code] otherwise. Does not include BBCodes.
			</description>
		</method>
		<method name="get_total_character_count" qualifiers="const">
			<return type="int" />
			<description>
				Returns the total number of characters from text tags. Does not include BBCodes.
			</description>
		</method>
		<method name="get_v_scroll_bar">
			<return type="VScrollBar" />
			<description>
				Returns the vertical scrollbar.
				[b]Warning:[/b] This is a required internal node, removing and freeing it may cause a crash. If you wish to hide it or any of its children, use their [member CanvasItem.visible] property.
			</description>
		</method>
		<method name="get_visible_line_count" qualifiers="const">
			<return type="int" />
			<description>
				Returns the number of visible lines.
				[b]Note:[/b] If [member threaded] is enabled, this method returns a value for the loaded part of the document. Use [method is_finished] or [signal finished] to determine whether document is fully loaded.
			</description>
		</method>
		<method name="get_visible_paragraph_count" qualifiers="const">
			<return type="int" />
			<description>
				Returns the number of visible paragraphs. A paragraph is considered visible if at least one of its lines is visible.
				[b]Note:[/b] If [member threaded] is enabled, this method returns a value for the loaded part of the document. Use [method is_finished] or [signal finished] to determine whether document is fully loaded.
			</description>
		</method>
		<method name="install_effect">
			<return type="void" />
			<param index="0" name="effect" type="Variant" />
			<description>
				Installs a custom effect. This can also be done in the Inspector through the [member custom_effects] property. [param effect] should be a valid [RichTextEffect].
				[b]Example:[/b] With the following script extending from [RichTextEffect]:
				[codeblock]
				# effect.gd
				class_name MyCustomEffect
				extends RichTextEffect

				var bbcode = "my_custom_effect"

				# ...
				[/codeblock]
				The above effect can be installed in [RichTextLabel] from a script:
				[codeblock]
				# rich_text_label.gd
				extends RichTextLabel

				func _ready():
				    install_effect(MyCustomEffect.new())

				    # Alternatively, if not using `class_name` in the script that extends RichTextEffect:
				    install_effect(preload("res://effect.gd").new())
				[/codeblock]
			</description>
		</method>
		<method name="invalidate_paragraph">
			<return type="bool" />
			<param index="0" name="paragraph" type="int" />
			<description>
				Invalidates [param paragraph] and all subsequent paragraphs cache.
			</description>
		</method>
		<method name="is_finished" qualifiers="const">
			<return type="bool" />
			<description>
				If [member threaded] is enabled, returns [code]true[/code] if the background thread has finished text processing, otherwise always return [code]true[/code].
			</description>
		</method>
		<method name="is_menu_visible" qualifiers="const">
			<return type="bool" />
			<description>
				Returns whether the menu is visible. Use this instead of [code]get_menu().visible[/code] to improve performance (so the creation of the menu is avoided).
			</description>
		</method>
		<method name="is_ready" qualifiers="const" deprecated="Use [method is_finished] instead.">
			<return type="bool" />
			<description>
				If [member threaded] is enabled, returns [code]true[/code] if the background thread has finished text processing, otherwise always return [code]true[/code].
			</description>
		</method>
		<method name="menu_option">
			<return type="void" />
			<param index="0" name="option" type="int" />
			<description>
				Executes a given action as defined in the [enum MenuItems] enum.
			</description>
		</method>
		<method name="newline">
			<return type="void" />
			<description>
				Adds a newline tag to the tag stack.
			</description>
		</method>
		<method name="parse_bbcode">
			<return type="void" />
			<param index="0" name="bbcode" type="String" />
			<description>
				The assignment version of [method append_text]. Clears the tag stack and inserts the new content.
			</description>
		</method>
		<method name="parse_expressions_for_values">
			<return type="Dictionary" />
			<param index="0" name="expressions" type="PackedStringArray" />
			<description>
				Parses BBCode parameter [param expressions] into a dictionary.
			</description>
		</method>
		<method name="pop">
			<return type="void" />
			<description>
				Terminates the current tag. Use after [code]push_*[/code] methods to close BBCodes manually. Does not need to follow [code]add_*[/code] methods.
			</description>
		</method>
		<method name="pop_all">
			<return type="void" />
			<description>
				Terminates all tags opened by [code]push_*[/code] methods.
			</description>
		</method>
		<method name="pop_context">
			<return type="void" />
			<description>
				Terminates tags opened after the last [method push_context] call (including context marker), or all tags if there's no context marker on the stack.
			</description>
		</method>
		<method name="push_bgcolor">
			<return type="void" />
			<param index="0" name="bgcolor" type="Color" />
			<description>
				Adds a [code skip-lint][bgcolor][/code] tag to the tag stack.
				[b]Note:[/b] The background color has padding applied by default, which is controlled using [theme_item text_highlight_h_padding] and [theme_item text_highlight_v_padding]. This can lead to overlapping highlights if background colors are placed on neighboring lines/columns, so consider setting those theme items to [code]0[/code] if you want to avoid this.
			</description>
		</method>
		<method name="push_bold">
			<return type="void" />
			<description>
				Adds a [code skip-lint][font][/code] tag with a bold font to the tag stack. This is the same as adding a [code skip-lint][b][/code] tag if not currently in a [code skip-lint][i][/code] tag.
			</description>
		</method>
		<method name="push_bold_italics">
			<return type="void" />
			<description>
				Adds a [code skip-lint][font][/code] tag with a bold italics font to the tag stack.
			</description>
		</method>
		<method name="push_cell">
			<return type="void" />
			<description>
				Adds a [code skip-lint][cell][/code] tag to the tag stack. Must be inside a [code skip-lint][table][/code] tag. See [method push_table] for details. Use [method set_table_column_expand] to set column expansion ratio, [method set_cell_border_color] to set cell border, [method set_cell_row_background_color] to set cell background, [method set_cell_size_override] to override cell size, and [method set_cell_padding] to set padding.
			</description>
		</method>
		<method name="push_color">
			<return type="void" />
			<param index="0" name="color" type="Color" />
			<description>
				Adds a [code skip-lint][color][/code] tag to the tag stack.
			</description>
		</method>
		<method name="push_context">
			<return type="void" />
			<description>
				Adds a context marker to the tag stack. See [method pop_context].
			</description>
		</method>
		<method name="push_customfx">
			<return type="void" />
			<param index="0" name="effect" type="RichTextEffect" />
			<param index="1" name="env" type="Dictionary" />
			<description>
				Adds a custom effect tag to the tag stack. The effect does not need to be in [member custom_effects]. The environment is directly passed to the effect.
			</description>
		</method>
		<method name="push_dropcap">
			<return type="void" />
			<param index="0" name="string" type="String" />
			<param index="1" name="font" type="Font" />
			<param index="2" name="size" type="int" />
			<param index="3" name="dropcap_margins" type="Rect2" default="Rect2(0, 0, 0, 0)" />
			<param index="4" name="color" type="Color" default="Color(1, 1, 1, 1)" />
			<param index="5" name="outline_size" type="int" default="0" />
			<param index="6" name="outline_color" type="Color" default="Color(0, 0, 0, 0)" />
			<description>
				Adds a [code skip-lint][dropcap][/code] tag to the tag stack. Drop cap (dropped capital) is a decorative element at the beginning of a paragraph that is larger than the rest of the text.
			</description>
		</method>
		<method name="push_fgcolor">
			<return type="void" />
			<param index="0" name="fgcolor" type="Color" />
			<description>
				Adds a [code skip-lint][fgcolor][/code] tag to the tag stack.
				[b]Note:[/b] The foreground color has padding applied by default, which is controlled using [theme_item text_highlight_h_padding] and [theme_item text_highlight_v_padding]. This can lead to overlapping highlights if foreground colors are placed on neighboring lines/columns, so consider setting those theme items to [code]0[/code] if you want to avoid this.
			</description>
		</method>
		<method name="push_font">
			<return type="void" />
			<param index="0" name="font" type="Font" />
			<param index="1" name="font_size" type="int" default="0" />
			<description>
				Adds a [code skip-lint][font][/code] tag to the tag stack. Overrides default fonts for its duration.
				Passing [code]0[/code] to [param font_size] will use the existing default font size.
			</description>
		</method>
		<method name="push_font_size">
			<return type="void" />
			<param index="0" name="font_size" type="int" />
			<description>
				Adds a [code skip-lint][font_size][/code] tag to the tag stack. Overrides default font size for its duration.
			</description>
		</method>
		<method name="push_hint">
			<return type="void" />
			<param index="0" name="description" type="String" />
			<description>
				Adds a [code skip-lint][hint][/code] tag to the tag stack. Same as BBCode [code skip-lint][hint=something]{text}[/hint][/code].
			</description>
		</method>
		<method name="push_indent">
			<return type="void" />
			<param index="0" name="level" type="int" />
			<description>
				Adds an [code skip-lint][indent][/code] tag to the tag stack. Multiplies [param level] by current [member tab_size] to determine new margin length.
			</description>
		</method>
		<method name="push_italics">
			<return type="void" />
			<description>
				Adds a [code skip-lint][font][/code] tag with an italics font to the tag stack. This is the same as adding an [code skip-lint][i][/code] tag if not currently in a [code skip-lint][b][/code] tag.
			</description>
		</method>
		<method name="push_language">
			<return type="void" />
			<param index="0" name="language" type="String" />
			<description>
				Adds language code used for text shaping algorithm and Open-Type font features.
			</description>
		</method>
		<method name="push_list">
			<return type="void" />
			<param index="0" name="level" type="int" />
			<param index="1" name="type" type="int" enum="RichTextLabel.ListType" />
			<param index="2" name="capitalize" type="bool" />
			<param index="3" name="bullet" type="String" default="&quot;•&quot;" />
			<description>
				Adds [code skip-lint][ol][/code] or [code skip-lint][ul][/code] tag to the tag stack. Multiplies [param level] by current [member tab_size] to determine new margin length.
			</description>
		</method>
		<method name="push_meta" keywords="push_url">
			<return type="void" />
			<param index="0" name="data" type="Variant" />
			<param index="1" name="underline_mode" type="int" enum="RichTextLabel.MetaUnderline" default="1" />
			<param index="2" name="tooltip" type="String" default="&quot;&quot;" />
			<description>
				Adds a meta tag to the tag stack. Similar to the BBCode [code skip-lint][url=something]{text}[/url][/code], but supports non-[String] metadata types.
				If [member meta_underlined] is [code]true[/code], meta tags display an underline. This behavior can be customized with [param underline_mode].
				[b]Note:[/b] Meta tags do nothing by default when clicked. To assign behavior when clicked, connect [signal meta_clicked] to a function that is called when the meta tag is clicked.
			</description>
		</method>
		<method name="push_mono">
			<return type="void" />
			<description>
				Adds a [code skip-lint][font][/code] tag with a monospace font to the tag stack.
			</description>
		</method>
		<method name="push_normal">
			<return type="void" />
			<description>
				Adds a [code skip-lint][font][/code] tag with a normal font to the tag stack.
			</description>
		</method>
		<method name="push_outline_color">
			<return type="void" />
			<param index="0" name="color" type="Color" />
			<description>
				Adds a [code skip-lint][outline_color][/code] tag to the tag stack. Adds text outline for its duration.
			</description>
		</method>
		<method name="push_outline_size">
			<return type="void" />
			<param index="0" name="outline_size" type="int" />
			<description>
				Adds a [code skip-lint][outline_size][/code] tag to the tag stack. Overrides default text outline size for its duration.
			</description>
		</method>
		<method name="push_paragraph">
			<return type="void" />
			<param index="0" name="alignment" type="int" enum="HorizontalAlignment" />
			<param index="1" name="base_direction" type="int" enum="Control.TextDirection" default="0" />
			<param index="2" name="language" type="String" default="&quot;&quot;" />
			<param index="3" name="st_parser" type="int" enum="TextServer.StructuredTextParser" default="0" />
			<param index="4" name="justification_flags" type="int" enum="TextServer.JustificationFlag" is_bitfield="true" default="163" />
			<param index="5" name="tab_stops" type="PackedFloat32Array" default="PackedFloat32Array()" />
			<description>
				Adds a [code skip-lint][p][/code] tag to the tag stack.
			</description>
		</method>
		<method name="push_strikethrough">
			<return type="void" />
			<description>
				Adds a [code skip-lint][s][/code] tag to the tag stack.
			</description>
		</method>
		<method name="push_table">
			<return type="void" />
			<param index="0" name="columns" type="int" />
			<param index="1" name="inline_align" type="int" enum="InlineAlignment" default="0" />
			<param index="2" name="align_to_row" type="int" default="-1" />
			<param index="3" name="name" type="String" default="&quot;&quot;" />
			<description>
				Adds a [code skip-lint][table=columns,inline_align][/code] tag to the tag stack. Use [method set_table_column_expand] to set column expansion ratio. Use [method push_cell] to add cells. [param name] is used as the table name for assistive apps.
			</description>
		</method>
		<method name="push_underline">
			<return type="void" />
			<description>
				Adds a [code skip-lint][u][/code] tag to the tag stack.
			</description>
		</method>
		<method name="reload_effects">
			<return type="void" />
			<description>
				Reloads custom effects. Useful when [member custom_effects] is modified manually.
			</description>
		</method>
		<method name="remove_paragraph">
			<return type="bool" />
			<param index="0" name="paragraph" type="int" />
			<param index="1" name="no_invalidate" type="bool" default="false" />
			<description>
				Removes a paragraph of content from the label. Returns [code]true[/code] if the paragraph exists.
				The [param paragraph] argument is the index of the paragraph to remove, it can take values in the interval [code][0, get_paragraph_count() - 1][/code].
				If [param no_invalidate] is set to [code]true[/code], cache for the subsequent paragraphs is not invalidated. Use it for faster updates if deleted paragraph is fully self-contained (have no unclosed tags), or this call is part of the complex edit operation and [method invalidate_paragraph] will be called at the end of operation.
			</description>
		</method>
		<method name="scroll_to_line">
			<return type="void" />
			<param index="0" name="line" type="int" />
			<description>
				Scrolls the window's top line to match [param line].
			</description>
		</method>
		<method name="scroll_to_paragraph">
			<return type="void" />
			<param index="0" name="paragraph" type="int" />
			<description>
				Scrolls the window's top line to match first line of the [param paragraph].
			</description>
		</method>
		<method name="scroll_to_selection">
			<return type="void" />
			<description>
				Scrolls to the beginning of the current selection.
			</description>
		</method>
		<method name="select_all">
			<return type="void" />
			<description>
				Select all the text.
				If [member selection_enabled] is [code]false[/code], no selection will occur.
			</description>
		</method>
		<method name="set_cell_border_color">
			<return type="void" />
			<param index="0" name="color" type="Color" />
			<description>
				Sets color of a table cell border.
			</description>
		</method>
		<method name="set_cell_padding">
			<return type="void" />
			<param index="0" name="padding" type="Rect2" />
			<description>
				Sets inner padding of a table cell.
			</description>
		</method>
		<method name="set_cell_row_background_color">
			<return type="void" />
			<param index="0" name="odd_row_bg" type="Color" />
			<param index="1" name="even_row_bg" type="Color" />
			<description>
				Sets color of a table cell. Separate colors for alternating rows can be specified.
			</description>
		</method>
		<method name="set_cell_size_override">
			<return type="void" />
			<param index="0" name="min_size" type="Vector2" />
			<param index="1" name="max_size" type="Vector2" />
			<description>
				Sets minimum and maximum size overrides for a table cell.
			</description>
		</method>
		<method name="set_table_column_expand">
			<return type="void" />
			<param index="0" name="column" type="int" />
			<param index="1" name="expand" type="bool" />
			<param index="2" name="ratio" type="int" default="1" />
			<param index="3" name="shrink" type="bool" default="true" />
			<description>
				Edits the selected column's expansion options. If [param expand] is [code]true[/code], the column expands in proportion to its expansion ratio versus the other columns' ratios.
				For example, 2 columns with ratios 3 and 4 plus 70 pixels in available width would expand 30 and 40 pixels, respectively.
				If [param expand] is [code]false[/code], the column will not contribute to the total ratio.
			</description>
		</method>
		<method name="set_table_column_name">
			<return type="void" />
			<param index="0" name="column" type="int" />
			<param index="1" name="name" type="String" />
			<description>
				Sets table column name for assistive apps.
			</description>
		</method>
		<method name="update_image">
			<return type="void" />
			<param index="0" name="key" type="Variant" />
			<param index="1" name="mask" type="int" enum="RichTextLabel.ImageUpdateMask" is_bitfield="true" />
			<param index="2" name="image" type="Texture2D" />
			<param index="3" name="width" type="int" default="0" />
			<param index="4" name="height" type="int" default="0" />
			<param index="5" name="color" type="Color" default="Color(1, 1, 1, 1)" />
			<param index="6" name="inline_align" type="int" enum="InlineAlignment" default="5" />
			<param index="7" name="region" type="Rect2" default="Rect2(0, 0, 0, 0)" />
			<param index="8" name="pad" type="bool" default="false" />
			<param index="9" name="tooltip" type="String" default="&quot;&quot;" />
			<param index="10" name="size_in_percent" type="bool" default="false" />
			<description>
				Updates the existing images with the key [param key]. Only properties specified by [param mask] bits are updated. See [method add_image].
			</description>
		</method>
	</methods>
	<members>
		<member name="autowrap_mode" type="int" setter="set_autowrap_mode" getter="get_autowrap_mode" enum="TextServer.AutowrapMode" default="3">
			If set to something other than [constant TextServer.AUTOWRAP_OFF], the text gets wrapped inside the node's bounding rectangle. To see how each mode behaves, see [enum TextServer.AutowrapMode].
		</member>
		<member name="autowrap_trim_flags" type="int" setter="set_autowrap_trim_flags" getter="get_autowrap_trim_flags" enum="TextServer.LineBreakFlag" is_bitfield="true" default="192">
			Autowrap space trimming flags. See [constant TextServer.BREAK_TRIM_START_EDGE_SPACES] and [constant TextServer.BREAK_TRIM_END_EDGE_SPACES] for more info.
		</member>
		<member name="bbcode_enabled" type="bool" setter="set_use_bbcode" getter="is_using_bbcode" default="false">
			If [code]true[/code], the label uses BBCode formatting.
			[b]Note:[/b] This only affects the contents of [member text], not the tag stack.
		</member>
		<member name="clip_contents" type="bool" setter="set_clip_contents" getter="is_clipping_contents" overrides="Control" default="true" />
		<member name="context_menu_enabled" type="bool" setter="set_context_menu_enabled" getter="is_context_menu_enabled" default="false">
			If [code]true[/code], a right-click displays the context menu.
		</member>
		<member name="custom_effects" type="Array" setter="set_effects" getter="get_effects" default="[]">
			The currently installed custom effects. This is an array of [RichTextEffect]s.
			To add a custom effect, it's more convenient to use [method install_effect].
		</member>
		<member name="deselect_on_focus_loss_enabled" type="bool" setter="set_deselect_on_focus_loss_enabled" getter="is_deselect_on_focus_loss_enabled" default="true">
			If [code]true[/code], the selected text will be deselected when focus is lost.
		</member>
		<member name="drag_and_drop_selection_enabled" type="bool" setter="set_drag_and_drop_selection_enabled" getter="is_drag_and_drop_selection_enabled" default="true">
			If [code]true[/code], allow drag and drop of selected text.
		</member>
		<member name="fit_content" type="bool" setter="set_fit_content" getter="is_fit_content_enabled" default="false">
			If [code]true[/code], the label's minimum size will be automatically updated to fit its content, matching the behavior of [Label].
		</member>
		<member name="focus_mode" type="int" setter="set_focus_mode" getter="get_focus_mode" overrides="Control" enum="Control.FocusMode" default="3" />
		<member name="hint_underlined" type="bool" setter="set_hint_underline" getter="is_hint_underlined" default="true">
			If [code]true[/code], the label underlines hint tags such as [code skip-lint][hint=description]{text}[/hint][/code].
		</member>
		<member name="horizontal_alignment" type="int" setter="set_horizontal_alignment" getter="get_horizontal_alignment" enum="HorizontalAlignment" default="0">
			Controls the text's horizontal alignment. Supports left, center, right, and fill, or justify. Set it to one of the [enum HorizontalAlignment] constants.
		</member>
		<member name="justification_flags" type="int" setter="set_justification_flags" getter="get_justification_flags" enum="TextServer.JustificationFlag" is_bitfield="true" default="163">
			Line fill alignment rules. See [enum TextServer.JustificationFlag] for more information.
		</member>
		<member name="language" type="String" setter="set_language" getter="get_language" default="&quot;&quot;">
			Language code used for line-breaking and text shaping algorithms, if left empty current locale is used instead.
		</member>
		<member name="meta_underlined" type="bool" setter="set_meta_underline" getter="is_meta_underlined" default="true" keywords="url_underlined">
			If [code]true[/code], the label underlines meta tags such as [code skip-lint][url]{text}[/url][/code]. These tags can call a function when clicked if [signal meta_clicked] is connected to a function.
		</member>
		<member name="progress_bar_delay" type="int" setter="set_progress_bar_delay" getter="get_progress_bar_delay" default="1000">
			The delay after which the loading progress bar is displayed, in milliseconds. Set to [code]-1[/code] to disable progress bar entirely.
			[b]Note:[/b] Progress bar is displayed only if [member threaded] is enabled.
		</member>
		<member name="scroll_active" type="bool" setter="set_scroll_active" getter="is_scroll_active" default="true">
			If [code]true[/code], the scrollbar is visible. Setting this to [code]false[/code] does not block scrolling completely. See [method scroll_to_line].
		</member>
		<member name="scroll_following" type="bool" setter="set_scroll_follow" getter="is_scroll_following" default="false">
			If [code]true[/code], the window scrolls down to display new content automatically.
		</member>
		<member name="selection_enabled" type="bool" setter="set_selection_enabled" getter="is_selection_enabled" default="false">
			If [code]true[/code], the label allows text selection.
		</member>
		<member name="shortcut_keys_enabled" type="bool" setter="set_shortcut_keys_enabled" getter="is_shortcut_keys_enabled" default="true">
			If [code]true[/code], shortcut keys for context menu items are enabled, even if the context menu is disabled.
		</member>
		<member name="structured_text_bidi_override" type="int" setter="set_structured_text_bidi_override" getter="get_structured_text_bidi_override" enum="TextServer.StructuredTextParser" default="0">
			Set BiDi algorithm override for the structured text.
		</member>
		<member name="structured_text_bidi_override_options" type="Array" setter="set_structured_text_bidi_override_options" getter="get_structured_text_bidi_override_options" default="[]">
			Set additional options for BiDi override.
		</member>
		<member name="tab_size" type="int" setter="set_tab_size" getter="get_tab_size" default="4">
			The number of spaces associated with a single tab length. Does not affect [code]\t[/code] in text tags, only indent tags.
		</member>
		<member name="tab_stops" type="PackedFloat32Array" setter="set_tab_stops" getter="get_tab_stops" default="PackedFloat32Array()">
			Aligns text to the given tab-stops.
		</member>
		<member name="text" type="String" setter="set_text" getter="get_text" default="&quot;&quot;">
			The label's text in BBCode format. Is not representative of manual modifications to the internal tag stack. Erases changes made by other methods when edited.
			[b]Note:[/b] If [member bbcode_enabled] is [code]true[/code], it is unadvised to use the [code]+=[/code] operator with [member text] (e.g. [code]text += "some string"[/code]) as it replaces the whole text and can cause slowdowns. It will also erase all BBCode that was added to stack using [code]push_*[/code] methods. Use [method append_text] for adding text instead, unless you absolutely need to close a tag that was opened in an earlier method call.
		</member>
		<member name="text_direction" type="int" setter="set_text_direction" getter="get_text_direction" enum="Control.TextDirection" default="0">
			Base text writing direction.
		</member>
		<member name="threaded" type="bool" setter="set_threaded" getter="is_threaded" default="false">
			If [code]true[/code], text processing is done in a background thread.
		</member>
		<member name="vertical_alignment" type="int" setter="set_vertical_alignment" getter="get_vertical_alignment" enum="VerticalAlignment" default="0">
			Controls the text's vertical alignment. Supports top, center, bottom, and fill. Set it to one of the [enum VerticalAlignment] constants.
		</member>
		<member name="visible_characters" type="int" setter="set_visible_characters" getter="get_visible_characters" default="-1">
			The number of characters to display. If set to [code]-1[/code], all characters are displayed. This can be useful when animating the text appearing in a dialog box.
			[b]Note:[/b] Setting this property updates [member visible_ratio] accordingly.
		</member>
		<member name="visible_characters_behavior" type="int" setter="set_visible_characters_behavior" getter="get_visible_characters_behavior" enum="TextServer.VisibleCharactersBehavior" default="0">
			Sets the clipping behavior when [member visible_characters] or [member visible_ratio] is set. See [enum TextServer.VisibleCharactersBehavior] for more info.
		</member>
		<member name="visible_ratio" type="float" setter="set_visible_ratio" getter="get_visible_ratio" default="1.0">
			The fraction of characters to display, relative to the total number of characters (see [method get_total_character_count]). If set to [code]1.0[/code], all characters are displayed. If set to [code]0.5[/code], only half of the characters will be displayed. This can be useful when animating the text appearing in a dialog box.
			[b]Note:[/b] Setting this property updates [member visible_characters] accordingly.
		</member>
	</members>
	<signals>
		<signal name="finished">
			<description>
				Triggered when the document is fully loaded.
				[b]Note:[/b] This can happen before the text is processed for drawing. Scrolling values may not be valid until the document is drawn for the first time after this signal.
			</description>
		</signal>
		<signal name="meta_clicked">
			<param index="0" name="meta" type="Variant" />
			<description>
				Triggered when the user clicks on content between meta (URL) tags. If the meta is defined in BBCode, e.g. [code skip-lint][url={"key": "value"}]Text[/url][/code], then the parameter for this signal will always be a [String] type. If a particular type or an object is desired, the [method push_meta] method must be used to manually insert the data into the tag stack. Alternatively, you can convert the [String] input to the desired type based on its contents (such as calling [method JSON.parse] on it).
				For example, the following method can be connected to [signal meta_clicked] to open clicked URLs using the user's default web browser:
				[codeblocks]
				[gdscript]
				# This assumes RichTextLabel's `meta_clicked` signal was connected to
				# the function below using the signal connection dialog.
				func _richtextlabel_on_meta_clicked(meta):
				    # `meta` is of Variant type, so convert it to a String to avoid script errors at run-time.
				    OS.shell_open(str(meta))
				[/gdscript]
				[/codeblocks]
			</description>
		</signal>
		<signal name="meta_hover_ended">
			<param index="0" name="meta" type="Variant" />
			<description>
				Triggers when the mouse exits a meta tag.
			</description>
		</signal>
		<signal name="meta_hover_started">
			<param index="0" name="meta" type="Variant" />
			<description>
				Triggers when the mouse enters a meta tag.
			</description>
		</signal>
	</signals>
	<constants>
		<constant name="LIST_NUMBERS" value="0" enum="ListType">
			Each list item has a number marker.
		</constant>
		<constant name="LIST_LETTERS" value="1" enum="ListType">
			Each list item has a letter marker.
		</constant>
		<constant name="LIST_ROMAN" value="2" enum="ListType">
			Each list item has a roman number marker.
		</constant>
		<constant name="LIST_DOTS" value="3" enum="ListType">
			Each list item has a filled circle marker.
		</constant>
		<constant name="MENU_COPY" value="0" enum="MenuItems">
			Copies the selected text.
		</constant>
		<constant name="MENU_SELECT_ALL" value="1" enum="MenuItems">
			Selects the whole [RichTextLabel] text.
		</constant>
		<constant name="MENU_MAX" value="2" enum="MenuItems">
			Represents the size of the [enum MenuItems] enum.
		</constant>
		<constant name="META_UNDERLINE_NEVER" value="0" enum="MetaUnderline">
			Meta tag does not display an underline, even if [member meta_underlined] is [code]true[/code].
		</constant>
		<constant name="META_UNDERLINE_ALWAYS" value="1" enum="MetaUnderline">
			If [member meta_underlined] is [code]true[/code], meta tag always display an underline.
		</constant>
		<constant name="META_UNDERLINE_ON_HOVER" value="2" enum="MetaUnderline">
			If [member meta_underlined] is [code]true[/code], meta tag display an underline when the mouse cursor is over it.
		</constant>
		<constant name="UPDATE_TEXTURE" value="1" enum="ImageUpdateMask" is_bitfield="true">
			If this bit is set, [method update_image] changes image texture.
		</constant>
		<constant name="UPDATE_SIZE" value="2" enum="ImageUpdateMask" is_bitfield="true">
			If this bit is set, [method update_image] changes image size.
		</constant>
		<constant name="UPDATE_COLOR" value="4" enum="ImageUpdateMask" is_bitfield="true">
			If this bit is set, [method update_image] changes image color.
		</constant>
		<constant name="UPDATE_ALIGNMENT" value="8" enum="ImageUpdateMask" is_bitfield="true">
			If this bit is set, [method update_image] changes image inline alignment.
		</constant>
		<constant name="UPDATE_REGION" value="16" enum="ImageUpdateMask" is_bitfield="true">
			If this bit is set, [method update_image] changes image texture region.
		</constant>
		<constant name="UPDATE_PAD" value="32" enum="ImageUpdateMask" is_bitfield="true">
			If this bit is set, [method update_image] changes image padding.
		</constant>
		<constant name="UPDATE_TOOLTIP" value="64" enum="ImageUpdateMask" is_bitfield="true">
			If this bit is set, [method update_image] changes image tooltip.
		</constant>
		<constant name="UPDATE_WIDTH_IN_PERCENT" value="128" enum="ImageUpdateMask" is_bitfield="true">
			If this bit is set, [method update_image] changes image width from/to percents.
		</constant>
	</constants>
	<theme_items>
		<theme_item name="default_color" data_type="color" type="Color" default="Color(1, 1, 1, 1)">
			The default text color.
		</theme_item>
		<theme_item name="font_outline_color" data_type="color" type="Color" default="Color(0, 0, 0, 1)">
			The default tint of text outline.
		</theme_item>
		<theme_item name="font_selected_color" data_type="color" type="Color" default="Color(0, 0, 0, 0)">
			The color of selected text, used when [member selection_enabled] is [code]true[/code]. If equal to [code]Color(0, 0, 0, 0)[/code], it will be ignored.
		</theme_item>
		<theme_item name="font_shadow_color" data_type="color" type="Color" default="Color(0, 0, 0, 0)">
			The color of the font's shadow.
		</theme_item>
		<theme_item name="selection_color" data_type="color" type="Color" default="Color(0.1, 0.1, 1, 0.8)">
			The color of the selection box.
		</theme_item>
		<theme_item name="table_border" data_type="color" type="Color" default="Color(0, 0, 0, 0)">
			The default cell border color.
		</theme_item>
		<theme_item name="table_even_row_bg" data_type="color" type="Color" default="Color(0, 0, 0, 0)">
			The default background color for even rows.
		</theme_item>
		<theme_item name="table_odd_row_bg" data_type="color" type="Color" default="Color(0, 0, 0, 0)">
			The default background color for odd rows.
		</theme_item>
		<theme_item name="line_separation" data_type="constant" type="int" default="0">
			Additional vertical spacing between lines (in pixels), spacing is added to line descent. This value can be negative.
		</theme_item>
		<theme_item name="outline_size" data_type="constant" type="int" default="0">
			The size of the text outline.
			[b]Note:[/b] If using a font with [member FontFile.multichannel_signed_distance_field] enabled, its [member FontFile.msdf_pixel_range] must be set to at least [i]twice[/i] the value of [theme_item outline_size] for outline rendering to look correct. Otherwise, the outline may appear to be cut off earlier than intended.
		</theme_item>
		<theme_item name="shadow_offset_x" data_type="constant" type="int" default="1">
			The horizontal offset of the font's shadow.
		</theme_item>
		<theme_item name="shadow_offset_y" data_type="constant" type="int" default="1">
			The vertical offset of the font's shadow.
		</theme_item>
		<theme_item name="shadow_outline_size" data_type="constant" type="int" default="1">
			The size of the shadow outline.
		</theme_item>
		<theme_item name="table_h_separation" data_type="constant" type="int" default="3">
			The horizontal separation of elements in a table.
		</theme_item>
		<theme_item name="table_v_separation" data_type="constant" type="int" default="3">
			The vertical separation of elements in a table.
		</theme_item>
		<theme_item name="text_highlight_h_padding" data_type="constant" type="int" default="3">
			The horizontal padding around boxes drawn by the [code][fgcolor][/code] and [code][bgcolor][/code] tags. This does not affect the appearance of text selection. To avoid any risk of neighboring highlights overlapping each other, set this to [code]0[/code] to disable padding.
		</theme_item>
		<theme_item name="text_highlight_v_padding" data_type="constant" type="int" default="3">
			The vertical padding around boxes drawn by the [code][fgcolor][/code] and [code][bgcolor][/code] tags. This does not affect the appearance of text selection. To avoid any risk of neighboring highlights overlapping each other, set this to [code]0[/code] to disable padding.
		</theme_item>
		<theme_item name="bold_font" data_type="font" type="Font">
			The font used for bold text.
		</theme_item>
		<theme_item name="bold_italics_font" data_type="font" type="Font">
			The font used for bold italics text.
		</theme_item>
		<theme_item name="italics_font" data_type="font" type="Font">
			The font used for italics text.
		</theme_item>
		<theme_item name="mono_font" data_type="font" type="Font">
			The font used for monospace text.
		</theme_item>
		<theme_item name="normal_font" data_type="font" type="Font">
			The default text font.
		</theme_item>
		<theme_item name="bold_font_size" data_type="font_size" type="int">
			The font size used for bold text.
		</theme_item>
		<theme_item name="bold_italics_font_size" data_type="font_size" type="int">
			The font size used for bold italics text.
		</theme_item>
		<theme_item name="italics_font_size" data_type="font_size" type="int">
			The font size used for italics text.
		</theme_item>
		<theme_item name="mono_font_size" data_type="font_size" type="int">
			The font size used for monospace text.
		</theme_item>
		<theme_item name="normal_font_size" data_type="font_size" type="int">
			The default text font size.
		</theme_item>
		<theme_item name="focus" data_type="style" type="StyleBox">
			The background used when the [RichTextLabel] is focused. The [theme_item focus] [StyleBox] is displayed [i]over[/i] the base [StyleBox], so a partially transparent [StyleBox] should be used to ensure the base [StyleBox] remains visible. A [StyleBox] that represents an outline or an underline works well for this purpose. To disable the focus visual effect, assign a [StyleBoxEmpty] resource. Note that disabling the focus visual effect will harm keyboard/controller navigation usability, so this is not recommended for accessibility reasons.
		</theme_item>
		<theme_item name="normal" data_type="style" type="StyleBox">
			The normal background for the [RichTextLabel].
		</theme_item>
	</theme_items>
</class>
