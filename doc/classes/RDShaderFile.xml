<?xml version="1.0" encoding="UTF-8" ?>
<class name="RDShaderFile" inherits="Resource" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Compiled shader file in SPIR-V form (used by [RenderingDevice]). Not to be confused with Redot's own [Shader].
	</brief_description>
	<description>
		Compiled shader file in SPIR-V form.
		See also [RDShaderSource]. [RDShaderFile] is only meant to be used with the [RenderingDevice] API. It should not be confused with Redot's own [Shader] resource, which is what Redot's various nodes use for high-level shader programming.
	</description>
	<tutorials>
	</tutorials>
	<methods>
		<method name="get_spirv" qualifiers="const">
			<return type="RDShaderSPIRV" />
			<param index="0" name="version" type="StringName" default="&amp;&quot;&quot;" />
			<description>
				Returns the SPIR-V intermediate representation for the specified shader [param version].
			</description>
		</method>
		<method name="get_version_list" qualifiers="const">
			<return type="StringName[]" />
			<description>
				Returns the list of compiled versions for this shader.
			</description>
		</method>
		<method name="set_bytecode">
			<return type="void" />
			<param index="0" name="bytecode" type="RDShaderSPIRV" />
			<param index="1" name="version" type="StringName" default="&amp;&quot;&quot;" />
			<description>
				Sets the SPIR-V [param bytecode] that will be compiled for the specified [param version].
			</description>
		</method>
	</methods>
	<members>
		<member name="base_error" type="String" setter="set_base_error" getter="get_base_error" default="&quot;&quot;">
			The base compilation error message, which indicates errors not related to a specific shader stage if non-empty. If empty, shader compilation is not necessarily successful (check [RDShaderSPIRV]'s error message members).
		</member>
	</members>
</class>
