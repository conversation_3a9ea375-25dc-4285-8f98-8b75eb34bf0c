<?xml version="1.0" encoding="UTF-8" ?>
<class name="VisualShaderNodeColorOp" inherits="VisualShaderNode" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A [Color] operator to be used within the visual shader graph.
	</brief_description>
	<description>
		Applies [member operator] to two color inputs.
	</description>
	<tutorials>
	</tutorials>
	<members>
		<member name="operator" type="int" setter="set_operator" getter="get_operator" enum="VisualShaderNodeColorOp.Operator" default="0">
			An operator to be applied to the inputs. See [enum Operator] for options.
		</member>
	</members>
	<constants>
		<constant name="OP_SCREEN" value="0" enum="Operator">
			Produce a screen effect with the following formula:
			[codeblock]
			result = vec3(1.0) - (vec3(1.0) - a) * (vec3(1.0) - b);
			[/codeblock]
		</constant>
		<constant name="OP_DIFFERENCE" value="1" enum="Operator">
			Produce a difference effect with the following formula:
			[codeblock]
			result = abs(a - b);
			[/codeblock]
		</constant>
		<constant name="OP_DARKEN" value="2" enum="Operator">
			Produce a darken effect with the following formula:
			[codeblock]
			result = min(a, b);
			[/codeblock]
		</constant>
		<constant name="OP_LIGHTEN" value="3" enum="Operator">
			Produce a lighten effect with the following formula:
			[codeblock]
			result = max(a, b);
			[/codeblock]
		</constant>
		<constant name="OP_OVERLAY" value="4" enum="Operator">
			Produce an overlay effect with the following formula:
			[codeblock]
			for (int i = 0; i &lt; 3; i++) {
			    float base = a[i];
			    float blend = b[i];
			    if (base &lt; 0.5) {
			        result[i] = 2.0 * base * blend;
			    } else {
			        result[i] = 1.0 - 2.0 * (1.0 - blend) * (1.0 - base);
			    }
			}
			[/codeblock]
		</constant>
		<constant name="OP_DODGE" value="5" enum="Operator">
			Produce a dodge effect with the following formula:
			[codeblock]
			result = a / (vec3(1.0) - b);
			[/codeblock]
		</constant>
		<constant name="OP_BURN" value="6" enum="Operator">
			Produce a burn effect with the following formula:
			[codeblock]
			result = vec3(1.0) - (vec3(1.0) - a) / b;
			[/codeblock]
		</constant>
		<constant name="OP_SOFT_LIGHT" value="7" enum="Operator">
			Produce a soft light effect with the following formula:
			[codeblock]
			for (int i = 0; i &lt; 3; i++) {
			    float base = a[i];
			    float blend = b[i];
			    if (base &lt; 0.5) {
			        result[i] = base * (blend + 0.5);
			    } else {
			        result[i] = 1.0 - (1.0 - base) * (1.0 - (blend - 0.5));
			    }
			}
			[/codeblock]
		</constant>
		<constant name="OP_HARD_LIGHT" value="8" enum="Operator">
			Produce a hard light effect with the following formula:
			[codeblock]
			for (int i = 0; i &lt; 3; i++) {
			    float base = a[i];
			    float blend = b[i];
			    if (base &lt; 0.5) {
			        result[i] = base * (2.0 * blend);
			    } else {
			        result[i] = 1.0 - (1.0 - base) * (1.0 - 2.0 * (blend - 0.5));
			    }
			}
			[/codeblock]
		</constant>
		<constant name="OP_MAX" value="9" enum="Operator">
			Represents the size of the [enum Operator] enum.
		</constant>
	</constants>
</class>
