<?xml version="1.0" encoding="UTF-8" ?>
<class name="RayCast3D" inherits="Node3D" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A ray in 3D space, used to find the first object it intersects.
	</brief_description>
	<description>
		A raycast represents a ray from its origin to its [member target_position] that finds the closest object along its path, if it intersects any.
		[RayCast3D] can ignore some objects by adding them to an exception list, by making its detection reporting ignore [Area3D]s ([member collide_with_areas]) or [PhysicsBody3D]s ([member collide_with_bodies]), or by configuring physics layers.
		[RayCast3D] calculates intersection every physics frame, and it holds the result until the next physics frame. For an immediate raycast, or if you want to configure a [RayCast3D] multiple times within the same physics frame, use [method force_raycast_update].
		To sweep over a region of 3D space, you can approximate the region with multiple [RayCast3D]s or use [ShapeCast3D].
	</description>
	<tutorials>
		<link title="Ray-casting">$DOCS_URL/tutorials/physics/ray-casting.html</link>
		<link title="3D Voxel Demo">https://godotengine.org/asset-library/asset/2755</link>
	</tutorials>
	<methods>
		<method name="add_exception">
			<return type="void" />
			<param index="0" name="node" type="CollisionObject3D" />
			<description>
				Adds a collision exception so the ray does not report collisions with the specified [CollisionObject3D] node.
			</description>
		</method>
		<method name="add_exception_rid">
			<return type="void" />
			<param index="0" name="rid" type="RID" />
			<description>
				Adds a collision exception so the ray does not report collisions with the specified [RID].
			</description>
		</method>
		<method name="clear_exceptions">
			<return type="void" />
			<description>
				Removes all collision exceptions for this ray.
			</description>
		</method>
		<method name="force_raycast_update">
			<return type="void" />
			<description>
				Updates the collision information for the ray immediately, without waiting for the next [code]_physics_process[/code] call. Use this method, for example, when the ray or its parent has changed state.
				[b]Note:[/b] [member enabled] does not need to be [code]true[/code] for this to work.
			</description>
		</method>
		<method name="get_collider" qualifiers="const">
			<return type="Object" />
			<description>
				Returns the first object that the ray intersects, or [code]null[/code] if no object is intersecting the ray (i.e. [method is_colliding] returns [code]false[/code]).
				[b]Note:[/b] This object is not guaranteed to be a [CollisionObject3D]. For example, if the ray intersects a [CSGShape3D] or a [GridMap], the method will return a [CSGShape3D] or [GridMap] instance.
			</description>
		</method>
		<method name="get_collider_rid" qualifiers="const">
			<return type="RID" />
			<description>
				Returns the [RID] of the first object that the ray intersects, or an empty [RID] if no object is intersecting the ray (i.e. [method is_colliding] returns [code]false[/code]).
			</description>
		</method>
		<method name="get_collider_shape" qualifiers="const">
			<return type="int" />
			<description>
				Returns the shape ID of the first object that the ray intersects, or [code]0[/code] if no object is intersecting the ray (i.e. [method is_colliding] returns [code]false[/code]).
				To get the intersected shape node, for a [CollisionObject3D] target, use:
				[codeblocks]
				[gdscript]
				var target = get_collider() # A CollisionObject3D.
				var shape_id = get_collider_shape() # The shape index in the collider.
				var owner_id = target.shape_find_owner(shape_id) # The owner ID in the collider.
				var shape = target.shape_owner_get_owner(owner_id)
				[/gdscript]
				[csharp]
				var target = (CollisionObject3D)GetCollider(); // A CollisionObject3D.
				var shapeId = GetColliderShape(); // The shape index in the collider.
				var ownerId = target.ShapeFindOwner(shapeId); // The owner ID in the collider.
				var shape = target.ShapeOwnerGetOwner(ownerId);
				[/csharp]
				[/codeblocks]
			</description>
		</method>
		<method name="get_collision_face_index" qualifiers="const">
			<return type="int" />
			<description>
				Returns the collision object's face index at the collision point, or [code]-1[/code] if the shape intersecting the ray is not a [ConcavePolygonShape3D].
			</description>
		</method>
		<method name="get_collision_mask_value" qualifiers="const">
			<return type="bool" />
			<param index="0" name="layer_number" type="int" />
			<description>
				Returns whether or not the specified layer of the [member collision_mask] is enabled, given a [param layer_number] between 1 and 32.
			</description>
		</method>
		<method name="get_collision_normal" qualifiers="const">
			<return type="Vector3" />
			<description>
				Returns the normal of the intersecting object's shape at the collision point, or [code]Vector3(0, 0, 0)[/code] if the ray starts inside the shape and [member hit_from_inside] is [code]true[/code].
				[b]Note:[/b] Check that [method is_colliding] returns [code]true[/code] before calling this method to ensure the returned normal is valid and up-to-date.
			</description>
		</method>
		<method name="get_collision_point" qualifiers="const">
			<return type="Vector3" />
			<description>
				Returns the collision point at which the ray intersects the closest object, in the global coordinate system. If [member hit_from_inside] is [code]true[/code] and the ray starts inside of a collision shape, this function will return the origin point of the ray.
				[b]Note:[/b] Check that [method is_colliding] returns [code]true[/code] before calling this method to ensure the returned point is valid and up-to-date.
			</description>
		</method>
		<method name="is_colliding" qualifiers="const">
			<return type="bool" />
			<description>
				Returns whether any object is intersecting with the ray's vector (considering the vector length).
			</description>
		</method>
		<method name="remove_exception">
			<return type="void" />
			<param index="0" name="node" type="CollisionObject3D" />
			<description>
				Removes a collision exception so the ray does report collisions with the specified [CollisionObject3D] node.
			</description>
		</method>
		<method name="remove_exception_rid">
			<return type="void" />
			<param index="0" name="rid" type="RID" />
			<description>
				Removes a collision exception so the ray does report collisions with the specified [RID].
			</description>
		</method>
		<method name="set_collision_mask_value">
			<return type="void" />
			<param index="0" name="layer_number" type="int" />
			<param index="1" name="value" type="bool" />
			<description>
				Based on [param value], enables or disables the specified layer in the [member collision_mask], given a [param layer_number] between 1 and 32.
			</description>
		</method>
	</methods>
	<members>
		<member name="collide_with_areas" type="bool" setter="set_collide_with_areas" getter="is_collide_with_areas_enabled" default="false">
			If [code]true[/code], collisions with [Area3D]s will be reported.
		</member>
		<member name="collide_with_bodies" type="bool" setter="set_collide_with_bodies" getter="is_collide_with_bodies_enabled" default="true">
			If [code]true[/code], collisions with [PhysicsBody3D]s will be reported.
		</member>
		<member name="collision_mask" type="int" setter="set_collision_mask" getter="get_collision_mask" default="1">
			The ray's collision mask. Only objects in at least one collision layer enabled in the mask will be detected. See [url=$DOCS_URL/tutorials/physics/physics_introduction.html#collision-layers-and-masks]Collision layers and masks[/url] in the documentation for more information.
		</member>
		<member name="debug_shape_custom_color" type="Color" setter="set_debug_shape_custom_color" getter="get_debug_shape_custom_color" default="Color(0, 0, 0, 1)">
			The custom color to use to draw the shape in the editor and at run-time if [b]Visible Collision Shapes[/b] is enabled in the [b]Debug[/b] menu. This color will be highlighted at run-time if the [RayCast3D] is colliding with something.
			If set to [code]Color(0.0, 0.0, 0.0)[/code] (by default), the color set in [member ProjectSettings.debug/shapes/collision/shape_color] is used.
		</member>
		<member name="debug_shape_thickness" type="int" setter="set_debug_shape_thickness" getter="get_debug_shape_thickness" default="2">
			If set to [code]1[/code], a line is used as the debug shape. Otherwise, a truncated pyramid is drawn to represent the [RayCast3D]. Requires [b]Visible Collision Shapes[/b] to be enabled in the [b]Debug[/b] menu for the debug shape to be visible at run-time.
		</member>
		<member name="enabled" type="bool" setter="set_enabled" getter="is_enabled" default="true">
			If [code]true[/code], collisions will be reported.
		</member>
		<member name="exclude_parent" type="bool" setter="set_exclude_parent_body" getter="get_exclude_parent_body" default="true">
			If [code]true[/code], collisions will be ignored for this RayCast3D's immediate parent.
		</member>
		<member name="hit_back_faces" type="bool" setter="set_hit_back_faces" getter="is_hit_back_faces_enabled" default="true">
			If [code]true[/code], the ray will hit back faces with concave polygon shapes with back face enabled or heightmap shapes.
		</member>
		<member name="hit_from_inside" type="bool" setter="set_hit_from_inside" getter="is_hit_from_inside_enabled" default="false">
			If [code]true[/code], the ray will detect a hit when starting inside shapes. In this case the collision normal will be [code]Vector3(0, 0, 0)[/code]. Does not affect shapes with no volume like concave polygon or heightmap.
		</member>
		<member name="target_position" type="Vector3" setter="set_target_position" getter="get_target_position" default="Vector3(0, -1, 0)">
			The ray's destination point, relative to the RayCast's [code]position[/code].
		</member>
	</members>
</class>
