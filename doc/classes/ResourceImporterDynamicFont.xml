<?xml version="1.0" encoding="UTF-8" ?>
<class name="ResourceImporterDynamicFont" inherits="ResourceImporter" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Imports a TTF, TTC, OTF, OTC, WOFF or WOFF2 font file for font rendering that adapts to any size.
	</brief_description>
	<description>
		Unlike bitmap fonts, dynamic fonts can be resized to any size and still look crisp. Dynamic fonts also optionally support MSDF font rendering, which allows for run-time scale changes with no re-rasterization cost.
		While WOFF and especially WOFF2 tend to result in smaller file sizes, there is no universally "better" font format. In most situations, it's recommended to use the font format that was shipped on the font developer's website.
		See also [ResourceImporterBMFont] and [ResourceImporterImageFont].
	</description>
	<tutorials>
		<link title="Dynamic fonts - Using fonts">$DOCS_URL/tutorials/ui/gui_using_fonts.html#dynamic-fonts</link>
	</tutorials>
	<members>
		<member name="allow_system_fallback" type="bool" setter="" getter="" default="true">
			If [code]true[/code], automatically use system fonts as a fallback if a glyph isn't found in this dynamic font. This makes supporting CJK characters or emoji more straightforward, as you don't need to include a CJK/emoji font in your project. See also [member fallbacks].
			[b]Note:[/b] The appearance of system fonts varies across platforms. Loading system fonts is only supported on Windows, macOS, Linux, Android and iOS.
		</member>
		<member name="antialiasing" type="int" setter="" getter="" default="1">
			The font antialiasing method to use.
			[b]Disabled:[/b] Most suited for pixel art fonts, although you do not [i]have[/i] to change the antialiasing from the default [b]Grayscale[/b] if the font file was well-created and the font is used at an integer multiple of its intended size. If pixel art fonts have a bad appearance at their intended size, try setting [member subpixel_positioning] to [b]Disabled[/b] instead.
			[b]Grayscale:[/b] Use grayscale antialiasing. This is the approach used by the operating system on macOS, Android and iOS.
			[b]LCD Subpixel:[/b] Use antialiasing with subpixel patterns to make fonts sharper on LCD displays. This is the approach used by the operating system on Windows and most Linux distributions. The downside is that this can introduce "fringing" on edges, especially on display technologies that don't use standard RGB subpixels (such as OLED displays). The LCD subpixel layout is globally controlled by [member ProjectSettings.gui/theme/lcd_subpixel_layout], which also allows falling back to grayscale antialiasing.
		</member>
		<member name="compress" type="bool" setter="" getter="" default="true">
			If [code]true[/code], uses lossless compression for the resulting font.
		</member>
		<member name="disable_embedded_bitmaps" type="bool" setter="" getter="" default="true">
			If set to [code]true[/code], embedded font bitmap loading is disabled (bitmap-only and color fonts ignore this property).
		</member>
		<member name="fallbacks" type="Array" setter="" getter="" default="[]">
			List of font fallbacks to use if a glyph isn't found in this dynamic font. Fonts at the beginning of the array are attempted first, but fallback fonts that don't support the glyph's language and script are attempted last (see [member language_support] and [member script_support]). See also [member allow_system_fallback].
		</member>
		<member name="force_autohinter" type="bool" setter="" getter="" default="false">
			If [code]true[/code], forces generation of hinting data for the font using [url=https://freetype.org/]FreeType[/url]'s autohinter. This will make [member hinting] effective with fonts that don't include hinting data.
		</member>
		<member name="generate_mipmaps" type="bool" setter="" getter="" default="false">
			If [code]true[/code], this font will have mipmaps generated. This prevents text from looking grainy when a [Control] is scaled down, or when a [Label3D] is viewed from a long distance (if [member Label3D.texture_filter] is set to a mode that displays mipmaps).
			Enabling [member generate_mipmaps] increases font generation time and memory usage. Only enable this setting if you actually need it.
		</member>
		<member name="hinting" type="int" setter="" getter="" default="1">
			The hinting mode to use. This controls how aggressively glyph edges should be snapped to pixels when rasterizing the font. Depending on personal preference, you may prefer using one hinting mode over the other. Hinting modes other than [b]None[/b] are only effective if the font contains hinting data (see [member force_autohinter]).
			[b]None:[/b] Smoothest appearance, which can make the font look blurry at small sizes.
			[b]Light:[/b] Sharp result by snapping glyph edges to pixels on the Y axis only.
			[b]Full:[/b] Sharpest by snapping glyph edges to pixels on both X and Y axes.
		</member>
		<member name="keep_rounding_remainders" type="bool" setter="" getter="" default="true">
			If set to [code]true[/code], when aligning glyphs to the pixel boundaries rounding remainders are accumulated to ensure more uniform glyph distribution. This setting has no effect if subpixel positioning is enabled.
		</member>
		<member name="language_support" type="Dictionary" setter="" getter="" default="{}">
			Override the list of languages supported by this font. If left empty, this is supplied by the font metadata. There is usually no need to change this. See also [member script_support].
		</member>
		<member name="modulate_color_glyphs" type="bool" setter="" getter="" default="false">
			If set to [code]true[/code], color modulation is applied when drawing colored glyphs, otherwise it's applied to the monochrome glyphs only.
		</member>
		<member name="msdf_pixel_range" type="int" setter="" getter="" default="8">
			The width of the range around the shape between the minimum and maximum representable signed distance. If using font outlines, [member msdf_pixel_range] must be set to at least [i]twice[/i] the size of the largest font outline. The default [member msdf_pixel_range] value of [code]8[/code] allows outline sizes up to [code]4[/code] to look correct.
		</member>
		<member name="msdf_size" type="int" setter="" getter="" default="48">
			Source font size used to generate MSDF textures. Higher values allow for more precision, but are slower to render and require more memory. Only increase this value if you notice a visible lack of precision in glyph rendering. Only effective if [member multichannel_signed_distance_field] is [code]true[/code].
		</member>
		<member name="multichannel_signed_distance_field" type="bool" setter="" getter="" default="false">
			If set to [code]true[/code], the font will use multichannel signed distance field (MSDF) for crisp rendering at any size. Since this approach does not rely on rasterizing the font every time its size changes, this allows for resizing the font in real-time without any performance penalty. Text will also not look grainy for [Control]s that are scaled down (or for [Label3D]s viewed from a long distance).
			MSDF font rendering can be combined with [member generate_mipmaps] to further improve font rendering quality when scaled down.
		</member>
		<member name="opentype_features" type="Dictionary" setter="" getter="" default="{}">
			The OpenType features to enable, disable or set a value for this font. This can be used to enable optional features provided by the font, such as ligatures or alternative glyphs. The list of supported OpenType features varies on a per-font basis.
		</member>
		<member name="preload" type="Array" setter="" getter="" default="[]">
			The glyph ranges to prerender. This can avoid stuttering during gameplay when new characters need to be rendered, especially if [member subpixel_positioning] is enabled. The downside of using preloading is that initial project load times will increase, as well as memory usage.
		</member>
		<member name="script_support" type="Dictionary" setter="" getter="" default="{}">
			Override the list of language scripts supported by this font. If left empty, this is supplied by the font metadata. There is usually no need to change this. See also [member language_support].
		</member>
		<member name="subpixel_positioning" type="int" setter="" getter="" default="4">
			Subpixel positioning improves font rendering appearance, especially at smaller font sizes. The downside is that it takes more time to initially render the font, which can cause stuttering during gameplay, especially if used with large font sizes. This should be set to [b]Disabled[/b] for fonts with a pixel art appearance.
			[b]Disabled:[/b] No subpixel positioning. Lowest quality, fastest rendering.
			[b]Auto:[/b] Use subpixel positioning at small font sizes (the chosen quality varies depending on font size). Large fonts will not use subpixel positioning. This is a good tradeoff between performance and quality.
			[b]One Half of a Pixel:[/b] Always perform intermediate subpixel positioning regardless of font size. High quality, slow rendering.
			[b]One Quarter of a Pixel:[/b] Always perform precise subpixel positioning regardless of font size. Highest quality, slowest rendering.
			[b]Auto (Except Pixel Fonts):[/b] [b]Disabled[/b] for the pixel style fonts (each glyph contours contain only straight horizontal and vertical lines), [b]Auto[/b] for the other fonts.
		</member>
	</members>
</class>
