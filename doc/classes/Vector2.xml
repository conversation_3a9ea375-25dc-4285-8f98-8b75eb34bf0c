<?xml version="1.0" encoding="UTF-8" ?>
<class name="Vector2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A 2D vector using floating-point coordinates.
	</brief_description>
	<description>
		A 2-element structure that can be used to represent 2D coordinates or any other pair of numeric values.
		It uses floating-point coordinates. By default, these floating-point values use 32-bit precision, unlike [float] which is always 64-bit. If double precision is needed, compile the engine with the option [code]precision=double[/code].
		See [Vector2i] for its integer counterpart.
		[b]Note:[/b] In a boolean context, a Vector2 will evaluate to [code]false[/code] if it's equal to [code]Vector2(0, 0)[/code]. Otherwise, a Vector2 will always evaluate to [code]true[/code].
	</description>
	<tutorials>
		<link title="Math documentation index">$DOCS_URL/tutorials/math/index.html</link>
		<link title="Vector math">$DOCS_URL/tutorials/math/vector_math.html</link>
		<link title="Advanced vector math">$DOCS_URL/tutorials/math/vectors_advanced.html</link>
		<link title="3Blue1Brown Essence of Linear Algebra">https://www.youtube.com/playlist?list=PLZHQObOWTQDPD3MizzM2xVFitgF8hE_ab</link>
		<link title="Matrix Transform Demo">https://godotengine.org/asset-library/asset/2787</link>
		<link title="All 2D Demos">https://github.com/redot-engine/redot-demo-projects/tree/master/2d</link>
	</tutorials>
	<constructors>
		<constructor name="Vector2">
			<return type="Vector2" />
			<description>
				Constructs a default-initialized [Vector2] with all components set to [code]0[/code].
			</description>
		</constructor>
		<constructor name="Vector2">
			<return type="Vector2" />
			<param index="0" name="from" type="Vector2" />
			<description>
				Constructs a [Vector2] as a copy of the given [Vector2].
			</description>
		</constructor>
		<constructor name="Vector2">
			<return type="Vector2" />
			<param index="0" name="from" type="Vector2i" />
			<description>
				Constructs a new [Vector2] from [Vector2i].
			</description>
		</constructor>
		<constructor name="Vector2">
			<return type="Vector2" />
			<param index="0" name="x" type="float" />
			<param index="1" name="y" type="float" />
			<description>
				Constructs a new [Vector2] from the given [param x] and [param y].
			</description>
		</constructor>
	</constructors>
	<methods>
		<method name="abs" qualifiers="const">
			<return type="Vector2" />
			<description>
				Returns a new vector with all components in absolute values (i.e. positive).
			</description>
		</method>
		<method name="angle" qualifiers="const">
			<return type="float" />
			<description>
				Returns this vector's angle with respect to the positive X axis, or [code](1, 0)[/code] vector, in radians.
				For example, [code]Vector2.RIGHT.angle()[/code] will return zero, [code]Vector2.DOWN.angle()[/code] will return [code]PI / 2[/code] (a quarter turn, or 90 degrees), and [code]Vector2(1, -1).angle()[/code] will return [code]-PI / 4[/code] (a negative eighth turn, or -45 degrees).
				[url=https://raw.githubusercontent.com/redot-engine/redot-docs/master/img/vector2_angle.png]Illustration of the returned angle.[/url]
				Equivalent to the result of [method @GlobalScope.atan2] when called with the vector's [member y] and [member x] as parameters: [code]atan2(y, x)[/code].
			</description>
		</method>
		<method name="angle_to" qualifiers="const">
			<return type="float" />
			<param index="0" name="to" type="Vector2" />
			<description>
				Returns the signed angle to the given vector, in radians.
				[url=https://raw.githubusercontent.com/redot-engine/redot-docs/master/img/vector2_angle_to.png]Illustration of the returned angle.[/url]
			</description>
		</method>
		<method name="angle_to_point" qualifiers="const">
			<return type="float" />
			<param index="0" name="to" type="Vector2" />
			<description>
				Returns the angle between the line connecting the two points and the X axis, in radians.
				[code]a.angle_to_point(b)[/code] is equivalent of doing [code](b - a).angle()[/code].
				[url=https://raw.githubusercontent.com/redot-engine/redot-docs/master/img/vector2_angle_to_point.png]Illustration of the returned angle.[/url]
			</description>
		</method>
		<method name="aspect" qualifiers="const">
			<return type="float" />
			<description>
				Returns the aspect ratio of this vector, the ratio of [member x] to [member y].
			</description>
		</method>
		<method name="bezier_derivative" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="control_1" type="Vector2" />
			<param index="1" name="control_2" type="Vector2" />
			<param index="2" name="end" type="Vector2" />
			<param index="3" name="t" type="float" />
			<description>
				Returns the derivative at the given [param t] on the [url=https://en.wikipedia.org/wiki/B%C3%A9zier_curve]Bézier curve[/url] defined by this vector and the given [param control_1], [param control_2], and [param end] points.
			</description>
		</method>
		<method name="bezier_interpolate" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="control_1" type="Vector2" />
			<param index="1" name="control_2" type="Vector2" />
			<param index="2" name="end" type="Vector2" />
			<param index="3" name="t" type="float" />
			<description>
				Returns the point at the given [param t] on the [url=https://en.wikipedia.org/wiki/B%C3%A9zier_curve]Bézier curve[/url] defined by this vector and the given [param control_1], [param control_2], and [param end] points.
			</description>
		</method>
		<method name="bounce" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="n" type="Vector2" />
			<description>
				Returns the vector "bounced off" from a line defined by the given normal [param n] perpendicular to the line.
				[b]Note:[/b] [method bounce] performs the operation that most engines and frameworks call [code skip-lint]reflect()[/code].
			</description>
		</method>
		<method name="ceil" qualifiers="const">
			<return type="Vector2" />
			<description>
				Returns a new vector with all components rounded up (towards positive infinity).
			</description>
		</method>
		<method name="clamp" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="min" type="Vector2" />
			<param index="1" name="max" type="Vector2" />
			<description>
				Returns a new vector with all components clamped between the components of [param min] and [param max], by running [method @GlobalScope.clamp] on each component.
			</description>
		</method>
		<method name="clampf" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="min" type="float" />
			<param index="1" name="max" type="float" />
			<description>
				Returns a new vector with all components clamped between [param min] and [param max], by running [method @GlobalScope.clamp] on each component.
			</description>
		</method>
		<method name="cross" qualifiers="const">
			<return type="float" />
			<param index="0" name="with" type="Vector2" />
			<description>
				Returns the 2D analog of the cross product for this vector and [param with].
				This is the signed area of the parallelogram formed by the two vectors. If the second vector is clockwise from the first vector, then the cross product is the positive area. If counter-clockwise, the cross product is the negative area. If the two vectors are parallel this returns zero, making it useful for testing if two vectors are parallel.
				[b]Note:[/b] Cross product is not defined in 2D mathematically. This method embeds the 2D vectors in the XY plane of 3D space and uses their cross product's Z component as the analog.
			</description>
		</method>
		<method name="cubic_interpolate" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="b" type="Vector2" />
			<param index="1" name="pre_a" type="Vector2" />
			<param index="2" name="post_b" type="Vector2" />
			<param index="3" name="weight" type="float" />
			<description>
				Performs a cubic interpolation between this vector and [param b] using [param pre_a] and [param post_b] as handles, and returns the result at position [param weight]. [param weight] is on the range of 0.0 to 1.0, representing the amount of interpolation.
			</description>
		</method>
		<method name="cubic_interpolate_in_time" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="b" type="Vector2" />
			<param index="1" name="pre_a" type="Vector2" />
			<param index="2" name="post_b" type="Vector2" />
			<param index="3" name="weight" type="float" />
			<param index="4" name="b_t" type="float" />
			<param index="5" name="pre_a_t" type="float" />
			<param index="6" name="post_b_t" type="float" />
			<description>
				Performs a cubic interpolation between this vector and [param b] using [param pre_a] and [param post_b] as handles, and returns the result at position [param weight]. [param weight] is on the range of 0.0 to 1.0, representing the amount of interpolation.
				It can perform smoother interpolation than [method cubic_interpolate] by the time values.
			</description>
		</method>
		<method name="direction_to" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="to" type="Vector2" />
			<description>
				Returns the normalized vector pointing from this vector to [param to]. This is equivalent to using [code](b - a).normalized()[/code].
			</description>
		</method>
		<method name="distance_squared_to" qualifiers="const">
			<return type="float" />
			<param index="0" name="to" type="Vector2" />
			<description>
				Returns the squared distance between this vector and [param to].
				This method runs faster than [method distance_to], so prefer it if you need to compare vectors or need the squared distance for some formula.
			</description>
		</method>
		<method name="distance_to" qualifiers="const">
			<return type="float" />
			<param index="0" name="to" type="Vector2" />
			<description>
				Returns the distance between this vector and [param to].
			</description>
		</method>
		<method name="dot" qualifiers="const">
			<return type="float" />
			<param index="0" name="with" type="Vector2" />
			<description>
				Returns the dot product of this vector and [param with]. This can be used to compare the angle between two vectors. For example, this can be used to determine whether an enemy is facing the player.
				The dot product will be [code]0[/code] for a right angle (90 degrees), greater than 0 for angles narrower than 90 degrees and lower than 0 for angles wider than 90 degrees.
				When using unit (normalized) vectors, the result will always be between [code]-1.0[/code] (180 degree angle) when the vectors are facing opposite directions, and [code]1.0[/code] (0 degree angle) when the vectors are aligned.
				[b]Note:[/b] [code]a.dot(b)[/code] is equivalent to [code]b.dot(a)[/code].
			</description>
		</method>
		<method name="floor" qualifiers="const">
			<return type="Vector2" />
			<description>
				Returns a new vector with all components rounded down (towards negative infinity).
			</description>
		</method>
		<method name="from_angle" qualifiers="static">
			<return type="Vector2" />
			<param index="0" name="angle" type="float" />
			<description>
				Creates a [Vector2] rotated to the given [param angle] in radians. This is equivalent to doing [code]Vector2(cos(angle), sin(angle))[/code] or [code]Vector2.RIGHT.rotated(angle)[/code].
				[codeblock]
				print(Vector2.from_angle(0)) # Prints (1.0, 0.0)
				print(Vector2(1, 0).angle()) # Prints 0.0, which is the angle used above.
				print(Vector2.from_angle(PI / 2)) # Prints (0.0, 1.0)
				[/codeblock]
				[b]Note:[/b] The length of the returned [Vector2] is [i]approximately[/i] [code]1.0[/code], but is is not guaranteed to be exactly [code]1.0[/code] due to floating-point precision issues. Call [method normalized] on the returned [Vector2] if you require a unit vector.
			</description>
		</method>
		<method name="is_equal_approx" qualifiers="const">
			<return type="bool" />
			<param index="0" name="to" type="Vector2" />
			<description>
				Returns [code]true[/code] if this vector and [param to] are approximately equal, by running [method @GlobalScope.is_equal_approx] on each component.
			</description>
		</method>
		<method name="is_finite" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if this vector is finite, by calling [method @GlobalScope.is_finite] on each component.
			</description>
		</method>
		<method name="is_normalized" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if the vector is normalized, i.e. its length is approximately equal to 1.
			</description>
		</method>
		<method name="is_zero_approx" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if this vector's values are approximately zero, by running [method @GlobalScope.is_zero_approx] on each component.
				This method is faster than using [method is_equal_approx] with one value as a zero vector.
			</description>
		</method>
		<method name="length" qualifiers="const" keywords="size">
			<return type="float" />
			<description>
				Returns the length (magnitude) of this vector.
			</description>
		</method>
		<method name="length_squared" qualifiers="const">
			<return type="float" />
			<description>
				Returns the squared length (squared magnitude) of this vector.
				This method runs faster than [method length], so prefer it if you need to compare vectors or need the squared distance for some formula.
			</description>
		</method>
		<method name="lerp" qualifiers="const" keywords="interpolate">
			<return type="Vector2" />
			<param index="0" name="to" type="Vector2" />
			<param index="1" name="weight" type="float" />
			<description>
				Returns the result of the linear interpolation between this vector and [param to] by amount [param weight]. [param weight] is on the range of [code]0.0[/code] to [code]1.0[/code], representing the amount of interpolation.
			</description>
		</method>
		<method name="limit_length" qualifiers="const" keywords="truncate">
			<return type="Vector2" />
			<param index="0" name="length" type="float" default="1.0" />
			<description>
				Returns the vector with a maximum length by limiting its length to [param length]. If the vector is non-finite, the result is undefined.
			</description>
		</method>
		<method name="max" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="with" type="Vector2" />
			<description>
				Returns the component-wise maximum of this and [param with], equivalent to [code]Vector2(maxf(x, with.x), maxf(y, with.y))[/code].
			</description>
		</method>
		<method name="max_axis_index" qualifiers="const">
			<return type="int" />
			<description>
				Returns the axis of the vector's highest value. See [code]AXIS_*[/code] constants. If all components are equal, this method returns [constant AXIS_X].
			</description>
		</method>
		<method name="maxf" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="with" type="float" />
			<description>
				Returns the component-wise maximum of this and [param with], equivalent to [code]Vector2(maxf(x, with), maxf(y, with))[/code].
			</description>
		</method>
		<method name="min" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="with" type="Vector2" />
			<description>
				Returns the component-wise minimum of this and [param with], equivalent to [code]Vector2(minf(x, with.x), minf(y, with.y))[/code].
			</description>
		</method>
		<method name="min_axis_index" qualifiers="const">
			<return type="int" />
			<description>
				Returns the axis of the vector's lowest value. See [code]AXIS_*[/code] constants. If all components are equal, this method returns [constant AXIS_Y].
			</description>
		</method>
		<method name="minf" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="with" type="float" />
			<description>
				Returns the component-wise minimum of this and [param with], equivalent to [code]Vector2(minf(x, with), minf(y, with))[/code].
			</description>
		</method>
		<method name="move_toward" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="to" type="Vector2" />
			<param index="1" name="delta" type="float" />
			<description>
				Returns a new vector moved toward [param to] by the fixed [param delta] amount. Will not go past the final value.
			</description>
		</method>
		<method name="normalized" qualifiers="const">
			<return type="Vector2" />
			<description>
				Returns the result of scaling the vector to unit length. Equivalent to [code]v / v.length()[/code]. Returns [code](0, 0)[/code] if [code]v.length() == 0[/code]. See also [method is_normalized].
				[b]Note:[/b] This function may return incorrect values if the input vector length is near zero.
			</description>
		</method>
		<method name="orthogonal" qualifiers="const">
			<return type="Vector2" />
			<description>
				Returns a perpendicular vector rotated 90 degrees counter-clockwise compared to the original, with the same length.
			</description>
		</method>
		<method name="posmod" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="mod" type="float" />
			<description>
				Returns a vector composed of the [method @GlobalScope.fposmod] of this vector's components and [param mod].
			</description>
		</method>
		<method name="posmodv" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="modv" type="Vector2" />
			<description>
				Returns a vector composed of the [method @GlobalScope.fposmod] of this vector's components and [param modv]'s components.
			</description>
		</method>
		<method name="project" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="b" type="Vector2" />
			<description>
				Returns a new vector resulting from projecting this vector onto the given vector [param b]. The resulting new vector is parallel to [param b]. See also [method slide].
				[b]Note:[/b] If the vector [param b] is a zero vector, the components of the resulting new vector will be [constant @GDScript.NAN].
			</description>
		</method>
		<method name="reflect" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="line" type="Vector2" />
			<description>
				Returns the result of reflecting the vector from a line defined by the given direction vector [param line].
				[b]Note:[/b] [method reflect] differs from what other engines and frameworks call [code skip-lint]reflect()[/code]. In other engines, [code skip-lint]reflect()[/code] takes a normal direction which is a direction perpendicular to the line. In Redot, you specify the direction of the line directly. See also [method bounce] which does what most engines call [code skip-lint]reflect()[/code].
			</description>
		</method>
		<method name="rotated" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="angle" type="float" />
			<description>
				Returns the result of rotating this vector by [param angle] (in radians). See also [method @GlobalScope.deg_to_rad].
			</description>
		</method>
		<method name="round" qualifiers="const">
			<return type="Vector2" />
			<description>
				Returns a new vector with all components rounded to the nearest integer, with halfway cases rounded away from zero.
			</description>
		</method>
		<method name="sign" qualifiers="const">
			<return type="Vector2" />
			<description>
				Returns a new vector with each component set to [code]1.0[/code] if it's positive, [code]-1.0[/code] if it's negative, and [code]0.0[/code] if it's zero. The result is identical to calling [method @GlobalScope.sign] on each component.
			</description>
		</method>
		<method name="slerp" qualifiers="const" keywords="interpolate">
			<return type="Vector2" />
			<param index="0" name="to" type="Vector2" />
			<param index="1" name="weight" type="float" />
			<description>
				Returns the result of spherical linear interpolation between this vector and [param to], by amount [param weight]. [param weight] is on the range of 0.0 to 1.0, representing the amount of interpolation.
				This method also handles interpolating the lengths if the input vectors have different lengths. For the special case of one or both input vectors having zero length, this method behaves like [method lerp].
			</description>
		</method>
		<method name="slide" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="n" type="Vector2" />
			<description>
				Returns a new vector resulting from sliding this vector along a line with normal [param n]. The resulting new vector is perpendicular to [param n], and is equivalent to this vector minus its projection on [param n]. See also [method project].
				[b]Note:[/b] The vector [param n] must be normalized. See also [method normalized].
			</description>
		</method>
		<method name="snapped" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="step" type="Vector2" />
			<description>
				Returns a new vector with each component snapped to the nearest multiple of the corresponding component in [param step]. This can also be used to round the components to an arbitrary number of decimals.
			</description>
		</method>
		<method name="snappedf" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="step" type="float" />
			<description>
				Returns a new vector with each component snapped to the nearest multiple of [param step]. This can also be used to round the components to an arbitrary number of decimals.
			</description>
		</method>
	</methods>
	<members>
		<member name="x" type="float" setter="" getter="" default="0.0">
			The vector's X component. Also accessible by using the index position [code][0][/code].
		</member>
		<member name="y" type="float" setter="" getter="" default="0.0">
			The vector's Y component. Also accessible by using the index position [code][1][/code].
		</member>
	</members>
	<constants>
		<constant name="AXIS_X" value="0" enum="Axis">
			Enumerated value for the X axis. Returned by [method max_axis_index] and [method min_axis_index].
		</constant>
		<constant name="AXIS_Y" value="1" enum="Axis">
			Enumerated value for the Y axis. Returned by [method max_axis_index] and [method min_axis_index].
		</constant>
		<constant name="ZERO" value="Vector2(0, 0)">
			Zero vector, a vector with all components set to [code]0[/code].
		</constant>
		<constant name="ONE" value="Vector2(1, 1)">
			One vector, a vector with all components set to [code]1[/code].
		</constant>
		<constant name="INF" value="Vector2(inf, inf)">
			Infinity vector, a vector with all components set to [constant @GDScript.INF].
		</constant>
		<constant name="LEFT" value="Vector2(-1, 0)">
			Left unit vector. Represents the direction of left.
		</constant>
		<constant name="RIGHT" value="Vector2(1, 0)">
			Right unit vector. Represents the direction of right.
		</constant>
		<constant name="UP" value="Vector2(0, -1)">
			Up unit vector. Y is down in 2D, so this vector points -Y.
		</constant>
		<constant name="DOWN" value="Vector2(0, 1)">
			Down unit vector. Y is down in 2D, so this vector points +Y.
		</constant>
	</constants>
	<operators>
		<operator name="operator !=">
			<return type="bool" />
			<param index="0" name="right" type="Vector2" />
			<description>
				Returns [code]true[/code] if the vectors are not equal.
				[b]Note:[/b] Due to floating-point precision errors, consider using [method is_equal_approx] instead, which is more reliable.
				[b]Note:[/b] Vectors with [constant @GDScript.NAN] elements don't behave the same as other vectors. Therefore, the results from this operator may not be accurate if NaNs are included.
			</description>
		</operator>
		<operator name="operator *">
			<return type="Vector2" />
			<param index="0" name="right" type="Transform2D" />
			<description>
				Inversely transforms (multiplies) the [Vector2] by the given [Transform2D] transformation matrix, under the assumption that the transformation basis is orthonormal (i.e. rotation/reflection is fine, scaling/skew is not).
				[code]vector * transform[/code] is equivalent to [code]transform.inverse() * vector[/code]. See [method Transform2D.inverse].
				For transforming by inverse of an affine transformation (e.g. with scaling) [code]transform.affine_inverse() * vector[/code] can be used instead. See [method Transform2D.affine_inverse].
			</description>
		</operator>
		<operator name="operator *">
			<return type="Vector2" />
			<param index="0" name="right" type="Vector2" />
			<description>
				Multiplies each component of the [Vector2] by the components of the given [Vector2].
				[codeblock]
				print(Vector2(10, 20) * Vector2(3, 4)) # Prints (30.0, 80.0)
				[/codeblock]
			</description>
		</operator>
		<operator name="operator *">
			<return type="Vector2" />
			<param index="0" name="right" type="float" />
			<description>
				Multiplies each component of the [Vector2] by the given [float].
			</description>
		</operator>
		<operator name="operator *">
			<return type="Vector2" />
			<param index="0" name="right" type="int" />
			<description>
				Multiplies each component of the [Vector2] by the given [int].
			</description>
		</operator>
		<operator name="operator +">
			<return type="Vector2" />
			<param index="0" name="right" type="Vector2" />
			<description>
				Adds each component of the [Vector2] by the components of the given [Vector2].
				[codeblock]
				print(Vector2(10, 20) + Vector2(3, 4)) # Prints (13.0, 24.0)
				[/codeblock]
			</description>
		</operator>
		<operator name="operator -">
			<return type="Vector2" />
			<param index="0" name="right" type="Vector2" />
			<description>
				Subtracts each component of the [Vector2] by the components of the given [Vector2].
				[codeblock]
				print(Vector2(10, 20) - Vector2(3, 4)) # Prints (7.0, 16.0)
				[/codeblock]
			</description>
		</operator>
		<operator name="operator /">
			<return type="Vector2" />
			<param index="0" name="right" type="Vector2" />
			<description>
				Divides each component of the [Vector2] by the components of the given [Vector2].
				[codeblock]
				print(Vector2(10, 20) / Vector2(2, 5)) # Prints (5.0, 4.0)
				[/codeblock]
			</description>
		</operator>
		<operator name="operator /">
			<return type="Vector2" />
			<param index="0" name="right" type="float" />
			<description>
				Divides each component of the [Vector2] by the given [float].
			</description>
		</operator>
		<operator name="operator /">
			<return type="Vector2" />
			<param index="0" name="right" type="int" />
			<description>
				Divides each component of the [Vector2] by the given [int].
			</description>
		</operator>
		<operator name="operator &lt;">
			<return type="bool" />
			<param index="0" name="right" type="Vector2" />
			<description>
				Compares two [Vector2] vectors by first checking if the X value of the left vector is less than the X value of the [param right] vector. If the X values are exactly equal, then it repeats this check with the Y values of the two vectors. This operator is useful for sorting vectors.
				[b]Note:[/b] Vectors with [constant @GDScript.NAN] elements don't behave the same as other vectors. Therefore, the results from this operator may not be accurate if NaNs are included.
			</description>
		</operator>
		<operator name="operator &lt;=">
			<return type="bool" />
			<param index="0" name="right" type="Vector2" />
			<description>
				Compares two [Vector2] vectors by first checking if the X value of the left vector is less than or equal to the X value of the [param right] vector. If the X values are exactly equal, then it repeats this check with the Y values of the two vectors. This operator is useful for sorting vectors.
				[b]Note:[/b] Vectors with [constant @GDScript.NAN] elements don't behave the same as other vectors. Therefore, the results from this operator may not be accurate if NaNs are included.
			</description>
		</operator>
		<operator name="operator ==">
			<return type="bool" />
			<param index="0" name="right" type="Vector2" />
			<description>
				Returns [code]true[/code] if the vectors are exactly equal.
				[b]Note:[/b] Due to floating-point precision errors, consider using [method is_equal_approx] instead, which is more reliable.
				[b]Note:[/b] Vectors with [constant @GDScript.NAN] elements don't behave the same as other vectors. Therefore, the results from this operator may not be accurate if NaNs are included.
			</description>
		</operator>
		<operator name="operator &gt;">
			<return type="bool" />
			<param index="0" name="right" type="Vector2" />
			<description>
				Compares two [Vector2] vectors by first checking if the X value of the left vector is greater than the X value of the [param right] vector. If the X values are exactly equal, then it repeats this check with the Y values of the two vectors. This operator is useful for sorting vectors.
				[b]Note:[/b] Vectors with [constant @GDScript.NAN] elements don't behave the same as other vectors. Therefore, the results from this operator may not be accurate if NaNs are included.
			</description>
		</operator>
		<operator name="operator &gt;=">
			<return type="bool" />
			<param index="0" name="right" type="Vector2" />
			<description>
				Compares two [Vector2] vectors by first checking if the X value of the left vector is greater than or equal to the X value of the [param right] vector. If the X values are exactly equal, then it repeats this check with the Y values of the two vectors. This operator is useful for sorting vectors.
				[b]Note:[/b] Vectors with [constant @GDScript.NAN] elements don't behave the same as other vectors. Therefore, the results from this operator may not be accurate if NaNs are included.
			</description>
		</operator>
		<operator name="operator []">
			<return type="float" />
			<param index="0" name="index" type="int" />
			<description>
				Access vector components using their [param index]. [code]v[0][/code] is equivalent to [code]v.x[/code], and [code]v[1][/code] is equivalent to [code]v.y[/code].
			</description>
		</operator>
		<operator name="operator unary+">
			<return type="Vector2" />
			<description>
				Returns the same value as if the [code]+[/code] was not there. Unary [code]+[/code] does nothing, but sometimes it can make your code more readable.
			</description>
		</operator>
		<operator name="operator unary-">
			<return type="Vector2" />
			<description>
				Returns the negative value of the [Vector2]. This is the same as writing [code]Vector2(-v.x, -v.y)[/code]. This operation flips the direction of the vector while keeping the same magnitude. With floats, the number zero can be either positive or negative.
			</description>
		</operator>
	</operators>
</class>
