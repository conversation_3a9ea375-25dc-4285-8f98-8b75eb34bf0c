<?xml version="1.0" encoding="UTF-8" ?>
<class name="ResourceImporter" inherits="RefCounted" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Base class for resource importers.
	</brief_description>
	<description>
		This is the base class for Redot's resource importers. To implement your own resource importers using editor plugins, see [EditorImportPlugin].
	</description>
	<tutorials>
		<link title="Import plugins">$DOCS_URL/tutorials/plugins/editor/import_plugins.html</link>
	</tutorials>
	<constants>
		<constant name="IMPORT_ORDER_DEFAULT" value="0" enum="ImportOrder">
			The default import order.
		</constant>
		<constant name="IMPORT_ORDER_SCENE" value="100" enum="ImportOrder">
			The import order for scenes, which ensures scenes are imported [i]after[/i] all other core resources such as textures. Custom importers should generally have an import order lower than [code]100[/code] to avoid issues when importing scenes that rely on custom resources.
		</constant>
	</constants>
</class>
