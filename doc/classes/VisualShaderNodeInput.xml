<?xml version="1.0" encoding="UTF-8" ?>
<class name="VisualShaderNodeInput" inherits="VisualShaderNode" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Represents the input shader parameter within the visual shader graph.
	</brief_description>
	<description>
		Gives access to input variables (built-ins) available for the shader. See the shading reference for the list of available built-ins for each shader type (check [code]Tutorials[/code] section for link).
	</description>
	<tutorials>
		<link title="Shading reference index">$DOCS_URL/tutorials/shaders/shader_reference/index.html</link>
	</tutorials>
	<methods>
		<method name="get_input_real_name" qualifiers="const">
			<return type="String" />
			<description>
				Returns a translated name of the current constant in the Redot Shader Language. E.g. [code]"ALBEDO"[/code] if the [member input_name] equal to [code]"albedo"[/code].
			</description>
		</method>
	</methods>
	<members>
		<member name="input_name" type="String" setter="set_input_name" getter="get_input_name" default="&quot;[None]&quot;">
			One of the several input constants in lower-case style like: "vertex" ([code]VERTEX[/code]) or "point_size" ([code]POINT_SIZE[/code]).
		</member>
	</members>
	<signals>
		<signal name="input_type_changed">
			<description>
				Emitted when input is changed via [member input_name].
			</description>
		</signal>
	</signals>
</class>
