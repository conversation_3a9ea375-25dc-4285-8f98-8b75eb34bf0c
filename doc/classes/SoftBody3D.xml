<?xml version="1.0" encoding="UTF-8" ?>
<class name="SoftBody3D" inherits="MeshInstance3D" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A deformable 3D physics mesh.
	</brief_description>
	<description>
		A deformable 3D physics mesh. Used to create elastic or deformable objects such as cloth, rubber, or other flexible materials.
		Additionally, [SoftBody3D] is subject to wind forces defined in [Area3D] (see [member Area3D.wind_source_path], [member Area3D.wind_force_magnitude], and [member Area3D.wind_attenuation_factor]).
		[b]Note:[/b] There are many known bugs in [SoftBody3D]. Therefore, it's not recommended to use them for things that can affect gameplay (such as trampolines).
	</description>
	<tutorials>
		<link title="SoftBody">$DOCS_URL/tutorials/physics/soft_body.html</link>
	</tutorials>
	<methods>
		<method name="add_collision_exception_with">
			<return type="void" />
			<param index="0" name="body" type="Node" />
			<description>
				Adds a body to the list of bodies that this body can't collide with.
			</description>
		</method>
		<method name="get_collision_exceptions">
			<return type="PhysicsBody3D[]" />
			<description>
				Returns an array of nodes that were added as collision exceptions for this body.
			</description>
		</method>
		<method name="get_collision_layer_value" qualifiers="const">
			<return type="bool" />
			<param index="0" name="layer_number" type="int" />
			<description>
				Returns whether or not the specified layer of the [member collision_layer] is enabled, given a [param layer_number] between 1 and 32.
			</description>
		</method>
		<method name="get_collision_mask_value" qualifiers="const">
			<return type="bool" />
			<param index="0" name="layer_number" type="int" />
			<description>
				Returns whether or not the specified layer of the [member collision_mask] is enabled, given a [param layer_number] between 1 and 32.
			</description>
		</method>
		<method name="get_physics_rid" qualifiers="const">
			<return type="RID" />
			<description>
				Returns the internal [RID] used by the [PhysicsServer3D] for this body.
			</description>
		</method>
		<method name="get_point_transform">
			<return type="Vector3" />
			<param index="0" name="point_index" type="int" />
			<description>
				Returns local translation of a vertex in the surface array.
			</description>
		</method>
		<method name="is_point_pinned" qualifiers="const">
			<return type="bool" />
			<param index="0" name="point_index" type="int" />
			<description>
				Returns [code]true[/code] if vertex is set to pinned.
			</description>
		</method>
		<method name="remove_collision_exception_with">
			<return type="void" />
			<param index="0" name="body" type="Node" />
			<description>
				Removes a body from the list of bodies that this body can't collide with.
			</description>
		</method>
		<method name="set_collision_layer_value">
			<return type="void" />
			<param index="0" name="layer_number" type="int" />
			<param index="1" name="value" type="bool" />
			<description>
				Based on [param value], enables or disables the specified layer in the [member collision_layer], given a [param layer_number] between 1 and 32.
			</description>
		</method>
		<method name="set_collision_mask_value">
			<return type="void" />
			<param index="0" name="layer_number" type="int" />
			<param index="1" name="value" type="bool" />
			<description>
				Based on [param value], enables or disables the specified layer in the [member collision_mask], given a [param layer_number] between 1 and 32.
			</description>
		</method>
		<method name="set_point_pinned">
			<return type="void" />
			<param index="0" name="point_index" type="int" />
			<param index="1" name="pinned" type="bool" />
			<param index="2" name="attachment_path" type="NodePath" default="NodePath(&quot;&quot;)" />
			<param index="3" name="insert_at" type="int" default="-1" />
			<description>
				Sets the pinned state of a surface vertex. When set to [code]true[/code], the optional [param attachment_path] can define a [Node3D] the pinned vertex will be attached to.
			</description>
		</method>
	</methods>
	<members>
		<member name="collision_layer" type="int" setter="set_collision_layer" getter="get_collision_layer" default="1">
			The physics layers this SoftBody3D [b]is in[/b]. Collision objects can exist in one or more of 32 different layers. See also [member collision_mask].
			[b]Note:[/b] Object A can detect a contact with object B only if object B is in any of the layers that object A scans. See [url=$DOCS_URL/tutorials/physics/physics_introduction.html#collision-layers-and-masks]Collision layers and masks[/url] in the documentation for more information.
		</member>
		<member name="collision_mask" type="int" setter="set_collision_mask" getter="get_collision_mask" default="1">
			The physics layers this SoftBody3D [b]scans[/b]. Collision objects can scan one or more of 32 different layers. See also [member collision_layer].
			[b]Note:[/b] Object A can detect a contact with object B only if object B is in any of the layers that object A scans. See [url=$DOCS_URL/tutorials/physics/physics_introduction.html#collision-layers-and-masks]Collision layers and masks[/url] in the documentation for more information.
		</member>
		<member name="damping_coefficient" type="float" setter="set_damping_coefficient" getter="get_damping_coefficient" default="0.01">
			The body's damping coefficient. Higher values will slow down the body more noticeably when forces are applied.
		</member>
		<member name="disable_mode" type="int" setter="set_disable_mode" getter="get_disable_mode" enum="SoftBody3D.DisableMode" default="0">
			Defines the behavior in physics when [member Node.process_mode] is set to [constant Node.PROCESS_MODE_DISABLED]. See [enum DisableMode] for more details about the different modes.
		</member>
		<member name="drag_coefficient" type="float" setter="set_drag_coefficient" getter="get_drag_coefficient" default="0.0">
			The body's drag coefficient. Higher values increase this body's air resistance.
			[b]Note:[/b] This value is currently unused by Redot's default physics implementation.
		</member>
		<member name="linear_stiffness" type="float" setter="set_linear_stiffness" getter="get_linear_stiffness" default="0.5">
			Higher values will result in a stiffer body, while lower values will increase the body's ability to bend. The value can be between [code]0.0[/code] and [code]1.0[/code] (inclusive).
		</member>
		<member name="parent_collision_ignore" type="NodePath" setter="set_parent_collision_ignore" getter="get_parent_collision_ignore" default="NodePath(&quot;&quot;)">
			[NodePath] to a [CollisionObject3D] this SoftBody3D should avoid clipping.
		</member>
		<member name="pressure_coefficient" type="float" setter="set_pressure_coefficient" getter="get_pressure_coefficient" default="0.0">
			The pressure coefficient of this soft body. Simulate pressure build-up from inside this body. Higher values increase the strength of this effect.
		</member>
		<member name="ray_pickable" type="bool" setter="set_ray_pickable" getter="is_ray_pickable" default="true">
			If [code]true[/code], the [SoftBody3D] will respond to [RayCast3D]s.
		</member>
		<member name="simulation_precision" type="int" setter="set_simulation_precision" getter="get_simulation_precision" default="5">
			Increasing this value will improve the resulting simulation, but can affect performance. Use with care.
		</member>
		<member name="total_mass" type="float" setter="set_total_mass" getter="get_total_mass" default="1.0">
			The SoftBody3D's mass.
		</member>
	</members>
	<constants>
		<constant name="DISABLE_MODE_REMOVE" value="0" enum="DisableMode">
			When [member Node.process_mode] is set to [constant Node.PROCESS_MODE_DISABLED], remove from the physics simulation to stop all physics interactions with this [SoftBody3D].
			Automatically re-added to the physics simulation when the [Node] is processed again.
		</constant>
		<constant name="DISABLE_MODE_KEEP_ACTIVE" value="1" enum="DisableMode">
			When [member Node.process_mode] is set to [constant Node.PROCESS_MODE_DISABLED], do not affect the physics simulation.
		</constant>
	</constants>
</class>
