<?xml version="1.0" encoding="UTF-8" ?>
<class name="Texture3D" inherits="Texture" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Base class for 3-dimensional textures.
	</brief_description>
	<description>
		Base class for [ImageTexture3D] and [CompressedTexture3D]. Cannot be used directly, but contains all the functions necessary for accessing the derived resource types. [Texture3D] is the base class for all 3-dimensional texture types. See also [TextureLayered].
		All images need to have the same width, height and number of mipmap levels.
		To create such a texture file yourself, reimport your image files using the Redot Editor import presets.
	</description>
	<tutorials>
	</tutorials>
	<methods>
		<method name="_get_data" qualifiers="virtual const">
			<return type="Image[]" />
			<description>
				Called when the [Texture3D]'s data is queried.
			</description>
		</method>
		<method name="_get_depth" qualifiers="virtual const">
			<return type="int" />
			<description>
				Called when the [Texture3D]'s depth is queried.
			</description>
		</method>
		<method name="_get_format" qualifiers="virtual const">
			<return type="int" enum="Image.Format" />
			<description>
				Called when the [Texture3D]'s format is queried.
			</description>
		</method>
		<method name="_get_height" qualifiers="virtual const">
			<return type="int" />
			<description>
				Called when the [Texture3D]'s height is queried.
			</description>
		</method>
		<method name="_get_width" qualifiers="virtual const">
			<return type="int" />
			<description>
				Called when the [Texture3D]'s width is queried.
			</description>
		</method>
		<method name="_has_mipmaps" qualifiers="virtual const">
			<return type="bool" />
			<description>
				Called when the presence of mipmaps in the [Texture3D] is queried.
			</description>
		</method>
		<method name="create_placeholder" qualifiers="const">
			<return type="Resource" />
			<description>
				Creates a placeholder version of this resource ([PlaceholderTexture3D]).
			</description>
		</method>
		<method name="get_data" qualifiers="const">
			<return type="Image[]" />
			<description>
				Returns the [Texture3D]'s data as an array of [Image]s. Each [Image] represents a [i]slice[/i] of the [Texture3D], with different slices mapping to different depth (Z axis) levels.
			</description>
		</method>
		<method name="get_depth" qualifiers="const">
			<return type="int" />
			<description>
				Returns the [Texture3D]'s depth in pixels. Depth is typically represented by the Z axis (a dimension not present in [Texture2D]).
			</description>
		</method>
		<method name="get_format" qualifiers="const">
			<return type="int" enum="Image.Format" />
			<description>
				Returns the current format being used by this texture. See [enum Image.Format] for details.
			</description>
		</method>
		<method name="get_height" qualifiers="const">
			<return type="int" />
			<description>
				Returns the [Texture3D]'s height in pixels. Width is typically represented by the Y axis.
			</description>
		</method>
		<method name="get_width" qualifiers="const">
			<return type="int" />
			<description>
				Returns the [Texture3D]'s width in pixels. Width is typically represented by the X axis.
			</description>
		</method>
		<method name="has_mipmaps" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if the [Texture3D] has generated mipmaps.
			</description>
		</method>
	</methods>
</class>
