<?xml version="1.0" encoding="UTF-8" ?>
<class name="PortableCompressedTexture2D" inherits="Texture2D" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Provides a compressed texture for disk and/or VRAM in a way that is portable.
	</brief_description>
	<description>
		This class allows storing compressed textures as self contained (not imported) resources.
		For 2D usage (compressed on disk, uncompressed on VRAM), the lossy and lossless modes are recommended. For 3D usage (compressed on VRAM) it depends on the target platform.
		If you intend to only use desktop, S3TC or BPTC are recommended. For only mobile, ETC2 is recommended.
		For portable, self contained 3D textures that work on both desktop and mobile, Basis Universal is recommended (although it has a small quality cost and longer compression time as a tradeoff).
		This resource is intended to be created from code.
	</description>
	<tutorials>
	</tutorials>
	<methods>
		<method name="create_from_image">
			<return type="void" />
			<param index="0" name="image" type="Image" />
			<param index="1" name="compression_mode" type="int" enum="PortableCompressedTexture2D.CompressionMode" />
			<param index="2" name="normal_map" type="bool" default="false" />
			<param index="3" name="lossy_quality" type="float" default="0.8" />
			<description>
				Initializes the compressed texture from a base image. The compression mode must be provided.
				[param normal_map] is recommended to ensure optimum quality if this image will be used as a normal map.
				If lossy compression is requested, the quality setting can optionally be provided. This maps to Lossy WebP compression quality.
			</description>
		</method>
		<method name="get_compression_mode" qualifiers="const">
			<return type="int" enum="PortableCompressedTexture2D.CompressionMode" />
			<description>
				Return the compression mode used (valid after initialized).
			</description>
		</method>
		<method name="get_format" qualifiers="const">
			<return type="int" enum="Image.Format" />
			<description>
				Return the image format used (valid after initialized).
			</description>
		</method>
		<method name="is_keeping_all_compressed_buffers" qualifiers="static">
			<return type="bool" />
			<description>
				Return whether the flag is overridden for all textures of this type.
			</description>
		</method>
		<method name="set_keep_all_compressed_buffers" qualifiers="static">
			<return type="void" />
			<param index="0" name="keep" type="bool" />
			<description>
				Overrides the flag globally for all textures of this type. This is used primarily by the editor.
			</description>
		</method>
	</methods>
	<members>
		<member name="keep_compressed_buffer" type="bool" setter="set_keep_compressed_buffer" getter="is_keeping_compressed_buffer" default="false">
			When running on the editor, this class will keep the source compressed data in memory. Otherwise, the source compressed data is lost after loading and the resource can't be re saved.
			This flag allows to keep the compressed data in memory if you intend it to persist after loading.
		</member>
		<member name="resource_local_to_scene" type="bool" setter="set_local_to_scene" getter="is_local_to_scene" overrides="Resource" default="false" />
		<member name="size_override" type="Vector2" setter="set_size_override" getter="get_size_override" default="Vector2(0, 0)">
			Allow overriding the texture size (for 2D only).
		</member>
	</members>
	<constants>
		<constant name="COMPRESSION_MODE_LOSSLESS" value="0" enum="CompressionMode">
		</constant>
		<constant name="COMPRESSION_MODE_LOSSY" value="1" enum="CompressionMode">
		</constant>
		<constant name="COMPRESSION_MODE_BASIS_UNIVERSAL" value="2" enum="CompressionMode">
		</constant>
		<constant name="COMPRESSION_MODE_S3TC" value="3" enum="CompressionMode">
		</constant>
		<constant name="COMPRESSION_MODE_ETC2" value="4" enum="CompressionMode">
		</constant>
		<constant name="COMPRESSION_MODE_BPTC" value="5" enum="CompressionMode">
		</constant>
		<constant name="COMPRESSION_MODE_ASTC" value="6" enum="CompressionMode">
		</constant>
	</constants>
</class>
