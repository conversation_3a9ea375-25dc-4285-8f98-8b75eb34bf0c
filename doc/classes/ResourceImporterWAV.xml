<?xml version="1.0" encoding="UTF-8" ?>
<class name="ResourceImporterWAV" inherits="ResourceImporter" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Imports a WAV audio file for playback.
	</brief_description>
	<description>
		WAV is an uncompressed format, which can provide higher quality compared to Ogg Vorbis and MP3. It also has the lowest CPU cost to decode. This means high numbers of WAV sounds can be played at the same time, even on low-end devices.
		By default, Redot imports WAV files using the lossy Quite OK Audio compression. You may change this by setting the [member compress/mode] property.
	</description>
	<tutorials>
		<link title="Importing audio samples">$DOCS_URL/tutorials/assets_pipeline/importing_audio_samples.html</link>
	</tutorials>
	<members>
		<member name="compress/mode" type="int" setter="" getter="" default="2">
			The compression mode to use on import.
			- [b]PCM (Uncompressed):[/b] Imports audio data without any form of compression, preserving the highest possible quality. It has the lowest CPU cost, but the highest memory usage.
			- [b]IMA ADPCM:[/b] Applies fast, lossy compression during import, noticeably decreasing the quality, but with low CPU cost and memory usage. Does not support seeking and only Forward loop mode is supported.
			- [b][url=https://qoaformat.org/]Quite OK Audio[/url]:[/b] Also applies lossy compression on import, having a slightly higher CPU cost compared to IMA ADPCM, but much higher quality and the lowest memory usage.
		</member>
		<member name="edit/loop_begin" type="int" setter="" getter="" default="0">
			The begin loop point to use when [member edit/loop_mode] is [b]Forward[/b], [b]Ping-Pong[/b], or [b]Backward[/b]. This is set in samples after the beginning of the audio file.
		</member>
		<member name="edit/loop_end" type="int" setter="" getter="" default="-1">
			The end loop point to use when [member edit/loop_mode] is [b]Forward[/b], [b]Ping-Pong[/b], or [b]Backward[/b]. This is set in samples after the beginning of the audio file. A value of [code]-1[/code] uses the end of the audio file as the end loop point.
		</member>
		<member name="edit/loop_mode" type="int" setter="" getter="" default="0">
			Controls how audio should loop.
			- [b]Detect From WAV:[/b] Uses loop information from the WAV metadata.
			- [b]Disabled:[/b] Don't loop audio, even if the metadata indicates the file playback should loop.
			- [b]Forward:[/b] Standard audio looping. Plays the audio forward from the beginning to [member edit/loop_end], then returns to [member edit/loop_begin] and repeats.
			- [b]Ping-Pong:[/b] Plays the audio forward until [member edit/loop_end], then backwards to [member edit/loop_begin], repeating this cycle.
			- [b]Backward:[/b] Plays the audio backwards from [member edit/loop_end] to [member edit/loop_begin], then repeats.
			[b]Note:[/b] In [AudioStreamPlayer], the [signal AudioStreamPlayer.finished] signal won't be emitted for looping audio when it reaches the end of the audio file, as the audio will keep playing indefinitely.
		</member>
		<member name="edit/normalize" type="bool" setter="" getter="" default="false">
			If [code]true[/code], normalize the audio volume so that its peak volume is equal to 0 dB. When enabled, normalization will make audio sound louder depending on its original peak volume.
		</member>
		<member name="edit/trim" type="bool" setter="" getter="" default="false">
			If [code]true[/code], automatically trim the beginning and end of the audio if it's lower than -50 dB after normalization (see [member edit/normalize]). This prevents having files with silence at the beginning or end, which increases their size unnecessarily and adds latency to the moment they are played back. A fade-in/fade-out period of 500 samples is also used during trimming to avoid audible pops.
		</member>
		<member name="force/8_bit" type="bool" setter="" getter="" default="false">
			If [code]true[/code], forces the imported audio to use 8-bit quantization if the source file is 16-bit or higher.
			Enabling this is generally not recommended, as 8-bit quantization decreases audio quality significantly. If you need smaller file sizes, consider using Ogg Vorbis or MP3 audio instead.
		</member>
		<member name="force/max_rate" type="bool" setter="" getter="" default="false">
			If set to a value greater than [code]0[/code], forces the audio's sample rate to be reduced to a value lower than or equal to the value specified in [member force/max_rate_hz].
			This can decrease file size noticeably on certain sounds, without impacting quality depending on the actual sound's contents. See [url=$DOCS_URL/tutorials/assets_pipeline/importing_audio_samples.html#doc-importing-audio-samples-best-practices]Best practices[/url] for more information.
		</member>
		<member name="force/max_rate_hz" type="float" setter="" getter="" default="44100">
			The frequency to limit the imported audio sample to (in Hz). Only effective if [member force/max_rate] is [code]true[/code].
		</member>
		<member name="force/mono" type="bool" setter="" getter="" default="false">
			If [code]true[/code], forces the imported audio to be mono if the source file is stereo. This decreases the file size by 50% by merging the two channels into one.
		</member>
	</members>
</class>
