<?xml version="1.0" encoding="UTF-8" ?>
<class name="SurfaceTool" inherits="RefCounted" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Helper tool to create geometry.
	</brief_description>
	<description>
		The [SurfaceTool] is used to construct a [Mesh] by specifying vertex attributes individually. It can be used to construct a [Mesh] from a script. All properties except indices need to be added before calling [method add_vertex]. For example, to add vertex colors and UVs:
		[codeblocks]
		[gdscript]
		var st = SurfaceTool.new()
		st.begin(Mesh.PRIMITIVE_TRIANGLES)
		st.set_color(Color(1, 0, 0))
		st.set_uv(Vector2(0, 0))
		st.add_vertex(Vector3(0, 0, 0))
		[/gdscript]
		[csharp]
		var st = new SurfaceTool();
		st.Begin(Mesh.PrimitiveType.Triangles);
		st.SetColor(new Color(1, 0, 0));
		st.SetUV(new Vector2(0, 0));
		st.AddVertex(new Vector3(0, 0, 0));
		[/csharp]
		[/codeblocks]
		The above [SurfaceTool] now contains one vertex of a triangle which has a UV coordinate and a specified [Color]. If another vertex were added without calling [method set_uv] or [method set_color], then the last values would be used.
		Vertex attributes must be passed [b]before[/b] calling [method add_vertex]. Failure to do so will result in an error when committing the vertex information to a mesh.
		Additionally, the attributes used before the first vertex is added determine the format of the mesh. For example, if you only add UVs to the first vertex, you cannot add color to any of the subsequent vertices.
		See also [ArrayMesh], [ImmediateMesh] and [MeshDataTool] for procedural geometry generation.
		[b]Note:[/b] Redot uses clockwise [url=https://learnopengl.com/Advanced-OpenGL/Face-culling]winding order[/url] for front faces of triangle primitive modes.
	</description>
	<tutorials>
		<link title="Using the SurfaceTool">$DOCS_URL/tutorials/3d/procedural_geometry/surfacetool.html</link>
		<link title="3D Voxel Demo">https://godotengine.org/asset-library/asset/2755</link>
	</tutorials>
	<methods>
		<method name="add_index">
			<return type="void" />
			<param index="0" name="index" type="int" />
			<description>
				Adds a vertex to index array if you are using indexed vertices. Does not need to be called before adding vertices.
			</description>
		</method>
		<method name="add_triangle_fan">
			<return type="void" />
			<param index="0" name="vertices" type="PackedVector3Array" />
			<param index="1" name="uvs" type="PackedVector2Array" default="PackedVector2Array()" />
			<param index="2" name="colors" type="PackedColorArray" default="PackedColorArray()" />
			<param index="3" name="uv2s" type="PackedVector2Array" default="PackedVector2Array()" />
			<param index="4" name="normals" type="PackedVector3Array" default="PackedVector3Array()" />
			<param index="5" name="tangents" type="Plane[]" default="[]" />
			<description>
				Inserts a triangle fan made of array data into [Mesh] being constructed.
				Requires the primitive type be set to [constant Mesh.PRIMITIVE_TRIANGLES].
			</description>
		</method>
		<method name="add_vertex">
			<return type="void" />
			<param index="0" name="vertex" type="Vector3" />
			<description>
				Specifies the position of current vertex. Should be called after specifying other vertex properties (e.g. Color, UV).
			</description>
		</method>
		<method name="append_from">
			<return type="void" />
			<param index="0" name="existing" type="Mesh" />
			<param index="1" name="surface" type="int" />
			<param index="2" name="transform" type="Transform3D" />
			<description>
				Append vertices from a given [Mesh] surface onto the current vertex array with specified [Transform3D].
			</description>
		</method>
		<method name="begin">
			<return type="void" />
			<param index="0" name="primitive" type="int" enum="Mesh.PrimitiveType" />
			<description>
				Called before adding any vertices. Takes the primitive type as an argument (e.g. [constant Mesh.PRIMITIVE_TRIANGLES]).
			</description>
		</method>
		<method name="clear">
			<return type="void" />
			<description>
				Clear all information passed into the surface tool so far.
			</description>
		</method>
		<method name="commit">
			<return type="ArrayMesh" />
			<param index="0" name="existing" type="ArrayMesh" default="null" />
			<param index="1" name="flags" type="int" default="0" />
			<description>
				Returns a constructed [ArrayMesh] from current information passed in. If an existing [ArrayMesh] is passed in as an argument, will add an extra surface to the existing [ArrayMesh].
				The [param flags] argument can be the bitwise OR of [constant Mesh.ARRAY_FLAG_USE_DYNAMIC_UPDATE], [constant Mesh.ARRAY_FLAG_USE_8_BONE_WEIGHTS], or [constant Mesh.ARRAY_FLAG_USES_EMPTY_VERTEX_ARRAY].
			</description>
		</method>
		<method name="commit_to_arrays">
			<return type="Array" />
			<description>
				Commits the data to the same format used by [method ArrayMesh.add_surface_from_arrays], [method ImporterMesh.add_surface], and [method create_from_arrays]. This way you can further process the mesh data using the [ArrayMesh] or [ImporterMesh] APIs.
			</description>
		</method>
		<method name="create_from">
			<return type="void" />
			<param index="0" name="existing" type="Mesh" />
			<param index="1" name="surface" type="int" />
			<description>
				Creates a vertex array from an existing [Mesh].
			</description>
		</method>
		<method name="create_from_arrays">
			<return type="void" />
			<param index="0" name="arrays" type="Array" />
			<param index="1" name="primitive_type" type="int" enum="Mesh.PrimitiveType" default="3" />
			<description>
				Creates this SurfaceTool from existing vertex arrays such as returned by [method commit_to_arrays], [method Mesh.surface_get_arrays], [method Mesh.surface_get_blend_shape_arrays], [method ImporterMesh.get_surface_arrays], and [method ImporterMesh.get_surface_blend_shape_arrays]. [param primitive_type] controls the type of mesh data, defaulting to [constant Mesh.PRIMITIVE_TRIANGLES].
			</description>
		</method>
		<method name="create_from_blend_shape">
			<return type="void" />
			<param index="0" name="existing" type="Mesh" />
			<param index="1" name="surface" type="int" />
			<param index="2" name="blend_shape" type="String" />
			<description>
				Creates a vertex array from the specified blend shape of an existing [Mesh]. This can be used to extract a specific pose from a blend shape.
			</description>
		</method>
		<method name="deindex">
			<return type="void" />
			<description>
				Removes the index array by expanding the vertex array.
			</description>
		</method>
		<method name="generate_lod" deprecated="This method is unused internally, as it does not preserve normals or UVs. Consider using [method ImporterMesh.generate_lods] instead.">
			<return type="PackedInt32Array" />
			<param index="0" name="nd_threshold" type="float" />
			<param index="1" name="target_index_count" type="int" default="3" />
			<description>
				Generates an LOD for a given [param nd_threshold] in linear units (square root of quadric error metric), using at most [param target_index_count] indices.
			</description>
		</method>
		<method name="generate_normals">
			<return type="void" />
			<param index="0" name="flip" type="bool" default="false" />
			<description>
				Generates normals from vertices so you do not have to do it manually. If [param flip] is [code]true[/code], the resulting normals will be inverted. [method generate_normals] should be called [i]after[/i] generating geometry and [i]before[/i] committing the mesh using [method commit] or [method commit_to_arrays]. For correct display of normal-mapped surfaces, you will also have to generate tangents using [method generate_tangents].
				[b]Note:[/b] [method generate_normals] only works if the primitive type is set to [constant Mesh.PRIMITIVE_TRIANGLES].
				[b]Note:[/b] [method generate_normals] takes smooth groups into account. To generate smooth normals, set the smooth group to a value greater than or equal to [code]0[/code] using [method set_smooth_group] or leave the smooth group at the default of [code]0[/code]. To generate flat normals, set the smooth group to [code]-1[/code] using [method set_smooth_group] prior to adding vertices.
			</description>
		</method>
		<method name="generate_tangents">
			<return type="void" />
			<description>
				Generates a tangent vector for each vertex. Requires that each vertex already has UVs and normals set (see [method generate_normals]).
			</description>
		</method>
		<method name="get_aabb" qualifiers="const">
			<return type="AABB" />
			<description>
				Returns the axis-aligned bounding box of the vertex positions.
			</description>
		</method>
		<method name="get_custom_format" qualifiers="const">
			<return type="int" enum="SurfaceTool.CustomFormat" />
			<param index="0" name="channel_index" type="int" />
			<description>
				Returns the format for custom [param channel_index] (currently up to 4). Returns [constant CUSTOM_MAX] if this custom channel is unused.
			</description>
		</method>
		<method name="get_primitive_type" qualifiers="const">
			<return type="int" enum="Mesh.PrimitiveType" />
			<description>
				Returns the type of mesh geometry, such as [constant Mesh.PRIMITIVE_TRIANGLES].
			</description>
		</method>
		<method name="get_skin_weight_count" qualifiers="const">
			<return type="int" enum="SurfaceTool.SkinWeightCount" />
			<description>
				By default, returns [constant SKIN_4_WEIGHTS] to indicate only 4 bone influences per vertex are used.
				Returns [constant SKIN_8_WEIGHTS] if up to 8 influences are used.
				[b]Note:[/b] This function returns an enum, not the exact number of weights.
			</description>
		</method>
		<method name="index">
			<return type="void" />
			<description>
				Shrinks the vertex array by creating an index array. This can improve performance by avoiding vertex reuse.
			</description>
		</method>
		<method name="optimize_indices_for_cache">
			<return type="void" />
			<description>
				Optimizes triangle sorting for performance. Requires that [method get_primitive_type] is [constant Mesh.PRIMITIVE_TRIANGLES].
			</description>
		</method>
		<method name="set_bones">
			<return type="void" />
			<param index="0" name="bones" type="PackedInt32Array" />
			<description>
				Specifies an array of bones to use for the [i]next[/i] vertex. [param bones] must contain 4 integers.
			</description>
		</method>
		<method name="set_color">
			<return type="void" />
			<param index="0" name="color" type="Color" />
			<description>
				Specifies a [Color] to use for the [i]next[/i] vertex. If every vertex needs to have this information set and you fail to submit it for the first vertex, this information may not be used at all.
				[b]Note:[/b] The material must have [member BaseMaterial3D.vertex_color_use_as_albedo] enabled for the vertex color to be visible.
			</description>
		</method>
		<method name="set_custom">
			<return type="void" />
			<param index="0" name="channel_index" type="int" />
			<param index="1" name="custom_color" type="Color" />
			<description>
				Sets the custom value on this vertex for [param channel_index].
				[method set_custom_format] must be called first for this [param channel_index]. Formats which are not RGBA will ignore other color channels.
			</description>
		</method>
		<method name="set_custom_format">
			<return type="void" />
			<param index="0" name="channel_index" type="int" />
			<param index="1" name="format" type="int" enum="SurfaceTool.CustomFormat" />
			<description>
				Sets the color format for this custom [param channel_index]. Use [constant CUSTOM_MAX] to disable.
				Must be invoked after [method begin] and should be set before [method commit] or [method commit_to_arrays].
			</description>
		</method>
		<method name="set_material">
			<return type="void" />
			<param index="0" name="material" type="Material" />
			<description>
				Sets [Material] to be used by the [Mesh] you are constructing.
			</description>
		</method>
		<method name="set_normal">
			<return type="void" />
			<param index="0" name="normal" type="Vector3" />
			<description>
				Specifies a normal to use for the [i]next[/i] vertex. If every vertex needs to have this information set and you fail to submit it for the first vertex, this information may not be used at all.
			</description>
		</method>
		<method name="set_skin_weight_count">
			<return type="void" />
			<param index="0" name="count" type="int" enum="SurfaceTool.SkinWeightCount" />
			<description>
				Set to [constant SKIN_8_WEIGHTS] to indicate that up to 8 bone influences per vertex may be used.
				By default, only 4 bone influences are used ([constant SKIN_4_WEIGHTS]).
				[b]Note:[/b] This function takes an enum, not the exact number of weights.
			</description>
		</method>
		<method name="set_smooth_group">
			<return type="void" />
			<param index="0" name="index" type="int" />
			<description>
				Specifies the smooth group to use for the [i]next[/i] vertex. If this is never called, all vertices will have the default smooth group of [code]0[/code] and will be smoothed with adjacent vertices of the same group. To produce a mesh with flat normals, set the smooth group to [code]-1[/code].
				[b]Note:[/b] This function actually takes a [code]uint32_t[/code], so C# users should use [code]uint32.MaxValue[/code] instead of [code]-1[/code] to produce a mesh with flat normals.
			</description>
		</method>
		<method name="set_tangent">
			<return type="void" />
			<param index="0" name="tangent" type="Plane" />
			<description>
				Specifies a tangent to use for the [i]next[/i] vertex. If every vertex needs to have this information set and you fail to submit it for the first vertex, this information may not be used at all.
			</description>
		</method>
		<method name="set_uv">
			<return type="void" />
			<param index="0" name="uv" type="Vector2" />
			<description>
				Specifies a set of UV coordinates to use for the [i]next[/i] vertex. If every vertex needs to have this information set and you fail to submit it for the first vertex, this information may not be used at all.
			</description>
		</method>
		<method name="set_uv2">
			<return type="void" />
			<param index="0" name="uv2" type="Vector2" />
			<description>
				Specifies an optional second set of UV coordinates to use for the [i]next[/i] vertex. If every vertex needs to have this information set and you fail to submit it for the first vertex, this information may not be used at all.
			</description>
		</method>
		<method name="set_weights">
			<return type="void" />
			<param index="0" name="weights" type="PackedFloat32Array" />
			<description>
				Specifies weight values to use for the [i]next[/i] vertex. [param weights] must contain 4 values. If every vertex needs to have this information set and you fail to submit it for the first vertex, this information may not be used at all.
			</description>
		</method>
	</methods>
	<constants>
		<constant name="CUSTOM_RGBA8_UNORM" value="0" enum="CustomFormat">
			Limits range of data passed to [method set_custom] to unsigned normalized 0 to 1 stored in 8 bits per channel. See [constant Mesh.ARRAY_CUSTOM_RGBA8_UNORM].
		</constant>
		<constant name="CUSTOM_RGBA8_SNORM" value="1" enum="CustomFormat">
			Limits range of data passed to [method set_custom] to signed normalized -1 to 1 stored in 8 bits per channel. See [constant Mesh.ARRAY_CUSTOM_RGBA8_SNORM].
		</constant>
		<constant name="CUSTOM_RG_HALF" value="2" enum="CustomFormat">
			Stores data passed to [method set_custom] as half precision floats, and uses only red and green color channels. See [constant Mesh.ARRAY_CUSTOM_RG_HALF].
		</constant>
		<constant name="CUSTOM_RGBA_HALF" value="3" enum="CustomFormat">
			Stores data passed to [method set_custom] as half precision floats and uses all color channels. See [constant Mesh.ARRAY_CUSTOM_RGBA_HALF].
		</constant>
		<constant name="CUSTOM_R_FLOAT" value="4" enum="CustomFormat">
			Stores data passed to [method set_custom] as full precision floats, and uses only red color channel. See [constant Mesh.ARRAY_CUSTOM_R_FLOAT].
		</constant>
		<constant name="CUSTOM_RG_FLOAT" value="5" enum="CustomFormat">
			Stores data passed to [method set_custom] as full precision floats, and uses only red and green color channels. See [constant Mesh.ARRAY_CUSTOM_RG_FLOAT].
		</constant>
		<constant name="CUSTOM_RGB_FLOAT" value="6" enum="CustomFormat">
			Stores data passed to [method set_custom] as full precision floats, and uses only red, green and blue color channels. See [constant Mesh.ARRAY_CUSTOM_RGB_FLOAT].
		</constant>
		<constant name="CUSTOM_RGBA_FLOAT" value="7" enum="CustomFormat">
			Stores data passed to [method set_custom] as full precision floats, and uses all color channels. See [constant Mesh.ARRAY_CUSTOM_RGBA_FLOAT].
		</constant>
		<constant name="CUSTOM_MAX" value="8" enum="CustomFormat">
			Used to indicate a disabled custom channel.
		</constant>
		<constant name="SKIN_4_WEIGHTS" value="0" enum="SkinWeightCount">
			Each individual vertex can be influenced by only 4 bone weights.
		</constant>
		<constant name="SKIN_8_WEIGHTS" value="1" enum="SkinWeightCount">
			Each individual vertex can be influenced by up to 8 bone weights.
		</constant>
	</constants>
</class>
