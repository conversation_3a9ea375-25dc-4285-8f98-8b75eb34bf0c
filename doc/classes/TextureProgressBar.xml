<?xml version="1.0" encoding="UTF-8" ?>
<class name="TextureProgressBar" inherits="Range" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Texture-based progress bar. Useful for loading screens and life or stamina bars.
	</brief_description>
	<description>
		TextureProgressBar works like [ProgressBar], but uses up to 3 textures instead of Redot's [Theme] resource. It can be used to create horizontal, vertical and radial progress bars.
	</description>
	<tutorials>
	</tutorials>
	<methods>
		<method name="get_stretch_margin" qualifiers="const">
			<return type="int" />
			<param index="0" name="margin" type="int" enum="Side" />
			<description>
				Returns the stretch margin with the specified index. See [member stretch_margin_bottom] and related properties.
			</description>
		</method>
		<method name="set_stretch_margin">
			<return type="void" />
			<param index="0" name="margin" type="int" enum="Side" />
			<param index="1" name="value" type="int" />
			<description>
				Sets the stretch margin with the specified index. See [member stretch_margin_bottom] and related properties.
			</description>
		</method>
	</methods>
	<members>
		<member name="fill_mode" type="int" setter="set_fill_mode" getter="get_fill_mode" default="0">
			The fill direction. See [enum FillMode] for possible values.
		</member>
		<member name="mouse_filter" type="int" setter="set_mouse_filter" getter="get_mouse_filter" overrides="Control" enum="Control.MouseFilter" default="1" />
		<member name="nine_patch_stretch" type="bool" setter="set_nine_patch_stretch" getter="get_nine_patch_stretch" default="false">
			If [code]true[/code], Redot treats the bar's textures like in [NinePatchRect]. Use the [code]stretch_margin_*[/code] properties like [member stretch_margin_bottom] to set up the nine patch's 3×3 grid. When using a radial [member fill_mode], this setting will only enable stretching for [member texture_progress], while [member texture_under] and [member texture_over] will be treated like in [NinePatchRect].
		</member>
		<member name="radial_center_offset" type="Vector2" setter="set_radial_center_offset" getter="get_radial_center_offset" default="Vector2(0, 0)">
			Offsets [member texture_progress] if [member fill_mode] is [constant FILL_CLOCKWISE], [constant FILL_COUNTER_CLOCKWISE], or [constant FILL_CLOCKWISE_AND_COUNTER_CLOCKWISE].
			[b]Note:[/b] The effective radial center always stays within the [member texture_progress] bounds. If you need to move it outside the texture's bounds, modify the [member texture_progress] to contain additional empty space where needed.
		</member>
		<member name="radial_fill_degrees" type="float" setter="set_fill_degrees" getter="get_fill_degrees" default="360.0">
			Upper limit for the fill of [member texture_progress] if [member fill_mode] is [constant FILL_CLOCKWISE], [constant FILL_COUNTER_CLOCKWISE], or [constant FILL_CLOCKWISE_AND_COUNTER_CLOCKWISE]. When the node's [code]value[/code] is equal to its [code]max_value[/code], the texture fills up to this angle.
			See [member Range.value], [member Range.max_value].
		</member>
		<member name="radial_initial_angle" type="float" setter="set_radial_initial_angle" getter="get_radial_initial_angle" default="0.0">
			Starting angle for the fill of [member texture_progress] if [member fill_mode] is [constant FILL_CLOCKWISE], [constant FILL_COUNTER_CLOCKWISE], or [constant FILL_CLOCKWISE_AND_COUNTER_CLOCKWISE]. When the node's [code]value[/code] is equal to its [code]min_value[/code], the texture doesn't show up at all. When the [code]value[/code] increases, the texture fills and tends towards [member radial_fill_degrees].
			[b]Note:[/b] [member radial_initial_angle] is wrapped between [code]0[/code] and [code]360[/code] degrees (inclusive).
		</member>
		<member name="size_flags_vertical" type="int" setter="set_v_size_flags" getter="get_v_size_flags" overrides="Control" enum="Control.SizeFlags" is_bitfield="true" default="1" />
		<member name="step" type="float" setter="set_step" getter="get_step" overrides="Range" default="1.0" />
		<member name="stretch_margin_bottom" type="int" setter="set_stretch_margin" getter="get_stretch_margin" default="0">
			The height of the 9-patch's bottom row. A margin of 16 means the 9-slice's bottom corners and side will have a height of 16 pixels. You can set all 4 margin values individually to create panels with non-uniform borders. Only effective if [member nine_patch_stretch] is [code]true[/code].
		</member>
		<member name="stretch_margin_left" type="int" setter="set_stretch_margin" getter="get_stretch_margin" default="0">
			The width of the 9-patch's left column. Only effective if [member nine_patch_stretch] is [code]true[/code].
		</member>
		<member name="stretch_margin_right" type="int" setter="set_stretch_margin" getter="get_stretch_margin" default="0">
			The width of the 9-patch's right column. Only effective if [member nine_patch_stretch] is [code]true[/code].
		</member>
		<member name="stretch_margin_top" type="int" setter="set_stretch_margin" getter="get_stretch_margin" default="0">
			The height of the 9-patch's top row. Only effective if [member nine_patch_stretch] is [code]true[/code].
		</member>
		<member name="texture_over" type="Texture2D" setter="set_over_texture" getter="get_over_texture">
			[Texture2D] that draws over the progress bar. Use it to add highlights or an upper-frame that hides part of [member texture_progress].
		</member>
		<member name="texture_progress" type="Texture2D" setter="set_progress_texture" getter="get_progress_texture">
			[Texture2D] that clips based on the node's [code]value[/code] and [member fill_mode]. As [code]value[/code] increased, the texture fills up. It shows entirely when [code]value[/code] reaches [code]max_value[/code]. It doesn't show at all if [code]value[/code] is equal to [code]min_value[/code].
			The [code]value[/code] property comes from [Range]. See [member Range.value], [member Range.min_value], [member Range.max_value].
		</member>
		<member name="texture_progress_offset" type="Vector2" setter="set_texture_progress_offset" getter="get_texture_progress_offset" default="Vector2(0, 0)">
			The offset of [member texture_progress]. Useful for [member texture_over] and [member texture_under] with fancy borders, to avoid transparent margins in your progress texture.
		</member>
		<member name="texture_under" type="Texture2D" setter="set_under_texture" getter="get_under_texture">
			[Texture2D] that draws under the progress bar. The bar's background.
		</member>
		<member name="tint_over" type="Color" setter="set_tint_over" getter="get_tint_over" default="Color(1, 1, 1, 1)" keywords="color, colour">
			Multiplies the color of the bar's [member texture_over] texture. The effect is similar to [member CanvasItem.modulate], except it only affects this specific texture instead of the entire node.
		</member>
		<member name="tint_progress" type="Color" setter="set_tint_progress" getter="get_tint_progress" default="Color(1, 1, 1, 1)" keywords="color, colour">
			Multiplies the color of the bar's [member texture_progress] texture.
		</member>
		<member name="tint_under" type="Color" setter="set_tint_under" getter="get_tint_under" default="Color(1, 1, 1, 1)" keywords="color, colour">
			Multiplies the color of the bar's [member texture_under] texture.
		</member>
	</members>
	<constants>
		<constant name="FILL_LEFT_TO_RIGHT" value="0" enum="FillMode">
			The [member texture_progress] fills from left to right.
		</constant>
		<constant name="FILL_RIGHT_TO_LEFT" value="1" enum="FillMode">
			The [member texture_progress] fills from right to left.
		</constant>
		<constant name="FILL_TOP_TO_BOTTOM" value="2" enum="FillMode">
			The [member texture_progress] fills from top to bottom.
		</constant>
		<constant name="FILL_BOTTOM_TO_TOP" value="3" enum="FillMode">
			The [member texture_progress] fills from bottom to top.
		</constant>
		<constant name="FILL_CLOCKWISE" value="4" enum="FillMode">
			Turns the node into a radial bar. The [member texture_progress] fills clockwise. See [member radial_center_offset], [member radial_initial_angle] and [member radial_fill_degrees] to control the way the bar fills up.
		</constant>
		<constant name="FILL_COUNTER_CLOCKWISE" value="5" enum="FillMode">
			Turns the node into a radial bar. The [member texture_progress] fills counterclockwise. See [member radial_center_offset], [member radial_initial_angle] and [member radial_fill_degrees] to control the way the bar fills up.
		</constant>
		<constant name="FILL_BILINEAR_LEFT_AND_RIGHT" value="6" enum="FillMode">
			The [member texture_progress] fills from the center, expanding both towards the left and the right.
		</constant>
		<constant name="FILL_BILINEAR_TOP_AND_BOTTOM" value="7" enum="FillMode">
			The [member texture_progress] fills from the center, expanding both towards the top and the bottom.
		</constant>
		<constant name="FILL_CLOCKWISE_AND_COUNTER_CLOCKWISE" value="8" enum="FillMode">
			Turns the node into a radial bar. The [member texture_progress] fills radially from the center, expanding both clockwise and counterclockwise. See [member radial_center_offset], [member radial_initial_angle] and [member radial_fill_degrees] to control the way the bar fills up.
		</constant>
	</constants>
</class>
