<?xml version="1.0" encoding="UTF-8" ?>
<class name="RDShaderSource" inherits="RefCounted" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Shader source code (used by [RenderingDevice]).
	</brief_description>
	<description>
		Shader source code in text form.
		See also [RDShaderFile]. [RDShaderSource] is only meant to be used with the [RenderingDevice] API. It should not be confused with Redot's own [Shader] resource, which is what Redot's various nodes use for high-level shader programming.
	</description>
	<tutorials>
	</tutorials>
	<methods>
		<method name="get_stage_source" qualifiers="const">
			<return type="String" />
			<param index="0" name="stage" type="int" enum="RenderingDevice.ShaderStage" />
			<description>
				Returns source code for the specified shader [param stage]. Equivalent to getting one of [member source_compute], [member source_fragment], [member source_tesselation_control], [member source_tesselation_evaluation] or [member source_vertex].
			</description>
		</method>
		<method name="set_stage_source">
			<return type="void" />
			<param index="0" name="stage" type="int" enum="RenderingDevice.ShaderStage" />
			<param index="1" name="source" type="String" />
			<description>
				Sets [param source] code for the specified shader [param stage]. Equivalent to setting one of [member source_compute], [member source_fragment], [member source_tesselation_control], [member source_tesselation_evaluation] or [member source_vertex].
				[b]Note:[/b] If you set the compute shader source code using this method directly, remember to remove the Redot-specific hint [code]#[compute][/code].
			</description>
		</method>
	</methods>
	<members>
		<member name="language" type="int" setter="set_language" getter="get_language" enum="RenderingDevice.ShaderLanguage" default="0">
			The language the shader is written in.
		</member>
		<member name="source_compute" type="String" setter="set_stage_source" getter="get_stage_source" default="&quot;&quot;">
			Source code for the shader's compute stage.
		</member>
		<member name="source_fragment" type="String" setter="set_stage_source" getter="get_stage_source" default="&quot;&quot;">
			Source code for the shader's fragment stage.
		</member>
		<member name="source_tesselation_control" type="String" setter="set_stage_source" getter="get_stage_source" default="&quot;&quot;">
			Source code for the shader's tessellation control stage.
		</member>
		<member name="source_tesselation_evaluation" type="String" setter="set_stage_source" getter="get_stage_source" default="&quot;&quot;">
			Source code for the shader's tessellation evaluation stage.
		</member>
		<member name="source_vertex" type="String" setter="set_stage_source" getter="get_stage_source" default="&quot;&quot;">
			Source code for the shader's vertex stage.
		</member>
	</members>
</class>
