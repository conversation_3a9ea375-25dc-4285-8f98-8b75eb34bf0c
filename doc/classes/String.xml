<?xml version="1.0" encoding="UTF-8" ?>
<class name="String" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A built-in type for strings.
	</brief_description>
	<description>
		This is the built-in string Variant type (and the one used by GDScript). Strings may contain any number of Unicode characters, and expose methods useful for manipulating and generating strings. Strings are reference-counted and use a copy-on-write approach (every modification to a string returns a new [String]), so passing them around is cheap in resources.
		Some string methods have corresponding variations. Variations suffixed with [code]n[/code] ([method countn], [method findn], [method replacen], etc.) are [b]case-insensitive[/b] (they make no distinction between uppercase and lowercase letters). Method variations prefixed with [code]r[/code] ([method rfind], [method rsplit], etc.) are reversed, and start from the end of the string, instead of the beginning.
		To convert any [Variant] to or from a string, see [method @GlobalScope.str], [method @GlobalScope.str_to_var], and [method @GlobalScope.var_to_str].
		[b]Note:[/b] In a boolean context, a string will evaluate to [code]false[/code] if it is empty ([code]""[/code]). Otherwise, a string will always evaluate to [code]true[/code].
	</description>
	<tutorials>
		<link title="GDScript format strings">$DOCS_URL/tutorials/scripting/gdscript/gdscript_format_string.html</link>
	</tutorials>
	<constructors>
		<constructor name="String">
			<return type="String" />
			<description>
				Constructs an empty [String] ([code]""[/code]).
			</description>
		</constructor>
		<constructor name="String">
			<return type="String" />
			<param index="0" name="from" type="String" />
			<description>
				Constructs a [String] as a copy of the given [String].
			</description>
		</constructor>
		<constructor name="String">
			<return type="String" />
			<param index="0" name="from" type="NodePath" />
			<description>
				Constructs a new [String] from the given [NodePath].
			</description>
		</constructor>
		<constructor name="String">
			<return type="String" />
			<param index="0" name="from" type="StringName" />
			<description>
				Constructs a new [String] from the given [StringName].
			</description>
		</constructor>
	</constructors>
	<methods>
		<method name="begins_with" qualifiers="const" keywords="starts_with">
			<return type="bool" />
			<param index="0" name="text" type="String" />
			<description>
				Returns [code]true[/code] if the string begins with the given [param text]. See also [method ends_with].
			</description>
		</method>
		<method name="bigrams" qualifiers="const">
			<return type="PackedStringArray" />
			<description>
				Returns an array containing the bigrams (pairs of consecutive characters) of this string.
				[codeblock]
				print("Get up!".bigrams()) # Prints ["Ge", "et", "t ", " u", "up", "p!"]
				[/codeblock]
			</description>
		</method>
		<method name="bin_to_int" qualifiers="const">
			<return type="int" />
			<description>
				Converts the string representing a binary number into an [int]. The string may optionally be prefixed with [code]"0b"[/code], and an additional [code]-[/code] prefix for negative numbers.
				[codeblocks]
				[gdscript]
				print("101".bin_to_int())   # Prints 5
				print("0b101".bin_to_int()) # Prints 5
				print("-0b10".bin_to_int()) # Prints -2
				[/gdscript]
				[csharp]
				GD.Print("101".BinToInt());   // Prints 5
				GD.Print("0b101".BinToInt()); // Prints 5
				GD.Print("-0b10".BinToInt()); // Prints -2
				[/csharp]
				[/codeblocks]
			</description>
		</method>
		<method name="c_escape" qualifiers="const">
			<return type="String" />
			<description>
				Returns a copy of the string with special characters escaped using the C language standard.
			</description>
		</method>
		<method name="c_unescape" qualifiers="const">
			<return type="String" />
			<description>
				Returns a copy of the string with escaped characters replaced by their meanings. Supported escape sequences are [code]\'[/code], [code]\"[/code], [code]\\[/code], [code]\a[/code], [code]\b[/code], [code]\f[/code], [code]\n[/code], [code]\r[/code], [code]\t[/code], [code]\v[/code].
				[b]Note:[/b] Unlike the GDScript parser, this method doesn't support the [code]\uXXXX[/code] escape sequence.
			</description>
		</method>
		<method name="capitalize" qualifiers="const">
			<return type="String" />
			<description>
				Changes the appearance of the string: replaces underscores ([code]_[/code]) with spaces, adds spaces before uppercase letters in the middle of a word, converts all letters to lowercase, then converts the first one and each one following a space to uppercase.
				[codeblocks]
				[gdscript]
				"move_local_x".capitalize()   # Returns "Move Local X"
				"sceneFile_path".capitalize() # Returns "Scene File Path"
				"2D, FPS, PNG".capitalize()   # Returns "2d, Fps, Png"
				[/gdscript]
				[csharp]
				"move_local_x".Capitalize();   // Returns "Move Local X"
				"sceneFile_path".Capitalize(); // Returns "Scene File Path"
				"2D, FPS, PNG".Capitalize();   // Returns "2d, Fps, Png"
				[/csharp]
				[/codeblocks]
			</description>
		</method>
		<method name="casecmp_to" qualifiers="const">
			<return type="int" />
			<param index="0" name="to" type="String" />
			<description>
				Performs a case-sensitive comparison to another string. Returns [code]-1[/code] if less than, [code]1[/code] if greater than, or [code]0[/code] if equal. "Less than" and "greater than" are determined by the [url=https://en.wikipedia.org/wiki/List_of_Unicode_characters]Unicode code points[/url] of each string, which roughly matches the alphabetical order.
				If the character comparison reaches the end of one string, but the other string contains more characters, then it will use length as the deciding factor: [code]1[/code] will be returned if this string is longer than the [param to] string, or [code]-1[/code] if shorter. Note that the length of empty strings is always [code]0[/code].
				To get a [bool] result from a string comparison, use the [code]==[/code] operator instead. See also [method nocasecmp_to], [method filecasecmp_to], and [method naturalcasecmp_to].
			</description>
		</method>
		<method name="chr" qualifiers="static">
			<return type="String" />
			<param index="0" name="char" type="int" />
			<description>
				Returns a single Unicode character from the decimal [param char]. You may use [url=https://unicodelookup.com/]unicodelookup.com[/url] or [url=https://www.unicode.org/charts/]unicode.org[/url] as points of reference.
				[codeblock]
				print(String.chr(65))     # Prints "A"
				print(String.chr(129302)) # Prints "🤖" (robot face emoji)
				[/codeblock]
			</description>
		</method>
		<method name="contains" qualifiers="const">
			<return type="bool" />
			<param index="0" name="what" type="String" />
			<description>
				Returns [code]true[/code] if the string contains [param what]. In GDScript, this corresponds to the [code]in[/code] operator.
				[codeblocks]
				[gdscript]
				print("Node".contains("de")) # Prints true
				print("team".contains("I"))  # Prints false
				print("I" in "team")         # Prints false
				[/gdscript]
				[csharp]
				GD.Print("Node".Contains("de")); // Prints True
				GD.Print("team".Contains("I"));  // Prints False
				[/csharp]
				[/codeblocks]
				If you need to know where [param what] is within the string, use [method find]. See also [method containsn].
			</description>
		</method>
		<method name="containsn" qualifiers="const">
			<return type="bool" />
			<param index="0" name="what" type="String" />
			<description>
				Returns [code]true[/code] if the string contains [param what], [b]ignoring case[/b].
				If you need to know where [param what] is within the string, use [method findn]. See also [method contains].
			</description>
		</method>
		<method name="count" qualifiers="const">
			<return type="int" />
			<param index="0" name="what" type="String" />
			<param index="1" name="from" type="int" default="0" />
			<param index="2" name="to" type="int" default="0" />
			<description>
				Returns the number of occurrences of the substring [param what] between [param from] and [param to] positions. If [param to] is 0, the search continues until the end of the string.
			</description>
		</method>
		<method name="countn" qualifiers="const">
			<return type="int" />
			<param index="0" name="what" type="String" />
			<param index="1" name="from" type="int" default="0" />
			<param index="2" name="to" type="int" default="0" />
			<description>
				Returns the number of occurrences of the substring [param what] between [param from] and [param to] positions, [b]ignoring case[/b]. If [param to] is 0, the search continues until the end of the string.
			</description>
		</method>
		<method name="dedent" qualifiers="const">
			<return type="String" />
			<description>
				Returns a copy of the string with indentation (leading tabs and spaces) removed. See also [method indent] to add indentation.
			</description>
		</method>
		<method name="ends_with" qualifiers="const">
			<return type="bool" />
			<param index="0" name="text" type="String" />
			<description>
				Returns [code]true[/code] if the string ends with the given [param text]. See also [method begins_with].
			</description>
		</method>
		<method name="erase" qualifiers="const">
			<return type="String" />
			<param index="0" name="position" type="int" />
			<param index="1" name="chars" type="int" default="1" />
			<description>
				Returns a string with [param chars] characters erased starting from [param position]. If [param chars] goes beyond the string's length given the specified [param position], fewer characters will be erased from the returned string. Returns an empty string if either [param position] or [param chars] is negative. Returns the original string unmodified if [param chars] is [code]0[/code].
			</description>
		</method>
		<method name="filecasecmp_to" qualifiers="const">
			<return type="int" />
			<param index="0" name="to" type="String" />
			<description>
				Like [method naturalcasecmp_to] but prioritizes strings that begin with periods ([code].[/code]) and underscores ([code]_[/code]) before any other character. Useful when sorting folders or file names.
				To get a [bool] result from a string comparison, use the [code]==[/code] operator instead. See also [method filenocasecmp_to], [method naturalcasecmp_to], and [method casecmp_to].
			</description>
		</method>
		<method name="filenocasecmp_to" qualifiers="const">
			<return type="int" />
			<param index="0" name="to" type="String" />
			<description>
				Like [method naturalnocasecmp_to] but prioritizes strings that begin with periods ([code].[/code]) and underscores ([code]_[/code]) before any other character. Useful when sorting folders or file names.
				To get a [bool] result from a string comparison, use the [code]==[/code] operator instead. See also [method filecasecmp_to], [method naturalnocasecmp_to], and [method nocasecmp_to].
			</description>
		</method>
		<method name="find" qualifiers="const">
			<return type="int" />
			<param index="0" name="what" type="String" />
			<param index="1" name="from" type="int" default="0" />
			<description>
				Returns the index of the [b]first[/b] occurrence of [param what] in this string, or [code]-1[/code] if there are none. The search's start can be specified with [param from], continuing to the end of the string.
				[codeblocks]
				[gdscript]
				print("Team".find("I")) # Prints -1

				print("Potato".find("t"))    # Prints 2
				print("Potato".find("t", 3)) # Prints 4
				print("Potato".find("t", 5)) # Prints -1
				[/gdscript]
				[csharp]
				GD.Print("Team".Find("I")); // Prints -1

				GD.Print("Potato".Find("t"));    // Prints 2
				GD.Print("Potato".Find("t", 3)); // Prints 4
				GD.Print("Potato".Find("t", 5)); // Prints -1
				[/csharp]
				[/codeblocks]
				[b]Note:[/b] If you just want to know whether the string contains [param what], use [method contains]. In GDScript, you may also use the [code]in[/code] operator.
			</description>
		</method>
		<method name="findn" qualifiers="const">
			<return type="int" />
			<param index="0" name="what" type="String" />
			<param index="1" name="from" type="int" default="0" />
			<description>
				Returns the index of the [b]first[/b] [b]case-insensitive[/b] occurrence of [param what] in this string, or [code]-1[/code] if there are none. The starting search index can be specified with [param from], continuing to the end of the string.
			</description>
		</method>
		<method name="format" qualifiers="const">
			<return type="String" />
			<param index="0" name="values" type="Variant" />
			<param index="1" name="placeholder" type="String" default="&quot;{_}&quot;" />
			<description>
				Formats the string by replacing all occurrences of [param placeholder] with the elements of [param values].
				[param values] can be a [Dictionary], an [Array], or an [Object]. Any underscores in [param placeholder] will be replaced with the corresponding keys in advance. Array elements use their index as keys.
				[codeblock]
				# Prints "Waiting for Redot is a play by Samuel Beckett, and Redot Engine is named after it."
				var use_array_values = "Waiting for {0} is a play by {1}, and {0} Engine is named after it."
				print(use_array_values.format(["Redot", "Samuel Beckett"]))

				# Prints "User 42 is Redot."
				print("User {id} is {name}.".format({"id": 42, "name": "Redot"}))
				[/codeblock]
				Some additional handling is performed when [param values] is an [Array]. If [param placeholder] does not contain an underscore, the elements of the [param values] array will be used to replace one occurrence of the placeholder in order; If an element of [param values] is another 2-element array, it'll be interpreted as a key-value pair.
				[codeblock]
				# Prints "User 42 is Redot."
				print("User {} is {}.".format([42, "Redot"], "{}"))
				print("User {id} is {name}.".format([["id", 42], ["name", "Redot"]]))
				[/codeblock]
				When passing an [Object], the property names from [method Object.get_property_list] are used as keys.
				[codeblock]
				# Prints "Visible true, position (0, 0)"
				var node = Node2D.new()
				print("Visible {visible}, position {position}".format(node))
				[/codeblock]
				See also the [url=$DOCS_URL/tutorials/scripting/gdscript/gdscript_format_string.html]GDScript format string[/url] tutorial.
				[b]Note:[/b] Each replacement is done sequentially for each element of [param values], [b]not[/b] all at once. This means that if any element is inserted and it contains another placeholder, it may be changed by the next replacement. While this can be very useful, it often causes unexpected results. If not necessary, make sure [param values]'s elements do not contain placeholders.
				[codeblock]
				print("{0} {1}".format(["{1}", "x"]))           # Prints "x x"
				print("{0} {1}".format(["x", "{0}"]))           # Prints "x {0}"
				print("{a} {b}".format({"a": "{b}", "b": "c"})) # Prints "c c"
				print("{a} {b}".format({"b": "c", "a": "{b}"})) # Prints "{b} c"
				[/codeblock]
				[b]Note:[/b] In C#, it's recommended to [url=https://learn.microsoft.com/en-us/dotnet/csharp/language-reference/tokens/interpolated]interpolate strings with "$"[/url], instead.
			</description>
		</method>
		<method name="get_base_dir" qualifiers="const">
			<return type="String" />
			<description>
				If the string is a valid file path, returns the base directory name.
				[codeblock]
				var dir_path = "/path/to/file.txt".get_base_dir() # dir_path is "/path/to"
				[/codeblock]
			</description>
		</method>
		<method name="get_basename" qualifiers="const">
			<return type="String" />
			<description>
				If the string is a valid file path, returns the full file path, without the extension.
				[codeblock]
				var base = "/path/to/file.txt".get_basename() # base is "/path/to/file"
				[/codeblock]
			</description>
		</method>
		<method name="get_extension" qualifiers="const">
			<return type="String" />
			<description>
				If the string is a valid file name or path, returns the file extension without the leading period ([code].[/code]). Otherwise, returns an empty string.
				[codeblock]
				var a = "/path/to/file.txt".get_extension() # a is "txt"
				var b = "cool.txt".get_extension()          # b is "txt"
				var c = "cool.font.tres".get_extension()    # c is "tres"
				var d = ".pack1".get_extension()            # d is "pack1"

				var e = "file.txt.".get_extension()  # e is ""
				var f = "file.txt..".get_extension() # f is ""
				var g = "txt".get_extension()        # g is ""
				var h = "".get_extension()           # h is ""
				[/codeblock]
			</description>
		</method>
		<method name="get_file" qualifiers="const">
			<return type="String" />
			<description>
				If the string is a valid file path, returns the file name, including the extension.
				[codeblock]
				var file = "/path/to/icon.png".get_file() # file is "icon.png"
				[/codeblock]
			</description>
		</method>
		<method name="get_slice" qualifiers="const">
			<return type="String" />
			<param index="0" name="delimiter" type="String" />
			<param index="1" name="slice" type="int" />
			<description>
				Splits the string using a [param delimiter] and returns the substring at index [param slice]. Returns the original string if [param delimiter] does not occur in the string. Returns an empty string if the [param slice] does not exist.
				This is faster than [method split], if you only need one substring.
				[codeblock]
				print("i/am/example/hi".get_slice("/", 2)) # Prints "example"
				[/codeblock]
			</description>
		</method>
		<method name="get_slice_count" qualifiers="const">
			<return type="int" />
			<param index="0" name="delimiter" type="String" />
			<description>
				Returns the total number of slices when the string is split with the given [param delimiter] (see [method split]).
			</description>
		</method>
		<method name="get_slicec" qualifiers="const">
			<return type="String" />
			<param index="0" name="delimiter" type="int" />
			<param index="1" name="slice" type="int" />
			<description>
				Splits the string using a Unicode character with code [param delimiter] and returns the substring at index [param slice]. Returns an empty string if the [param slice] does not exist.
				This is faster than [method split], if you only need one substring.
			</description>
		</method>
		<method name="hash" qualifiers="const">
			<return type="int" />
			<description>
				Returns the 32-bit hash value representing the string's contents.
				[b]Note:[/b] Strings with equal hash values are [i]not[/i] guaranteed to be the same, as a result of hash collisions. On the contrary, strings with different hash values are guaranteed to be different.
			</description>
		</method>
		<method name="hex_decode" qualifiers="const">
			<return type="PackedByteArray" />
			<description>
				Decodes a hexadecimal string as a [PackedByteArray].
				[codeblocks]
				[gdscript]
				var text = "hello world"
				var encoded = text.to_utf8_buffer().hex_encode() # outputs "68656c6c6f20776f726c64"
				print(encoded.hex_decode().get_string_from_utf8())
				[/gdscript]
				[csharp]
				var text = "hello world";
				var encoded = text.ToUtf8Buffer().HexEncode(); // outputs "68656c6c6f20776f726c64"
				GD.Print(encoded.HexDecode().GetStringFromUtf8());
				[/csharp]
				[/codeblocks]
			</description>
		</method>
		<method name="hex_to_int" qualifiers="const">
			<return type="int" />
			<description>
				Converts the string representing a hexadecimal number into an [int]. The string may be optionally prefixed with [code]"0x"[/code], and an additional [code]-[/code] prefix for negative numbers.
				[codeblocks]
				[gdscript]
				print("0xff".hex_to_int()) # Prints 255
				print("ab".hex_to_int())   # Prints 171
				[/gdscript]
				[csharp]
				GD.Print("0xff".HexToInt()); // Prints 255
				GD.Print("ab".HexToInt());   // Prints 171
				[/csharp]
				[/codeblocks]
			</description>
		</method>
		<method name="humanize_size" qualifiers="static">
			<return type="String" />
			<param index="0" name="size" type="int" />
			<description>
				Converts [param size] which represents a number of bytes into a human-readable form.
				The result is in [url=https://en.wikipedia.org/wiki/Binary_prefix#IEC_prefixes]IEC prefix format[/url], which may end in either [code]"B"[/code], [code]"KiB"[/code], [code]"MiB"[/code], [code]"GiB"[/code], [code]"TiB"[/code], [code]"PiB"[/code], or [code]"EiB"[/code].
			</description>
		</method>
		<method name="indent" qualifiers="const">
			<return type="String" />
			<param index="0" name="prefix" type="String" />
			<description>
				Indents every line of the string with the given [param prefix]. Empty lines are not indented. See also [method dedent] to remove indentation.
				For example, the string can be indented with two tabulations using [code]"\t\t"[/code], or four spaces using [code]"    "[/code].
			</description>
		</method>
		<method name="insert" qualifiers="const">
			<return type="String" />
			<param index="0" name="position" type="int" />
			<param index="1" name="what" type="String" />
			<description>
				Inserts [param what] at the given [param position] in the string.
			</description>
		</method>
		<method name="is_absolute_path" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if the string is a path to a file or directory, and its starting point is explicitly defined. This method is the opposite of [method is_relative_path].
				This includes all paths starting with [code]"res://"[/code], [code]"user://"[/code], [code]"C:\"[/code], [code]"/"[/code], etc.
			</description>
		</method>
		<method name="is_empty" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if the string's length is [code]0[/code] ([code]""[/code]). See also [method length].
			</description>
		</method>
		<method name="is_relative_path" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if the string is a path, and its starting point is dependent on context. The path could begin from the current directory, or the current [Node] (if the string is derived from a [NodePath]), and may sometimes be prefixed with [code]"./"[/code]. This method is the opposite of [method is_absolute_path].
			</description>
		</method>
		<method name="is_subsequence_of" qualifiers="const">
			<return type="bool" />
			<param index="0" name="text" type="String" />
			<description>
				Returns [code]true[/code] if all characters of this string can be found in [param text] in their original order.
				[codeblock]
				var text = "Wow, incredible!"

				print("inedible".is_subsequence_of(text)) # Prints true
				print("Word!".is_subsequence_of(text))    # Prints true
				print("Window".is_subsequence_of(text))   # Prints false
				print("".is_subsequence_of(text))         # Prints true
				[/codeblock]
			</description>
		</method>
		<method name="is_subsequence_ofn" qualifiers="const">
			<return type="bool" />
			<param index="0" name="text" type="String" />
			<description>
				Returns [code]true[/code] if all characters of this string can be found in [param text] in their original order, [b]ignoring case[/b].
			</description>
		</method>
		<method name="is_valid_ascii_identifier" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if this string is a valid ASCII identifier. A valid ASCII identifier may contain only letters, digits, and underscores ([code]_[/code]), and the first character may not be a digit.
				[codeblock]
				print("node_2d".is_valid_ascii_identifier())    # Prints true
				print("TYPE_FLOAT".is_valid_ascii_identifier()) # Prints true
				print("1st_method".is_valid_ascii_identifier()) # Prints false
				print("MyMethod#2".is_valid_ascii_identifier()) # Prints false
				[/codeblock]
				See also [method is_valid_unicode_identifier].
			</description>
		</method>
		<method name="is_valid_filename" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if this string is a valid file name. A valid file name cannot be empty, begin or end with space characters, or contain characters that are not allowed ([code]:[/code] [code]/[/code] [code]\[/code] [code]?[/code] [code]*[/code] [code]"[/code] [code]|[/code] [code]%[/code] [code]&lt;[/code] [code]&gt;[/code]).
			</description>
		</method>
		<method name="is_valid_float" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if this string represents a valid floating-point number. A valid float may contain only digits, one decimal point ([code].[/code]), and the exponent letter ([code]e[/code]). It may also be prefixed with a positive ([code]+[/code]) or negative ([code]-[/code]) sign. Any valid integer is also a valid float (see [method is_valid_int]). See also [method to_float].
				[codeblock]
				print("1.7".is_valid_float())   # Prints true
				print("24".is_valid_float())    # Prints true
				print("7e3".is_valid_float())   # Prints true
				print("Hello".is_valid_float()) # Prints false
				[/codeblock]
			</description>
		</method>
		<method name="is_valid_hex_number" qualifiers="const">
			<return type="bool" />
			<param index="0" name="with_prefix" type="bool" default="false" />
			<description>
				Returns [code]true[/code] if this string is a valid hexadecimal number. A valid hexadecimal number only contains digits or letters [code]A[/code] to [code]F[/code] (either uppercase or lowercase), and may be prefixed with a positive ([code]+[/code]) or negative ([code]-[/code]) sign.
				If [param with_prefix] is [code]true[/code], the hexadecimal number needs to prefixed by [code]"0x"[/code] to be considered valid.
				[codeblock]
				print("A08E".is_valid_hex_number())    # Prints true
				print("-AbCdEf".is_valid_hex_number()) # Prints true
				print("2.5".is_valid_hex_number())     # Prints false

				print("0xDEADC0DE".is_valid_hex_number(true)) # Prints true
				[/codeblock]
			</description>
		</method>
		<method name="is_valid_html_color" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if this string is a valid color in hexadecimal HTML notation. The string must be a hexadecimal value (see [method is_valid_hex_number]) of either 3, 4, 6 or 8 digits, and may be prefixed by a hash sign ([code]#[/code]). Other HTML notations for colors, such as names or [code]hsl()[/code], are not considered valid. See also [method Color.html].
			</description>
		</method>
		<method name="is_valid_identifier" qualifiers="const" deprecated="Use [method is_valid_ascii_identifier] instead.">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if this string is a valid identifier. A valid identifier may contain only letters, digits and underscores ([code]_[/code]), and the first character may not be a digit.
				[codeblock]
				print("node_2d".is_valid_identifier())    # Prints true
				print("TYPE_FLOAT".is_valid_identifier()) # Prints true
				print("1st_method".is_valid_identifier()) # Prints false
				print("MyMethod#2".is_valid_identifier()) # Prints false
				[/codeblock]
			</description>
		</method>
		<method name="is_valid_int" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if this string represents a valid integer. A valid integer only contains digits, and may be prefixed with a positive ([code]+[/code]) or negative ([code]-[/code]) sign. See also [method to_int].
				[codeblock]
				print("7".is_valid_int())    # Prints true
				print("1.65".is_valid_int()) # Prints false
				print("Hi".is_valid_int())   # Prints false
				print("+3".is_valid_int())   # Prints true
				print("-12".is_valid_int())  # Prints true
				[/codeblock]
			</description>
		</method>
		<method name="is_valid_ip_address" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if this string represents a well-formatted IPv4 or IPv6 address. This method considers [url=https://en.wikipedia.org/wiki/Reserved_IP_addresses]reserved IP addresses[/url] such as [code]"0.0.0.0"[/code] and [code]"ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff"[/code] as valid.
			</description>
		</method>
		<method name="is_valid_unicode_identifier" qualifiers="const">
			<return type="bool" />
			<description>
				Returns [code]true[/code] if this string is a valid Unicode identifier.
				A valid Unicode identifier must begin with a Unicode character of class [code]XID_Start[/code] or [code]"_"[/code], and may contain Unicode characters of class [code]XID_Continue[/code] in the other positions.
				[codeblock]
				print("node_2d".is_valid_unicode_identifier())      # Prints true
				print("1st_method".is_valid_unicode_identifier())   # Prints false
				print("MyMethod#2".is_valid_unicode_identifier())   # Prints false
				print("állóképesség".is_valid_unicode_identifier()) # Prints true
				print("выносливость".is_valid_unicode_identifier()) # Prints true
				print("体力".is_valid_unicode_identifier())         # Prints true
				[/codeblock]
				See also [method is_valid_ascii_identifier].
				[b]Note:[/b] This method checks identifiers the same way as GDScript. See [method TextServer.is_valid_identifier] for more advanced checks.
			</description>
		</method>
		<method name="join" qualifiers="const">
			<return type="String" />
			<param index="0" name="parts" type="PackedStringArray" />
			<description>
				Returns the concatenation of [param parts]' elements, with each element separated by the string calling this method. This method is the opposite of [method split].
				[codeblocks]
				[gdscript]
				var fruits = ["Apple", "Orange", "Pear", "Kiwi"]

				print(", ".join(fruits))  # Prints "Apple, Orange, Pear, Kiwi"
				print("---".join(fruits)) # Prints "Apple---Orange---Pear---Kiwi"
				[/gdscript]
				[csharp]
				string[] fruits = ["Apple", "Orange", "Pear", "Kiwi"];

				// In C#, this method is static.
				GD.Print(string.Join(", ", fruits));  // Prints "Apple, Orange, Pear, Kiwi"
				GD.Print(string.Join("---", fruits)); // Prints "Apple---Orange---Pear---Kiwi"
				[/csharp]
				[/codeblocks]
			</description>
		</method>
		<method name="json_escape" qualifiers="const">
			<return type="String" />
			<description>
				Returns a copy of the string with special characters escaped using the JSON standard. Because it closely matches the C standard, it is possible to use [method c_unescape] to unescape the string, if necessary.
			</description>
		</method>
		<method name="left" qualifiers="const">
			<return type="String" />
			<param index="0" name="length" type="int" />
			<description>
				Returns the first [param length] characters from the beginning of the string. If [param length] is negative, strips the last [param length] characters from the string's end.
				[codeblock]
				print("Hello World!".left(3))  # Prints "Hel"
				print("Hello World!".left(-4)) # Prints "Hello Wo"
				[/codeblock]
			</description>
		</method>
		<method name="length" qualifiers="const" keywords="size">
			<return type="int" />
			<description>
				Returns the number of characters in the string. Empty strings ([code]""[/code]) always return [code]0[/code]. See also [method is_empty].
			</description>
		</method>
		<method name="lpad" qualifiers="const">
			<return type="String" />
			<param index="0" name="min_length" type="int" />
			<param index="1" name="character" type="String" default="&quot; &quot;" />
			<description>
				Formats the string to be at least [param min_length] long by adding [param character]s to the left of the string, if necessary. See also [method rpad].
			</description>
		</method>
		<method name="lstrip" qualifiers="const">
			<return type="String" />
			<param index="0" name="chars" type="String" />
			<description>
				Removes a set of characters defined in [param chars] from the string's beginning. See also [method rstrip].
				[b]Note:[/b] [param chars] is not a prefix. Use [method trim_prefix] to remove a single prefix, rather than a set of characters.
			</description>
		</method>
		<method name="match" qualifiers="const">
			<return type="bool" />
			<param index="0" name="expr" type="String" />
			<description>
				Does a simple expression match (also called "glob" or "globbing"), where [code]*[/code] matches zero or more arbitrary characters and [code]?[/code] matches any single character except a period ([code].[/code]). An empty string or empty expression always evaluates to [code]false[/code].
			</description>
		</method>
		<method name="matchn" qualifiers="const">
			<return type="bool" />
			<param index="0" name="expr" type="String" />
			<description>
				Does a simple [b]case-insensitive[/b] expression match, where [code]*[/code] matches zero or more arbitrary characters and [code]?[/code] matches any single character except a period ([code].[/code]). An empty string or empty expression always evaluates to [code]false[/code].
			</description>
		</method>
		<method name="md5_buffer" qualifiers="const">
			<return type="PackedByteArray" />
			<description>
				Returns the [url=https://en.wikipedia.org/wiki/MD5]MD5 hash[/url] of the string as a [PackedByteArray].
			</description>
		</method>
		<method name="md5_text" qualifiers="const">
			<return type="String" />
			<description>
				Returns the [url=https://en.wikipedia.org/wiki/MD5]MD5 hash[/url] of the string as another [String].
			</description>
		</method>
		<method name="naturalcasecmp_to" qualifiers="const">
			<return type="int" />
			<param index="0" name="to" type="String" />
			<description>
				Performs a [b]case-sensitive[/b], [i]natural order[/i] comparison to another string. Returns [code]-1[/code] if less than, [code]1[/code] if greater than, or [code]0[/code] if equal. "Less than" or "greater than" are determined by the [url=https://en.wikipedia.org/wiki/List_of_Unicode_characters]Unicode code points[/url] of each string, which roughly matches the alphabetical order.
				When used for sorting, natural order comparison orders sequences of numbers by the combined value of each digit as is often expected, instead of the single digit's value. A sorted sequence of numbered strings will be [code]["1", "2", "3", ...][/code], not [code]["1", "10", "2", "3", ...][/code].
				If the character comparison reaches the end of one string, but the other string contains more characters, then it will use length as the deciding factor: [code]1[/code] will be returned if this string is longer than the [param to] string, or [code]-1[/code] if shorter. Note that the length of empty strings is always [code]0[/code].
				To get a [bool] result from a string comparison, use the [code]==[/code] operator instead. See also [method naturalnocasecmp_to], [method filecasecmp_to], and [method nocasecmp_to].
			</description>
		</method>
		<method name="naturalnocasecmp_to" qualifiers="const">
			<return type="int" />
			<param index="0" name="to" type="String" />
			<description>
				Performs a [b]case-insensitive[/b], [i]natural order[/i] comparison to another string. Returns [code]-1[/code] if less than, [code]1[/code] if greater than, or [code]0[/code] if equal. "Less than" or "greater than" are determined by the [url=https://en.wikipedia.org/wiki/List_of_Unicode_characters]Unicode code points[/url] of each string, which roughly matches the alphabetical order. Internally, lowercase characters are converted to uppercase for the comparison.
				When used for sorting, natural order comparison orders sequences of numbers by the combined value of each digit as is often expected, instead of the single digit's value. A sorted sequence of numbered strings will be [code]["1", "2", "3", ...][/code], not [code]["1", "10", "2", "3", ...][/code].
				If the character comparison reaches the end of one string, but the other string contains more characters, then it will use length as the deciding factor: [code]1[/code] will be returned if this string is longer than the [param to] string, or [code]-1[/code] if shorter. Note that the length of empty strings is always [code]0[/code].
				To get a [bool] result from a string comparison, use the [code]==[/code] operator instead. See also [method naturalcasecmp_to], [method filenocasecmp_to], and [method casecmp_to].
			</description>
		</method>
		<method name="nocasecmp_to" qualifiers="const">
			<return type="int" />
			<param index="0" name="to" type="String" />
			<description>
				Performs a [b]case-insensitive[/b] comparison to another string. Returns [code]-1[/code] if less than, [code]1[/code] if greater than, or [code]0[/code] if equal. "Less than" or "greater than" are determined by the [url=https://en.wikipedia.org/wiki/List_of_Unicode_characters]Unicode code points[/url] of each string, which roughly matches the alphabetical order. Internally, lowercase characters are converted to uppercase for the comparison.
				If the character comparison reaches the end of one string, but the other string contains more characters, then it will use length as the deciding factor: [code]1[/code] will be returned if this string is longer than the [param to] string, or [code]-1[/code] if shorter. Note that the length of empty strings is always [code]0[/code].
				To get a [bool] result from a string comparison, use the [code]==[/code] operator instead. See also [method casecmp_to], [method filenocasecmp_to], and [method naturalnocasecmp_to].
			</description>
		</method>
		<method name="num" qualifiers="static">
			<return type="String" />
			<param index="0" name="number" type="float" />
			<param index="1" name="decimals" type="int" default="-1" />
			<description>
				Converts a [float] to a string representation of a decimal number, with the number of decimal places specified in [param decimals].
				If [param decimals] is [code]-1[/code] as by default, the string representation may only have up to 14 significant digits, with digits before the decimal point having priority over digits after.
				Trailing zeros are not included in the string. The last digit is rounded, not truncated.
				[codeblock]
				String.num(3.141593)     # Returns "3.141593"
				String.num(3.141593, 3)  # Returns "3.142"
				String.num(3.14159300)   # Returns "3.141593"

				# Here, the last digit will be rounded up,
				# which reduces the total digit count, since trailing zeros are removed:
				String.num(42.129999, 5) # Returns "42.13"

				# If `decimals` is not specified, the maximum number of significant digits is 14:
				String.num(-0.0000012345432123454321)     # Returns "-0.00000123454321"
				String.num(-10000.0000012345432123454321) # Returns "-10000.0000012345"
				[/codeblock]
			</description>
		</method>
		<method name="num_int64" qualifiers="static">
			<return type="String" />
			<param index="0" name="number" type="int" />
			<param index="1" name="base" type="int" default="10" />
			<param index="2" name="capitalize_hex" type="bool" default="false" />
			<description>
				Converts the given [param number] to a string representation, with the given [param base].
				By default, [param base] is set to decimal ([code]10[/code]). Other common bases in programming include binary ([code]2[/code]), [url=https://en.wikipedia.org/wiki/Octal]octal[/url] ([code]8[/code]), hexadecimal ([code]16[/code]).
				If [param capitalize_hex] is [code]true[/code], digits higher than 9 are represented in uppercase.
			</description>
		</method>
		<method name="num_scientific" qualifiers="static">
			<return type="String" />
			<param index="0" name="number" type="float" />
			<description>
				Converts the given [param number] to a string representation, in scientific notation.
				[codeblocks]
				[gdscript]
				var n = -5.2e8
				print(n)                        # Prints -520000000
				print(String.num_scientific(n)) # Prints -5.2e+08
				[/gdscript]
				[csharp]
				// This method is not implemented in C#.
				// Use `string.ToString()` with "e" to achieve similar results.
				var n = -5.2e8f;
				GD.Print(n);                // Prints -520000000
				GD.Print(n.ToString("e1")); // Prints -5.2e+008
				[/csharp]
				[/codeblocks]
				[b]Note:[/b] In C#, this method is not implemented. To achieve similar results, see C#'s [url=https://learn.microsoft.com/en-us/dotnet/standard/base-types/standard-numeric-format-strings]Standard numeric format strings[/url].
			</description>
		</method>
		<method name="num_uint64" qualifiers="static">
			<return type="String" />
			<param index="0" name="number" type="int" />
			<param index="1" name="base" type="int" default="10" />
			<param index="2" name="capitalize_hex" type="bool" default="false" />
			<description>
				Converts the given unsigned [int] to a string representation, with the given [param base].
				By default, [param base] is set to decimal ([code]10[/code]). Other common bases in programming include binary ([code]2[/code]), [url=https://en.wikipedia.org/wiki/Octal]octal[/url] ([code]8[/code]), hexadecimal ([code]16[/code]).
				If [param capitalize_hex] is [code]true[/code], digits higher than 9 are represented in uppercase.
			</description>
		</method>
		<method name="pad_decimals" qualifiers="const">
			<return type="String" />
			<param index="0" name="digits" type="int" />
			<description>
				Formats the string representing a number to have an exact number of [param digits] [i]after[/i] the decimal point.
			</description>
		</method>
		<method name="pad_zeros" qualifiers="const">
			<return type="String" />
			<param index="0" name="digits" type="int" />
			<description>
				Formats the string representing a number to have an exact number of [param digits] [i]before[/i] the decimal point.
			</description>
		</method>
		<method name="path_join" qualifiers="const">
			<return type="String" />
			<param index="0" name="file" type="String" />
			<description>
				Concatenates [param file] at the end of the string as a subpath, adding [code]/[/code] if necessary.
				[b]Example:[/b] [code]"this/is".path_join("path") == "this/is/path"[/code].
			</description>
		</method>
		<method name="remove_char" qualifiers="const">
			<return type="String" />
			<param index="0" name="what" type="int" />
			<description>
				Removes all occurrences of the Unicode character with code [param what]. Faster version of [method replace] when the key is only one character long and the replacement is [code]""[/code].
			</description>
		</method>
		<method name="remove_chars" qualifiers="const">
			<return type="String" />
			<param index="0" name="chars" type="String" />
			<description>
				Removes any occurrence of the characters in [param chars]. See also [method remove_char].
			</description>
		</method>
		<method name="repeat" qualifiers="const">
			<return type="String" />
			<param index="0" name="count" type="int" />
			<description>
				Repeats this string a number of times. [param count] needs to be greater than [code]0[/code]. Otherwise, returns an empty string.
			</description>
		</method>
		<method name="replace" qualifiers="const">
			<return type="String" />
			<param index="0" name="what" type="String" />
			<param index="1" name="forwhat" type="String" />
			<description>
				Replaces all occurrences of [param what] inside the string with the given [param forwhat].
			</description>
		</method>
		<method name="replace_char" qualifiers="const">
			<return type="String" />
			<param index="0" name="key" type="int" />
			<param index="1" name="with" type="int" />
			<description>
				Replaces all occurrences of the Unicode character with code [param key] with the Unicode character with code [param with]. Faster version of [method replace] when the key is only one character long. To get a single character use [code]"X".unicode_at(0)[/code] (note that some strings, like compound letters and emoji, can be made up of multiple unicode codepoints, and will not work with this method, use [method length] to make sure).
			</description>
		</method>
		<method name="replace_chars" qualifiers="const">
			<return type="String" />
			<param index="0" name="keys" type="String" />
			<param index="1" name="with" type="int" />
			<description>
				Replaces any occurrence of the characters in [param keys] with the Unicode character with code [param with]. See also [method replace_char].
			</description>
		</method>
		<method name="replacen" qualifiers="const">
			<return type="String" />
			<param index="0" name="what" type="String" />
			<param index="1" name="forwhat" type="String" />
			<description>
				Replaces all [b]case-insensitive[/b] occurrences of [param what] inside the string with the given [param forwhat].
			</description>
		</method>
		<method name="reverse" qualifiers="const">
			<return type="String" />
			<description>
				Returns the copy of this string in reverse order. This operation works on unicode codepoints, rather than sequences of codepoints, and may break things like compound letters or emojis.
			</description>
		</method>
		<method name="rfind" qualifiers="const">
			<return type="int" />
			<param index="0" name="what" type="String" />
			<param index="1" name="from" type="int" default="-1" />
			<description>
				Returns the index of the [b]last[/b] occurrence of [param what] in this string, or [code]-1[/code] if there are none. The search's start can be specified with [param from], continuing to the beginning of the string. This method is the reverse of [method find].
			</description>
		</method>
		<method name="rfindn" qualifiers="const">
			<return type="int" />
			<param index="0" name="what" type="String" />
			<param index="1" name="from" type="int" default="-1" />
			<description>
				Returns the index of the [b]last[/b] [b]case-insensitive[/b] occurrence of [param what] in this string, or [code]-1[/code] if there are none. The starting search index can be specified with [param from], continuing to the beginning of the string. This method is the reverse of [method findn].
			</description>
		</method>
		<method name="right" qualifiers="const">
			<return type="String" />
			<param index="0" name="length" type="int" />
			<description>
				Returns the last [param length] characters from the end of the string. If [param length] is negative, strips the first [param length] characters from the string's beginning.
				[codeblock]
				print("Hello World!".right(3))  # Prints "ld!"
				print("Hello World!".right(-4)) # Prints "o World!"
				[/codeblock]
			</description>
		</method>
		<method name="rpad" qualifiers="const">
			<return type="String" />
			<param index="0" name="min_length" type="int" />
			<param index="1" name="character" type="String" default="&quot; &quot;" />
			<description>
				Formats the string to be at least [param min_length] long, by adding [param character]s to the right of the string, if necessary. See also [method lpad].
			</description>
		</method>
		<method name="rsplit" qualifiers="const">
			<return type="PackedStringArray" />
			<param index="0" name="delimiter" type="String" default="&quot;&quot;" />
			<param index="1" name="allow_empty" type="bool" default="true" />
			<param index="2" name="maxsplit" type="int" default="0" />
			<description>
				Splits the string using a [param delimiter] and returns an array of the substrings, starting from the end of the string. The splits in the returned array appear in the same order as the original string. If [param delimiter] is an empty string, each substring will be a single character.
				If [param allow_empty] is [code]false[/code], empty strings between adjacent delimiters are excluded from the array.
				If [param maxsplit] is greater than [code]0[/code], the number of splits may not exceed [param maxsplit]. By default, the entire string is split, which is mostly identical to [method split].
				[codeblocks]
				[gdscript]
				var some_string = "One,Two,Three,Four"
				var some_array = some_string.rsplit(",", true, 1)

				print(some_array.size()) # Prints 2
				print(some_array[0])     # Prints "One,Two,Three"
				print(some_array[1])     # Prints "Four"
				[/gdscript]
				[csharp]
				// In C#, there is no String.RSplit() method.
				[/csharp]
				[/codeblocks]
			</description>
		</method>
		<method name="rstrip" qualifiers="const">
			<return type="String" />
			<param index="0" name="chars" type="String" />
			<description>
				Removes a set of characters defined in [param chars] from the string's end. See also [method lstrip].
				[b]Note:[/b] [param chars] is not a suffix. Use [method trim_suffix] to remove a single suffix, rather than a set of characters.
			</description>
		</method>
		<method name="sha1_buffer" qualifiers="const">
			<return type="PackedByteArray" />
			<description>
				Returns the [url=https://en.wikipedia.org/wiki/SHA-1]SHA-1[/url] hash of the string as a [PackedByteArray].
			</description>
		</method>
		<method name="sha1_text" qualifiers="const">
			<return type="String" />
			<description>
				Returns the [url=https://en.wikipedia.org/wiki/SHA-1]SHA-1[/url] hash of the string as another [String].
			</description>
		</method>
		<method name="sha256_buffer" qualifiers="const">
			<return type="PackedByteArray" />
			<description>
				Returns the [url=https://en.wikipedia.org/wiki/SHA-2]SHA-256[/url] hash of the string as a [PackedByteArray].
			</description>
		</method>
		<method name="sha256_text" qualifiers="const">
			<return type="String" />
			<description>
				Returns the [url=https://en.wikipedia.org/wiki/SHA-2]SHA-256[/url] hash of the string as another [String].
			</description>
		</method>
		<method name="similarity" qualifiers="const">
			<return type="float" />
			<param index="0" name="text" type="String" />
			<description>
				Returns the similarity index ([url=https://en.wikipedia.org/wiki/S%C3%B8rensen%E2%80%93Dice_coefficient]Sørensen-Dice coefficient[/url]) of this string compared to another. A result of [code]1.0[/code] means totally similar, while [code]0.0[/code] means totally dissimilar.
				[codeblock]
				print("ABC123".similarity("ABC123")) # Prints 1.0
				print("ABC123".similarity("XYZ456")) # Prints 0.0
				print("ABC123".similarity("123ABC")) # Prints 0.8
				print("ABC123".similarity("abc123")) # Prints 0.4
				[/codeblock]
			</description>
		</method>
		<method name="simplify_path" qualifiers="const">
			<return type="String" />
			<description>
				If the string is a valid file path, converts the string into a canonical path. This is the shortest possible path, without [code]"./"[/code], and all the unnecessary [code]".."[/code] and [code]"/"[/code].
				[codeblock]
				var simple_path = "./path/to///../file".simplify_path()
				print(simple_path) # Prints "path/file"
				[/codeblock]
			</description>
		</method>
		<method name="split" qualifiers="const">
			<return type="PackedStringArray" />
			<param index="0" name="delimiter" type="String" default="&quot;&quot;" />
			<param index="1" name="allow_empty" type="bool" default="true" />
			<param index="2" name="maxsplit" type="int" default="0" />
			<description>
				Splits the string using a [param delimiter] and returns an array of the substrings. If [param delimiter] is an empty string, each substring will be a single character. This method is the opposite of [method join].
				If [param allow_empty] is [code]false[/code], empty strings between adjacent delimiters are excluded from the array.
				If [param maxsplit] is greater than [code]0[/code], the number of splits may not exceed [param maxsplit]. By default, the entire string is split.
				[codeblocks]
				[gdscript]
				var some_array = "One,Two,Three,Four".split(",", true, 2)

				print(some_array.size()) # Prints 3
				print(some_array[0])     # Prints "One"
				print(some_array[1])     # Prints "Two"
				print(some_array[2])     # Prints "Three,Four"
				[/gdscript]
				[csharp]
				// C#'s `Split()` does not support the `maxsplit` parameter.
				var someArray = "One,Two,Three".Split(",");

				GD.Print(someArray[0]); // Prints "One"
				GD.Print(someArray[1]); // Prints "Two"
				GD.Print(someArray[2]); // Prints "Three"
				[/csharp]
				[/codeblocks]
				[b]Note:[/b] If you only need one substring from the array, consider using [method get_slice] which is faster. If you need to split strings with more complex rules, use the [RegEx] class instead.
			</description>
		</method>
		<method name="split_floats" qualifiers="const">
			<return type="PackedFloat64Array" />
			<param index="0" name="delimiter" type="String" />
			<param index="1" name="allow_empty" type="bool" default="true" />
			<description>
				Splits the string into floats by using a [param delimiter] and returns a [PackedFloat64Array].
				If [param allow_empty] is [code]false[/code], empty or invalid [float] conversions between adjacent delimiters are excluded.
				[codeblock]
				var a = "1,2,4.5".split_floats(",")         # a is [1.0, 2.0, 4.5]
				var c = "1| ||4.5".split_floats("|")        # c is [1.0, 0.0, 0.0, 4.5]
				var b = "1| ||4.5".split_floats("|", false) # b is [1.0, 4.5]
				[/codeblock]
			</description>
		</method>
		<method name="strip_edges" qualifiers="const">
			<return type="String" />
			<param index="0" name="left" type="bool" default="true" />
			<param index="1" name="right" type="bool" default="true" />
			<description>
				Strips all non-printable characters from the beginning and the end of the string. These include spaces, tabulations ([code]\t[/code]), and newlines ([code]\n[/code] [code]\r[/code]).
				If [param left] is [code]false[/code], ignores the string's beginning. Likewise, if [param right] is [code]false[/code], ignores the string's end.
			</description>
		</method>
		<method name="strip_escapes" qualifiers="const">
			<return type="String" />
			<description>
				Strips all escape characters from the string. These include all non-printable control characters of the first page of the ASCII table (values from 0 to 31), such as tabulation ([code]\t[/code]) and newline ([code]\n[/code], [code]\r[/code]) characters, but [i]not[/i] spaces.
			</description>
		</method>
		<method name="substr" qualifiers="const">
			<return type="String" />
			<param index="0" name="from" type="int" />
			<param index="1" name="len" type="int" default="-1" />
			<description>
				Returns part of the string from the position [param from] with length [param len]. If [param len] is [code]-1[/code] (as by default), returns the rest of the string starting from the given position.
			</description>
		</method>
		<method name="to_ascii_buffer" qualifiers="const">
			<return type="PackedByteArray" />
			<description>
				Converts the string to an [url=https://en.wikipedia.org/wiki/ASCII]ASCII[/url]/Latin-1 encoded [PackedByteArray]. This method is slightly faster than [method to_utf8_buffer], but replaces all unsupported characters with spaces. This is the inverse of [method PackedByteArray.get_string_from_ascii].
			</description>
		</method>
		<method name="to_camel_case" qualifiers="const">
			<return type="String" />
			<description>
				Returns the string converted to [code]camelCase[/code].
			</description>
		</method>
		<method name="to_float" qualifiers="const">
			<return type="float" />
			<description>
				Converts the string representing a decimal number into a [float]. This method stops on the first non-number character, except the first decimal point ([code].[/code]) and the exponent letter ([code]e[/code]). See also [method is_valid_float].
				[codeblock]
				var a = "12.35".to_float()  # a is 12.35
				var b = "1.2.3".to_float()  # b is 1.2
				var c = "12xy3".to_float()  # c is 12.0
				var d = "1e3".to_float()    # d is 1000.0
				var e = "Hello!".to_float() # e is 0.0
				[/codeblock]
			</description>
		</method>
		<method name="to_int" qualifiers="const">
			<return type="int" />
			<description>
				Converts the string representing an integer number into an [int]. This method removes any non-number character and stops at the first decimal point ([code].[/code]). See also [method is_valid_int].
				[codeblock]
				var a = "123".to_int()    # a is 123
				var b = "x1y2z3".to_int() # b is 123
				var c = "-1.2.3".to_int() # c is -1
				var d = "Hello!".to_int() # d is 0
				[/codeblock]
			</description>
		</method>
		<method name="to_kebab_case" qualifiers="const">
			<return type="String" />
			<description>
				Returns the string converted to [code]kebab-case[/code].
				[b]Note:[/b] Numbers followed by a [i]single[/i] letter are not separated in the conversion to keep some words (such as "2D") together.
				[codeblocks]
				[gdscript]
				"Node2D".to_kebab_case()               # Returns "node-2d"
				"2nd place".to_kebab_case()            # Returns "2-nd-place"
				"Texture3DAssetFolder".to_kebab_case() # Returns "texture-3d-asset-folder"
				[/gdscript]
				[csharp]
				"Node2D".ToKebabCase();               // Returns "node-2d"
				"2nd place".ToKebabCase();            // Returns "2-nd-place"
				"Texture3DAssetFolder".ToKebabCase(); // Returns "texture-3d-asset-folder"
				[/csharp]
				[/codeblocks]
			</description>
		</method>
		<method name="to_lower" qualifiers="const">
			<return type="String" />
			<description>
				Returns the string converted to [code]lowercase[/code].
			</description>
		</method>
		<method name="to_multibyte_char_buffer" qualifiers="const">
			<return type="PackedByteArray" />
			<param index="0" name="encoding" type="String" default="&quot;&quot;" />
			<description>
				Converts the string to system multibyte code page encoded [PackedByteArray]. If conversion fails, empty array is returned.
				The values permitted for [param encoding] are system dependent. If [param encoding] is empty string, system default encoding is used.
				- For Windows, see [url=https://learn.microsoft.com/en-us/windows/win32/Intl/code-page-identifiers]Code Page Identifiers[/url] .NET names.
				- For macOS and Linux/BSD, see [code]libiconv[/code] library documentation and [code]iconv --list[/code] for a list of supported encodings.
			</description>
		</method>
		<method name="to_pascal_case" qualifiers="const">
			<return type="String" />
			<description>
				Returns the string converted to [code]PascalCase[/code].
			</description>
		</method>
		<method name="to_snake_case" qualifiers="const">
			<return type="String" />
			<description>
				Returns the string converted to [code]snake_case[/code].
				[b]Note:[/b] Numbers followed by a [i]single[/i] letter are not separated in the conversion to keep some words (such as "2D") together.
				[codeblocks]
				[gdscript]
				"Node2D".to_snake_case()               # Returns "node_2d"
				"2nd place".to_snake_case()            # Returns "2_nd_place"
				"Texture3DAssetFolder".to_snake_case() # Returns "texture_3d_asset_folder"
				[/gdscript]
				[csharp]
				"Node2D".ToSnakeCase();               // Returns "node_2d"
				"2nd place".ToSnakeCase();            // Returns "2_nd_place"
				"Texture3DAssetFolder".ToSnakeCase(); // Returns "texture_3d_asset_folder"
				[/csharp]
				[/codeblocks]
			</description>
		</method>
		<method name="to_upper" qualifiers="const">
			<return type="String" />
			<description>
				Returns the string converted to [code]UPPERCASE[/code].
			</description>
		</method>
		<method name="to_utf8_buffer" qualifiers="const">
			<return type="PackedByteArray" />
			<description>
				Converts the string to a [url=https://en.wikipedia.org/wiki/UTF-8]UTF-8[/url] encoded [PackedByteArray]. This method is slightly slower than [method to_ascii_buffer], but supports all UTF-8 characters. For most cases, prefer using this method. This is the inverse of [method PackedByteArray.get_string_from_utf8].
			</description>
		</method>
		<method name="to_utf16_buffer" qualifiers="const">
			<return type="PackedByteArray" />
			<description>
				Converts the string to a [url=https://en.wikipedia.org/wiki/UTF-16]UTF-16[/url] encoded [PackedByteArray]. This is the inverse of [method PackedByteArray.get_string_from_utf16].
			</description>
		</method>
		<method name="to_utf32_buffer" qualifiers="const">
			<return type="PackedByteArray" />
			<description>
				Converts the string to a [url=https://en.wikipedia.org/wiki/UTF-32]UTF-32[/url] encoded [PackedByteArray]. This is the inverse of [method PackedByteArray.get_string_from_utf32].
			</description>
		</method>
		<method name="to_wchar_buffer" qualifiers="const">
			<return type="PackedByteArray" />
			<description>
				Converts the string to a [url=https://en.wikipedia.org/wiki/Wide_character]wide character[/url] ([code]wchar_t[/code], UTF-16 on Windows, UTF-32 on other platforms) encoded [PackedByteArray]. This is the inverse of [method PackedByteArray.get_string_from_wchar].
			</description>
		</method>
		<method name="trim_prefix" qualifiers="const">
			<return type="String" />
			<param index="0" name="prefix" type="String" />
			<description>
				Removes the given [param prefix] from the start of the string, or returns the string unchanged.
			</description>
		</method>
		<method name="trim_suffix" qualifiers="const">
			<return type="String" />
			<param index="0" name="suffix" type="String" />
			<description>
				Removes the given [param suffix] from the end of the string, or returns the string unchanged.
			</description>
		</method>
		<method name="unicode_at" qualifiers="const">
			<return type="int" />
			<param index="0" name="at" type="int" />
			<description>
				Returns the character code at position [param at].
			</description>
		</method>
		<method name="uri_decode" qualifiers="const">
			<return type="String" />
			<description>
				Decodes the string from its URL-encoded format. This method is meant to properly decode the parameters in a URL when receiving an HTTP request. See also [method uri_encode].
				[codeblocks]
				[gdscript]
				var url = "$DOCS_URL/?highlight=Godot%20Engine%3%docs"
				print(url.uri_decode()) # Prints "$DOCS_URL/?highlight=Godot Engine:docs"
				[/gdscript]
				[csharp]
				var url = "$DOCS_URL/?highlight=Godot%20Engine%3%docs"
				GD.Print(url.URIDecode()) // Prints "$DOCS_URL/?highlight=Godot Engine:docs"
				[/csharp]
				[/codeblocks]
				[b]Note:[/b] This method decodes [code]+[/code] as space.
			</description>
		</method>
		<method name="uri_encode" qualifiers="const">
			<return type="String" />
			<description>
				Encodes the string to URL-friendly format. This method is meant to properly encode the parameters in a URL when sending an HTTP request. See also [method uri_decode].
				[codeblocks]
				[gdscript]
				var prefix = "$DOCS_URL/?highlight="
				var url = prefix + "Redot Engine:docs".uri_encode()

				print(url) # Prints "$DOCS_URL/?highlight=Godot%20Engine%3%docs"
				[/gdscript]
				[csharp]
				var prefix = "$DOCS_URL/?highlight=";
				var url = prefix + "Redot Engine:docs".URIEncode();

				GD.Print(url); // Prints "$DOCS_URL/?highlight=Godot%20Engine%3%docs"
				[/csharp]
				[/codeblocks]
			</description>
		</method>
		<method name="uri_file_decode" qualifiers="const">
			<return type="String" />
			<description>
				Decodes the file path from its URL-encoded format. Unlike [method uri_decode] this method leaves [code]+[/code] as is.
			</description>
		</method>
		<method name="validate_filename" qualifiers="const">
			<return type="String" />
			<description>
				Returns a copy of the string with all characters that are not allowed in [method is_valid_filename] replaced with underscores.
			</description>
		</method>
		<method name="validate_node_name" qualifiers="const">
			<return type="String" />
			<description>
				Returns a copy of the string with all characters that are not allowed in [member Node.name] ([code].[/code] [code]:[/code] [code]@[/code] [code]/[/code] [code]"[/code] [code]%[/code]) replaced with underscores.
			</description>
		</method>
		<method name="xml_escape" qualifiers="const">
			<return type="String" />
			<param index="0" name="escape_quotes" type="bool" default="false" />
			<description>
				Returns a copy of the string with special characters escaped using the XML standard. If [param escape_quotes] is [code]true[/code], the single quote ([code]'[/code]) and double quote ([code]"[/code]) characters are also escaped.
			</description>
		</method>
		<method name="xml_unescape" qualifiers="const">
			<return type="String" />
			<description>
				Returns a copy of the string with escaped characters replaced by their meanings according to the XML standard.
			</description>
		</method>
	</methods>
	<operators>
		<operator name="operator !=">
			<return type="bool" />
			<param index="0" name="right" type="String" />
			<description>
				Returns [code]true[/code] if both strings do not contain the same sequence of characters.
			</description>
		</operator>
		<operator name="operator !=">
			<return type="bool" />
			<param index="0" name="right" type="StringName" />
			<description>
				Returns [code]true[/code] if this [String] is not equivalent to the given [StringName].
			</description>
		</operator>
		<operator name="operator %">
			<return type="String" />
			<param index="0" name="right" type="Variant" />
			<description>
				Formats the [String], replacing the placeholders with one or more parameters. To pass multiple parameters, [param right] needs to be an [Array].
				[codeblock]
				print("I caught %d fishes!" % 2) # Prints "I caught 2 fishes!"

				var my_message = "Travelling to %s, at %2.2f km/h."
				var location = "Deep Valley"
				var speed = 40.3485
				print(my_message % [location, speed]) # Prints "Travelling to Deep Valley, at 40.35 km/h."
				[/codeblock]
				For more information, see the [url=$DOCS_URL/tutorials/scripting/gdscript/gdscript_format_string.html]GDScript format strings[/url] tutorial.
				[b]Note:[/b] In C#, this operator is not available. Instead, see [url=https://learn.microsoft.com/en-us/dotnet/csharp/language-reference/tokens/interpolated]how to interpolate strings with "$"[/url].
			</description>
		</operator>
		<operator name="operator +">
			<return type="String" />
			<param index="0" name="right" type="String" />
			<description>
				Appends [param right] at the end of this [String], also known as a string concatenation.
			</description>
		</operator>
		<operator name="operator +">
			<return type="String" />
			<param index="0" name="right" type="StringName" />
			<description>
				Appends [param right] at the end of this [String], returning a [String]. This is also known as a string concatenation.
			</description>
		</operator>
		<operator name="operator &lt;">
			<return type="bool" />
			<param index="0" name="right" type="String" />
			<description>
				Returns [code]true[/code] if the left [String] comes before [param right] in [url=https://en.wikipedia.org/wiki/List_of_Unicode_characters]Unicode order[/url], which roughly matches the alphabetical order. Useful for sorting.
			</description>
		</operator>
		<operator name="operator &lt;=">
			<return type="bool" />
			<param index="0" name="right" type="String" />
			<description>
				Returns [code]true[/code] if the left [String] comes before [param right] in [url=https://en.wikipedia.org/wiki/List_of_Unicode_characters]Unicode order[/url], which roughly matches the alphabetical order, or if both are equal.
			</description>
		</operator>
		<operator name="operator ==">
			<return type="bool" />
			<param index="0" name="right" type="String" />
			<description>
				Returns [code]true[/code] if both strings contain the same sequence of characters.
			</description>
		</operator>
		<operator name="operator ==">
			<return type="bool" />
			<param index="0" name="right" type="StringName" />
			<description>
				Returns [code]true[/code] if this [String] is equivalent to the given [StringName].
			</description>
		</operator>
		<operator name="operator &gt;">
			<return type="bool" />
			<param index="0" name="right" type="String" />
			<description>
				Returns [code]true[/code] if the left [String] comes after [param right] in [url=https://en.wikipedia.org/wiki/List_of_Unicode_characters]Unicode order[/url], which roughly matches the alphabetical order. Useful for sorting.
			</description>
		</operator>
		<operator name="operator &gt;=">
			<return type="bool" />
			<param index="0" name="right" type="String" />
			<description>
				Returns [code]true[/code] if the left [String] comes after [param right] in [url=https://en.wikipedia.org/wiki/List_of_Unicode_characters]Unicode order[/url], which roughly matches the alphabetical order, or if both are equal.
			</description>
		</operator>
		<operator name="operator []">
			<return type="String" />
			<param index="0" name="index" type="int" />
			<description>
				Returns a new [String] that only contains the character at [param index]. Indices start from [code]0[/code]. If [param index] is greater or equal to [code]0[/code], the character is fetched starting from the beginning of the string. If [param index] is a negative value, it is fetched starting from the end. Accessing a string out-of-bounds will cause a run-time error, pausing the project execution if run from the editor.
			</description>
		</operator>
	</operators>
</class>
