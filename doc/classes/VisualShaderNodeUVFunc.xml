<?xml version="1.0" encoding="UTF-8" ?>
<class name="VisualShaderNodeUVFunc" inherits="VisualShaderNode" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Contains functions to modify texture coordinates ([code]uv[/code]) to be used within the visual shader graph.
	</brief_description>
	<description>
		UV functions are similar to [Vector2] functions, but the input port of this node uses the shader's UV value by default.
	</description>
	<tutorials>
	</tutorials>
	<members>
		<member name="function" type="int" setter="set_function" getter="get_function" enum="VisualShaderNodeUVFunc.Function" default="0">
			A function to be applied to the texture coordinates. See [enum Function] for options.
		</member>
	</members>
	<constants>
		<constant name="FUNC_PANNING" value="0" enum="Function">
			Translates [code]uv[/code] by using [code]scale[/code] and [code]offset[/code] values using the following formula: [code]uv = uv + offset * scale[/code]. [code]uv[/code] port is connected to [code]UV[/code] built-in by default.
		</constant>
		<constant name="FUNC_SCALING" value="1" enum="Function">
			Scales [code]uv[/code] by using [code]scale[/code] and [code]pivot[/code] values using the following formula: [code]uv = (uv - pivot) * scale + pivot[/code]. [code]uv[/code] port is connected to [code]UV[/code] built-in by default.
		</constant>
		<constant name="FUNC_MAX" value="2" enum="Function">
			Represents the size of the [enum Function] enum.
		</constant>
	</constants>
</class>
