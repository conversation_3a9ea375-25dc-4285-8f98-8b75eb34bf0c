<?xml version="1.0" encoding="UTF-8" ?>
<class name="TileSetScenesCollectionSource" inherits="TileSetSource" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Exposes a set of scenes as tiles for a [TileSet] resource.
	</brief_description>
	<description>
		When placed on a [TileMapLayer], tiles from [TileSetScenesCollectionSource] will automatically instantiate an associated scene at the cell's position in the TileMapLayer.
		Scenes are instantiated as children of the [TileMapLayer] when it enters the tree. If you add/remove a scene tile in the [TileMapLayer] that is already inside the tree, the [TileMapLayer] will automatically instantiate/free the scene accordingly.
		[b]Note:[/b] Scene tiles all occupy one tile slot and instead use alternate tile ID to identify scene index. [method TileSetSource.get_tiles_count] will always return [code]1[/code]. Use [method get_scene_tiles_count] to get a number of scenes in a [TileSetScenesCollectionSource].
		Use this code if you want to find the scene path at a given tile in [TileMapLayer]:
		[codeblocks]
		[gdscript]
		var source_id = tile_map_layer.get_cell_source_id(Vector2i(x, y))
		if source_id &gt; -1:
		    var scene_source = tile_map_layer.tile_set.get_source(source_id)
		    if scene_source is TileSetScenesCollectionSource:
		        var alt_id = tile_map_layer.get_cell_alternative_tile(Vector2i(x, y))
		        # The assigned PackedScene.
		        var scene = scene_source.get_scene_tile_scene(alt_id)
		[/gdscript]
		[csharp]
		int sourceId = tileMapLayer.GetCellSourceId(new Vector2I(x, y));
		if (sourceId &gt; -1)
		{
		    TileSetSource source = tileMapLayer.TileSet.GetSource(sourceId);
		    if (source is TileSetScenesCollectionSource sceneSource)
		    {
		        int altId = tileMapLayer.GetCellAlternativeTile(new Vector2I(x, y));
		        // The assigned PackedScene.
		        PackedScene scene = sceneSource.GetSceneTileScene(altId);
		    }
		}
		[/csharp]
		[/codeblocks]
	</description>
	<tutorials>
	</tutorials>
	<methods>
		<method name="create_scene_tile">
			<return type="int" />
			<param index="0" name="packed_scene" type="PackedScene" />
			<param index="1" name="id_override" type="int" default="-1" />
			<description>
				Creates a scene-based tile out of the given scene.
				Returns a newly generated unique ID.
			</description>
		</method>
		<method name="get_next_scene_tile_id" qualifiers="const">
			<return type="int" />
			<description>
				Returns the scene ID a following call to [method create_scene_tile] would return.
			</description>
		</method>
		<method name="get_scene_tile_display_placeholder" qualifiers="const">
			<return type="bool" />
			<param index="0" name="id" type="int" />
			<description>
				Returns whether the scene tile with [param id] displays a placeholder in the editor.
			</description>
		</method>
		<method name="get_scene_tile_id">
			<return type="int" />
			<param index="0" name="index" type="int" />
			<description>
				Returns the scene tile ID of the scene tile at [param index].
			</description>
		</method>
		<method name="get_scene_tile_scene" qualifiers="const">
			<return type="PackedScene" />
			<param index="0" name="id" type="int" />
			<description>
				Returns the [PackedScene] resource of scene tile with [param id].
			</description>
		</method>
		<method name="get_scene_tiles_count">
			<return type="int" />
			<description>
				Returns the number or scene tiles this TileSet source has.
			</description>
		</method>
		<method name="has_scene_tile_id">
			<return type="bool" />
			<param index="0" name="id" type="int" />
			<description>
				Returns whether this TileSet source has a scene tile with [param id].
			</description>
		</method>
		<method name="remove_scene_tile">
			<return type="void" />
			<param index="0" name="id" type="int" />
			<description>
				Remove the scene tile with [param id].
			</description>
		</method>
		<method name="set_scene_tile_display_placeholder">
			<return type="void" />
			<param index="0" name="id" type="int" />
			<param index="1" name="display_placeholder" type="bool" />
			<description>
				Sets whether or not the scene tile with [param id] should display a placeholder in the editor. This might be useful for scenes that are not visible.
			</description>
		</method>
		<method name="set_scene_tile_id">
			<return type="void" />
			<param index="0" name="id" type="int" />
			<param index="1" name="new_id" type="int" />
			<description>
				Changes a scene tile's ID from [param id] to [param new_id]. This will fail if there is already a tile with an ID equal to [param new_id].
			</description>
		</method>
		<method name="set_scene_tile_scene">
			<return type="void" />
			<param index="0" name="id" type="int" />
			<param index="1" name="packed_scene" type="PackedScene" />
			<description>
				Assigns a [PackedScene] resource to the scene tile with [param id]. This will fail if the scene does not extend [CanvasItem], as positioning properties are needed to place the scene on the [TileMapLayer].
			</description>
		</method>
	</methods>
</class>
