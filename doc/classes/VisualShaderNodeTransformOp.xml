<?xml version="1.0" encoding="UTF-8" ?>
<class name="VisualShaderNodeTransformOp" inherits="VisualShaderNode" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A [Transform3D] operator to be used within the visual shader graph.
	</brief_description>
	<description>
		Applies [member operator] to two transform (4×4 matrices) inputs.
	</description>
	<tutorials>
	</tutorials>
	<members>
		<member name="operator" type="int" setter="set_operator" getter="get_operator" enum="VisualShaderNodeTransformOp.Operator" default="0">
			The type of the operation to be performed on the transforms. See [enum Operator] for options.
		</member>
	</members>
	<constants>
		<constant name="OP_AxB" value="0" enum="Operator">
			Multiplies transform [code]a[/code] by the transform [code]b[/code].
		</constant>
		<constant name="OP_BxA" value="1" enum="Operator">
			Multiplies transform [code]b[/code] by the transform [code]a[/code].
		</constant>
		<constant name="OP_AxB_COMP" value="2" enum="Operator">
			Performs a component-wise multiplication of transform [code]a[/code] by the transform [code]b[/code].
		</constant>
		<constant name="OP_BxA_COMP" value="3" enum="Operator">
			Performs a component-wise multiplication of transform [code]b[/code] by the transform [code]a[/code].
		</constant>
		<constant name="OP_ADD" value="4" enum="Operator">
			Adds two transforms.
		</constant>
		<constant name="OP_A_MINUS_B" value="5" enum="Operator">
			Subtracts the transform [code]a[/code] from the transform [code]b[/code].
		</constant>
		<constant name="OP_B_MINUS_A" value="6" enum="Operator">
			Subtracts the transform [code]b[/code] from the transform [code]a[/code].
		</constant>
		<constant name="OP_A_DIV_B" value="7" enum="Operator">
			Divides the transform [code]a[/code] by the transform [code]b[/code].
		</constant>
		<constant name="OP_B_DIV_A" value="8" enum="Operator">
			Divides the transform [code]b[/code] by the transform [code]a[/code].
		</constant>
		<constant name="OP_MAX" value="9" enum="Operator">
			Represents the size of the [enum Operator] enum.
		</constant>
	</constants>
</class>
