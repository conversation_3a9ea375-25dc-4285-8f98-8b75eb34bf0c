<?xml version="1.0" encoding="UTF-8" ?>
<class name="SkeletonModification2D" inherits="Resource" experimental="" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Base class for resources that operate on [Bone2D]s in a [Skeleton2D].
	</brief_description>
	<description>
		This resource provides an interface that can be expanded so code that operates on [Bone2D] nodes in a [Skeleton2D] can be mixed and matched together to create complex interactions.
		This is used to provide Redot with a flexible and powerful Inverse Kinematics solution that can be adapted for many different uses.
	</description>
	<tutorials>
	</tutorials>
	<methods>
		<method name="_draw_editor_gizmo" qualifiers="virtual">
			<return type="void" />
			<description>
				Used for drawing [b]editor-only[/b] modification gizmos. This function will only be called in the Redot editor and can be overridden to draw custom gizmos.
				[b]Note:[/b] You will need to use the Skeleton2D from [method SkeletonModificationStack2D.get_skeleton] and it's draw functions, as the [SkeletonModification2D] resource cannot draw on its own.
			</description>
		</method>
		<method name="_execute" qualifiers="virtual">
			<return type="void" />
			<param index="0" name="delta" type="float" />
			<description>
				Executes the given modification. This is where the modification performs whatever function it is designed to do.
			</description>
		</method>
		<method name="_setup_modification" qualifiers="virtual">
			<return type="void" />
			<param index="0" name="modification_stack" type="SkeletonModificationStack2D" />
			<description>
				Called when the modification is setup. This is where the modification performs initialization.
			</description>
		</method>
		<method name="clamp_angle">
			<return type="float" />
			<param index="0" name="angle" type="float" />
			<param index="1" name="min" type="float" />
			<param index="2" name="max" type="float" />
			<param index="3" name="invert" type="bool" />
			<description>
				Takes an angle and clamps it so it is within the passed-in [param min] and [param max] range. [param invert] will inversely clamp the angle, clamping it to the range outside of the given bounds.
			</description>
		</method>
		<method name="get_editor_draw_gizmo" qualifiers="const">
			<return type="bool" />
			<description>
				Returns whether this modification will call [method _draw_editor_gizmo] in the Redot editor to draw modification-specific gizmos.
			</description>
		</method>
		<method name="get_is_setup" qualifiers="const">
			<return type="bool" />
			<description>
				Returns whether this modification has been successfully setup or not.
			</description>
		</method>
		<method name="get_modification_stack">
			<return type="SkeletonModificationStack2D" />
			<description>
				Returns the [SkeletonModificationStack2D] that this modification is bound to. Through the modification stack, you can access the Skeleton2D the modification is operating on.
			</description>
		</method>
		<method name="set_editor_draw_gizmo">
			<return type="void" />
			<param index="0" name="draw_gizmo" type="bool" />
			<description>
				Sets whether this modification will call [method _draw_editor_gizmo] in the Redot editor to draw modification-specific gizmos.
			</description>
		</method>
		<method name="set_is_setup">
			<return type="void" />
			<param index="0" name="is_setup" type="bool" />
			<description>
				Manually allows you to set the setup state of the modification. This function should only rarely be used, as the [SkeletonModificationStack2D] the modification is bound to should handle setting the modification up.
			</description>
		</method>
	</methods>
	<members>
		<member name="enabled" type="bool" setter="set_enabled" getter="get_enabled" default="true">
			If [code]true[/code], the modification's [method _execute] function will be called by the [SkeletonModificationStack2D].
		</member>
		<member name="execution_mode" type="int" setter="set_execution_mode" getter="get_execution_mode" default="0">
			The execution mode for the modification. This tells the modification stack when to execute the modification. Some modifications have settings that are only available in certain execution modes.
		</member>
	</members>
</class>
