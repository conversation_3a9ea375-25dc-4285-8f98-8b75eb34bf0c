<?xml version="1.0" encoding="UTF-8" ?>
<class name="VisualShaderNodeGlobalExpression" inherits="VisualShaderNodeExpression" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A custom global visual shader graph expression written in Redot Shading Language.
	</brief_description>
	<description>
		Custom Redot Shader Language expression, which is placed on top of the generated shader. You can place various function definitions inside to call later in [VisualShaderNodeExpression]s (which are injected in the main shader functions). You can also declare varyings, uniforms and global constants.
	</description>
	<tutorials>
	</tutorials>
</class>
