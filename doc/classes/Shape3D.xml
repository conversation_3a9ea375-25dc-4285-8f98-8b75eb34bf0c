<?xml version="1.0" encoding="UTF-8" ?>
<class name="Shape3D" inherits="Resource" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Abstract base class for 3D shapes used for physics collision.
	</brief_description>
	<description>
		Abstract base class for all 3D shapes, intended for use in physics.
		[b]Performance:[/b] Primitive shapes, especially [SphereShape3D], are fast to check collisions against. [ConvexPolygonShape3D] and [HeightMapShape3D] are slower, and [ConcavePolygonShape3D] is the slowest.
	</description>
	<tutorials>
		<link title="Physics introduction">$DOCS_URL/tutorials/physics/physics_introduction.html</link>
	</tutorials>
	<methods>
		<method name="get_debug_mesh">
			<return type="ArrayMesh" />
			<description>
				Returns the [ArrayMesh] used to draw the debug collision for this [Shape3D].
			</description>
		</method>
	</methods>
	<members>
		<member name="custom_solver_bias" type="float" setter="set_custom_solver_bias" getter="get_custom_solver_bias" default="0.0">
			The shape's custom solver bias. Defines how much bodies react to enforce contact separation when this shape is involved.
			When set to [code]0[/code], the default value from [member ProjectSettings.physics/3d/solver/default_contact_bias] is used.
		</member>
		<member name="margin" type="float" setter="set_margin" getter="get_margin" default="0.04">
			The collision margin for the shape. This is not used in Redot Physics.
			Collision margins allow collision detection to be more efficient by adding an extra shell around shapes. Collision algorithms are more expensive when objects overlap by more than their margin, so a higher value for margins is better for performance, at the cost of accuracy around edges as it makes them less sharp.
		</member>
	</members>
</class>
