<?xml version="1.0" encoding="UTF-8" ?>
<class name="VisualShaderNodeTextureParameter" inherits="VisualShaderNodeParameter" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Performs a uniform texture lookup within the visual shader graph.
	</brief_description>
	<description>
		Performs a lookup operation on the texture provided as a uniform for the shader.
	</description>
	<tutorials>
	</tutorials>
	<members>
		<member name="color_default" type="int" setter="set_color_default" getter="get_color_default" enum="VisualShaderNodeTextureParameter.ColorDefault" default="0">
			Sets the default color if no texture is assigned to the uniform.
		</member>
		<member name="texture_filter" type="int" setter="set_texture_filter" getter="get_texture_filter" enum="VisualShaderNodeTextureParameter.TextureFilter" default="0">
			Sets the texture filtering mode. See [enum TextureFilter] for options.
		</member>
		<member name="texture_repeat" type="int" setter="set_texture_repeat" getter="get_texture_repeat" enum="VisualShaderNodeTextureParameter.TextureRepeat" default="0">
			Sets the texture repeating mode. See [enum TextureRepeat] for options.
		</member>
		<member name="texture_source" type="int" setter="set_texture_source" getter="get_texture_source" enum="VisualShaderNodeTextureParameter.TextureSource" default="0">
			Sets the texture source mode. Used for reading from the screen, depth, or normal_roughness texture. See [enum TextureSource] for options.
		</member>
		<member name="texture_type" type="int" setter="set_texture_type" getter="get_texture_type" enum="VisualShaderNodeTextureParameter.TextureType" default="0">
			Defines the type of data provided by the source texture. See [enum TextureType] for options.
		</member>
	</members>
	<constants>
		<constant name="TYPE_DATA" value="0" enum="TextureType">
			No hints are added to the uniform declaration.
		</constant>
		<constant name="TYPE_COLOR" value="1" enum="TextureType">
			Adds [code]source_color[/code] as hint to the uniform declaration for proper sRGB to linear conversion.
		</constant>
		<constant name="TYPE_NORMAL_MAP" value="2" enum="TextureType">
			Adds [code]hint_normal[/code] as hint to the uniform declaration, which internally converts the texture for proper usage as normal map.
		</constant>
		<constant name="TYPE_ANISOTROPY" value="3" enum="TextureType">
			Adds [code]hint_anisotropy[/code] as hint to the uniform declaration to use for a flowmap.
		</constant>
		<constant name="TYPE_MAX" value="4" enum="TextureType">
			Represents the size of the [enum TextureType] enum.
		</constant>
		<constant name="COLOR_DEFAULT_WHITE" value="0" enum="ColorDefault">
			Defaults to fully opaque white color.
		</constant>
		<constant name="COLOR_DEFAULT_BLACK" value="1" enum="ColorDefault">
			Defaults to fully opaque black color.
		</constant>
		<constant name="COLOR_DEFAULT_TRANSPARENT" value="2" enum="ColorDefault">
			Defaults to fully transparent black color.
		</constant>
		<constant name="COLOR_DEFAULT_MAX" value="3" enum="ColorDefault">
			Represents the size of the [enum ColorDefault] enum.
		</constant>
		<constant name="FILTER_DEFAULT" value="0" enum="TextureFilter">
			Sample the texture using the filter determined by the node this shader is attached to.
		</constant>
		<constant name="FILTER_NEAREST" value="1" enum="TextureFilter">
			The texture filter reads from the nearest pixel only. This makes the texture look pixelated from up close, and grainy from a distance (due to mipmaps not being sampled).
		</constant>
		<constant name="FILTER_LINEAR" value="2" enum="TextureFilter">
			The texture filter blends between the nearest 4 pixels. This makes the texture look smooth from up close, and grainy from a distance (due to mipmaps not being sampled).
		</constant>
		<constant name="FILTER_NEAREST_MIPMAP" value="3" enum="TextureFilter">
			The texture filter reads from the nearest pixel and blends between the nearest 2 mipmaps (or uses the nearest mipmap if [member ProjectSettings.rendering/textures/default_filters/use_nearest_mipmap_filter] is [code]true[/code]). This makes the texture look pixelated from up close, and smooth from a distance.
			Use this for non-pixel art textures that may be viewed at a low scale (e.g. due to [Camera2D] zoom or sprite scaling), as mipmaps are important to smooth out pixels that are smaller than on-screen pixels.
		</constant>
		<constant name="FILTER_LINEAR_MIPMAP" value="4" enum="TextureFilter">
			The texture filter blends between the nearest 4 pixels and between the nearest 2 mipmaps (or uses the nearest mipmap if [member ProjectSettings.rendering/textures/default_filters/use_nearest_mipmap_filter] is [code]true[/code]). This makes the texture look smooth from up close, and smooth from a distance.
			Use this for non-pixel art textures that may be viewed at a low scale (e.g. due to [Camera2D] zoom or sprite scaling), as mipmaps are important to smooth out pixels that are smaller than on-screen pixels.
		</constant>
		<constant name="FILTER_NEAREST_MIPMAP_ANISOTROPIC" value="5" enum="TextureFilter">
			The texture filter reads from the nearest pixel and blends between 2 mipmaps (or uses the nearest mipmap if [member ProjectSettings.rendering/textures/default_filters/use_nearest_mipmap_filter] is [code]true[/code]) based on the angle between the surface and the camera view. This makes the texture look pixelated from up close, and smooth from a distance. Anisotropic filtering improves texture quality on surfaces that are almost in line with the camera, but is slightly slower. The anisotropic filtering level can be changed by adjusting [member ProjectSettings.rendering/textures/default_filters/anisotropic_filtering_level].
			[b]Note:[/b] This texture filter is rarely useful in 2D projects. [constant FILTER_NEAREST_MIPMAP] is usually more appropriate in this case.
		</constant>
		<constant name="FILTER_LINEAR_MIPMAP_ANISOTROPIC" value="6" enum="TextureFilter">
			The texture filter blends between the nearest 4 pixels and blends between 2 mipmaps (or uses the nearest mipmap if [member ProjectSettings.rendering/textures/default_filters/use_nearest_mipmap_filter] is [code]true[/code]) based on the angle between the surface and the camera view. This makes the texture look smooth from up close, and smooth from a distance. Anisotropic filtering improves texture quality on surfaces that are almost in line with the camera, but is slightly slower. The anisotropic filtering level can be changed by adjusting [member ProjectSettings.rendering/textures/default_filters/anisotropic_filtering_level].
			[b]Note:[/b] This texture filter is rarely useful in 2D projects. [constant FILTER_LINEAR_MIPMAP] is usually more appropriate in this case.
		</constant>
		<constant name="FILTER_MAX" value="7" enum="TextureFilter">
			Represents the size of the [enum TextureFilter] enum.
		</constant>
		<constant name="REPEAT_DEFAULT" value="0" enum="TextureRepeat">
			Sample the texture using the repeat mode determined by the node this shader is attached to.
		</constant>
		<constant name="REPEAT_ENABLED" value="1" enum="TextureRepeat">
			Texture will repeat normally.
		</constant>
		<constant name="REPEAT_DISABLED" value="2" enum="TextureRepeat">
			Texture will not repeat.
		</constant>
		<constant name="REPEAT_MAX" value="3" enum="TextureRepeat">
			Represents the size of the [enum TextureRepeat] enum.
		</constant>
		<constant name="SOURCE_NONE" value="0" enum="TextureSource">
			The texture source is not specified in the shader.
		</constant>
		<constant name="SOURCE_SCREEN" value="1" enum="TextureSource">
			The texture source is the screen texture which captures all opaque objects drawn this frame.
		</constant>
		<constant name="SOURCE_DEPTH" value="2" enum="TextureSource">
			The texture source is the depth texture from the depth prepass.
		</constant>
		<constant name="SOURCE_NORMAL_ROUGHNESS" value="3" enum="TextureSource">
			The texture source is the normal-roughness buffer from the depth prepass.
		</constant>
		<constant name="SOURCE_MAX" value="4" enum="TextureSource">
			Represents the size of the [enum TextureSource] enum.
		</constant>
	</constants>
</class>
