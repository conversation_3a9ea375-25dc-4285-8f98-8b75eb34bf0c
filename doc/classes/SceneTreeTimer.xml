<?xml version="1.0" encoding="UTF-8" ?>
<class name="SceneTreeTimer" inherits="RefCounted" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		One-shot timer.
	</brief_description>
	<description>
		A one-shot timer managed by the scene tree, which emits [signal timeout] on completion. See also [method SceneTree.create_timer].
		As opposed to [Timer], it does not require the instantiation of a node. Commonly used to create a one-shot delay timer as in the following example:
		[codeblocks]
		[gdscript]
		func some_function():
		    print("Timer started.")
		    await get_tree().create_timer(1.0).timeout
		    print("Timer ended.")
		[/gdscript]
		[csharp]
		public async Task SomeFunction()
		{
		    GD.Print("Timer started.");
		    await ToSignal(GetTree().CreateTimer(1.0f), SceneTreeTimer.SignalName.Timeout);
		    GD.Print("Timer ended.");
		}
		[/csharp]
		[/codeblocks]
		The timer will be dereferenced after its time elapses. To preserve the timer, you can keep a reference to it. See [RefCounted].
		[b]Note:[/b] The timer is processed after all of the nodes in the current frame, i.e. node's [method Node._process] method would be called before the timer (or [method Node._physics_process] if [code]process_in_physics[/code] in [method SceneTree.create_timer] has been set to [code]true[/code]).
	</description>
	<tutorials>
	</tutorials>
	<members>
		<member name="time_left" type="float" setter="set_time_left" getter="get_time_left">
			The time remaining (in seconds).
		</member>
	</members>
	<signals>
		<signal name="timeout">
			<description>
				Emitted when the timer reaches 0.
			</description>
		</signal>
	</signals>
</class>
