<?xml version="1.0" encoding="UTF-8" ?>
<class name="VisualShaderNodeFloatFunc" inherits="VisualShaderNode" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A scalar floating-point function to be used within the visual shader graph.
	</brief_description>
	<description>
		Accept a floating-point scalar ([code]x[/code]) to the input port and transform it according to [member function].
	</description>
	<tutorials>
	</tutorials>
	<members>
		<member name="function" type="int" setter="set_function" getter="get_function" enum="VisualShaderNodeFloatFunc.Function" default="13">
			A function to be applied to the scalar. See [enum Function] for options.
		</member>
	</members>
	<constants>
		<constant name="FUNC_SIN" value="0" enum="Function">
			Returns the sine of the parameter. Translates to [code]sin(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_COS" value="1" enum="Function">
			Returns the cosine of the parameter. Translates to [code]cos(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_TAN" value="2" enum="Function">
			Returns the tangent of the parameter. Translates to [code]tan(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_ASIN" value="3" enum="Function">
			Returns the arc-sine of the parameter. Translates to [code]asin(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_ACOS" value="4" enum="Function">
			Returns the arc-cosine of the parameter. Translates to [code]acos(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_ATAN" value="5" enum="Function">
			Returns the arc-tangent of the parameter. Translates to [code]atan(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_SINH" value="6" enum="Function">
			Returns the hyperbolic sine of the parameter. Translates to [code]sinh(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_COSH" value="7" enum="Function">
			Returns the hyperbolic cosine of the parameter. Translates to [code]cosh(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_TANH" value="8" enum="Function">
			Returns the hyperbolic tangent of the parameter. Translates to [code]tanh(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_LOG" value="9" enum="Function">
			Returns the natural logarithm of the parameter. Translates to [code]log(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_EXP" value="10" enum="Function">
			Returns the natural exponentiation of the parameter. Translates to [code]exp(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_SQRT" value="11" enum="Function">
			Returns the square root of the parameter. Translates to [code]sqrt(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_ABS" value="12" enum="Function">
			Returns the absolute value of the parameter. Translates to [code]abs(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_SIGN" value="13" enum="Function">
			Extracts the sign of the parameter. Translates to [code]sign(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_FLOOR" value="14" enum="Function">
			Finds the nearest integer less than or equal to the parameter. Translates to [code]floor(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_ROUND" value="15" enum="Function">
			Finds the nearest integer to the parameter. Translates to [code]round(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_CEIL" value="16" enum="Function">
			Finds the nearest integer that is greater than or equal to the parameter. Translates to [code]ceil(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_FRACT" value="17" enum="Function">
			Computes the fractional part of the argument. Translates to [code]fract(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_SATURATE" value="18" enum="Function">
			Clamps the value between [code]0.0[/code] and [code]1.0[/code] using [code]min(max(x, 0.0), 1.0)[/code].
		</constant>
		<constant name="FUNC_NEGATE" value="19" enum="Function">
			Negates the [code]x[/code] using [code]-(x)[/code].
		</constant>
		<constant name="FUNC_ACOSH" value="20" enum="Function">
			Returns the arc-hyperbolic-cosine of the parameter. Translates to [code]acosh(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_ASINH" value="21" enum="Function">
			Returns the arc-hyperbolic-sine of the parameter. Translates to [code]asinh(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_ATANH" value="22" enum="Function">
			Returns the arc-hyperbolic-tangent of the parameter. Translates to [code]atanh(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_DEGREES" value="23" enum="Function">
			Convert a quantity in radians to degrees. Translates to [code]degrees(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_EXP2" value="24" enum="Function">
			Returns 2 raised by the power of the parameter. Translates to [code]exp2(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_INVERSE_SQRT" value="25" enum="Function">
			Returns the inverse of the square root of the parameter. Translates to [code]inversesqrt(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_LOG2" value="26" enum="Function">
			Returns the base 2 logarithm of the parameter. Translates to [code]log2(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_RADIANS" value="27" enum="Function">
			Convert a quantity in degrees to radians. Translates to [code]radians(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_RECIPROCAL" value="28" enum="Function">
			Finds reciprocal value of dividing 1 by [code]x[/code] (i.e. [code]1 / x[/code]).
		</constant>
		<constant name="FUNC_ROUNDEVEN" value="29" enum="Function">
			Finds the nearest even integer to the parameter. Translates to [code]roundEven(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_TRUNC" value="30" enum="Function">
			Returns a value equal to the nearest integer to [code]x[/code] whose absolute value is not larger than the absolute value of [code]x[/code]. Translates to [code]trunc(x)[/code] in the GodotShader Language.
		</constant>
		<constant name="FUNC_ONEMINUS" value="31" enum="Function">
			Subtracts scalar [code]x[/code] from 1 (i.e. [code]1 - x[/code]).
		</constant>
		<constant name="FUNC_MAX" value="32" enum="Function">
			Represents the size of the [enum Function] enum.
		</constant>
	</constants>
</class>
