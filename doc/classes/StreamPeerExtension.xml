<?xml version="1.0" encoding="UTF-8" ?>
<class name="StreamPeerExtension" inherits="StreamPeer" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
	</brief_description>
	<description>
	</description>
	<tutorials>
	</tutorials>
	<methods>
		<method name="_get_available_bytes" qualifiers="virtual const">
			<return type="int" />
			<description>
			</description>
		</method>
		<method name="_get_data" qualifiers="virtual">
			<return type="int" enum="Error" />
			<param index="0" name="r_buffer" type="uint8_t*" />
			<param index="1" name="r_bytes" type="int" />
			<param index="2" name="r_received" type="int32_t*" />
			<description>
			</description>
		</method>
		<method name="_get_partial_data" qualifiers="virtual">
			<return type="int" enum="Error" />
			<param index="0" name="r_buffer" type="uint8_t*" />
			<param index="1" name="r_bytes" type="int" />
			<param index="2" name="r_received" type="int32_t*" />
			<description>
			</description>
		</method>
		<method name="_put_data" qualifiers="virtual">
			<return type="int" enum="Error" />
			<param index="0" name="p_data" type="const uint8_t*" />
			<param index="1" name="p_bytes" type="int" />
			<param index="2" name="r_sent" type="int32_t*" />
			<description>
			</description>
		</method>
		<method name="_put_partial_data" qualifiers="virtual">
			<return type="int" enum="Error" />
			<param index="0" name="p_data" type="const uint8_t*" />
			<param index="1" name="p_bytes" type="int" />
			<param index="2" name="r_sent" type="int32_t*" />
			<description>
			</description>
		</method>
	</methods>
</class>
