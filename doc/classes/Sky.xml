<?xml version="1.0" encoding="UTF-8" ?>
<class name="Sky" inherits="Resource" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Defines a 3D environment's background by using a [Material].
	</brief_description>
	<description>
		The [Sky] class uses a [Material] to render a 3D environment's background and the light it emits by updating the reflection/radiance cubemaps.
	</description>
	<tutorials>
	</tutorials>
	<members>
		<member name="process_mode" type="int" setter="set_process_mode" getter="get_process_mode" enum="Sky.ProcessMode" default="0">
			Sets the method for generating the radiance map from the sky. The radiance map is a cubemap with increasingly blurry versions of the sky corresponding to different levels of roughness. Radiance maps can be expensive to calculate. See [enum ProcessMode] for options.
		</member>
		<member name="radiance_size" type="int" setter="set_radiance_size" getter="get_radiance_size" enum="Sky.RadianceSize" default="3">
			The [Sky]'s radiance map size. The higher the radiance map size, the more detailed the lighting from the [Sky] will be.
			See [enum RadianceSize] constants for values.
			[b]Note:[/b] Some hardware will have trouble with higher radiance sizes, especially [constant RADIANCE_SIZE_512] and above. Only use such high values on high-end hardware.
		</member>
		<member name="sky_material" type="Material" setter="set_material" getter="get_material">
			[Material] used to draw the background. Can be [PanoramaSkyMaterial], [ProceduralSkyMaterial], [PhysicalSkyMaterial], or even a [ShaderMaterial] if you want to use your own custom shader.
		</member>
	</members>
	<constants>
		<constant name="RADIANCE_SIZE_32" value="0" enum="RadianceSize">
			Radiance texture size is 32×32 pixels.
		</constant>
		<constant name="RADIANCE_SIZE_64" value="1" enum="RadianceSize">
			Radiance texture size is 64×64 pixels.
		</constant>
		<constant name="RADIANCE_SIZE_128" value="2" enum="RadianceSize">
			Radiance texture size is 128×128 pixels.
		</constant>
		<constant name="RADIANCE_SIZE_256" value="3" enum="RadianceSize">
			Radiance texture size is 256×256 pixels.
		</constant>
		<constant name="RADIANCE_SIZE_512" value="4" enum="RadianceSize">
			Radiance texture size is 512×512 pixels.
		</constant>
		<constant name="RADIANCE_SIZE_1024" value="5" enum="RadianceSize">
			Radiance texture size is 1024×1024 pixels.
		</constant>
		<constant name="RADIANCE_SIZE_2048" value="6" enum="RadianceSize">
			Radiance texture size is 2048×2048 pixels.
		</constant>
		<constant name="RADIANCE_SIZE_MAX" value="7" enum="RadianceSize">
			Represents the size of the [enum RadianceSize] enum.
		</constant>
		<constant name="PROCESS_MODE_AUTOMATIC" value="0" enum="ProcessMode">
			Automatically selects the appropriate process mode based on your sky shader. If your shader uses [code]TIME[/code] or [code]POSITION[/code], this will use [constant PROCESS_MODE_REALTIME]. If your shader uses any of the [code]LIGHT_*[/code] variables or any custom uniforms, this uses [constant PROCESS_MODE_INCREMENTAL]. Otherwise, this defaults to [constant PROCESS_MODE_QUALITY].
		</constant>
		<constant name="PROCESS_MODE_QUALITY" value="1" enum="ProcessMode">
			Uses high quality importance sampling to process the radiance map. In general, this results in much higher quality than [constant PROCESS_MODE_REALTIME] but takes much longer to generate. This should not be used if you plan on changing the sky at runtime. If you are finding that the reflection is not blurry enough and is showing sparkles or fireflies, try increasing [member ProjectSettings.rendering/reflections/sky_reflections/ggx_samples].
		</constant>
		<constant name="PROCESS_MODE_INCREMENTAL" value="2" enum="ProcessMode">
			Uses the same high quality importance sampling to process the radiance map as [constant PROCESS_MODE_QUALITY], but updates over several frames. The number of frames is determined by [member ProjectSettings.rendering/reflections/sky_reflections/roughness_layers]. Use this when you need highest quality radiance maps, but have a sky that updates slowly.
		</constant>
		<constant name="PROCESS_MODE_REALTIME" value="3" enum="ProcessMode">
			Uses the fast filtering algorithm to process the radiance map. In general this results in lower quality, but substantially faster run times. If you need better quality, but still need to update the sky every frame, consider turning on [member ProjectSettings.rendering/reflections/sky_reflections/fast_filter_high_quality].
			[b]Note:[/b] The fast filtering algorithm is limited to 256×256 cubemaps, so [member radiance_size] must be set to [constant RADIANCE_SIZE_256]. Otherwise, a warning is printed and the overridden radiance size is ignored.
		</constant>
	</constants>
</class>
