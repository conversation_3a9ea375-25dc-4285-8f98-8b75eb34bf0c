<?xml version="1.0" encoding="UTF-8" ?>
<class name="ProjectSettings" inherits="Object" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Stores globally-accessible variables.
	</brief_description>
	<description>
		Stores variables that can be accessed from everywhere. Use [method get_setting], [method set_setting] or [method has_setting] to access them. Variables stored in [code]project.godot[/code] are also loaded into [ProjectSettings], making this object very useful for reading custom game configuration options.
		When naming a Project Settings property, use the full path to the setting including the category. For example, [code]"application/config/name"[/code] for the project name. Category and property names can be viewed in the Project Settings dialog.
		[b]Feature tags:[/b] Project settings can be overridden for specific platforms and configurations (debug, release, ...) using [url=$DOCS_URL/tutorials/export/feature_tags.html]feature tags[/url].
		[b]Overriding:[/b] Any project setting can be overridden by creating a file named [code]override.cfg[/code] in the project's root directory. This can also be used in exported projects by placing this file in the same directory as the project binary. Overriding will still take the base project settings' [url=$DOCS_URL/tutorials/export/feature_tags.html]feature tags[/url] in account. Therefore, make sure to [i]also[/i] override the setting with the desired feature tags if you want them to override base project settings on all platforms and configurations.
	</description>
	<tutorials>
		<link title="Project Settings">$DOCS_URL/tutorials/editor/project_settings.html</link>
		<link title="3D Physics Tests Demo">https://godotengine.org/asset-library/asset/2747</link>
		<link title="3D Platformer Demo">https://godotengine.org/asset-library/asset/2748</link>
		<link title="Operating System Testing Demo">https://godotengine.org/asset-library/asset/2789</link>
	</tutorials>
	<methods>
		<method name="add_property_info">
			<return type="void" />
			<param index="0" name="hint" type="Dictionary" />
			<description>
				Adds a custom property info to a property. The dictionary must contain:
				- [code]"name"[/code]: [String] (the property's name)
				- [code]"type"[/code]: [int] (see [enum Variant.Type])
				- optionally [code]"hint"[/code]: [int] (see [enum PropertyHint]) and [code]"hint_string"[/code]: [String]
				[codeblocks]
				[gdscript]
				ProjectSettings.set("category/property_name", 0)

				var property_info = {
				    "name": "category/property_name",
				    "type": TYPE_INT,
				    "hint": PROPERTY_HINT_ENUM,
				    "hint_string": "one,two,three"
				}

				ProjectSettings.add_property_info(property_info)
				[/gdscript]
				[csharp]
				ProjectSettings.Singleton.Set("category/property_name", 0);

				var propertyInfo = new Godot.Collections.Dictionary
				{
				    {"name", "category/propertyName"},
				    {"type", (int)Variant.Type.Int},
				    {"hint", (int)PropertyHint.Enum},
				    {"hint_string", "one,two,three"},
				};

				ProjectSettings.AddPropertyInfo(propertyInfo);
				[/csharp]
				[/codeblocks]
			</description>
		</method>
		<method name="clear">
			<return type="void" />
			<param index="0" name="name" type="String" />
			<description>
				Clears the whole configuration (not recommended, may break things).
			</description>
		</method>
		<method name="get_global_class_list">
			<return type="Dictionary[]" />
			<description>
				Returns an [Array] of registered global classes. Each global class is represented as a [Dictionary] that contains the following entries:
				- [code]base[/code] is a name of the base class;
				- [code]class[/code] is a name of the registered global class;
				- [code]icon[/code] is a path to a custom icon of the global class, if it has any;
				- [code]language[/code] is a name of a programming language in which the global class is written;
				- [code]path[/code] is a path to a file containing the global class.
				[b]Note:[/b] Both the script and the icon paths are local to the project filesystem, i.e. they start with [code]res://[/code].
			</description>
		</method>
		<method name="get_order" qualifiers="const">
			<return type="int" />
			<param index="0" name="name" type="String" />
			<description>
				Returns the order of a configuration value (influences when saved to the config file).
			</description>
		</method>
		<method name="get_setting" qualifiers="const">
			<return type="Variant" />
			<param index="0" name="name" type="String" />
			<param index="1" name="default_value" type="Variant" default="null" />
			<description>
				Returns the value of the setting identified by [param name]. If the setting doesn't exist and [param default_value] is specified, the value of [param default_value] is returned. Otherwise, [code]null[/code] is returned.
				[codeblocks]
				[gdscript]
				print(ProjectSettings.get_setting("application/config/name"))
				print(ProjectSettings.get_setting("application/config/custom_description", "No description specified."))
				[/gdscript]
				[csharp]
				GD.Print(ProjectSettings.GetSetting("application/config/name"));
				GD.Print(ProjectSettings.GetSetting("application/config/custom_description", "No description specified."));
				[/csharp]
				[/codeblocks]
				[b]Note:[/b] This method doesn't take potential feature overrides into account automatically. Use [method get_setting_with_override] to handle seamlessly.
			</description>
		</method>
		<method name="get_setting_with_override" qualifiers="const">
			<return type="Variant" />
			<param index="0" name="name" type="StringName" />
			<description>
				Similar to [method get_setting], but applies feature tag overrides if any exists and is valid.
				[b]Example:[/b] If the setting override [code]"application/config/name.windows"[/code] exists, and the following code is executed on a [i]Windows[/i] operating system, the overridden setting is printed instead:
				[codeblocks]
				[gdscript]
				print(ProjectSettings.get_setting_with_override("application/config/name"))
				[/gdscript]
				[csharp]
				GD.Print(ProjectSettings.GetSettingWithOverride("application/config/name"));
				[/csharp]
				[/codeblocks]
			</description>
		</method>
		<method name="get_setting_with_override_and_custom_features" qualifiers="const">
			<return type="Variant" />
			<param index="0" name="name" type="StringName" />
			<param index="1" name="features" type="PackedStringArray" />
			<description>
				Similar to [method get_setting_with_override], but applies feature tag overrides instead of current OS features.
			</description>
		</method>
		<method name="globalize_path" qualifiers="const">
			<return type="String" />
			<param index="0" name="path" type="String" />
			<description>
				Returns the absolute, native OS path corresponding to the localized [param path] (starting with [code]res://[/code] or [code]user://[/code]). The returned path will vary depending on the operating system and user preferences. See [url=$DOCS_URL/tutorials/io/data_paths.html]File paths in Redot projects[/url] to see what those paths convert to. See also [method localize_path].
				[b]Note:[/b] [method globalize_path] with [code]res://[/code] will not work in an exported project. Instead, prepend the executable's base directory to the path when running from an exported project:
				[codeblock]
				var path = ""
				if OS.has_feature("editor"):
				    # Running from an editor binary.
				    # `path` will contain the absolute path to `hello.txt` located in the project root.
				    path = ProjectSettings.globalize_path("res://hello.txt")
				else:
				    # Running from an exported project.
				    # `path` will contain the absolute path to `hello.txt` next to the executable.
				    # This is *not* identical to using `ProjectSettings.globalize_path()` with a `res://` path,
				    # but is close enough in spirit.
				    path = OS.get_executable_path().get_base_dir().path_join("hello.txt")
				[/codeblock]
			</description>
		</method>
		<method name="has_setting" qualifiers="const">
			<return type="bool" />
			<param index="0" name="name" type="String" />
			<description>
				Returns [code]true[/code] if a configuration value is present.
			</description>
		</method>
		<method name="load_resource_pack">
			<return type="bool" />
			<param index="0" name="pack" type="String" />
			<param index="1" name="replace_files" type="bool" default="true" />
			<param index="2" name="offset" type="int" default="0" />
			<description>
				Loads the contents of the .pck or .zip file specified by [param pack] into the resource filesystem ([code]res://[/code]). Returns [code]true[/code] on success.
				[b]Note:[/b] If a file from [param pack] shares the same path as a file already in the resource filesystem, any attempts to load that file will use the file from [param pack] unless [param replace_files] is set to [code]false[/code].
				[b]Note:[/b] The optional [param offset] parameter can be used to specify the offset in bytes to the start of the resource pack. This is only supported for .pck files.
				[b]Note:[/b] [DirAccess] will not show changes made to the contents of [code]res://[/code] after calling this function.
			</description>
		</method>
		<method name="localize_path" qualifiers="const">
			<return type="String" />
			<param index="0" name="path" type="String" />
			<description>
				Returns the localized path (starting with [code]res://[/code]) corresponding to the absolute, native OS [param path]. See also [method globalize_path].
			</description>
		</method>
		<method name="save">
			<return type="int" enum="Error" />
			<description>
				Saves the configuration to the [code]project.godot[/code] file.
				[b]Note:[/b] This method is intended to be used by editor plugins, as modified [ProjectSettings] can't be loaded back in the running app. If you want to change project settings in exported projects, use [method save_custom] to save [code]override.cfg[/code] file.
			</description>
		</method>
		<method name="save_custom">
			<return type="int" enum="Error" />
			<param index="0" name="file" type="String" />
			<description>
				Saves the configuration to a custom file. The file extension must be [code].godot[/code] (to save in text-based [ConfigFile] format) or [code].binary[/code] (to save in binary format). You can also save [code]override.cfg[/code] file, which is also text, but can be used in exported projects unlike other formats.
			</description>
		</method>
		<method name="set_as_basic">
			<return type="void" />
			<param index="0" name="name" type="String" />
			<param index="1" name="basic" type="bool" />
			<description>
				Defines if the specified setting is considered basic or advanced. Basic settings will always be shown in the project settings. Advanced settings will only be shown if the user enables the "Advanced Settings" option.
			</description>
		</method>
		<method name="set_as_internal">
			<return type="void" />
			<param index="0" name="name" type="String" />
			<param index="1" name="internal" type="bool" />
			<description>
				Defines if the specified setting is considered internal. An internal setting won't show up in the Project Settings dialog. This is mostly useful for addons that need to store their own internal settings without exposing them directly to the user.
			</description>
		</method>
		<method name="set_initial_value">
			<return type="void" />
			<param index="0" name="name" type="String" />
			<param index="1" name="value" type="Variant" />
			<description>
				Sets the specified setting's initial value. This is the value the setting reverts to.
			</description>
		</method>
		<method name="set_order">
			<return type="void" />
			<param index="0" name="name" type="String" />
			<param index="1" name="position" type="int" />
			<description>
				Sets the order of a configuration value (influences when saved to the config file).
			</description>
		</method>
		<method name="set_restart_if_changed">
			<return type="void" />
			<param index="0" name="name" type="String" />
			<param index="1" name="restart" type="bool" />
			<description>
				Sets whether a setting requires restarting the editor to properly take effect.
				[b]Note:[/b] This is just a hint to display to the user that the editor must be restarted for changes to take effect. Enabling [method set_restart_if_changed] does [i]not[/i] delay the setting being set when changed.
			</description>
		</method>
		<method name="set_setting">
			<return type="void" />
			<param index="0" name="name" type="String" />
			<param index="1" name="value" type="Variant" />
			<description>
				Sets the value of a setting.
				[codeblocks]
				[gdscript]
				ProjectSettings.set_setting("application/config/name", "Example")
				[/gdscript]
				[csharp]
				ProjectSettings.SetSetting("application/config/name", "Example");
				[/csharp]
				[/codeblocks]
				This can also be used to erase custom project settings. To do this change the setting value to [code]null[/code].
			</description>
		</method>
	</methods>
	<members>
		<member name="accessibility/general/accessibility_support" type="int" setter="" getter="" default="0">
			Accessibility support mode:
			- [b]Auto[/b] ([code]0[/code]): accessibility support is enabled, but accessibility information updates are processed only if an assistive app (e.g. screen reader or Braille display) is active (default).
			- [b]Always Active[/b] ([code]1[/code]): accessibility support is enabled, and accessibility information updates are processed regardless of current assistive apps' status.
			- [b]Disabled[/b] ([code]2[/code]): accessibility support is fully disabled.
			[b]Note:[/b] Accessibility debugging tools, such as Accessibility Insights for Windows, macOS Accessibility Inspector, or AT-SPI Browser do not count as assistive apps. To test your app with these tools, use [code]1[/code].
		</member>
		<member name="accessibility/general/updates_per_second" type="int" setter="" getter="" default="60">
			The number of accessibility information updates per second.
		</member>
		<member name="animation/warnings/check_angle_interpolation_type_conflicting" type="bool" setter="" getter="" default="true">
			If [code]true[/code], [AnimationMixer] prints the warning of interpolation being forced to choose the shortest rotation path due to multiple angle interpolation types being mixed in the [AnimationMixer] cache.
		</member>
		<member name="animation/warnings/check_invalid_track_paths" type="bool" setter="" getter="" default="true">
			If [code]true[/code], [AnimationMixer] prints the warning of no matching object of the track path in the scene.
		</member>
		<member name="application/boot_splash/bg_color" type="Color" setter="" getter="" default="Color(0.14, 0.14, 0.14, 1)">
			Background color for the boot splash.
		</member>
		<member name="application/boot_splash/fullsize" type="bool" setter="" getter="" default="true">
			If [code]true[/code], scale the boot splash image to the full window size (preserving the aspect ratio) when the engine starts. If [code]false[/code], the engine will leave it at the default pixel size.
		</member>
		<member name="application/boot_splash/image" type="String" setter="" getter="" default="&quot;&quot;">
			Path to an image used as the boot splash. If left empty, the default Redot Engine splash will be displayed instead.
			[b]Note:[/b] Only effective if [member application/boot_splash/show_image] is [code]true[/code].
			[b]Note:[/b] The only supported format is PNG. Using another image format will result in an error.
			[b]Note:[/b] The image will also show when opening the project in the editor. If you want to display the default splash image in the editor, add an empty override for [code]editor_hint[/code] feature.
		</member>
		<member name="application/boot_splash/minimum_display_time" type="int" setter="" getter="" default="0">
			Minimum boot splash display time (in milliseconds). It is not recommended to set too high values for this setting.
		</member>
		<member name="application/boot_splash/show_image" type="bool" setter="" getter="" default="true">
			If [code]true[/code], displays the image specified in [member application/boot_splash/image] when the engine starts. If [code]false[/code], only displays the plain color specified in [member application/boot_splash/bg_color].
		</member>
		<member name="application/boot_splash/use_filter" type="bool" setter="" getter="" default="true">
			If [code]true[/code], applies linear filtering when scaling the image (recommended for high-resolution artwork). If [code]false[/code], uses nearest-neighbor interpolation (recommended for pixel art).
		</member>
		<member name="application/config/auto_accept_quit" type="bool" setter="" getter="" default="true">
			If [code]true[/code], the application automatically accepts quitting requests.
		</member>
		<member name="application/config/custom_user_dir_name" type="String" setter="" getter="" default="&quot;&quot;">
			This user directory is used for storing persistent data ([code]user://[/code] filesystem). If a custom directory name is defined, this name will be appended to the system-specific user data directory (same parent folder as the Redot configuration folder documented in [method OS.get_user_data_dir]).
			The [member application/config/use_custom_user_dir] setting must be enabled for this to take effect.
			[b]Note:[/b] If [member application/config/custom_user_dir_name] contains trailing periods, they will be stripped as folder names ending with a period are not allowed on Windows.
		</member>
		<member name="application/config/description" type="String" setter="" getter="" default="&quot;&quot;">
			The project's description, displayed as a tooltip in the Project Manager when hovering the project.
		</member>
		<member name="application/config/icon" type="String" setter="" getter="" default="&quot;&quot;">
			Icon used for the project, set when project loads. Exporters will also use this icon as a fallback if necessary.
		</member>
		<member name="application/config/macos_native_icon" type="String" setter="" getter="" default="&quot;&quot;">
			Icon set in [code].icns[/code] format used on macOS to set the game's icon. This is done automatically on start by calling [method DisplayServer.set_native_icon].
		</member>
		<member name="application/config/name" type="String" setter="" getter="" default="&quot;&quot;">
			The project's name. It is used both by the Project Manager and by exporters. The project name can be translated by translating its value in localization files. The window title will be set to match the project name automatically on startup.
			[b]Note:[/b] Changing this value will also change the user data folder's path if [member application/config/use_custom_user_dir] is [code]false[/code]. After renaming the project, you will no longer be able to access existing data in [code]user://[/code] unless you rename the old folder to match the new project name. See [url=$DOCS_URL/tutorials/io/data_paths.html]Data paths[/url] in the documentation for more information.
		</member>
		<member name="application/config/name_localized" type="Dictionary" setter="" getter="" default="{}">
			Translations of the project's name. This setting is used by OS tools to translate application name on Android, iOS and macOS.
		</member>
		<member name="application/config/project_settings_override" type="String" setter="" getter="" default="&quot;&quot;">
			Specifies a file to override project settings. For example: [code]user://custom_settings.cfg[/code]. See "Overriding" in the [ProjectSettings] class description at the top for more information.
			[b]Note:[/b] Regardless of this setting's value, [code]res://override.cfg[/code] will still be read to override the project settings.
		</member>
		<member name="application/config/quit_on_go_back" type="bool" setter="" getter="" default="true">
			If [code]true[/code], the application quits automatically when navigating back (e.g. using the system "Back" button on Android).
		</member>
		<member name="application/config/use_custom_user_dir" type="bool" setter="" getter="" default="false">
			If [code]true[/code], the project will save user data to its own user directory. If [member application/config/custom_user_dir_name] is empty, [code]&lt;OS user data directory&gt;/&lt;project name&gt;[/code] directory will be used. If [code]false[/code], the project will save user data to [code]&lt;OS user data directory&gt;/Redot/app_userdata/&lt;project name&gt;[/code].
			See also [url=$DOCS_URL/tutorials/io/data_paths.html#accessing-persistent-user-data-user]File paths in Redot projects[/url]. This setting is only effective on desktop platforms.
		</member>
		<member name="application/config/use_hidden_project_data_directory" type="bool" setter="" getter="" default="true">
			If [code]true[/code], the project will use a hidden directory ([code].godot[/code]) for storing project-specific data (metadata, shader cache, etc.).
			If [code]false[/code], a non-hidden directory ([code]godot[/code]) will be used instead.
			[b]Note:[/b] Restart the application after changing this setting.
			[b]Note:[/b] Changing this value can help on platforms or with third-party tools where hidden directory patterns are disallowed. Only modify this setting if you know that your environment requires it, as changing the default can impact compatibility with some external tools or plugins which expect the default [code].godot[/code] folder.
		</member>
		<member name="application/config/version" type="String" setter="" getter="" default="&quot;&quot;">
			The project's human-readable version identifier. This is used by exporters if the version identifier isn't overridden there. If [member application/config/version] is an empty string and the version identifier isn't overridden in an exporter, the exporter will use [code]1.0.0[/code] as a version identifier.
		</member>
		<member name="application/config/windows_native_icon" type="String" setter="" getter="" default="&quot;&quot;">
			Icon set in [code].ico[/code] format used on Windows to set the game's icon. This is done automatically on start by calling [method DisplayServer.set_native_icon].
		</member>
		<member name="application/run/delta_smoothing" type="bool" setter="" getter="" default="true">
			Time samples for frame deltas are subject to random variation introduced by the platform, even when frames are displayed at regular intervals thanks to V-Sync. This can lead to jitter. Delta smoothing can often give a better result by filtering the input deltas to correct for minor fluctuations from the refresh rate.
			[b]Note:[/b] Delta smoothing is only attempted when [member display/window/vsync/vsync_mode] is set to [code]enabled[/code], as it does not work well without V-Sync.
			It may take several seconds at a stable frame rate before the smoothing is initially activated. It will only be active on machines where performance is adequate to render frames at the refresh rate.
		</member>
		<member name="application/run/disable_stderr" type="bool" setter="" getter="" default="false">
			If [code]true[/code], disables printing to standard error. If [code]true[/code], this also hides error and warning messages printed by [method @GlobalScope.push_error] and [method @GlobalScope.push_warning]. See also [member application/run/disable_stdout].
			Changes to this setting will only be applied upon restarting the application. To control this at runtime, use [member Engine.print_error_messages].
		</member>
		<member name="application/run/disable_stdout" type="bool" setter="" getter="" default="false">
			If [code]true[/code], disables printing to standard output. This is equivalent to starting the editor or project with the [code]--quiet[/code] [url=$DOCS_URL/tutorials/editor/command_line_tutorial.html]command line argument[/url]. See also [member application/run/disable_stderr].
			Changes to this setting will only be applied upon restarting the application. To control this at runtime, use [member Engine.print_to_stdout].
		</member>
		<member name="application/run/enable_alt_space_menu" type="bool" setter="" getter="" default="false">
			If [code]true[/code], allows the [kbd]Alt + Space[/kbd] keys to display the window menu. This menu allows the user to perform various window management operations such as moving, resizing, or minimizing the window.
			[b]Note:[/b] When the menu is displayed, project execution will pause until the menu is [i]fully[/i] closed due to Windows behavior. Consider this when enabling this setting in a networked multiplayer game. The menu is only considered fully closed when an option is selected, when the user clicks outside, or when [kbd]Escape[/kbd] is pressed after bringing up the window menu [i]and[/i] another key is pressed afterwards.
			[b]Note:[/b] This setting is implemented only on Windows.
		</member>
		<member name="application/run/flush_stdout_on_print" type="bool" setter="" getter="" default="false">
			If [code]true[/code], flushes the standard output stream every time a line is printed. This affects both terminal logging and file logging.
			When running a project, this setting must be enabled if you want logs to be collected by service managers such as systemd/journalctl. This setting is disabled by default on release builds, since flushing on every printed line will negatively affect performance if lots of lines are printed in a rapid succession. Also, if this setting is enabled, logged files will still be written successfully if the application crashes or is otherwise killed by the user (without being closed "normally").
			[b]Note:[/b] Regardless of this setting, the standard error stream ([code]stderr[/code]) is always flushed when a line is printed to it.
			Changes to this setting will only be applied upon restarting the application.
		</member>
		<member name="application/run/flush_stdout_on_print.debug" type="bool" setter="" getter="" default="true">
			Debug build override for [member application/run/flush_stdout_on_print], as performance is less important during debugging.
			Changes to this setting will only be applied upon restarting the application.
		</member>
		<member name="application/run/frame_delay_msec" type="int" setter="" getter="" default="0">
			Forces a [i]constant[/i] delay between frames in the main loop (in milliseconds). In most situations, [member application/run/max_fps] should be preferred as an FPS limiter as it's more precise.
			This setting can be overridden using the [code]--frame-delay &lt;ms;&gt;[/code] command line argument.
		</member>
		<member name="application/run/low_processor_mode" type="bool" setter="" getter="" default="false">
			If [code]true[/code], enables low-processor usage mode. When enabled, the engine takes longer to redraw, but only redraws the screen if necessary. This may lower power consumption, and is intended for editors or mobile applications. For most games, because the screen needs to be redrawn every frame, it is recommended to keep this setting disabled.
		</member>
		<member name="application/run/low_processor_mode_sleep_usec" type="int" setter="" getter="" default="6900">
			Amount of sleeping between frames when the low-processor usage mode is enabled (in microseconds). Higher values will result in lower CPU usage.
		</member>
		<member name="application/run/main_loop_type" type="String" setter="" getter="" default="&quot;SceneTree&quot;">
			The name of the type implementing the engine's main loop.
		</member>
		<member name="application/run/main_scene" type="String" setter="" getter="" default="&quot;&quot;">
			Path to the main scene file that will be loaded when the project runs.
		</member>
		<member name="application/run/max_fps" type="int" setter="" getter="" default="0">
			Maximum number of frames per second allowed. A value of [code]0[/code] means "no limit". The actual number of frames per second may still be below this value if the CPU or GPU cannot keep up with the project logic and rendering.
			Limiting the FPS can be useful to reduce system power consumption, which reduces heat and noise emissions (and improves battery life on mobile devices).
			If [member display/window/vsync/vsync_mode] is set to [code]Enabled[/code] or [code]Adaptive[/code], it takes precedence and the forced FPS number cannot exceed the monitor's refresh rate.
			If [member display/window/vsync/vsync_mode] is [code]Enabled[/code], on monitors with variable refresh rate enabled (G-Sync/FreeSync), using an FPS limit a few frames lower than the monitor's refresh rate will [url=https://blurbusters.com/howto-low-lag-vsync-on/]reduce input lag while avoiding tearing[/url].
			If [member display/window/vsync/vsync_mode] is [code]Disabled[/code], limiting the FPS to a high value that can be consistently reached on the system can reduce input lag compared to an uncapped framerate. Since this works by ensuring the GPU load is lower than 100%, this latency reduction is only effective in GPU-bottlenecked scenarios, not CPU-bottlenecked scenarios.
			See also [member physics/common/physics_ticks_per_second].
			This setting can be overridden using the [code]--max-fps &lt;fps&gt;[/code] command line argument (including with a value of [code]0[/code] for unlimited framerate).
			[b]Note:[/b] This property is only read when the project starts. To change the rendering FPS cap at runtime, set [member Engine.max_fps] instead.
		</member>
		<member name="application/run/print_header" type="bool" setter="" getter="" default="true">
			If [code]true[/code], the engine header is printed in the console on startup. This header describes the current version of the engine, as well as the renderer being used. This behavior can also be disabled on the command line with the [code]--no-header[/code] option.
		</member>
		<member name="audio/buses/channel_disable_threshold_db" type="float" setter="" getter="" default="-60.0">
			Audio buses will disable automatically when sound goes below a given dB threshold for a given time. This saves CPU as effects assigned to that bus will no longer do any processing.
		</member>
		<member name="audio/buses/channel_disable_time" type="float" setter="" getter="" default="2.0">
			Audio buses will disable automatically when sound goes below a given dB threshold for a given time. This saves CPU as effects assigned to that bus will no longer do any processing.
		</member>
		<member name="audio/buses/default_bus_layout" type="String" setter="" getter="" default="&quot;res://default_bus_layout.tres&quot;">
			Default [AudioBusLayout] resource file to use in the project, unless overridden by the scene.
		</member>
		<member name="audio/driver/driver" type="String" setter="" getter="">
			Specifies the audio driver to use. This setting is platform-dependent as each platform supports different audio drivers. If left empty, the default audio driver will be used.
			The [code]Dummy[/code] audio driver disables all audio playback and recording, which is useful for non-game applications as it reduces CPU usage. It also prevents the engine from appearing as an application playing audio in the OS' audio mixer.
			To query the value that is being used at run-time (which may be overridden by command-line arguments or headless mode), use [method AudioServer.get_driver_name].
			[b]Note:[/b] The driver in use can be overridden at runtime via the [code]--audio-driver[/code] [url=$DOCS_URL/tutorials/editor/command_line_tutorial.html]command line argument[/url].
		</member>
		<member name="audio/driver/enable_input" type="bool" setter="" getter="" default="false">
			If [code]true[/code], microphone input will be allowed. This requires appropriate permissions to be set when exporting to Android or iOS.
			[b]Note:[/b] If the operating system blocks access to audio input devices (due to the user's privacy settings), audio capture will only return silence. On Windows 10 and later, make sure that apps are allowed to access the microphone in the OS' privacy settings.
		</member>
		<member name="audio/driver/mix_rate" type="int" setter="" getter="" default="44100">
			Target mixing rate used for audio (in Hz). In general, it's better to not touch this and leave it to the host operating system.
			[b]Note:[/b] On iOS and macOS, mixing rate is determined by audio driver, this value is ignored.
			[b]Note:[/b] Input and output mixing rates might be different. Use [method AudioServer.get_mix_rate] and [method AudioServer.get_input_mix_rate] to get actual values.
		</member>
		<member name="audio/driver/mix_rate.web" type="int" setter="" getter="" default="0">
			Safer override for [member audio/driver/mix_rate] in the Web platform. Here [code]0[/code] means "let the browser choose" (since some browsers do not like forcing the mix rate).
		</member>
		<member name="audio/driver/output_latency" type="int" setter="" getter="" default="15">
			Specifies the preferred output latency in milliseconds for audio. Lower values will result in lower audio latency at the cost of increased CPU usage. Low values may result in audible crackling on slower hardware.
			Audio output latency may be constrained by the host operating system and audio hardware drivers. If the host can not provide the specified audio output latency then Redot will attempt to use the nearest latency allowed by the host. As such you should always use [method AudioServer.get_output_latency] to determine the actual audio output latency.
			Audio output latency can be overridden using the [code]--audio-output-latency &lt;ms&gt;[/code] command line argument.
			[b]Note:[/b] This setting is ignored on Android, and on all versions of Windows prior to Windows 10.
		</member>
		<member name="audio/driver/output_latency.web" type="int" setter="" getter="" default="50">
			Safer override for [member audio/driver/output_latency] in the Web platform, to avoid audio issues especially on mobile devices.
		</member>
		<member name="audio/general/2d_panning_strength" type="float" setter="" getter="" default="0.5">
			The base strength of the panning effect for all [AudioStreamPlayer2D] nodes. The panning strength can be further scaled on each Node using [member AudioStreamPlayer2D.panning_strength]. A value of [code]0.0[/code] disables stereo panning entirely, leaving only volume attenuation in place. A value of [code]1.0[/code] completely mutes one of the channels if the sound is located exactly to the left (or right) of the listener.
			The default value of [code]0.5[/code] is tuned for headphones. When using speakers, you may find lower values to sound better as speakers have a lower stereo separation compared to headphones.
		</member>
		<member name="audio/general/3d_panning_strength" type="float" setter="" getter="" default="0.5">
			The base strength of the panning effect for all [AudioStreamPlayer3D] nodes. The panning strength can be further scaled on each Node using [member AudioStreamPlayer3D.panning_strength]. A value of [code]0.0[/code] disables stereo panning entirely, leaving only volume attenuation in place. A value of [code]1.0[/code] completely mutes one of the channels if the sound is located exactly to the left (or right) of the listener.
			The default value of [code]0.5[/code] is tuned for headphones which means that the opposite side channel goes no lower than 50% of the volume of the nearside channel. You may find that you can set this value higher for speakers to have the same effect since both ears can hear from each speaker.
		</member>
		<member name="audio/general/default_playback_type" type="int" setter="" getter="" default="0" experimental="">
			Specifies the default playback type of the platform.
			The default value is set to [b]Stream[/b], as most platforms have no issues mixing streams.
		</member>
		<member name="audio/general/default_playback_type.web" type="int" setter="" getter="" default="1" experimental="">
			Specifies the default playback type of the Web platform.
			The default value is set to [b]Sample[/b] as the Web platform is not suited to mix audio streams outside of the Web Audio API, especially when exporting a single-threaded game. [b]Sample[/b] allows for lower latency on the web platform at the cost of flexibility ([AudioEffect]s are not supported).
			[b]Warning:[/b] Forcing [b]Stream[/b] on the Web platform may cause high audio latency and crackling, especially when exporting a multi-threaded game.
		</member>
		<member name="audio/general/ios/mix_with_others" type="bool" setter="" getter="" default="false">
			Sets the [url=https://developer.apple.com/documentation/avfaudio/avaudiosession/categoryoptions/1616611-mixwithothers]mixWithOthers[/url] option for the AVAudioSession on iOS. This will override the mix behavior, if the category is set to [code]Play and Record[/code], [code]Playback[/code], or [code]Multi Route[/code].
			[code]Ambient[/code] always has this set per default.
		</member>
		<member name="audio/general/ios/session_category" type="int" setter="" getter="" default="0">
			Sets the [url=https://developer.apple.com/documentation/avfaudio/avaudiosessioncategory]AVAudioSessionCategory[/url] on iOS. Use the [code]Playback[/code] category to get sound output, even if the phone is in silent mode.
		</member>
		<member name="audio/general/text_to_speech" type="bool" setter="" getter="" default="false">
			If [code]true[/code], text-to-speech support is enabled on startup, otherwise it is enabled first time TTS method is used, see [method DisplayServer.tts_get_voices] and [method DisplayServer.tts_speak].
			[b]Note:[/b] Enabling TTS can cause addition idle CPU usage and interfere with the sleep mode, so consider disabling it if TTS is not used.
		</member>
		<member name="audio/video/video_delay_compensation_ms" type="int" setter="" getter="" default="0">
			Setting to hardcode audio delay when playing video. Best to leave this unchanged unless you know what you are doing.
		</member>
		<member name="collada/use_ambient" type="bool" setter="" getter="" default="false">
			If [code]true[/code], ambient lights will be imported from COLLADA models as [DirectionalLight3D]. If [code]false[/code], ambient lights will be ignored.
		</member>
		<member name="compression/formats/gzip/compression_level" type="int" setter="" getter="" default="-1">
			The default compression level for gzip. Affects compressed scenes and resources. Higher levels result in smaller files at the cost of compression speed. Decompression speed is mostly unaffected by the compression level. [code]-1[/code] uses the default gzip compression level, which is identical to [code]6[/code] but could change in the future due to underlying zlib updates.
		</member>
		<member name="compression/formats/zlib/compression_level" type="int" setter="" getter="" default="-1">
			The default compression level for Zlib. Affects compressed scenes and resources. Higher levels result in smaller files at the cost of compression speed. Decompression speed is mostly unaffected by the compression level. [code]-1[/code] uses the default gzip compression level, which is identical to [code]6[/code] but could change in the future due to underlying zlib updates.
		</member>
		<member name="compression/formats/zstd/compression_level" type="int" setter="" getter="" default="3">
			The default compression level for Zstandard. Affects compressed scenes and resources. Higher levels result in smaller files at the cost of compression speed. Decompression speed is mostly unaffected by the compression level.
		</member>
		<member name="compression/formats/zstd/long_distance_matching" type="bool" setter="" getter="" default="false">
			Enables [url=https://github.com/facebook/zstd/releases/tag/v1.3.2]long-distance matching[/url] in Zstandard.
		</member>
		<member name="compression/formats/zstd/window_log_size" type="int" setter="" getter="" default="27">
			Largest size limit (in power of 2) allowed when compressing using long-distance matching with Zstandard. Higher values can result in better compression, but will require more memory when compressing and decompressing.
		</member>
		<member name="debug/canvas_items/debug_redraw_color" type="Color" setter="" getter="" default="Color(1, 0.2, 0.2, 0.5)">
			If canvas item redraw debugging is active, this color will be flashed on canvas items when they redraw.
		</member>
		<member name="debug/canvas_items/debug_redraw_time" type="float" setter="" getter="" default="1.0">
			If canvas item redraw debugging is active, this will be the time the flash will last each time they redraw.
		</member>
		<member name="debug/file_logging/enable_file_logging" type="bool" setter="" getter="" default="false">
			If [code]true[/code], logs all output and error messages to files. See also [member debug/file_logging/log_path], [member debug/file_logging/max_log_files], and [member application/run/flush_stdout_on_print].
		</member>
		<member name="debug/file_logging/enable_file_logging.pc" type="bool" setter="" getter="" default="true">
			Desktop override for [member debug/file_logging/enable_file_logging], as log files are not readily accessible on mobile/Web platforms.
		</member>
		<member name="debug/file_logging/log_path" type="String" setter="" getter="" default="&quot;user://logs/godot.log&quot;">
			Path at which to store log files for the project. Using a path under [code]user://[/code] is recommended.
			This can be specified manually on the command line using the [code]--log-file &lt;file&gt;[/code] [url=$DOCS_URL/tutorials/editor/command_line_tutorial.html]command line argument[/url]. If this command line argument is specified, log rotation is automatically disabled (see [member debug/file_logging/max_log_files]).
		</member>
		<member name="debug/file_logging/max_log_files" type="int" setter="" getter="" default="5">
			Specifies the maximum number of log files allowed (used for rotation). Set to [code]1[/code] to disable log file rotation.
			If the [code]--log-file &lt;file&gt;[/code] [url=$DOCS_URL/tutorials/editor/command_line_tutorial.html]command line argument[/url] is used, log rotation is always disabled.
		</member>
		<member name="debug/gdscript/warnings/assert_always_false" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when an [code]assert[/code] call always evaluates to [code]false[/code].
		</member>
		<member name="debug/gdscript/warnings/assert_always_true" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when an [code]assert[/code] call always evaluates to [code]true[/code].
		</member>
		<member name="debug/gdscript/warnings/confusable_capture_reassignment" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when a local variable captured by a lambda is reassigned, since this does not modify the outer local variable.
		</member>
		<member name="debug/gdscript/warnings/confusable_identifier" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when an identifier contains characters that can be confused with something else, like when mixing different alphabets.
		</member>
		<member name="debug/gdscript/warnings/confusable_local_declaration" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when an identifier declared in the nested block has the same name as an identifier declared below in the parent block.
		</member>
		<member name="debug/gdscript/warnings/confusable_local_usage" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when an identifier that will be shadowed below in the block is used.
		</member>
		<member name="debug/gdscript/warnings/constant_used_as_function" type="int" setter="" getter="" default="1" deprecated="This warning is never produced. Instead, an error is generated if the expression type is known at compile time.">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when a constant is used as a function.
		</member>
		<member name="debug/gdscript/warnings/deprecated_keyword" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when deprecated keywords are used.
			[b]Note:[/b] There are currently no deprecated keywords, so this warning is never produced.
		</member>
		<member name="debug/gdscript/warnings/empty_file" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when an empty file is parsed.
		</member>
		<member name="debug/gdscript/warnings/enable" type="bool" setter="" getter="" default="true">
			If [code]true[/code], enables specific GDScript warnings (see [code]debug/gdscript/warnings/*[/code] settings). If [code]false[/code], disables all GDScript warnings.
		</member>
		<member name="debug/gdscript/warnings/enum_variable_without_default" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when a variable has an enum type but no explicit default value, but only if the enum does not contain [code]0[/code] as a valid value.
		</member>
		<member name="debug/gdscript/warnings/exclude_addons" type="bool" setter="" getter="" default="true">
			If [code]true[/code], scripts in the [code]res://addons[/code] folder will not generate warnings.
		</member>
		<member name="debug/gdscript/warnings/function_used_as_property" type="int" setter="" getter="" default="1" deprecated="This warning is never produced. When a function is used as a property, a [Callable] is returned.">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when using a function as if it is a property.
		</member>
		<member name="debug/gdscript/warnings/get_node_default_without_onready" type="int" setter="" getter="" default="2">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when [method Node.get_node] (or the shorthand [code]$[/code]) is used as default value of a class variable without the [code]@onready[/code] annotation.
		</member>
		<member name="debug/gdscript/warnings/incompatible_ternary" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when a ternary operator may emit values with incompatible types.
		</member>
		<member name="debug/gdscript/warnings/inference_on_variant" type="int" setter="" getter="" default="2">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when a static inferred type uses a [Variant] as initial value, which makes the static type to also be Variant.
		</member>
		<member name="debug/gdscript/warnings/inferred_declaration" type="int" setter="" getter="" default="0">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when a variable, constant, or parameter has an implicitly inferred static type.
			[b]Note:[/b] This warning is recommended [i]in addition[/i] to [member debug/gdscript/warnings/untyped_declaration] if you want to always specify the type explicitly. Having [code]INFERRED_DECLARATION[/code] warning level higher than [code]UNTYPED_DECLARATION[/code] warning level makes little sense and is not recommended.
		</member>
		<member name="debug/gdscript/warnings/int_as_enum_without_cast" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when trying to use an integer as an enum without an explicit cast.
		</member>
		<member name="debug/gdscript/warnings/int_as_enum_without_match" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when trying to use an integer as an enum when there is no matching enum member for that numeric value.
		</member>
		<member name="debug/gdscript/warnings/integer_division" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when dividing an integer by another integer (the decimal part will be discarded).
		</member>
		<member name="debug/gdscript/warnings/missing_tool" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when the base class script has the [code]@tool[/code] annotation, but the current class script does not have it.
		</member>
		<member name="debug/gdscript/warnings/narrowing_conversion" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when passing a floating-point value to a function that expects an integer (it will be converted and lose precision).
		</member>
		<member name="debug/gdscript/warnings/native_method_override" type="int" setter="" getter="" default="2">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when a method in the script overrides a native method, because it may not behave as expected.
		</member>
		<member name="debug/gdscript/warnings/onready_with_export" type="int" setter="" getter="" default="2">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when the [code]@onready[/code] annotation is used together with the [code]@export[/code] annotation, since it may not behave as expected.
		</member>
		<member name="debug/gdscript/warnings/property_used_as_function" type="int" setter="" getter="" default="1" deprecated="This warning is never produced. Instead, an error is generated if the expression type is known at compile time.">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when using a property as if it is a function.
		</member>
		<member name="debug/gdscript/warnings/redundant_await" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when a function that is not a coroutine is called with await.
		</member>
		<member name="debug/gdscript/warnings/redundant_static_unload" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when the [code]@static_unload[/code] annotation is used in a script without any static variables.
		</member>
		<member name="debug/gdscript/warnings/renamed_in_godot_4_hint" type="bool" setter="" getter="" default="true">
			When enabled, using a property, enum, or function that was renamed since Godot 3 will produce a hint if an error occurs.
		</member>
		<member name="debug/gdscript/warnings/return_value_discarded" type="int" setter="" getter="" default="0">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when calling a function without using its return value (by assigning it to a variable or using it as a function argument). These return values are sometimes used to indicate possible errors using the [enum Error] enum.
		</member>
		<member name="debug/gdscript/warnings/shadowed_global_identifier" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when defining a local or member variable, signal, or enum that would have the same name as a built-in function or global class name, thus shadowing it.
		</member>
		<member name="debug/gdscript/warnings/shadowed_variable" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when a local variable or local constant shadows a member declared in the current class.
		</member>
		<member name="debug/gdscript/warnings/shadowed_variable_base_class" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when a local variable or local constant shadows a member declared in a base class.
		</member>
		<member name="debug/gdscript/warnings/standalone_expression" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when calling an expression that may have no effect on the surrounding code, such as writing [code]2 + 2[/code] as a statement.
		</member>
		<member name="debug/gdscript/warnings/standalone_ternary" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when calling a ternary expression that may have no effect on the surrounding code, such as writing [code]42 if active else 0[/code] as a statement.
		</member>
		<member name="debug/gdscript/warnings/static_called_on_instance" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when calling a static method from an instance of a class instead of from the class directly.
		</member>
		<member name="debug/gdscript/warnings/unassigned_variable" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when using a variable that wasn't previously assigned.
		</member>
		<member name="debug/gdscript/warnings/unassigned_variable_op_assign" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when assigning a variable using an assignment operator like [code]+=[/code] if the variable wasn't previously assigned.
		</member>
		<member name="debug/gdscript/warnings/unreachable_code" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when unreachable code is detected (such as after a [code]return[/code] statement that will always be executed).
		</member>
		<member name="debug/gdscript/warnings/unreachable_pattern" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when an unreachable [code]match[/code] pattern is detected.
		</member>
		<member name="debug/gdscript/warnings/unsafe_call_argument" type="int" setter="" getter="" default="0">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when using an expression whose type may not be compatible with the function parameter expected.
		</member>
		<member name="debug/gdscript/warnings/unsafe_cast" type="int" setter="" getter="" default="0">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when a [Variant] value is cast to a non-Variant.
		</member>
		<member name="debug/gdscript/warnings/unsafe_method_access" type="int" setter="" getter="" default="0">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when calling a method whose presence is not guaranteed at compile-time in the class.
		</member>
		<member name="debug/gdscript/warnings/unsafe_property_access" type="int" setter="" getter="" default="0">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when accessing a property whose presence is not guaranteed at compile-time in the class.
		</member>
		<member name="debug/gdscript/warnings/unsafe_void_return" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when returning a call from a [code]void[/code] function when such call cannot be guaranteed to be also [code]void[/code].
		</member>
		<member name="debug/gdscript/warnings/untyped_declaration" type="int" setter="" getter="" default="0">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when a variable or parameter has no static type, or if a function has no static return type.
			[b]Note:[/b] This warning is recommended together with [member EditorSettings.text_editor/completion/add_type_hints] to help achieve type safety.
		</member>
		<member name="debug/gdscript/warnings/unused_local_constant" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when a local constant is never used.
		</member>
		<member name="debug/gdscript/warnings/unused_onready_overriding_trait" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when the [code]@onready[/code] is not used overridden variable from traits and  trait variable uses [code]@onready[/code].
		</member>
		<member name="debug/gdscript/warnings/unused_parameter" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when a function parameter is never used.
		</member>
		<member name="debug/gdscript/warnings/unused_private_class_variable" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when a private member variable is never used.
		</member>
		<member name="debug/gdscript/warnings/unused_signal" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when a signal is declared but never explicitly used in the class.
		</member>
		<member name="debug/gdscript/warnings/unused_static_overriding_trait" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when the [code]static[/code] is not used overridden variable or function from traits and  trait variable or function uses [code]static[/code].
		</member>
		<member name="debug/gdscript/warnings/unused_variable" type="int" setter="" getter="" default="1">
			When set to [code]warn[/code] or [code]error[/code], produces a warning or an error respectively when a local variable is unused.
		</member>
		<member name="debug/settings/crash_handler/message" type="String" setter="" getter="" default="&quot;Please include this when reporting the bug to the project developer.&quot;">
			Message to be displayed before the backtrace when the engine crashes. By default, this message is only used in exported projects due to the editor-only override applied to this setting.
		</member>
		<member name="debug/settings/crash_handler/message.editor" type="String" setter="" getter="" default="&quot;Please include this when reporting the bug on: https://github.com/Redot-Engine/redot-engine/issues&quot;">
			Editor-only override for [member debug/settings/crash_handler/message]. Does not affect exported projects in debug or release mode.
		</member>
		<member name="debug/settings/gdscript/always_track_call_stacks" type="bool" setter="" getter="" default="false">
			Whether GDScript call stacks will be tracked in release builds, thus allowing [method Engine.capture_script_backtraces] to function.
			Enabling this comes at the cost of roughly 40 KiB for every thread that runs any GDScript code.
			[b]Note:[/b] This setting has no effect on editor builds or debug builds, where GDScript call stacks are tracked regardless.
		</member>
		<member name="debug/settings/gdscript/always_track_local_variables" type="bool" setter="" getter="" default="false">
			Whether GDScript local variables will be tracked in all builds, including export builds, thus allowing [method Engine.capture_script_backtraces] to capture them when enabling its [code]include_variables[/code] parameter.
			Enabling this comes at the cost of roughly 50 bytes of memory per local variable, for every compiled class in the entire project, so can be several MiB in larger projects.
			[b]Note:[/b] This setting has no effect when running the game from the editor, where GDScript local variables are tracked regardless.
		</member>
		<member name="debug/settings/gdscript/max_call_stack" type="int" setter="" getter="" default="1024">
			Maximum call stack allowed for debugging GDScript.
		</member>
		<member name="debug/settings/physics_interpolation/enable_warnings" type="bool" setter="" getter="" default="true">
			If [code]true[/code], enables warnings which can help pinpoint where nodes are being incorrectly updated, which will result in incorrect interpolation and visual glitches.
			When a node is being interpolated, it is essential that the transform is set during [method Node._physics_process] (during a physics tick) rather than [method Node._process] (during a frame).
		</member>
		<member name="debug/settings/profiler/max_functions" type="int" setter="" getter="" default="16384">
			Maximum number of functions per frame allowed when profiling.
		</member>
		<member name="debug/settings/profiler/max_timestamp_query_elements" type="int" setter="" getter="" default="256">
			Maximum number of timestamp query elements allowed per frame for visual profiling.
		</member>
		<member name="debug/settings/stdout/print_fps" type="bool" setter="" getter="" default="false">
			Print frames per second to standard output every second.
		</member>
		<member name="debug/settings/stdout/print_gpu_profile" type="bool" setter="" getter="" default="false">
			Print GPU profile information to standard output every second. This includes how long each frame takes the GPU to render on average, broken down into different steps of the render pipeline, such as CanvasItems, shadows, glow, etc.
		</member>
		<member name="debug/settings/stdout/verbose_stdout" type="bool" setter="" getter="" default="false">
			Print more information to standard output when running. It displays information such as memory leaks, which scenes and resources are being loaded, etc. This can also be enabled using the [code]--verbose[/code] or [code]-v[/code] [url=$DOCS_URL/tutorials/editor/command_line_tutorial.html]command line argument[/url], even on an exported project. See also [method OS.is_stdout_verbose] and [method @GlobalScope.print_verbose].
		</member>
		<member name="debug/shader_language/warnings/device_limit_exceeded" type="bool" setter="" getter="" default="true">
			When set to [code]true[/code], produces a warning when the shader exceeds certain device limits. Currently, the only device limit checked is the limit on uniform buffer size. More device limits will be added in the future.
		</member>
		<member name="debug/shader_language/warnings/enable" type="bool" setter="" getter="" default="true">
			If [code]true[/code], enables specific shader warnings (see [code]debug/shader_language/warnings/*[/code] settings). If [code]false[/code], disables all shader warnings.
		</member>
		<member name="debug/shader_language/warnings/float_comparison" type="bool" setter="" getter="" default="true">
			When set to [code]true[/code], produces a warning when two floating-point numbers are compared directly with the [code]==[/code] operator or the [code]!=[/code] operator.
		</member>
		<member name="debug/shader_language/warnings/formatting_error" type="bool" setter="" getter="" default="true">
			When set to [code]true[/code], produces a warning upon encountering certain formatting errors. Currently this only checks for empty statements. More formatting errors may be added over time.
		</member>
		<member name="debug/shader_language/warnings/magic_position_write" type="bool" setter="" getter="" default="true">
			When set to [code]true[/code], produces a warning when the shader contains [code]POSITION = vec4(vertex,[/code] as this was very common code written in Godot 4.2 and earlier that was paired with a QuadMesh to produce a full screen post processes pass. With the switch to reversed z in 4.3, this trick no longer works, as it implicitly relied on the [code]VERTEX.z[/code] being 0.
		</member>
		<member name="debug/shader_language/warnings/treat_warnings_as_errors" type="bool" setter="" getter="" default="false">
			When set to [code]true[/code], warnings are treated as errors.
		</member>
		<member name="debug/shader_language/warnings/unused_constant" type="bool" setter="" getter="" default="true">
			When set to [code]true[/code], produces a warning when a constant is never used.
		</member>
		<member name="debug/shader_language/warnings/unused_function" type="bool" setter="" getter="" default="true">
			When set to [code]true[/code], produces a warning when a function is never used.
		</member>
		<member name="debug/shader_language/warnings/unused_local_variable" type="bool" setter="" getter="" default="true">
			When set to [code]true[/code], produces a warning when a local variable is never used.
		</member>
		<member name="debug/shader_language/warnings/unused_struct" type="bool" setter="" getter="" default="true">
			When set to [code]true[/code], produces a warning when a struct is never used.
		</member>
		<member name="debug/shader_language/warnings/unused_uniform" type="bool" setter="" getter="" default="true">
			When set to [code]true[/code], produces a warning when a uniform is never used.
		</member>
		<member name="debug/shader_language/warnings/unused_varying" type="bool" setter="" getter="" default="true">
			When set to [code]true[/code], produces a warning when a varying is never used.
		</member>
		<member name="debug/shapes/avoidance/2d/agents_radius_color" type="Color" setter="" getter="" default="Color(1, 1, 0, 0.25)">
			Color of the avoidance agents radius, visible when "Visible Avoidance" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/avoidance/2d/enable_agents_radius" type="bool" setter="" getter="" default="true">
			If enabled, displays avoidance agents radius when "Visible Avoidance" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/avoidance/2d/enable_obstacles_radius" type="bool" setter="" getter="" default="true">
			If enabled, displays avoidance obstacles radius when "Visible Avoidance" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/avoidance/2d/enable_obstacles_static" type="bool" setter="" getter="" default="true">
			If enabled, displays static avoidance obstacles when "Visible Avoidance" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/avoidance/2d/obstacles_radius_color" type="Color" setter="" getter="" default="Color(1, 0.5, 0, 0.25)">
			Color of the avoidance obstacles radius, visible when "Visible Avoidance" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/avoidance/2d/obstacles_static_edge_pushin_color" type="Color" setter="" getter="" default="Color(1, 0, 0, 1)">
			Color of the static avoidance obstacles edges when their vertices are winded in order to push agents in, visible when "Visible Avoidance" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/avoidance/2d/obstacles_static_edge_pushout_color" type="Color" setter="" getter="" default="Color(1, 1, 0, 1)">
			Color of the static avoidance obstacles edges when their vertices are winded in order to push agents out, visible when "Visible Avoidance" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/avoidance/2d/obstacles_static_face_pushin_color" type="Color" setter="" getter="" default="Color(1, 0, 0, 0)">
			Color of the static avoidance obstacles faces when their vertices are winded in order to push agents in, visible when "Visible Avoidance" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/avoidance/2d/obstacles_static_face_pushout_color" type="Color" setter="" getter="" default="Color(1, 1, 0, 0.5)">
			Color of the static avoidance obstacles faces when their vertices are winded in order to push agents out, visible when "Visible Avoidance" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/avoidance/3d/agents_radius_color" type="Color" setter="" getter="" default="Color(1, 1, 0, 0.25)">
			Color of the avoidance agents radius, visible when "Visible Avoidance" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/avoidance/3d/enable_agents_radius" type="bool" setter="" getter="" default="true">
			If enabled, displays avoidance agents radius when "Visible Avoidance" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/avoidance/3d/enable_obstacles_radius" type="bool" setter="" getter="" default="true">
			If enabled, displays avoidance obstacles radius when "Visible Avoidance" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/avoidance/3d/enable_obstacles_static" type="bool" setter="" getter="" default="true">
			If enabled, displays static avoidance obstacles when "Visible Avoidance" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/avoidance/3d/obstacles_radius_color" type="Color" setter="" getter="" default="Color(1, 0.5, 0, 0.25)">
			Color of the avoidance obstacles radius, visible when "Visible Avoidance" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/avoidance/3d/obstacles_static_edge_pushin_color" type="Color" setter="" getter="" default="Color(1, 0, 0, 1)">
			Color of the static avoidance obstacles edges when their vertices are winded in order to push agents in, visible when "Visible Avoidance" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/avoidance/3d/obstacles_static_edge_pushout_color" type="Color" setter="" getter="" default="Color(1, 1, 0, 1)">
			Color of the static avoidance obstacles edges when their vertices are winded in order to push agents out, visible when "Visible Avoidance" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/avoidance/3d/obstacles_static_face_pushin_color" type="Color" setter="" getter="" default="Color(1, 0, 0, 0)">
			Color of the static avoidance obstacles faces when their vertices are winded in order to push agents in, visible when "Visible Avoidance" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/avoidance/3d/obstacles_static_face_pushout_color" type="Color" setter="" getter="" default="Color(1, 1, 0, 0.5)">
			Color of the static avoidance obstacles faces when their vertices are winded in order to push agents out, visible when "Visible Avoidance" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/collision/contact_color" type="Color" setter="" getter="" default="Color(1, 0.2, 0.1, 0.8)">
			Color of the contact points between collision shapes, visible when "Visible Collision Shapes" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/collision/draw_2d_outlines" type="bool" setter="" getter="" default="true">
			Sets whether 2D physics will display collision outlines in game when "Visible Collision Shapes" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/collision/max_contacts_displayed" type="int" setter="" getter="" default="10000">
			Maximum number of contact points between collision shapes to display when "Visible Collision Shapes" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/collision/shape_color" type="Color" setter="" getter="" default="Color(0, 0.6, 0.7, 0.42)">
			Color of the collision shapes, visible when "Visible Collision Shapes" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/2d/agent_path_color" type="Color" setter="" getter="" default="Color(1, 0, 0, 1)">
			Color to display enabled navigation agent paths when an agent has debug enabled.
		</member>
		<member name="debug/shapes/navigation/2d/agent_path_point_size" type="float" setter="" getter="" default="4.0">
			Rasterized size (pixel) used to render navigation agent path points when an agent has debug enabled.
		</member>
		<member name="debug/shapes/navigation/2d/edge_connection_color" type="Color" setter="" getter="" default="Color(1, 0, 1, 1)">
			Color to display edge connections between navigation regions, visible when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/2d/enable_agent_paths" type="bool" setter="" getter="" default="true">
			If enabled, displays navigation agent paths when an agent has debug enabled.
		</member>
		<member name="debug/shapes/navigation/2d/enable_edge_connections" type="bool" setter="" getter="" default="true">
			If enabled, displays edge connections between navigation regions when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/2d/enable_edge_lines" type="bool" setter="" getter="" default="true">
			If enabled, displays navigation mesh polygon edges when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/2d/enable_geometry_face_random_color" type="bool" setter="" getter="" default="true">
			If enabled, colorizes each navigation mesh polygon face with a random color when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/2d/enable_link_connections" type="bool" setter="" getter="" default="true">
			If enabled, displays navigation link connections when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/2d/geometry_edge_color" type="Color" setter="" getter="" default="Color(0.5, 1, 1, 1)">
			Color to display enabled navigation mesh polygon edges, visible when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/2d/geometry_edge_disabled_color" type="Color" setter="" getter="" default="Color(0.5, 0.5, 0.5, 1)">
			Color to display disabled navigation mesh polygon edges, visible when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/2d/geometry_face_color" type="Color" setter="" getter="" default="Color(0.5, 1, 1, 0.4)">
			Color to display enabled navigation mesh polygon faces, visible when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/2d/geometry_face_disabled_color" type="Color" setter="" getter="" default="Color(0.5, 0.5, 0.5, 0.4)">
			Color to display disabled navigation mesh polygon faces, visible when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/2d/link_connection_color" type="Color" setter="" getter="" default="Color(1, 0.5, 1, 1)">
			Color to use to display navigation link connections, visible when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/2d/link_connection_disabled_color" type="Color" setter="" getter="" default="Color(0.5, 0.5, 0.5, 1)">
			Color to use to display disabled navigation link connections, visible when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/3d/agent_path_color" type="Color" setter="" getter="" default="Color(1, 0, 0, 1)">
			Color to display enabled navigation agent paths when an agent has debug enabled.
		</member>
		<member name="debug/shapes/navigation/3d/agent_path_point_size" type="float" setter="" getter="" default="4.0">
			Rasterized size (pixel) used to render navigation agent path points when an agent has debug enabled.
		</member>
		<member name="debug/shapes/navigation/3d/edge_connection_color" type="Color" setter="" getter="" default="Color(1, 0, 1, 1)">
			Color to display edge connections between navigation regions, visible when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/3d/enable_agent_paths" type="bool" setter="" getter="" default="true">
			If enabled, displays navigation agent paths when an agent has debug enabled.
		</member>
		<member name="debug/shapes/navigation/3d/enable_agent_paths_xray" type="bool" setter="" getter="" default="true">
			If enabled, displays navigation agent paths through geometry when an agent has debug enabled.
		</member>
		<member name="debug/shapes/navigation/3d/enable_edge_connections" type="bool" setter="" getter="" default="true">
			If enabled, displays edge connections between navigation regions when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/3d/enable_edge_connections_xray" type="bool" setter="" getter="" default="true">
			If enabled, displays edge connections between navigation regions through geometry when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/3d/enable_edge_lines" type="bool" setter="" getter="" default="true">
			If enabled, displays navigation mesh polygon edges when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/3d/enable_edge_lines_xray" type="bool" setter="" getter="" default="true">
			If enabled, displays navigation mesh polygon edges through geometry when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/3d/enable_geometry_face_random_color" type="bool" setter="" getter="" default="true">
			If enabled, colorizes each navigation mesh polygon face with a random color when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/3d/enable_link_connections" type="bool" setter="" getter="" default="true">
			If enabled, displays navigation link connections when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/3d/enable_link_connections_xray" type="bool" setter="" getter="" default="true">
			If enabled, displays navigation link connections through geometry when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/3d/geometry_edge_color" type="Color" setter="" getter="" default="Color(0.5, 1, 1, 1)">
			Color to display enabled navigation mesh polygon edges, visible when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/3d/geometry_edge_disabled_color" type="Color" setter="" getter="" default="Color(0.5, 0.5, 0.5, 1)">
			Color to display disabled navigation mesh polygon edges, visible when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/3d/geometry_face_color" type="Color" setter="" getter="" default="Color(0.5, 1, 1, 0.4)">
			Color to display enabled navigation mesh polygon faces, visible when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/3d/geometry_face_disabled_color" type="Color" setter="" getter="" default="Color(0.5, 0.5, 0.5, 0.4)">
			Color to display disabled navigation mesh polygon faces, visible when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/3d/link_connection_color" type="Color" setter="" getter="" default="Color(1, 0.5, 1, 1)">
			Color to use to display navigation link connections, visible when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/navigation/3d/link_connection_disabled_color" type="Color" setter="" getter="" default="Color(0.5, 0.5, 0.5, 1)">
			Color to use to display disabled navigation link connections, visible when "Visible Navigation" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/paths/geometry_color" type="Color" setter="" getter="" default="Color(0.1, 1, 0.7, 0.4)">
			Color of the curve path geometry, visible when "Visible Paths" is enabled in the Debug menu.
		</member>
		<member name="debug/shapes/paths/geometry_width" type="float" setter="" getter="" default="2.0">
			Line width of the curve path geometry, visible when "Visible Paths" is enabled in the Debug menu.
		</member>
		<member name="display/display_server/driver" type="String" setter="" getter="">
			Sets the driver to be used by the display server. This property can not be edited directly, instead, set the driver using the platform-specific overrides.
		</member>
		<member name="display/display_server/driver.android" type="String" setter="" getter="">
			Android override for [member display/display_server/driver].
		</member>
		<member name="display/display_server/driver.ios" type="String" setter="" getter="">
			iOS override for [member display/display_server/driver].
		</member>
		<member name="display/display_server/driver.linuxbsd" type="String" setter="" getter="">
			LinuxBSD override for [member display/display_server/driver].
		</member>
		<member name="display/display_server/driver.macos" type="String" setter="" getter="">
			MacOS override for [member display/display_server/driver].
		</member>
		<member name="display/display_server/driver.windows" type="String" setter="" getter="">
			Windows override for [member display/display_server/driver].
		</member>
		<member name="display/mouse_cursor/custom_image" type="String" setter="" getter="" default="&quot;&quot;">
			Custom image for the mouse cursor (limited to 256×256).
		</member>
		<member name="display/mouse_cursor/custom_image_hotspot" type="Vector2" setter="" getter="" default="Vector2(0, 0)">
			Hotspot for the custom mouse cursor image.
		</member>
		<member name="display/mouse_cursor/tooltip_position_offset" type="Vector2" setter="" getter="" default="Vector2(10, 10)">
			Position offset for tooltips, relative to the mouse cursor's hotspot.
		</member>
		<member name="display/window/dpi/allow_hidpi" type="bool" setter="" getter="" default="true">
			If [code]true[/code], allows HiDPI display on Windows, macOS, Android, iOS and Web. If [code]false[/code], the platform's low-DPI fallback will be used on HiDPI displays, which causes the window to be displayed in a blurry or pixelated manner (and can cause various window management bugs). Therefore, it is recommended to make your project scale to [url=$DOCS_URL/tutorials/rendering/multiple_resolutions.html]multiple resolutions[/url] instead of disabling this setting.
			[b]Note:[/b] This setting has no effect on Linux as DPI-awareness fallbacks are not supported there.
		</member>
		<member name="display/window/energy_saving/keep_screen_on" type="bool" setter="" getter="" default="true">
			If [code]true[/code], keeps the screen on (even in case of inactivity), so the screensaver does not take over. Works on desktop and mobile platforms.
		</member>
		<member name="display/window/frame_pacing/android/enable_frame_pacing" type="bool" setter="" getter="" default="true">
			Enable Swappy for stable frame pacing on Android. Highly recommended.
			[b]Note:[/b] This option will be forced off when using OpenXR.
		</member>
		<member name="display/window/frame_pacing/android/swappy_mode" type="int" setter="" getter="" default="2">
			Swappy mode to use. The options are:
			- [code]pipeline_forced_on[/code]: Try to honor [member Engine.max_fps]. Pipelining is always on. This is the same behavior as a desktop PC.
			- [code]auto_fps_pipeline_forced_on[/code]: Calculate the max FPS automatically. The actual max FPS will be between [code]0[/code] and [member Engine.max_fps]. While this sounds convenient, beware that Swappy will often downgrade the max FPS until it finds a value that can be maintained. That means, if your game runs between 40fps and 60fps on a 60hz screen, after some time Swappy will downgrade the max FPS so that the game renders at a perfect 30fps.
			- [code]auto_fps_auto_pipeline[/code]: Same as [code]auto_fps_pipeline_forced_on[/code], but if Swappy detects that rendering is very fast (for example it takes less than 8ms to render on a 60hz screen), Swappy will disable pipelining to minimize input latency. This is the default.
			[b]Note:[/b] If [member Engine.max_fps] is [code]0[/code], the actual max FPS will be considered to be the screen's refresh rate (often 60hz, 90hz, or 120hz, depending on device model and OS settings).
		</member>
		<member name="display/window/handheld/orientation" type="int" setter="" getter="" default="0">
			The default screen orientation to use on mobile devices. See [enum DisplayServer.ScreenOrientation] for possible values.
			[b]Note:[/b] When set to a portrait orientation, this project setting does not flip the project resolution's width and height automatically. Instead, you have to set [member display/window/size/viewport_width] and [member display/window/size/viewport_height] accordingly.
		</member>
		<member name="display/window/ios/allow_high_refresh_rate" type="bool" setter="" getter="" default="true">
			If [code]true[/code], iOS devices that support high refresh rate/"ProMotion" will be allowed to render at up to 120 frames per second.
		</member>
		<member name="display/window/ios/hide_home_indicator" type="bool" setter="" getter="" default="true">
			If [code]true[/code], the home indicator is hidden automatically. This only affects iOS devices without a physical home button.
		</member>
		<member name="display/window/ios/hide_status_bar" type="bool" setter="" getter="" default="true">
			If [code]true[/code], the status bar is hidden while the app is running.
		</member>
		<member name="display/window/ios/suppress_ui_gesture" type="bool" setter="" getter="" default="true">
			If [code]true[/code], it will require two swipes to access iOS UI that uses gestures.
			[b]Note:[/b] This setting has no effect on the home indicator if [code]hide_home_indicator[/code] is [code]true[/code].
		</member>
		<member name="display/window/per_pixel_transparency/allowed" type="bool" setter="" getter="" default="false">
			If [code]true[/code], allows per-pixel transparency for the window background. This affects performance, so leave it on [code]false[/code] unless you need it. See also [member display/window/size/transparent] and [member rendering/viewport/transparent_background].
		</member>
		<member name="display/window/size/always_on_top" type="bool" setter="" getter="" default="false">
			Forces the main window to be always on top.
			[b]Note:[/b] This setting is ignored on iOS, Android, and Web.
		</member>
		<member name="display/window/size/borderless" type="bool" setter="" getter="" default="false">
			Forces the main window to be borderless.
			[b]Note:[/b] This setting is ignored on iOS, Android, and Web.
		</member>
		<member name="display/window/size/extend_to_title" type="bool" setter="" getter="" default="false">
			Main window content is expanded to the full size of the window. Unlike a borderless window, the frame is left intact and can be used to resize the window, and the title bar is transparent, but has minimize/maximize/close buttons.
			[b]Note:[/b] This setting is implemented only on macOS.
		</member>
		<member name="display/window/size/initial_position" type="Vector2i" setter="" getter="" default="Vector2i(0, 0)">
			Main window initial position (in virtual desktop coordinates), this setting is used only if [member display/window/size/initial_position_type] is set to "Absolute" ([code]0[/code]).
			[b]Note:[/b] This setting only affects the exported project, or when the project is run from the command line. In the editor, the value of [member EditorSettings.run/window_placement/rect_custom_position] is used instead.
		</member>
		<member name="display/window/size/initial_position_type" type="int" setter="" getter="" default="1">
			Main window initial position.
			[code]0[/code] - "Absolute", [member display/window/size/initial_position] is used to set window position.
			[code]1[/code] - "Primary Screen Center".
			[code]2[/code] - "Other Screen Center", [member display/window/size/initial_screen] is used to set the screen.
			[b]Note:[/b] This setting only affects the exported project, or when the project is run from the command line. In the editor, the value of [member EditorSettings.run/window_placement/rect] is used instead.
		</member>
		<member name="display/window/size/initial_screen" type="int" setter="" getter="" default="0">
			Main window initial screen, this setting is used only if [member display/window/size/initial_position_type] is set to "Other Screen Center" ([code]2[/code]).
			[b]Note:[/b] This setting only affects the exported project, or when the project is run from the command line. In the editor, the value of [member EditorSettings.run/window_placement/screen] is used instead.
		</member>
		<member name="display/window/size/maximize_disabled" type="bool" setter="" getter="" default="false">
			If [code]true[/code], the main window's maximize button is disabled.
		</member>
		<member name="display/window/size/minimize_disabled" type="bool" setter="" getter="" default="false">
			If [code]true[/code], the main window's minimize button is disabled.
		</member>
		<member name="display/window/size/mode" type="int" setter="" getter="" default="0">
			Main window mode. See [enum DisplayServer.WindowMode] for possible values and how each mode behaves.
			[b]Note:[/b] Game embedding is available only in the "Windowed" mode.
		</member>
		<member name="display/window/size/no_focus" type="bool" setter="" getter="" default="false">
			Main window can't be focused. No-focus window will ignore all input, except mouse clicks.
		</member>
		<member name="display/window/size/resizable" type="bool" setter="" getter="" default="true">
			If [code]true[/code], allows the window to be resizable by default.
			[b]Note:[/b] This property is only read when the project starts. To change whether the window is resizable at runtime, set [member Window.unresizable] instead on the root Window, which can be retrieved using [code]get_viewport().get_window()[/code]. [member Window.unresizable] takes the opposite value of this setting.
			[b]Note:[/b] Certain window managers can be configured to ignore the non-resizable status of a window. Do not rely on this setting as a guarantee that the window will [i]never[/i] be resizable.
			[b]Note:[/b] This setting is ignored on iOS.
		</member>
		<member name="display/window/size/sharp_corners" type="bool" setter="" getter="" default="false">
			If [code]true[/code], the main window uses sharp corners by default.
			[b]Note:[/b] This property is implemented only on Windows (11).
		</member>
		<member name="display/window/size/transparent" type="bool" setter="" getter="" default="false">
			If [code]true[/code], enables a window manager hint that the main window background [i]can[/i] be transparent. This does not make the background actually transparent. For the background to be transparent, the root viewport must also be made transparent by enabling [member rendering/viewport/transparent_background].
			[b]Note:[/b] To use a transparent splash screen, set [member application/boot_splash/bg_color] to [code]Color(0, 0, 0, 0)[/code].
			[b]Note:[/b] This setting has no effect if [member display/window/per_pixel_transparency/allowed] is set to [code]false[/code].
		</member>
		<member name="display/window/size/viewport_height" type="int" setter="" getter="" default="648">
			Sets the game's main viewport height. On desktop platforms, this is also the initial window height, represented by an indigo-colored rectangle in the 2D editor. Stretch mode settings also use this as a reference when using the [code]canvas_items[/code] or [code]viewport[/code] stretch modes. See also [member display/window/size/viewport_width], [member display/window/size/window_width_override] and [member display/window/size/window_height_override].
		</member>
		<member name="display/window/size/viewport_width" type="int" setter="" getter="" default="1152">
			Sets the game's main viewport width. On desktop platforms, this is also the initial window width, represented by an indigo-colored rectangle in the 2D editor. Stretch mode settings also use this as a reference when using the [code]canvas_items[/code] or [code]viewport[/code] stretch modes. See also [member display/window/size/viewport_height], [member display/window/size/window_width_override] and [member display/window/size/window_height_override].
		</member>
		<member name="display/window/size/window_height_override" type="int" setter="" getter="" default="0">
			On desktop platforms, overrides the game's initial window height. See also [member display/window/size/window_width_override], [member display/window/size/viewport_width] and [member display/window/size/viewport_height].
			[b]Note:[/b] By default, or when set to [code]0[/code], the initial window height is the [member display/window/size/viewport_height]. This setting is ignored on iOS, Android, and Web.
		</member>
		<member name="display/window/size/window_width_override" type="int" setter="" getter="" default="0">
			On desktop platforms, overrides the game's initial window width. See also [member display/window/size/window_height_override], [member display/window/size/viewport_width] and [member display/window/size/viewport_height].
			[b]Note:[/b] By default, or when set to [code]0[/code], the initial window width is the [member display/window/size/viewport_width]. This setting is ignored on iOS, Android, and Web.
		</member>
		<member name="display/window/stretch/aspect" type="String" setter="" getter="" default="&quot;keep&quot;">
		</member>
		<member name="display/window/stretch/mode" type="String" setter="" getter="" default="&quot;disabled&quot;">
			Defines how the base size is stretched to fit the resolution of the window or screen.
			[b]"disabled"[/b]: No stretching happens. One unit in the scene corresponds to one pixel on the screen. In this mode, [member display/window/stretch/aspect] has no effect. Recommended for non-game applications.
			[b]"canvas_items"[/b]: The base size specified in width and height in the project settings is stretched to cover the whole screen (taking [member display/window/stretch/aspect] into account). This means that everything is rendered directly at the target resolution. 3D is unaffected, while in 2D, there is no longer a 1:1 correspondence between sprite pixels and screen pixels, which may result in scaling artifacts. Recommended for most games that don't use a pixel art aesthetic, although it is possible to use this stretch mode for pixel art games too (especially in 3D).
			[b]"viewport"[/b]: The size of the root [Viewport] is set precisely to the base size specified in the Project Settings' Display section. The scene is rendered to this viewport first. Finally, this viewport is scaled to fit the screen (taking [member display/window/stretch/aspect] into account). Recommended for games that use a pixel art aesthetic.
		</member>
		<member name="display/window/stretch/scale" type="float" setter="" getter="" default="1.0">
			The scale factor multiplier to use for 2D elements. This multiplies the final scale factor determined by [member display/window/stretch/mode]. If using the [b]Disabled[/b] stretch mode, this scale factor is applied as-is. This can be adjusted to make the UI easier to read on certain displays.
		</member>
		<member name="display/window/stretch/scale_mode" type="String" setter="" getter="" default="&quot;fractional&quot;">
			The policy to use to determine the final scale factor for 2D elements. This affects how [member display/window/stretch/scale] is applied, in addition to the automatic scale factor determined by [member display/window/stretch/mode].
			[b]"fractional"[/b]: The scale factor will not be modified.
			[b]"integer"[/b]: The scale factor will be floored to an integer value, which means that the screen size will always be an integer multiple of the base viewport size. This provides a crisp pixel art appearance.
			[b]Note:[/b] When using integer scaling with a stretch mode, resizing the window to be smaller than the base viewport size will clip the contents. Consider preventing that by setting [member Window.min_size] to the same value as the base viewport size defined in [member display/window/size/viewport_width] and [member display/window/size/viewport_height].
		</member>
		<member name="display/window/subwindows/embed_subwindows" type="bool" setter="" getter="" default="true">
			If [code]true[/code], subwindows are embedded in the main window (this is also called single-window mode). Single-window mode can be faster as it does not need to create a separate window for every popup and tooltip, which can be a slow operation depending on the operating system and rendering method in use.
			If [code]false[/code], subwindows are created as separate windows (this is also called multi-window mode). This allows them to be moved outside the main window and use native operating system window decorations.
			This is equivalent to [member EditorSettings.interface/editor/single_window_mode] in the editor, except the setting's value is inverted.
		</member>
		<member name="display/window/vsync/vsync_mode" type="int" setter="" getter="" default="1">
			Sets the V-Sync mode for the main game window. The editor's own V-Sync mode can be set using [member EditorSettings.interface/editor/vsync_mode].
			See [enum DisplayServer.VSyncMode] for possible values and how they affect the behavior of your application.
			Depending on the platform and rendering method, the engine will fall back to [b]Enabled[/b] if the desired mode is not supported.
			V-Sync can be disabled on the command line using the [code]--disable-vsync[/code] [url=$DOCS_URL/tutorials/editor/command_line_tutorial.html]command line argument[/url].
			[b]Note:[/b] The [b]Adaptive[/b] and [b]Mailbox[/b] V-Sync modes are only supported in the Forward+ and Mobile rendering methods, not Compatibility.
			[b]Note:[/b] This property is only read when the project starts. To change the V-Sync mode at runtime, call [method DisplayServer.window_set_vsync_mode] instead.
		</member>
		<member name="dotnet/project/assembly_name" type="String" setter="" getter="" default="&quot;&quot;">
			Name of the .NET assembly. This name is used as the name of the [code].csproj[/code] and [code].sln[/code] files. By default, it's set to the name of the project ([member application/config/name]) allowing to change it in the future without affecting the .NET assembly.
		</member>
		<member name="dotnet/project/assembly_reload_attempts" type="int" setter="" getter="" default="3">
			Number of times to attempt assembly reloading after rebuilding .NET assemblies. Effectively also the timeout in seconds to wait for unloading of script assemblies to finish.
		</member>
		<member name="dotnet/project/solution_directory" type="String" setter="" getter="" default="&quot;&quot;">
			Directory that contains the [code].sln[/code] file. By default, the [code].sln[/code] files is in the root of the project directory, next to the [code]project.godot[/code] and [code].csproj[/code] files.
			Changing this value allows setting up a multi-project scenario where there are multiple [code].csproj[/code]. Keep in mind that the Redot project is considered one of the C# projects in the workspace and it's root directory should contain the [code]project.godot[/code] and [code].csproj[/code] next to each other.
		</member>
		<member name="editor/export/convert_text_resources_to_binary" type="bool" setter="" getter="" default="true">
			If [code]true[/code], text resource ([code]tres[/code]) and text scene ([code]tscn[/code]) files are converted to their corresponding binary format on export. This decreases file sizes and speeds up loading slightly.
			[b]Note:[/b] Because a resource's file extension may change in an exported project, it is heavily recommended to use [method @GDScript.load] or [ResourceLoader] instead of [FileAccess] to load resources dynamically.
			[b]Note:[/b] The project settings file ([code]project.godot[/code]) will always be converted to binary on export, regardless of this setting.
		</member>
		<member name="editor/import/atlas_max_width" type="int" setter="" getter="" default="2048">
			The maximum width to use when importing textures as an atlas. The value will be rounded to the nearest power of two when used. Use this to prevent imported textures from growing too large in the other direction.
		</member>
		<member name="editor/import/reimport_missing_imported_files" type="bool" setter="" getter="" default="true">
		</member>
		<member name="editor/import/use_multiple_threads" type="bool" setter="" getter="" default="true">
			If [code]true[/code] importing of resources is run on multiple threads.
		</member>
		<member name="editor/movie_writer/disable_vsync" type="bool" setter="" getter="" default="false">
			If [code]true[/code], requests V-Sync to be disabled when writing a movie (similar to setting [member display/window/vsync/vsync_mode] to [b]Disabled[/b]). This can speed up video writing if the hardware is fast enough to render, encode and save the video at a framerate higher than the monitor's refresh rate.
			[b]Note:[/b] [member editor/movie_writer/disable_vsync] has no effect if the operating system or graphics driver forces V-Sync with no way for applications to disable it.
		</member>
		<member name="editor/movie_writer/fps" type="int" setter="" getter="" default="60">
			The number of frames per second to record in the video when writing a movie. Simulation speed will adjust to always match the specified framerate, which means the engine will appear to run slower at higher [member editor/movie_writer/fps] values. Certain FPS values will require you to adjust [member editor/movie_writer/mix_rate] to prevent audio from desynchronizing over time.
			This can be specified manually on the command line using the [code]--fixed-fps &lt;fps&gt;[/code] [url=$DOCS_URL/tutorials/editor/command_line_tutorial.html]command line argument[/url].
		</member>
		<member name="editor/movie_writer/mix_rate" type="int" setter="" getter="" default="48000">
			The audio mix rate to use in the recorded audio when writing a movie (in Hz). This can be different from [member audio/driver/mix_rate], but this value must be divisible by [member editor/movie_writer/fps] to prevent audio from desynchronizing over time.
		</member>
		<member name="editor/movie_writer/mjpeg_quality" type="float" setter="" getter="" default="0.75">
			The JPEG quality to use when writing a video to an AVI file, between [code]0.01[/code] and [code]1.0[/code] (inclusive). Higher [code]quality[/code] values result in better-looking output at the cost of larger file sizes. Recommended [code]quality[/code] values are between [code]0.75[/code] and [code]0.9[/code]. Even at quality [code]1.0[/code], JPEG compression remains lossy.
			[b]Note:[/b] This does not affect the audio quality or writing PNG image sequences.
		</member>
		<member name="editor/movie_writer/movie_file" type="String" setter="" getter="" default="&quot;&quot;">
			The output path for the movie. The file extension determines the [MovieWriter] that will be used.
			Redot has 2 built-in [MovieWriter]s:
			- AVI container with MJPEG for video and uncompressed audio ([code].avi[/code] file extension). Lossy compression, medium file sizes, fast encoding. The lossy compression quality can be adjusted by changing [member ProjectSettings.editor/movie_writer/mjpeg_quality]. The resulting file can be viewed in most video players, but it must be converted to another format for viewing on the web or by Redot with [VideoStreamPlayer]. MJPEG does not support transparency. AVI output is currently limited to a file of 4 GB in size at most.
			- PNG image sequence for video and WAV for audio ([code].png[/code] file extension). Lossless compression, large file sizes, slow encoding. Designed to be encoded to a video file with another tool such as [url=https://ffmpeg.org/]FFmpeg[/url] after recording. Transparency is currently not supported, even if the root viewport is set to be transparent.
			If you need to encode to a different format or pipe a stream through third-party software, you can extend this [MovieWriter] class to create your own movie writers.
			When using PNG output, the frame number will be appended at the end of the file name. It starts from 0 and is padded with 8 digits to ensure correct sorting and easier processing. For example, if the output path is [code]/tmp/hello.png[/code], the first two frames will be [code]/tmp/hello00000000.png[/code] and [code]/tmp/hello00000001.png[/code]. The audio will be saved at [code]/tmp/hello.wav[/code].
		</member>
		<member name="editor/movie_writer/speaker_mode" type="int" setter="" getter="" default="0">
			The speaker mode to use in the recorded audio when writing a movie. See [enum AudioServer.SpeakerMode] for possible values.
		</member>
		<member name="editor/naming/default_signal_callback_name" type="String" setter="" getter="" default="&quot;_on_{node_name}_{signal_name}&quot;">
			The format of the default signal callback name (in the Signal Connection Dialog). The following substitutions are available: [code]{NodeName}[/code], [code]{nodeName}[/code], [code]{node_name}[/code], [code]{SignalName}[/code], [code]{signalName}[/code], and [code]{signal_name}[/code].
		</member>
		<member name="editor/naming/default_signal_callback_to_self_name" type="String" setter="" getter="" default="&quot;_on_{signal_name}&quot;">
			The format of the default signal callback name when a signal connects to the same node that emits it (in the Signal Connection Dialog). The following substitutions are available: [code]{NodeName}[/code], [code]{nodeName}[/code], [code]{node_name}[/code], [code]{SignalName}[/code], [code]{signalName}[/code], and [code]{signal_name}[/code].
		</member>
		<member name="editor/naming/node_name_casing" type="int" setter="" getter="" default="0">
			When creating node names automatically, set the type of casing to use in this project. This is mostly an editor setting.
		</member>
		<member name="editor/naming/node_name_num_separator" type="int" setter="" getter="" default="0">
			What to use to separate node name from number. This is mostly an editor setting.
		</member>
		<member name="editor/naming/scene_name_casing" type="int" setter="" getter="" default="2">
			When generating scene file names from scene root node, set the type of casing to use in this project. This is mostly an editor setting.
		</member>
		<member name="editor/naming/script_name_casing" type="int" setter="" getter="" default="0">
			When generating script file names from the selected node, set the type of casing to use in this project. This is mostly an editor setting.
		</member>
		<member name="editor/run/main_run_args" type="String" setter="" getter="" default="&quot;&quot;">
			The command-line arguments to append to Redot's own command line when running the project. This doesn't affect the editor itself.
			It is possible to make another executable run Redot by using the [code]%command%[/code] placeholder. The placeholder will be replaced with Redot's own command line. Program-specific arguments should be placed [i]before[/i] the placeholder, whereas Redot-specific arguments should be placed [i]after[/i] the placeholder.
			For example, this can be used to force the project to run on the dedicated GPU in an NVIDIA Optimus system on Linux:
			[codeblock lang=text]
			prime-run %command%
			[/codeblock]
		</member>
		<member name="editor/script/search_in_file_extensions" type="PackedStringArray" setter="" getter="">
			Text-based file extensions to include in the script editor's "Find in Files" feature. You can add e.g. [code]tscn[/code] if you wish to also parse your scene files, especially if you use built-in scripts which are serialized in the scene files.
		</member>
		<member name="editor/script/templates_search_path" type="String" setter="" getter="" default="&quot;res://script_templates&quot;">
			Search path for project-specific script templates. Redot will search for script templates both in the editor-specific path and in this project-specific path.
		</member>
		<member name="editor/version_control/autoload_on_startup" type="bool" setter="" getter="" default="false">
		</member>
		<member name="editor/version_control/plugin_name" type="String" setter="" getter="" default="&quot;&quot;">
		</member>
		<member name="filesystem/import/blender/enabled" type="bool" setter="" getter="" default="true">
			If [code]true[/code], Blender 3D scene files with the [code].blend[/code] extension will be imported by converting them to glTF 2.0.
			This requires configuring a path to a Blender executable in the [member EditorSettings.filesystem/import/blender/blender_path] setting. Blender 3.0 or later is required.
		</member>
		<member name="filesystem/import/blender/enabled.android" type="bool" setter="" getter="" default="false">
			Override for [member filesystem/import/blender/enabled] on Android where Blender can't easily be accessed from Redot.
		</member>
		<member name="filesystem/import/blender/enabled.web" type="bool" setter="" getter="" default="false">
			Override for [member filesystem/import/blender/enabled] on the Web where Blender can't easily be accessed from Redot.
		</member>
		<member name="filesystem/import/fbx2gltf/enabled" type="bool" setter="" getter="" default="true">
			If [code]true[/code], Autodesk FBX 3D scene files with the [code].fbx[/code] extension will be imported by converting them to glTF 2.0.
			This requires configuring a path to an FBX2glTF executable in the editor settings at [member EditorSettings.filesystem/import/fbx/fbx2gltf_path].
		</member>
		<member name="filesystem/import/fbx2gltf/enabled.android" type="bool" setter="" getter="" default="false">
			Override for [member filesystem/import/fbx2gltf/enabled] on Android where FBX2glTF can't easily be accessed from Redot.
		</member>
		<member name="filesystem/import/fbx2gltf/enabled.web" type="bool" setter="" getter="" default="false">
			Override for [member filesystem/import/fbx2gltf/enabled] on the Web where FBX2glTF can't easily be accessed from Redot.
		</member>
		<member name="gui/common/default_scroll_deadzone" type="int" setter="" getter="" default="0">
			Default value for [member ScrollContainer.scroll_deadzone], which will be used for all [ScrollContainer]s unless overridden.
		</member>
		<member name="gui/common/snap_controls_to_pixels" type="bool" setter="" getter="" default="true">
			If [code]true[/code], snaps [Control] node vertices to the nearest pixel to ensure they remain crisp even when the camera moves or zooms.
		</member>
		<member name="gui/common/swap_cancel_ok" type="int" setter="" getter="" default="0">
			How to position the Cancel and OK buttons in the project's [AcceptDialog]s. Different platforms have different standard behaviors for this, which can be overridden using this setting.
			- [b]Auto[/b] ([code]0[/code]) follows the platform convention: OK first on Windows, KDE, and LXQt, Cancel first on macOS and other Linux desktop environments. [method DisplayServer.get_swap_cancel_ok] can be used to query whether buttons are swapped at run-time.
			- [b]Cancel First[/b] ([code]1[/code]) forces the ordering Cancel/OK.
			- [b]OK First[/b] ([code]2[/code]) forces the ordering OK/Cancel.
			[b]Note:[/b] This doesn't affect native dialogs such as the ones spawned by [method DisplayServer.dialog_show].
		</member>
		<member name="gui/common/text_edit_undo_stack_max_size" type="int" setter="" getter="" default="1024">
			Maximum undo/redo history size for [TextEdit] fields.
		</member>
		<member name="gui/fonts/dynamic_fonts/use_oversampling" type="bool" setter="" getter="" default="true">
			If set to [code]true[/code] and [member display/window/stretch/mode] is set to [b]"canvas_items"[/b], font and [SVGTexture] oversampling is enabled in the main window. Use [member Viewport.oversampling] to control oversampling in other viewports and windows.
		</member>
		<member name="gui/theme/custom" type="String" setter="" getter="" default="&quot;&quot;">
			Path to a custom [Theme] resource file to use for the project ([code].theme[/code] or generic [code].tres[/code]/[code].res[/code] extension).
		</member>
		<member name="gui/theme/custom_font" type="String" setter="" getter="" default="&quot;&quot;">
			Path to a custom [Font] resource to use as default for all GUI elements of the project.
		</member>
		<member name="gui/theme/default_font_antialiasing" type="int" setter="" getter="" default="1">
			Font anti-aliasing mode for the default project font. See [member FontFile.antialiasing].
			[b]Note:[/b] This setting does not affect custom [Font]s used within the project. Use the [b]Import[/b] dock for that instead (see [member ResourceImporterDynamicFont.antialiasing]).
		</member>
		<member name="gui/theme/default_font_generate_mipmaps" type="bool" setter="" getter="" default="false">
			If set to [code]true[/code], the default font will have mipmaps generated. This prevents text from looking grainy when a [Control] is scaled down, or when a [Label3D] is viewed from a long distance (if [member Label3D.texture_filter] is set to a mode that displays mipmaps).
			Enabling [member gui/theme/default_font_generate_mipmaps] increases font generation time and memory usage. Only enable this setting if you actually need it.
			[b]Note:[/b] This setting does not affect custom [Font]s used within the project. Use the [b]Import[/b] dock for that instead (see [member ResourceImporterDynamicFont.generate_mipmaps]).
		</member>
		<member name="gui/theme/default_font_hinting" type="int" setter="" getter="" default="1">
			Font hinting mode for the default project font. See [member FontFile.hinting].
			[b]Note:[/b] This setting does not affect custom [Font]s used within the project. Use the [b]Import[/b] dock for that instead (see [member ResourceImporterDynamicFont.hinting]).
		</member>
		<member name="gui/theme/default_font_multichannel_signed_distance_field" type="bool" setter="" getter="" default="false">
			If set to [code]true[/code], the default font will use multichannel signed distance field (MSDF) for crisp rendering at any size. Since this approach does not rely on rasterizing the font every time its size changes, this allows for resizing the font in real-time without any performance penalty. Text will also not look grainy for [Control]s that are scaled down (or for [Label3D]s viewed from a long distance).
			MSDF font rendering can be combined with [member gui/theme/default_font_generate_mipmaps] to further improve font rendering quality when scaled down.
			[b]Note:[/b] This setting does not affect custom [Font]s used within the project. Use the [b]Import[/b] dock for that instead (see [member ResourceImporterDynamicFont.multichannel_signed_distance_field]).
		</member>
		<member name="gui/theme/default_font_subpixel_positioning" type="int" setter="" getter="" default="1">
			Font glyph subpixel positioning mode for the default project font. See [member FontFile.subpixel_positioning].
			[b]Note:[/b] This setting does not affect custom [Font]s used within the project. Use the [b]Import[/b] dock for that instead (see [member ResourceImporterDynamicFont.subpixel_positioning]).
		</member>
		<member name="gui/theme/default_theme_scale" type="float" setter="" getter="" default="1.0">
			The default scale factor for [Control]s, when not overridden by a [Theme].
			[b]Note:[/b] This property is only read when the project starts. To change the default scale at runtime, set [member ThemeDB.fallback_base_scale] instead.
		</member>
		<member name="gui/theme/lcd_subpixel_layout" type="int" setter="" getter="" default="1">
			LCD subpixel layout used for font anti-aliasing. See [enum TextServer.FontLCDSubpixelLayout].
		</member>
		<member name="gui/timers/button_shortcut_feedback_highlight_time" type="float" setter="" getter="" default="0.2">
			When [member BaseButton.shortcut_feedback] is enabled, this is the time the [BaseButton] will remain highlighted after a shortcut.
		</member>
		<member name="gui/timers/incremental_search_max_interval_msec" type="int" setter="" getter="" default="2000">
			Timer setting for incremental search in [Tree], [ItemList], etc. controls (in milliseconds).
		</member>
		<member name="gui/timers/text_edit_idle_detect_sec" type="float" setter="" getter="" default="3">
			Timer for detecting idle in [TextEdit] (in seconds).
		</member>
		<member name="gui/timers/tooltip_delay_sec" type="float" setter="" getter="" default="0.5">
			Default delay for tooltips (in seconds).
		</member>
		<member name="gui/timers/tooltip_delay_sec.editor_hint" type="float" setter="" getter="" default="0.5">
			Delay for tooltips in the editor.
		</member>
		<member name="input/ui_accept" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to confirm a focused button, menu or list item, or validate input.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_accessibility_drag_and_drop" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to start or end a drag-and-drop operation without using mouse.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_cancel" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to discard a modal or pending input.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_colorpicker_delete_preset" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to delete a color preset in a [ColorPicker].
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_copy" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to copy a selection to the clipboard.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_cut" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to cut a selection to the clipboard.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_down" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to move down in the UI.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_end" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to go to the end position of a [Control] (e.g. last item in an [ItemList] or a [Tree]), matching the behavior of [constant KEY_END] on typical desktop UI systems.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_filedialog_refresh" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to refresh the contents of the current directory of a [FileDialog].
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_filedialog_show_hidden" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to toggle showing hidden files and directories in a [FileDialog].
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_filedialog_up_one_level" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to go up one directory in a [FileDialog].
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_focus_mode" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to switch [TextEdit] [member input/ui_text_indent] between moving keyboard focus to the next [Control] in the scene and inputting a [code]Tab[/code] character.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_focus_next" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to focus the next [Control] in the scene. The focus behavior can be configured via [member Control.focus_next].
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_focus_prev" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to focus the previous [Control] in the scene. The focus behavior can be configured via [member Control.focus_previous].
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_graph_delete" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to delete a [GraphNode] in a [GraphEdit].
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_graph_duplicate" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to duplicate a [GraphNode] in a [GraphEdit].
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_graph_follow_left" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to follow a [GraphNode] input port connection.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_graph_follow_left.macos" type="Dictionary" setter="" getter="">
			macOS specific override for the shortcut to follow a [GraphNode] input port connection.
		</member>
		<member name="input/ui_graph_follow_right" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to follow a [GraphNode] output port connection.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_graph_follow_right.macos" type="Dictionary" setter="" getter="">
			macOS specific override for the shortcut to follow a [GraphNode] output port connection.
		</member>
		<member name="input/ui_home" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to go to the start position of a [Control] (e.g. first item in an [ItemList] or a [Tree]), matching the behavior of [constant KEY_HOME] on typical desktop UI systems.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_left" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to move left in the UI.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_menu" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to open a context menu in a text field.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_page_down" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to go down a page in a [Control] (e.g. in an [ItemList] or a [Tree]), matching the behavior of [constant KEY_PAGEDOWN] on typical desktop UI systems.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_page_up" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to go up a page in a [Control] (e.g. in an [ItemList] or a [Tree]), matching the behavior of [constant KEY_PAGEUP] on typical desktop UI systems.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_paste" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to paste from the clipboard.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_redo" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to redo an undone action.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_right" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to move right in the UI.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_select" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to select an item in a [Control] (e.g. in an [ItemList] or a [Tree]).
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_swap_input_direction" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to swap input direction, i.e. change between left-to-right to right-to-left modes. Affects text-editing controls ([LineEdit], [TextEdit]).
		</member>
		<member name="input/ui_text_add_selection_for_next_occurrence" type="Dictionary" setter="" getter="">
			If a selection is currently active with the last caret in text fields, searches for the next occurrence of the selection, adds a caret and selects the next occurrence.
			If no selection is currently active with the last caret in text fields, selects the word currently under the caret.
			The action can be performed sequentially for all occurrences of the selection of the last caret and for all existing carets.
			The viewport is adjusted to the latest newly added caret.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_backspace" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to delete the character before the text cursor.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_backspace_all_to_left" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to delete [b]all[/b] text before the text cursor.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_backspace_all_to_left.macos" type="Dictionary" setter="" getter="">
			macOS specific override for the shortcut to delete all text before the text cursor.
		</member>
		<member name="input/ui_text_backspace_word" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to delete all characters before the cursor up until a whitespace or punctuation character.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_backspace_word.macos" type="Dictionary" setter="" getter="">
			macOS specific override for the shortcut to delete a word.
		</member>
		<member name="input/ui_text_caret_add_above" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to add an additional caret above every caret of a text.
		</member>
		<member name="input/ui_text_caret_add_above.macos" type="Dictionary" setter="" getter="">
			macOS specific override for the shortcut to add a caret above every caret.
		</member>
		<member name="input/ui_text_caret_add_below" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to add an additional caret below every caret of a text.
		</member>
		<member name="input/ui_text_caret_add_below.macos" type="Dictionary" setter="" getter="">
			macOS specific override for the shortcut to add a caret below every caret.
		</member>
		<member name="input/ui_text_caret_document_end" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to move the text cursor to the end of the text.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_caret_document_end.macos" type="Dictionary" setter="" getter="">
			macOS specific override for the shortcut to move the text cursor to the end of the text.
		</member>
		<member name="input/ui_text_caret_document_start" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to move the text cursor to the start of the text.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_caret_document_start.macos" type="Dictionary" setter="" getter="">
			macOS specific override for the shortcut to move the text cursor to the start of the text.
		</member>
		<member name="input/ui_text_caret_down" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to move the text cursor down.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_caret_left" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to move the text cursor left.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_caret_line_end" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to move the text cursor to the end of the line.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_caret_line_end.macos" type="Dictionary" setter="" getter="">
			macOS specific override for the shortcut to move the text cursor to the end of the line.
		</member>
		<member name="input/ui_text_caret_line_start" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to move the text cursor to the start of the line.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_caret_line_start.macos" type="Dictionary" setter="" getter="">
			macOS specific override for the shortcut to move the text cursor to the start of the line.
		</member>
		<member name="input/ui_text_caret_page_down" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to move the text cursor down one page.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_caret_page_up" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to move the text cursor up one page.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_caret_right" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to move the text cursor right.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_caret_up" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to move the text cursor up.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_caret_word_left" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to move the text cursor left to the next whitespace or punctuation.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_caret_word_left.macos" type="Dictionary" setter="" getter="">
			macOS specific override for the shortcut to move the text cursor back one word.
		</member>
		<member name="input/ui_text_caret_word_right" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to move the text cursor right to the next whitespace or punctuation.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_caret_word_right.macos" type="Dictionary" setter="" getter="">
			macOS specific override for the shortcut to move the text cursor forward one word.
		</member>
		<member name="input/ui_text_clear_carets_and_selection" type="Dictionary" setter="" getter="">
			If there's only one caret active and with a selection, clears the selection.
			In case there's more than one caret active, removes the secondary carets and clears their selections.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_completion_accept" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to accept an autocompletion hint.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_completion_query" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to request autocompletion.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_completion_replace" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to accept an autocompletion hint, replacing existing text.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_dedent" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to unindent text.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_delete" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to delete the character after the text cursor.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_delete_all_to_right" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to delete [b]all[/b] text after the text cursor.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_delete_all_to_right.macos" type="Dictionary" setter="" getter="">
			macOS specific override for the shortcut to delete all text after the text cursor.
		</member>
		<member name="input/ui_text_delete_word" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to delete all characters after the cursor up until a whitespace or punctuation character.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_delete_word.macos" type="Dictionary" setter="" getter="">
			macOS specific override for the shortcut to delete a word after the text cursor.
		</member>
		<member name="input/ui_text_indent" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to indent the current line.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_newline" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to insert a new line at the position of the text cursor.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_newline_above" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to insert a new line before the current one.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_newline_blank" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to insert a new line after the current one.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_scroll_down" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to scroll down one line of text.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_scroll_down.macos" type="Dictionary" setter="" getter="">
			macOS specific override for the shortcut to scroll down one line.
		</member>
		<member name="input/ui_text_scroll_up" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to scroll up one line of text.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_scroll_up.macos" type="Dictionary" setter="" getter="">
			macOS specific override for the shortcut to scroll up one line.
		</member>
		<member name="input/ui_text_select_all" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to select all text.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_select_word_under_caret" type="Dictionary" setter="" getter="">
			If no selection is currently active, selects the word currently under the caret in text fields. If a selection is currently active, deselects the current selection.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_select_word_under_caret.macos" type="Dictionary" setter="" getter="">
			macOS specific override for the shortcut to select the word currently under the caret.
		</member>
		<member name="input/ui_text_skip_selection_for_next_occurrence" type="Dictionary" setter="" getter="">
			If no selection is currently active with the last caret in text fields, searches for the next occurrence of the word currently under the caret and moves the caret to the next occurrence. The action can be performed sequentially for other occurrences of the word under the last caret.
			If a selection is currently active with the last caret in text fields, searches for the next occurrence of the selection, adds a caret, selects the next occurrence then deselects the previous selection and its associated caret. The action can be performed sequentially for other occurrences of the selection of the last caret.
			The viewport is adjusted to the latest newly added caret.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_submit" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to submit a text field.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_text_toggle_insert_mode" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to toggle [i]insert mode[/i] in a text field. While in insert mode, inserting new text overrides the character after the cursor, unless the next character is a new line.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_undo" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to undo the most recent action.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_unicode_start" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to start Unicode character hexadecimal code input in a text field.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input/ui_up" type="Dictionary" setter="" getter="">
			Default [InputEventAction] to move up in the UI.
			[b]Note:[/b] Default [code]ui_*[/code] actions cannot be removed as they are necessary for the internal logic of several [Control]s. The events assigned to the action can however be modified.
		</member>
		<member name="input_devices/buffering/agile_event_flushing" type="bool" setter="" getter="" default="false">
			If [code]true[/code], key/touch/joystick events will be flushed just before every idle and physics frame.
			If [code]false[/code], such events will be flushed only once per process frame, between iterations of the engine.
			Enabling this can greatly improve the responsiveness to input, specially in devices that need to run multiple physics frames per visible (process) frame, because they can't run at the target frame rate.
			[b]Note:[/b] Currently implemented only on Android.
		</member>
		<member name="input_devices/compatibility/legacy_just_pressed_behavior" type="bool" setter="" getter="" default="false">
			If [code]true[/code], [method Input.is_action_just_pressed] and [method Input.is_action_just_released] will only return [code]true[/code] if the action is still in the respective state, i.e. an action that is pressed [i]and[/i] released on the same frame will be missed.
			If [code]false[/code], no input will be lost.
			[b]Note:[/b] You should in nearly all cases prefer the [code]false[/code] setting. The legacy behavior is to enable supporting old projects that rely on the old logic, without changes to script.
		</member>
		<member name="input_devices/pen_tablet/driver" type="String" setter="" getter="">
			Specifies the tablet driver to use. If left empty, the default driver will be used.
			[b]Note:[/b] The driver in use can be overridden at runtime via the [code]--tablet-driver[/code] [url=$DOCS_URL/tutorials/editor/command_line_tutorial.html]command line argument[/url].
			[b]Note:[/b] Use [method DisplayServer.tablet_set_current_driver] to switch tablet driver in runtime.
		</member>
		<member name="input_devices/pen_tablet/driver.windows" type="String" setter="" getter="">
			Override for [member input_devices/pen_tablet/driver] on Windows. Supported values are:
			- [code]auto[/code] (default), uses [code]wintab[/code] if Windows Ink is disabled in the Wacom Tablet Properties or system settings, [code]winink[/code] otherwise.
			- [code]winink[/code], uses Windows native "Windows Ink" driver.
			- [code]wintab[/code], uses Wacom "WinTab" driver.
			- [code]dummy[/code], tablet input is disabled.
		</member>
		<member name="input_devices/pointing/android/disable_scroll_deadzone" type="bool" setter="" getter="" default="false">
			If [code]true[/code], disables the scroll deadzone on Android, allowing even very small scroll movements to be registered. This may increase scroll sensitivity but can also lead to unintended scrolling from slight finger movements.
		</member>
		<member name="input_devices/pointing/android/enable_long_press_as_right_click" type="bool" setter="" getter="" default="false">
			If [code]true[/code], long press events on an Android touchscreen are transformed into right click events.
		</member>
		<member name="input_devices/pointing/android/enable_pan_and_scale_gestures" type="bool" setter="" getter="" default="false">
			If [code]true[/code], multi-touch pan and scale gestures are enabled on Android devices.
		</member>
		<member name="input_devices/pointing/android/override_volume_buttons" type="bool" setter="" getter="" default="false">
			If [code]true[/code], system volume changes are disabled when the buttons are used within the app.
		</member>
		<member name="input_devices/pointing/android/rotary_input_scroll_axis" type="int" setter="" getter="" default="1">
			On Wear OS devices, defines which axis of the mouse wheel rotary input is mapped to. This rotary input is usually performed by rotating the physical or virtual (touch-based) bezel on a smartwatch.
		</member>
		<member name="input_devices/pointing/emulate_mouse_from_touch" type="bool" setter="" getter="" default="true">
			If [code]true[/code], sends mouse input events when tapping or swiping on the touchscreen.
		</member>
		<member name="input_devices/pointing/emulate_touch_from_mouse" type="bool" setter="" getter="" default="false">
			If [code]true[/code], sends touch input events when clicking or dragging the mouse.
		</member>
		<member name="input_devices/sensors/enable_accelerometer" type="bool" setter="" getter="" default="false">
			If [code]true[/code], the accelerometer sensor is enabled and [method Input.get_accelerometer] returns valid data.
		</member>
		<member name="input_devices/sensors/enable_gravity" type="bool" setter="" getter="" default="false">
			If [code]true[/code], the gravity sensor is enabled and [method Input.get_gravity] returns valid data.
		</member>
		<member name="input_devices/sensors/enable_gyroscope" type="bool" setter="" getter="" default="false">
			If [code]true[/code], the gyroscope sensor is enabled and [method Input.get_gyroscope] returns valid data.
		</member>
		<member name="input_devices/sensors/enable_magnetometer" type="bool" setter="" getter="" default="false">
			If [code]true[/code], the magnetometer sensor is enabled and [method Input.get_magnetometer] returns valid data.
		</member>
		<member name="internationalization/locale/fallback" type="String" setter="" getter="" default="&quot;en&quot;">
			The locale to fall back to if a translation isn't available in a given language. If left empty, [code]en[/code] (English) will be used.
			[b]Note:[/b] Not to be confused with [TextServerFallback].
		</member>
		<member name="internationalization/locale/include_text_server_data" type="bool" setter="" getter="" default="false">
			If [code]true[/code], text server break iteration rule sets, dictionaries and other optional data are included in the exported project.
			[b]Note:[/b] "ICU / HarfBuzz / Graphite" text server data includes dictionaries for Burmese, Chinese, Japanese, Khmer, Lao and Thai as well as Unicode Standard Annex #29 and Unicode Standard Annex #14 word and line breaking rules. Data is about 4 MB large.
			[b]Note:[/b] [TextServerFallback] does not use additional data.
		</member>
		<member name="internationalization/locale/test" type="String" setter="" getter="" default="&quot;&quot;">
			If non-empty, this locale will be used instead of the automatically detected system locale.
			[b]Note:[/b] This setting also applies to the exported project. To only affect testing within the editor, override this setting with an [code]editor[/code] [url=$DOCS_URL/tutorials/export/feature_tags.html]feature tag[/url] for localization testing purposes.
		</member>
		<member name="internationalization/pseudolocalization/double_vowels" type="bool" setter="" getter="" default="false">
			Double vowels in strings during pseudolocalization to simulate the lengthening of text due to localization.
		</member>
		<member name="internationalization/pseudolocalization/expansion_ratio" type="float" setter="" getter="" default="0.0">
			The expansion ratio to use during pseudolocalization. A value of [code]0.3[/code] is sufficient for most practical purposes, and will increase the length of each string by 30%.
		</member>
		<member name="internationalization/pseudolocalization/fake_bidi" type="bool" setter="" getter="" default="false">
			If [code]true[/code], emulate bidirectional (right-to-left) text when pseudolocalization is enabled. This can be used to spot issues with RTL layout and UI mirroring that will crop up if the project is localized to RTL languages such as Arabic or Hebrew. See also [member internationalization/rendering/force_right_to_left_layout_direction].
		</member>
		<member name="internationalization/pseudolocalization/override" type="bool" setter="" getter="" default="false">
			Replace all characters in the string with [code]*[/code]. Useful for finding non-localizable strings.
		</member>
		<member name="internationalization/pseudolocalization/prefix" type="String" setter="" getter="" default="&quot;[&quot;">
			Prefix that will be prepended to the pseudolocalized string.
		</member>
		<member name="internationalization/pseudolocalization/replace_with_accents" type="bool" setter="" getter="" default="true">
			Replace all characters with their accented variants during pseudolocalization.
		</member>
		<member name="internationalization/pseudolocalization/skip_placeholders" type="bool" setter="" getter="" default="true">
			Skip placeholders for string formatting like [code]%s[/code] or [code]%f[/code] during pseudolocalization. Useful to identify strings which need additional control characters to display correctly.
		</member>
		<member name="internationalization/pseudolocalization/suffix" type="String" setter="" getter="" default="&quot;]&quot;">
			Suffix that will be appended to the pseudolocalized string.
		</member>
		<member name="internationalization/pseudolocalization/use_pseudolocalization" type="bool" setter="" getter="" default="false">
			If [code]true[/code], enables pseudolocalization for the project. This can be used to spot untranslatable strings or layout issues that may occur once the project is localized to languages that have longer strings than the source language.
			[b]Note:[/b] This property is only read when the project starts. To toggle pseudolocalization at run-time, use [member TranslationServer.pseudolocalization_enabled] instead.
		</member>
		<member name="internationalization/rendering/force_right_to_left_layout_direction" type="bool" setter="" getter="" default="false">
			Force layout direction and text writing direction to RTL for all controls, even if the current locale is intended to use a left-to-right layout and text writing direction. This should be enabled for testing purposes only. See also [member internationalization/pseudolocalization/fake_bidi].
		</member>
		<member name="internationalization/rendering/root_node_auto_translate" type="bool" setter="" getter="" default="true">
			If [code]true[/code], root node will use [constant Node.AUTO_TRANSLATE_MODE_ALWAYS], otherwise [constant Node.AUTO_TRANSLATE_MODE_DISABLED] will be used.
			[b]Note:[/b] This property is only read when the project starts. To change the auto translate mode at runtime, set [member Node.auto_translate_mode] of [member SceneTree.root] instead.
		</member>
		<member name="internationalization/rendering/root_node_layout_direction" type="int" setter="" getter="" default="0">
			Root node default layout direction.
		</member>
		<member name="internationalization/rendering/text_driver" type="String" setter="" getter="" default="&quot;&quot;">
			Specifies the [TextServer] to use. If left empty, the default will be used.
			"ICU / HarfBuzz / Graphite" ([TextServerAdvanced]) is the most advanced text driver, supporting right-to-left typesetting and complex scripts (for languages like Arabic, Hebrew, etc.). The "Fallback" text driver ([TextServerFallback]) does not support right-to-left typesetting and complex scripts.
			[b]Note:[/b] The driver in use can be overridden at runtime via the [code]--text-driver[/code] [url=$DOCS_URL/tutorials/editor/command_line_tutorial.html]command line argument[/url].
			[b]Note:[/b] There is an additional [code]Dummy[/code] text driver available, which disables all text rendering and font-related functionality. This driver is not listed in the project settings, but it can be enabled when running the editor or project using the [code]--text-driver Dummy[/code] [url=$DOCS_URL/tutorials/editor/command_line_tutorial.html]command line argument[/url].
		</member>
		<member name="layer_names/2d_navigation/layer_1" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 1. If left empty, the layer will display as "Layer 1".
		</member>
		<member name="layer_names/2d_navigation/layer_2" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 2. If left empty, the layer will display as "Layer 2".
		</member>
		<member name="layer_names/2d_navigation/layer_3" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 3. If left empty, the layer will display as "Layer 3".
		</member>
		<member name="layer_names/2d_navigation/layer_4" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 4. If left empty, the layer will display as "Layer 4".
		</member>
		<member name="layer_names/2d_navigation/layer_5" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 5. If left empty, the layer will display as "Layer 5".
		</member>
		<member name="layer_names/2d_navigation/layer_6" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 6. If left empty, the layer will display as "Layer 6".
		</member>
		<member name="layer_names/2d_navigation/layer_7" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 7. If left empty, the layer will display as "Layer 7".
		</member>
		<member name="layer_names/2d_navigation/layer_8" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 8. If left empty, the layer will display as "Layer 8".
		</member>
		<member name="layer_names/2d_navigation/layer_9" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 9. If left empty, the layer will display as "Layer 9".
		</member>
		<member name="layer_names/2d_navigation/layer_10" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 10. If left empty, the layer will display as "Layer 10".
		</member>
		<member name="layer_names/2d_navigation/layer_11" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 11. If left empty, the layer will display as "Layer 11".
		</member>
		<member name="layer_names/2d_navigation/layer_12" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 12. If left empty, the layer will display as "Layer 12".
		</member>
		<member name="layer_names/2d_navigation/layer_13" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 13. If left empty, the layer will display as "Layer 13".
		</member>
		<member name="layer_names/2d_navigation/layer_14" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 14. If left empty, the layer will display as "Layer 14".
		</member>
		<member name="layer_names/2d_navigation/layer_15" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 15. If left empty, the layer will display as "Layer 15".
		</member>
		<member name="layer_names/2d_navigation/layer_16" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 16. If left empty, the layer will display as "Layer 16".
		</member>
		<member name="layer_names/2d_navigation/layer_17" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 17. If left empty, the layer will display as "Layer 17".
		</member>
		<member name="layer_names/2d_navigation/layer_18" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 18. If left empty, the layer will display as "Layer 18".
		</member>
		<member name="layer_names/2d_navigation/layer_19" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 19. If left empty, the layer will display as "Layer 19".
		</member>
		<member name="layer_names/2d_navigation/layer_20" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 20. If left empty, the layer will display as "Layer 20".
		</member>
		<member name="layer_names/2d_navigation/layer_21" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 21. If left empty, the layer will display as "Layer 21".
		</member>
		<member name="layer_names/2d_navigation/layer_22" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 22. If left empty, the layer will display as "Layer 22".
		</member>
		<member name="layer_names/2d_navigation/layer_23" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 23. If left empty, the layer will display as "Layer 23".
		</member>
		<member name="layer_names/2d_navigation/layer_24" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 24. If left empty, the layer will display as "Layer 24".
		</member>
		<member name="layer_names/2d_navigation/layer_25" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 25. If left empty, the layer will display as "Layer 25".
		</member>
		<member name="layer_names/2d_navigation/layer_26" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 26. If left empty, the layer will display as "Layer 26".
		</member>
		<member name="layer_names/2d_navigation/layer_27" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 27. If left empty, the layer will display as "Layer 27".
		</member>
		<member name="layer_names/2d_navigation/layer_28" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 28. If left empty, the layer will display as "Layer 28".
		</member>
		<member name="layer_names/2d_navigation/layer_29" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 29. If left empty, the layer will display as "Layer 29".
		</member>
		<member name="layer_names/2d_navigation/layer_30" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 30. If left empty, the layer will display as "Layer 30".
		</member>
		<member name="layer_names/2d_navigation/layer_31" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 31. If left empty, the layer will display as "Layer 31".
		</member>
		<member name="layer_names/2d_navigation/layer_32" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D navigation layer 32. If left empty, the layer will display as "Layer 32".
		</member>
		<member name="layer_names/2d_physics/layer_1" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 1. If left empty, the layer will display as "Layer 1".
		</member>
		<member name="layer_names/2d_physics/layer_2" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 2. If left empty, the layer will display as "Layer 2".
		</member>
		<member name="layer_names/2d_physics/layer_3" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 3. If left empty, the layer will display as "Layer 3".
		</member>
		<member name="layer_names/2d_physics/layer_4" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 4. If left empty, the layer will display as "Layer 4".
		</member>
		<member name="layer_names/2d_physics/layer_5" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 5. If left empty, the layer will display as "Layer 5".
		</member>
		<member name="layer_names/2d_physics/layer_6" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 6. If left empty, the layer will display as "Layer 6".
		</member>
		<member name="layer_names/2d_physics/layer_7" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 7. If left empty, the layer will display as "Layer 7".
		</member>
		<member name="layer_names/2d_physics/layer_8" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 8. If left empty, the layer will display as "Layer 8".
		</member>
		<member name="layer_names/2d_physics/layer_9" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 9. If left empty, the layer will display as "Layer 9".
		</member>
		<member name="layer_names/2d_physics/layer_10" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 10. If left empty, the layer will display as "Layer 10".
		</member>
		<member name="layer_names/2d_physics/layer_11" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 11. If left empty, the layer will display as "Layer 11".
		</member>
		<member name="layer_names/2d_physics/layer_12" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 12. If left empty, the layer will display as "Layer 12".
		</member>
		<member name="layer_names/2d_physics/layer_13" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 13. If left empty, the layer will display as "Layer 13".
		</member>
		<member name="layer_names/2d_physics/layer_14" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 14. If left empty, the layer will display as "Layer 14".
		</member>
		<member name="layer_names/2d_physics/layer_15" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 15. If left empty, the layer will display as "Layer 15".
		</member>
		<member name="layer_names/2d_physics/layer_16" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 16. If left empty, the layer will display as "Layer 16".
		</member>
		<member name="layer_names/2d_physics/layer_17" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 17. If left empty, the layer will display as "Layer 17".
		</member>
		<member name="layer_names/2d_physics/layer_18" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 18. If left empty, the layer will display as "Layer 18".
		</member>
		<member name="layer_names/2d_physics/layer_19" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 19. If left empty, the layer will display as "Layer 19".
		</member>
		<member name="layer_names/2d_physics/layer_20" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 20. If left empty, the layer will display as "Layer 20".
		</member>
		<member name="layer_names/2d_physics/layer_21" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 21. If left empty, the layer will display as "Layer 21".
		</member>
		<member name="layer_names/2d_physics/layer_22" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 22. If left empty, the layer will display as "Layer 22".
		</member>
		<member name="layer_names/2d_physics/layer_23" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 23. If left empty, the layer will display as "Layer 23".
		</member>
		<member name="layer_names/2d_physics/layer_24" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 24. If left empty, the layer will display as "Layer 24".
		</member>
		<member name="layer_names/2d_physics/layer_25" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 25. If left empty, the layer will display as "Layer 25".
		</member>
		<member name="layer_names/2d_physics/layer_26" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 26. If left empty, the layer will display as "Layer 26".
		</member>
		<member name="layer_names/2d_physics/layer_27" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 27. If left empty, the layer will display as "Layer 27".
		</member>
		<member name="layer_names/2d_physics/layer_28" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 28. If left empty, the layer will display as "Layer 28".
		</member>
		<member name="layer_names/2d_physics/layer_29" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 29. If left empty, the layer will display as "Layer 29".
		</member>
		<member name="layer_names/2d_physics/layer_30" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 30. If left empty, the layer will display as "Layer 30".
		</member>
		<member name="layer_names/2d_physics/layer_31" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 31. If left empty, the layer will display as "Layer 31".
		</member>
		<member name="layer_names/2d_physics/layer_32" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D physics layer 32. If left empty, the layer will display as "Layer 32".
		</member>
		<member name="layer_names/2d_render/layer_1" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D render layer 1. If left empty, the layer will display as "Layer 1".
		</member>
		<member name="layer_names/2d_render/layer_2" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D render layer 2. If left empty, the layer will display as "Layer 2".
		</member>
		<member name="layer_names/2d_render/layer_3" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D render layer 3. If left empty, the layer will display as "Layer 3".
		</member>
		<member name="layer_names/2d_render/layer_4" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D render layer 4. If left empty, the layer will display as "Layer 4".
		</member>
		<member name="layer_names/2d_render/layer_5" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D render layer 5. If left empty, the layer will display as "Layer 5".
		</member>
		<member name="layer_names/2d_render/layer_6" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D render layer 6. If left empty, the layer will display as "Layer 6".
		</member>
		<member name="layer_names/2d_render/layer_7" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D render layer 7. If left empty, the layer will display as "Layer 7".
		</member>
		<member name="layer_names/2d_render/layer_8" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D render layer 8. If left empty, the layer will display as "Layer 8".
		</member>
		<member name="layer_names/2d_render/layer_9" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D render layer 9. If left empty, the layer will display as "Layer 9".
		</member>
		<member name="layer_names/2d_render/layer_10" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D render layer 10. If left empty, the layer will display as "Layer 10".
		</member>
		<member name="layer_names/2d_render/layer_11" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D render layer 11. If left empty, the layer will display as "Layer 11".
		</member>
		<member name="layer_names/2d_render/layer_12" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D render layer 12. If left empty, the layer will display as "Layer 12".
		</member>
		<member name="layer_names/2d_render/layer_13" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D render layer 13. If left empty, the layer will display as "Layer 13".
		</member>
		<member name="layer_names/2d_render/layer_14" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D render layer 14. If left empty, the layer will display as "Layer 14".
		</member>
		<member name="layer_names/2d_render/layer_15" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D render layer 15. If left empty, the layer will display as "Layer 15".
		</member>
		<member name="layer_names/2d_render/layer_16" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D render layer 16. If left empty, the layer will display as "Layer 16".
		</member>
		<member name="layer_names/2d_render/layer_17" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D render layer 17. If left empty, the layer will display as "Layer 17".
		</member>
		<member name="layer_names/2d_render/layer_18" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D render layer 18. If left empty, the layer will display as "Layer 18".
		</member>
		<member name="layer_names/2d_render/layer_19" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D render layer 19. If left empty, the layer will display as "Layer 19".
		</member>
		<member name="layer_names/2d_render/layer_20" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 2D render layer 20. If left empty, the layer will display as "Layer 20".
		</member>
		<member name="layer_names/3d_navigation/layer_1" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 1. If left empty, the layer will display as "Layer 1".
		</member>
		<member name="layer_names/3d_navigation/layer_2" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 2. If left empty, the layer will display as "Layer 2".
		</member>
		<member name="layer_names/3d_navigation/layer_3" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 3. If left empty, the layer will display as "Layer 3".
		</member>
		<member name="layer_names/3d_navigation/layer_4" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 4. If left empty, the layer will display as "Layer 4".
		</member>
		<member name="layer_names/3d_navigation/layer_5" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 5. If left empty, the layer will display as "Layer 5".
		</member>
		<member name="layer_names/3d_navigation/layer_6" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 6. If left empty, the layer will display as "Layer 6".
		</member>
		<member name="layer_names/3d_navigation/layer_7" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 7. If left empty, the layer will display as "Layer 7".
		</member>
		<member name="layer_names/3d_navigation/layer_8" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 8. If left empty, the layer will display as "Layer 8".
		</member>
		<member name="layer_names/3d_navigation/layer_9" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 9. If left empty, the layer will display as "Layer 9".
		</member>
		<member name="layer_names/3d_navigation/layer_10" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 10. If left empty, the layer will display as "Layer 10".
		</member>
		<member name="layer_names/3d_navigation/layer_11" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 11. If left empty, the layer will display as "Layer 11".
		</member>
		<member name="layer_names/3d_navigation/layer_12" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 12. If left empty, the layer will display as "Layer 12".
		</member>
		<member name="layer_names/3d_navigation/layer_13" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 13. If left empty, the layer will display as "Layer 13".
		</member>
		<member name="layer_names/3d_navigation/layer_14" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 14. If left empty, the layer will display as "Layer 14".
		</member>
		<member name="layer_names/3d_navigation/layer_15" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 15. If left empty, the layer will display as "Layer 15".
		</member>
		<member name="layer_names/3d_navigation/layer_16" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 16. If left empty, the layer will display as "Layer 16".
		</member>
		<member name="layer_names/3d_navigation/layer_17" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 17. If left empty, the layer will display as "Layer 17".
		</member>
		<member name="layer_names/3d_navigation/layer_18" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 18. If left empty, the layer will display as "Layer 18".
		</member>
		<member name="layer_names/3d_navigation/layer_19" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 19. If left empty, the layer will display as "Layer 19".
		</member>
		<member name="layer_names/3d_navigation/layer_20" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 20. If left empty, the layer will display as "Layer 20".
		</member>
		<member name="layer_names/3d_navigation/layer_21" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 21. If left empty, the layer will display as "Layer 21".
		</member>
		<member name="layer_names/3d_navigation/layer_22" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 22. If left empty, the layer will display as "Layer 22".
		</member>
		<member name="layer_names/3d_navigation/layer_23" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 23. If left empty, the layer will display as "Layer 23".
		</member>
		<member name="layer_names/3d_navigation/layer_24" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 24. If left empty, the layer will display as "Layer 24".
		</member>
		<member name="layer_names/3d_navigation/layer_25" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 25. If left empty, the layer will display as "Layer 25".
		</member>
		<member name="layer_names/3d_navigation/layer_26" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 26. If left empty, the layer will display as "Layer 26".
		</member>
		<member name="layer_names/3d_navigation/layer_27" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 27. If left empty, the layer will display as "Layer 27".
		</member>
		<member name="layer_names/3d_navigation/layer_28" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 28. If left empty, the layer will display as "Layer 28".
		</member>
		<member name="layer_names/3d_navigation/layer_29" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 29. If left empty, the layer will display as "Layer 29".
		</member>
		<member name="layer_names/3d_navigation/layer_30" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 30. If left empty, the layer will display as "Layer 30".
		</member>
		<member name="layer_names/3d_navigation/layer_31" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 31. If left empty, the layer will display as "Layer 31".
		</member>
		<member name="layer_names/3d_navigation/layer_32" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D navigation layer 32. If left empty, the layer will display as "Layer 32".
		</member>
		<member name="layer_names/3d_physics/layer_1" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 1. If left empty, the layer will display as "Layer 1".
		</member>
		<member name="layer_names/3d_physics/layer_2" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 2. If left empty, the layer will display as "Layer 2".
		</member>
		<member name="layer_names/3d_physics/layer_3" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 3. If left empty, the layer will display as "Layer 3".
		</member>
		<member name="layer_names/3d_physics/layer_4" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 4. If left empty, the layer will display as "Layer 4".
		</member>
		<member name="layer_names/3d_physics/layer_5" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 5. If left empty, the layer will display as "Layer 5".
		</member>
		<member name="layer_names/3d_physics/layer_6" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 6. If left empty, the layer will display as "Layer 6".
		</member>
		<member name="layer_names/3d_physics/layer_7" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 7. If left empty, the layer will display as "Layer 7".
		</member>
		<member name="layer_names/3d_physics/layer_8" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 8. If left empty, the layer will display as "Layer 8".
		</member>
		<member name="layer_names/3d_physics/layer_9" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 9. If left empty, the layer will display as "Layer 9".
		</member>
		<member name="layer_names/3d_physics/layer_10" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 10. If left empty, the layer will display as "Layer 10".
		</member>
		<member name="layer_names/3d_physics/layer_11" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 11. If left empty, the layer will display as "Layer 11".
		</member>
		<member name="layer_names/3d_physics/layer_12" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 12. If left empty, the layer will display as "Layer 12".
		</member>
		<member name="layer_names/3d_physics/layer_13" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 13. If left empty, the layer will display as "Layer 13".
		</member>
		<member name="layer_names/3d_physics/layer_14" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 14. If left empty, the layer will display as "Layer 14".
		</member>
		<member name="layer_names/3d_physics/layer_15" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 15. If left empty, the layer will display as "Layer 15".
		</member>
		<member name="layer_names/3d_physics/layer_16" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 16. If left empty, the layer will display as "Layer 16".
		</member>
		<member name="layer_names/3d_physics/layer_17" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 17. If left empty, the layer will display as "Layer 17".
		</member>
		<member name="layer_names/3d_physics/layer_18" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 18. If left empty, the layer will display as "Layer 18".
		</member>
		<member name="layer_names/3d_physics/layer_19" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 19. If left empty, the layer will display as "Layer 19".
		</member>
		<member name="layer_names/3d_physics/layer_20" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 20. If left empty, the layer will display as "Layer 20".
		</member>
		<member name="layer_names/3d_physics/layer_21" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 21. If left empty, the layer will display as "Layer 21".
		</member>
		<member name="layer_names/3d_physics/layer_22" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 22. If left empty, the layer will display as "Layer 22".
		</member>
		<member name="layer_names/3d_physics/layer_23" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 23. If left empty, the layer will display as "Layer 23".
		</member>
		<member name="layer_names/3d_physics/layer_24" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 24. If left empty, the layer will display as "Layer 24".
		</member>
		<member name="layer_names/3d_physics/layer_25" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 25. If left empty, the layer will display as "Layer 25".
		</member>
		<member name="layer_names/3d_physics/layer_26" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 26. If left empty, the layer will display as "Layer 26".
		</member>
		<member name="layer_names/3d_physics/layer_27" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 27. If left empty, the layer will display as "Layer 27".
		</member>
		<member name="layer_names/3d_physics/layer_28" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 28. If left empty, the layer will display as "Layer 28".
		</member>
		<member name="layer_names/3d_physics/layer_29" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 29. If left empty, the layer will display as "Layer 29".
		</member>
		<member name="layer_names/3d_physics/layer_30" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 30. If left empty, the layer will display as "Layer 30".
		</member>
		<member name="layer_names/3d_physics/layer_31" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 31. If left empty, the layer will display as "Layer 31".
		</member>
		<member name="layer_names/3d_physics/layer_32" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D physics layer 32. If left empty, the layer will display as "Layer 32".
		</member>
		<member name="layer_names/3d_render/layer_1" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D render layer 1. If left empty, the layer will display as "Layer 1".
		</member>
		<member name="layer_names/3d_render/layer_2" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D render layer 2. If left empty, the layer will display as "Layer 2".
		</member>
		<member name="layer_names/3d_render/layer_3" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D render layer 3. If left empty, the layer will display as "Layer 3".
		</member>
		<member name="layer_names/3d_render/layer_4" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D render layer 4. If left empty, the layer will display as "Layer 4".
		</member>
		<member name="layer_names/3d_render/layer_5" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D render layer 5. If left empty, the layer will display as "Layer 5".
		</member>
		<member name="layer_names/3d_render/layer_6" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D render layer 6. If left empty, the layer will display as "Layer 6".
		</member>
		<member name="layer_names/3d_render/layer_7" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D render layer 7. If left empty, the layer will display as "Layer 7".
		</member>
		<member name="layer_names/3d_render/layer_8" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D render layer 8. If left empty, the layer will display as "Layer 8".
		</member>
		<member name="layer_names/3d_render/layer_9" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D render layer 9. If left empty, the layer will display as "Layer 9".
		</member>
		<member name="layer_names/3d_render/layer_10" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D render layer 10. If left empty, the layer will display as "Layer 10".
		</member>
		<member name="layer_names/3d_render/layer_11" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D render layer 11. If left empty, the layer will display as "Layer 11".
		</member>
		<member name="layer_names/3d_render/layer_12" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D render layer 12. If left empty, the layer will display as "Layer 12".
		</member>
		<member name="layer_names/3d_render/layer_13" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D render layer 13. If left empty, the layer will display as "Layer 13".
		</member>
		<member name="layer_names/3d_render/layer_14" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D render layer 14. If left empty, the layer will display as "Layer 14".
		</member>
		<member name="layer_names/3d_render/layer_15" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D render layer 15. If left empty, the layer will display as "Layer 15".
		</member>
		<member name="layer_names/3d_render/layer_16" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D render layer 16. If left empty, the layer will display as "Layer 16".
		</member>
		<member name="layer_names/3d_render/layer_17" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D render layer 17. If left empty, the layer will display as "Layer 17".
		</member>
		<member name="layer_names/3d_render/layer_18" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D render layer 18. If left empty, the layer will display as "Layer 18".
		</member>
		<member name="layer_names/3d_render/layer_19" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D render layer 19. If left empty, the layer will display as "Layer 19".
		</member>
		<member name="layer_names/3d_render/layer_20" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the 3D render layer 20. If left empty, the layer will display as "Layer 20".
		</member>
		<member name="layer_names/avoidance/layer_1" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 1. If left empty, the layer will display as "Layer 1".
		</member>
		<member name="layer_names/avoidance/layer_2" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 2. If left empty, the layer will display as "Layer 2".
		</member>
		<member name="layer_names/avoidance/layer_3" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 3. If left empty, the layer will display as "Layer 3".
		</member>
		<member name="layer_names/avoidance/layer_4" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 4. If left empty, the layer will display as "Layer 4".
		</member>
		<member name="layer_names/avoidance/layer_5" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 5. If left empty, the layer will display as "Layer 5".
		</member>
		<member name="layer_names/avoidance/layer_6" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 6. If left empty, the layer will display as "Layer 6".
		</member>
		<member name="layer_names/avoidance/layer_7" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 7. If left empty, the layer will display as "Layer 7".
		</member>
		<member name="layer_names/avoidance/layer_8" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 8. If left empty, the layer will display as "Layer 8".
		</member>
		<member name="layer_names/avoidance/layer_9" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 9. If left empty, the layer will display as "Layer 9".
		</member>
		<member name="layer_names/avoidance/layer_10" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 10. If left empty, the layer will display as "Layer 10".
		</member>
		<member name="layer_names/avoidance/layer_11" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 11. If left empty, the layer will display as "Layer 11".
		</member>
		<member name="layer_names/avoidance/layer_12" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 12. If left empty, the layer will display as "Layer 12".
		</member>
		<member name="layer_names/avoidance/layer_13" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 13. If left empty, the layer will display as "Layer 13".
		</member>
		<member name="layer_names/avoidance/layer_14" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 14. If left empty, the layer will display as "Layer 14".
		</member>
		<member name="layer_names/avoidance/layer_15" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 15. If left empty, the layer will display as "Layer 15".
		</member>
		<member name="layer_names/avoidance/layer_16" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 16. If left empty, the layer will display as "Layer 16".
		</member>
		<member name="layer_names/avoidance/layer_17" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 17. If left empty, the layer will display as "Layer 17".
		</member>
		<member name="layer_names/avoidance/layer_18" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 18. If left empty, the layer will display as "Layer 18".
		</member>
		<member name="layer_names/avoidance/layer_19" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 19. If left empty, the layer will display as "Layer 19".
		</member>
		<member name="layer_names/avoidance/layer_20" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 20. If left empty, the layer will display as "Layer 20".
		</member>
		<member name="layer_names/avoidance/layer_21" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 21. If left empty, the layer will display as "Layer 21".
		</member>
		<member name="layer_names/avoidance/layer_22" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 22. If left empty, the layer will display as "Layer 22".
		</member>
		<member name="layer_names/avoidance/layer_23" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 23. If left empty, the layer will display as "Layer 23".
		</member>
		<member name="layer_names/avoidance/layer_24" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 24. If left empty, the layer will display as "Layer 24".
		</member>
		<member name="layer_names/avoidance/layer_25" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 25. If left empty, the layer will display as "Layer 25".
		</member>
		<member name="layer_names/avoidance/layer_26" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 26. If left empty, the layer will display as "Layer 26".
		</member>
		<member name="layer_names/avoidance/layer_27" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 27. If left empty, the layer will display as "Layer 27".
		</member>
		<member name="layer_names/avoidance/layer_28" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 28. If left empty, the layer will display as "Layer 28".
		</member>
		<member name="layer_names/avoidance/layer_29" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 29. If left empty, the layer will display as "Layer 29".
		</member>
		<member name="layer_names/avoidance/layer_30" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 30. If left empty, the layer will display as "Layer 30".
		</member>
		<member name="layer_names/avoidance/layer_31" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 31. If left empty, the layer will display as "Layer 31".
		</member>
		<member name="layer_names/avoidance/layer_32" type="String" setter="" getter="" default="&quot;&quot;">
			Optional name for the navigation avoidance layer 32. If left empty, the layer will display as "Layer 32".
		</member>
		<member name="memory/limits/message_queue/max_size_mb" type="int" setter="" getter="" default="32">
			Redot uses a message queue to defer some function calls. If you run out of space on it (you will see an error), you can increase the size here.
		</member>
		<member name="navigation/2d/default_cell_size" type="float" setter="" getter="" default="1.0">
			Default cell size for 2D navigation maps. See [method NavigationServer2D.map_set_cell_size].
		</member>
		<member name="navigation/2d/default_edge_connection_margin" type="float" setter="" getter="" default="1.0">
			Default edge connection margin for 2D navigation maps. See [method NavigationServer2D.map_set_edge_connection_margin].
		</member>
		<member name="navigation/2d/default_link_connection_radius" type="float" setter="" getter="" default="4.0">
			Default link connection radius for 2D navigation maps. See [method NavigationServer2D.map_set_link_connection_radius].
		</member>
		<member name="navigation/2d/use_edge_connections" type="bool" setter="" getter="" default="true">
			If enabled 2D navigation regions will use edge connections to connect with other navigation regions within proximity of the navigation map edge connection margin. This setting only affects World2D default navigation maps.
		</member>
		<member name="navigation/3d/default_cell_height" type="float" setter="" getter="" default="0.25">
			Default cell height for 3D navigation maps. See [method NavigationServer3D.map_set_cell_height].
		</member>
		<member name="navigation/3d/default_cell_size" type="float" setter="" getter="" default="0.25">
			Default cell size for 3D navigation maps. See [method NavigationServer3D.map_set_cell_size].
		</member>
		<member name="navigation/3d/default_edge_connection_margin" type="float" setter="" getter="" default="0.25">
			Default edge connection margin for 3D navigation maps. See [method NavigationServer3D.map_set_edge_connection_margin].
		</member>
		<member name="navigation/3d/default_link_connection_radius" type="float" setter="" getter="" default="1.0">
			Default link connection radius for 3D navigation maps. See [method NavigationServer3D.map_set_link_connection_radius].
		</member>
		<member name="navigation/3d/default_up" type="Vector3" setter="" getter="" default="Vector3(0, 1, 0)">
			Default up orientation for 3D navigation maps. See [method NavigationServer3D.map_set_up].
		</member>
		<member name="navigation/3d/merge_rasterizer_cell_scale" type="float" setter="" getter="" default="1.0">
			Default merge rasterizer cell scale for 3D navigation maps. See [method NavigationServer3D.map_set_merge_rasterizer_cell_scale].
		</member>
		<member name="navigation/3d/use_edge_connections" type="bool" setter="" getter="" default="true">
			If enabled 3D navigation regions will use edge connections to connect with other navigation regions within proximity of the navigation map edge connection margin. This setting only affects World3D default navigation maps.
		</member>
		<member name="navigation/avoidance/thread_model/avoidance_use_high_priority_threads" type="bool" setter="" getter="" default="true">
			If enabled and avoidance calculations use multiple threads the threads run with high priority.
		</member>
		<member name="navigation/avoidance/thread_model/avoidance_use_multiple_threads" type="bool" setter="" getter="" default="true">
			If enabled the avoidance calculations use multiple threads.
		</member>
		<member name="navigation/baking/thread_model/baking_use_high_priority_threads" type="bool" setter="" getter="" default="true">
			If enabled and async navmesh baking uses multiple threads the threads run with high priority.
		</member>
		<member name="navigation/baking/thread_model/baking_use_multiple_threads" type="bool" setter="" getter="" default="true">
			If enabled the async navmesh baking uses multiple threads.
		</member>
		<member name="navigation/baking/use_crash_prevention_checks" type="bool" setter="" getter="" default="true">
			If enabled, and baking would potentially lead to an engine crash, the baking will be interrupted and an error message with explanation will be raised.
		</member>
		<member name="navigation/pathfinding/max_threads" type="int" setter="" getter="" default="4">
			Maximum number of threads that can run pathfinding queries simultaneously on the same pathfinding graph, for example the same navigation map. Additional threads increase memory consumption and synchronization time due to the need for extra data copies prepared for each thread. A value of [code]-1[/code] means unlimited and the maximum available OS processor count is used. Defaults to [code]1[/code] when the OS does not support threads.
		</member>
		<member name="navigation/world/map_use_async_iterations" type="bool" setter="" getter="" default="true">
			If enabled, navigation map synchronization uses an async process that runs on a background thread. This avoids stalling the main thread but adds an additional delay to any navigation map change.
		</member>
		<member name="network/limits/debugger/max_chars_per_second" type="int" setter="" getter="" default="32768">
			Maximum number of characters allowed to send as output from the debugger. Over this value, content is dropped. This helps not to stall the debugger connection.
		</member>
		<member name="network/limits/debugger/max_errors_per_second" type="int" setter="" getter="" default="400">
			Maximum number of errors allowed to be sent from the debugger. Over this value, content is dropped. This helps not to stall the debugger connection.
		</member>
		<member name="network/limits/debugger/max_queued_messages" type="int" setter="" getter="" default="2048">
			Maximum number of messages in the debugger queue. Over this value, content is dropped. This helps to limit the debugger memory usage.
		</member>
		<member name="network/limits/debugger/max_warnings_per_second" type="int" setter="" getter="" default="400">
			Maximum number of warnings allowed to be sent from the debugger. Over this value, content is dropped. This helps not to stall the debugger connection.
		</member>
		<member name="network/limits/packet_peer_stream/max_buffer_po2" type="int" setter="" getter="" default="16">
			Default size of packet peer stream for deserializing Redot data (in bytes, specified as a power of two). The default value [code]16[/code] is equal to 65,536 bytes. Over this size, data is dropped.
		</member>
		<member name="network/limits/tcp/connect_timeout_seconds" type="int" setter="" getter="" default="30">
			Timeout (in seconds) for connection attempts using TCP.
		</member>
		<member name="network/limits/webrtc/max_channel_in_buffer_kb" type="int" setter="" getter="" default="64">
			Maximum size (in kiB) for the [WebRTCDataChannel] input buffer.
		</member>
		<member name="network/tls/certificate_bundle_override" type="String" setter="" getter="" default="&quot;&quot;">
			The CA certificates bundle to use for TLS connections. If this is set to a non-empty value, this will [i]override[/i] Redot's default [url=https://github.com/redot-engine/godot-engine/blob/master/thirdparty/certs/ca-certificates.crt]Mozilla certificate bundle[/url]. If left empty, the default certificate bundle will be used.
			If in doubt, leave this setting empty.
		</member>
		<member name="network/tls/enable_tls_v1.3" type="bool" setter="" getter="" default="true">
			If [code]true[/code], enable TLSv1.3 negotiation.
			[b]Note:[/b] Only supported when using Mbed TLS 3.0 or later (Linux distribution packages may be compiled against older system Mbed TLS packages), otherwise the maximum supported TLS version is always TLSv1.2.
		</member>
		<member name="physics/2d/default_angular_damp" type="float" setter="" getter="" default="1.0">
			The default rotational motion damping in 2D. Damping is used to gradually slow down physical objects over time. RigidBodies will fall back to this value when combining their own damping values and no area damping value is present.
			Suggested values are in the range [code]0[/code] to [code]30[/code]. At value [code]0[/code] objects will keep moving with the same velocity. Greater values will stop the object faster. A value equal to or greater than the physics tick rate ([member physics/common/physics_ticks_per_second]) will bring the object to a stop in one iteration.
			[b]Note:[/b] Redot damping calculations are velocity-dependent, meaning bodies moving faster will take a longer time to come to rest. They do not simulate inertia, friction, or air resistance. Therefore heavier or larger bodies will lose speed at the same proportional rate as lighter or smaller bodies.
			During each physics tick, Redot will multiply the linear velocity of RigidBodies by [code]1.0 - combined_damp / physics_ticks_per_second[/code]. By default, bodies combine damp factors: [code]combined_damp[/code] is the sum of the damp value of the body and this value or the area's value the body is in. See [enum RigidBody2D.DampMode].
			[b]Warning:[/b] Redot's damping calculations are simulation tick rate dependent. Changing [member physics/common/physics_ticks_per_second] may significantly change the outcomes and feel of your simulation. This is true for the entire range of damping values greater than 0. To get back to a similar feel, you also need to change your damp values. This needed change is not proportional and differs from case to case.
		</member>
		<member name="physics/2d/default_gravity" type="float" setter="" getter="" default="980.0">
			The default gravity strength in 2D (in pixels per second squared).
			[b]Note:[/b] This property is only read when the project starts. To change the default gravity at runtime, use the following code sample:
			[codeblocks]
			[gdscript]
			# Set the default gravity strength to 980.
			PhysicsServer2D.area_set_param(get_viewport().find_world_2d().space, PhysicsServer2D.AREA_PARAM_GRAVITY, 980)
			[/gdscript]
			[csharp]
			// Set the default gravity strength to 980.
			PhysicsServer2D.AreaSetParam(GetViewport().FindWorld2D().Space, PhysicsServer2D.AreaParameter.Gravity, 980);
			[/csharp]
			[/codeblocks]
		</member>
		<member name="physics/2d/default_gravity_vector" type="Vector2" setter="" getter="" default="Vector2(0, 1)">
			The default gravity direction in 2D.
			[b]Note:[/b] This property is only read when the project starts. To change the default gravity vector at runtime, use the following code sample:
			[codeblocks]
			[gdscript]
			# Set the default gravity direction to `Vector2(0, 1)`.
			PhysicsServer2D.area_set_param(get_viewport().find_world_2d().space, PhysicsServer2D.AREA_PARAM_GRAVITY_VECTOR, Vector2.DOWN)
			[/gdscript]
			[csharp]
			// Set the default gravity direction to `Vector2(0, 1)`.
			PhysicsServer2D.AreaSetParam(GetViewport().FindWorld2D().Space, PhysicsServer2D.AreaParameter.GravityVector, Vector2.Down)
			[/csharp]
			[/codeblocks]
		</member>
		<member name="physics/2d/default_linear_damp" type="float" setter="" getter="" default="0.1">
			The default linear motion damping in 2D. Damping is used to gradually slow down physical objects over time. RigidBodies will fall back to this value when combining their own damping values and no area damping value is present.
			Suggested values are in the range [code]0[/code] to [code]30[/code]. At value [code]0[/code] objects will keep moving with the same velocity. Greater values will stop the object faster. A value equal to or greater than the physics tick rate ([member physics/common/physics_ticks_per_second]) will bring the object to a stop in one iteration.
			[b]Note:[/b] Redot damping calculations are velocity-dependent, meaning bodies moving faster will take a longer time to come to rest. They do not simulate inertia, friction, or air resistance. Therefore heavier or larger bodies will lose speed at the same proportional rate as lighter or smaller bodies.
			During each physics tick, Redot will multiply the linear velocity of RigidBodies by [code]1.0 - combined_damp / physics_ticks_per_second[/code], where [code]combined_damp[/code] is the sum of the linear damp of the body and this value, or the area's value the body is in, assuming the body defaults to combine damp values. See [enum RigidBody2D.DampMode].
			[b]Warning:[/b] Redot's damping calculations are simulation tick rate dependent. Changing [member physics/common/physics_ticks_per_second] may significantly change the outcomes and feel of your simulation. This is true for the entire range of damping values greater than 0. To get back to a similar feel, you also need to change your damp values. This needed change is not proportional and differs from case to case.
		</member>
		<member name="physics/2d/physics_engine" type="String" setter="" getter="" default="&quot;DEFAULT&quot;">
			Sets which physics engine to use for 2D physics.
			[b]DEFAULT[/b] is currently equivalent to [b]GodotPhysics2D[/b], but may change in future releases. Select an explicit implementation if you want to ensure that your project stays on the same engine.
			[b]GodotPhysics2D[/b] is Redot's internal 2D physics engine.
			[b]Dummy[/b] is a 2D physics server that does nothing and returns only dummy values, effectively disabling all 2D physics functionality.
			Third-party extensions and modules can add other physics engines to select with this setting.
		</member>
		<member name="physics/2d/run_on_separate_thread" type="bool" setter="" getter="" default="false">
			If [code]true[/code], the 2D physics server runs on a separate thread, making better use of multi-core CPUs. If [code]false[/code], the 2D physics server runs on the main thread. Running the physics server on a separate thread can increase performance, but restricts API access to only physics process.
		</member>
		<member name="physics/2d/sleep_threshold_angular" type="float" setter="" getter="" default="0.139626">
			Threshold angular velocity under which a 2D physics body will be considered inactive. See [constant PhysicsServer2D.SPACE_PARAM_BODY_ANGULAR_VELOCITY_SLEEP_THRESHOLD].
		</member>
		<member name="physics/2d/sleep_threshold_linear" type="float" setter="" getter="" default="2.0">
			Threshold linear velocity under which a 2D physics body will be considered inactive. See [constant PhysicsServer2D.SPACE_PARAM_BODY_LINEAR_VELOCITY_SLEEP_THRESHOLD].
		</member>
		<member name="physics/2d/solver/contact_max_allowed_penetration" type="float" setter="" getter="" default="0.3">
			Maximum distance a shape can penetrate another shape before it is considered a collision. See [constant PhysicsServer2D.SPACE_PARAM_CONTACT_MAX_ALLOWED_PENETRATION].
		</member>
		<member name="physics/2d/solver/contact_max_separation" type="float" setter="" getter="" default="1.5">
			Maximum distance a shape can be from another before they are considered separated and the contact is discarded. See [constant PhysicsServer2D.SPACE_PARAM_CONTACT_MAX_SEPARATION].
		</member>
		<member name="physics/2d/solver/contact_recycle_radius" type="float" setter="" getter="" default="1.0">
			Maximum distance a pair of bodies has to move before their collision status has to be recalculated. See [constant PhysicsServer2D.SPACE_PARAM_CONTACT_RECYCLE_RADIUS].
		</member>
		<member name="physics/2d/solver/default_constraint_bias" type="float" setter="" getter="" default="0.2">
			Default solver bias for all physics constraints. Defines how much bodies react to enforce constraints. See [constant PhysicsServer2D.SPACE_PARAM_CONSTRAINT_DEFAULT_BIAS].
			Individual constraints can have a specific bias value (see [member Joint2D.bias]).
		</member>
		<member name="physics/2d/solver/default_contact_bias" type="float" setter="" getter="" default="0.8">
			Default solver bias for all physics contacts. Defines how much bodies react to enforce contact separation. See [constant PhysicsServer2D.SPACE_PARAM_CONTACT_DEFAULT_BIAS].
			Individual shapes can have a specific bias value (see [member Shape2D.custom_solver_bias]).
		</member>
		<member name="physics/2d/solver/solver_iterations" type="int" setter="" getter="" default="16">
			Number of solver iterations for all contacts and constraints. The greater the number of iterations, the more accurate the collisions will be. However, a greater number of iterations requires more CPU power, which can decrease performance. See [constant PhysicsServer2D.SPACE_PARAM_SOLVER_ITERATIONS].
		</member>
		<member name="physics/2d/time_before_sleep" type="float" setter="" getter="" default="0.5">
			Time (in seconds) of inactivity before which a 2D physics body will put to sleep. See [constant PhysicsServer2D.SPACE_PARAM_BODY_TIME_TO_SLEEP].
		</member>
		<member name="physics/3d/default_angular_damp" type="float" setter="" getter="" default="0.1">
			The default rotational motion damping in 3D. Damping is used to gradually slow down physical objects over time. RigidBodies will fall back to this value when combining their own damping values and no area damping value is present.
			Suggested values are in the range [code]0[/code] to [code]30[/code]. At value [code]0[/code] objects will keep moving with the same velocity. Greater values will stop the object faster. A value equal to or greater than the physics tick rate ([member physics/common/physics_ticks_per_second]) will bring the object to a stop in one iteration.
			[b]Note:[/b] Redot damping calculations are velocity-dependent, meaning bodies moving faster will take a longer time to come to rest. They do not simulate inertia, friction, or air resistance. Therefore heavier or larger bodies will lose speed at the same proportional rate as lighter or smaller bodies.
			During each physics tick, Redot will multiply the angular velocity of RigidBodies by [code]1.0 - combined_damp / physics_ticks_per_second[/code]. By default, bodies combine damp factors: [code]combined_damp[/code] is the sum of the damp value of the body and this value or the area's value the body is in. See [enum RigidBody3D.DampMode].
			[b]Warning:[/b] Redot's damping calculations are simulation tick rate dependent. Changing [member physics/common/physics_ticks_per_second] may significantly change the outcomes and feel of your simulation. This is true for the entire range of damping values greater than 0. To get back to a similar feel, you also need to change your damp values. This needed change is not proportional and differs from case to case.
		</member>
		<member name="physics/3d/default_gravity" type="float" setter="" getter="" default="9.8">
			The default gravity strength in 3D (in meters per second squared).
			[b]Note:[/b] This property is only read when the project starts. To change the default gravity at runtime, use the following code sample:
			[codeblocks]
			[gdscript]
			# Set the default gravity strength to 9.8.
			PhysicsServer3D.area_set_param(get_viewport().find_world_3d().space, PhysicsServer3D.AREA_PARAM_GRAVITY, 9.8)
			[/gdscript]
			[csharp]
			// Set the default gravity strength to 9.8.
			PhysicsServer3D.AreaSetParam(GetViewport().FindWorld3D().Space, PhysicsServer3D.AreaParameter.Gravity, 9.8);
			[/csharp]
			[/codeblocks]
		</member>
		<member name="physics/3d/default_gravity_vector" type="Vector3" setter="" getter="" default="Vector3(0, -1, 0)">
			The default gravity direction in 3D.
			[b]Note:[/b] This property is only read when the project starts. To change the default gravity vector at runtime, use the following code sample:
			[codeblocks]
			[gdscript]
			# Set the default gravity direction to `Vector3(0, -1, 0)`.
			PhysicsServer3D.area_set_param(get_viewport().find_world_3d().space, PhysicsServer3D.AREA_PARAM_GRAVITY_VECTOR, Vector3.DOWN)
			[/gdscript]
			[csharp]
			// Set the default gravity direction to `Vector3(0, -1, 0)`.
			PhysicsServer3D.AreaSetParam(GetViewport().FindWorld3D().Space, PhysicsServer3D.AreaParameter.GravityVector, Vector3.Down)
			[/csharp]
			[/codeblocks]
		</member>
		<member name="physics/3d/default_linear_damp" type="float" setter="" getter="" default="0.1">
			The default linear motion damping in 3D. Damping is used to gradually slow down physical objects over time. RigidBodies will fall back to this value when combining their own damping values and no area damping value is present.
			Suggested values are in the range [code]0[/code] to [code]30[/code]. At value [code]0[/code] objects will keep moving with the same velocity. Greater values will stop the object faster. A value equal to or greater than the physics tick rate ([member physics/common/physics_ticks_per_second]) will bring the object to a stop in one iteration.
			[b]Note:[/b] Redot damping calculations are velocity-dependent, meaning bodies moving faster will take a longer time to come to rest. They do not simulate inertia, friction, or air resistance. Therefore heavier or larger bodies will lose speed at the same proportional rate as lighter or smaller bodies.
			During each physics tick, Redot will multiply the linear velocity of RigidBodies by [code]1.0 - combined_damp / physics_ticks_per_second[/code]. By default, bodies combine damp factors: [code]combined_damp[/code] is the sum of the damp value of the body and this value or the area's value the body is in. See [enum RigidBody3D.DampMode].
			[b]Warning:[/b] Redot's damping calculations are simulation tick rate dependent. Changing [member physics/common/physics_ticks_per_second] may significantly change the outcomes and feel of your simulation. This is true for the entire range of damping values greater than 0. To get back to a similar feel, you also need to change your damp values. This needed change is not proportional and differs from case to case.
		</member>
		<member name="physics/3d/physics_engine" type="String" setter="" getter="" default="&quot;DEFAULT&quot;">
			Sets which physics engine to use for 3D physics.
			[b]DEFAULT[/b] is currently equivalent to [b]GodotPhysics3D[/b], but may change in future releases. Select an explicit implementation if you want to ensure that your project stays on the same engine.
			[b]GodotPhysics3D[/b] is Redot's internal 3D physics engine.
			[b]Jolt Physics[/b] is an alternative physics engine that is generally faster and more reliable than [b]GodotPhysics3D[/b]. As it was recently implemented, it is currently considered experimental and its behavior may change in future releases.
			[b]Dummy[/b] is a 3D physics server that does nothing and returns only dummy values, effectively disabling all 3D physics functionality.
			Third-party extensions and modules can add other physics engines to select with this setting.
		</member>
		<member name="physics/3d/run_on_separate_thread" type="bool" setter="" getter="" default="false">
			If [code]true[/code], the 3D physics server runs on a separate thread, making better use of multi-core CPUs. If [code]false[/code], the 3D physics server runs on the main thread. Running the physics server on a separate thread can increase performance, but restricts API access to only physics process.
			[b]Note:[/b] When [member physics/3d/physics_engine] is set to [code]Jolt Physics[/code], enabling this setting will prevent the 3D physics server from being able to provide any context when reporting errors and warnings, and will instead always refer to nodes as [code]&lt;unknown&gt;[/code].
		</member>
		<member name="physics/3d/sleep_threshold_angular" type="float" setter="" getter="" default="0.139626">
			Threshold angular velocity under which a 3D physics body will be considered inactive. See [constant PhysicsServer3D.SPACE_PARAM_BODY_ANGULAR_VELOCITY_SLEEP_THRESHOLD].
		</member>
		<member name="physics/3d/sleep_threshold_linear" type="float" setter="" getter="" default="0.1">
			Threshold linear velocity under which a 3D physics body will be considered inactive. See [constant PhysicsServer3D.SPACE_PARAM_BODY_LINEAR_VELOCITY_SLEEP_THRESHOLD].
		</member>
		<member name="physics/3d/solver/contact_max_allowed_penetration" type="float" setter="" getter="" default="0.01">
			Maximum distance a shape can penetrate another shape before it is considered a collision. See [constant PhysicsServer3D.SPACE_PARAM_CONTACT_MAX_ALLOWED_PENETRATION].
		</member>
		<member name="physics/3d/solver/contact_max_separation" type="float" setter="" getter="" default="0.05">
			Maximum distance a shape can be from another before they are considered separated and the contact is discarded. See [constant PhysicsServer3D.SPACE_PARAM_CONTACT_MAX_SEPARATION].
		</member>
		<member name="physics/3d/solver/contact_recycle_radius" type="float" setter="" getter="" default="0.01">
			Maximum distance a pair of bodies has to move before their collision status has to be recalculated. See [constant PhysicsServer3D.SPACE_PARAM_CONTACT_RECYCLE_RADIUS].
		</member>
		<member name="physics/3d/solver/default_contact_bias" type="float" setter="" getter="" default="0.8">
			Default solver bias for all physics contacts. Defines how much bodies react to enforce contact separation. See [constant PhysicsServer3D.SPACE_PARAM_CONTACT_DEFAULT_BIAS].
			Individual shapes can have a specific bias value (see [member Shape3D.custom_solver_bias]).
		</member>
		<member name="physics/3d/solver/solver_iterations" type="int" setter="" getter="" default="16">
			Number of solver iterations for all contacts and constraints. The greater the number of iterations, the more accurate the collisions will be. However, a greater number of iterations requires more CPU power, which can decrease performance. See [constant PhysicsServer3D.SPACE_PARAM_SOLVER_ITERATIONS].
		</member>
		<member name="physics/3d/time_before_sleep" type="float" setter="" getter="" default="0.5">
			Time (in seconds) of inactivity before which a 3D physics body will put to sleep. See [constant PhysicsServer3D.SPACE_PARAM_BODY_TIME_TO_SLEEP].
		</member>
		<member name="physics/common/enable_object_picking" type="bool" setter="" getter="" default="true">
			Enables [member Viewport.physics_object_picking] on the root viewport.
		</member>
		<member name="physics/common/max_physics_steps_per_frame" type="int" setter="" getter="" default="8">
			Controls the maximum number of physics steps that can be simulated each rendered frame. The default value is tuned to avoid "spiral of death" situations where expensive physics simulations trigger more expensive simulations indefinitely. However, the game will appear to slow down if the rendering FPS is less than [code]1 / max_physics_steps_per_frame[/code] of [member physics/common/physics_ticks_per_second]. This occurs even if [code]delta[/code] is consistently used in physics calculations. To avoid this, increase [member physics/common/max_physics_steps_per_frame] if you have increased [member physics/common/physics_ticks_per_second] significantly above its default value.
			[b]Note:[/b] This property is only read when the project starts. To change the maximum number of simulated physics steps per frame at runtime, set [member Engine.max_physics_steps_per_frame] instead.
		</member>
		<member name="physics/common/physics_interpolation" type="bool" setter="" getter="" default="false">
			If [code]true[/code], the renderer will interpolate the transforms of objects (both physics and non-physics) between the last two transforms, so that smooth motion is seen even when physics ticks do not coincide with rendered frames. See also [method Node.reset_physics_interpolation].
			[b]Note:[/b] Although this is a global setting, finer control of individual branches of the [SceneTree] is possible using [member Node.physics_interpolation_mode].
			[b]Note:[/b] This property is only read when the project starts. To toggle physics interpolation at runtime, set [member SceneTree.physics_interpolation] instead.
			[b]Note:[/b] Property [member physics/common/physics_jitter_fix] is automatically disabled if [member physics/common/physics_interpolation] is set to [code]true[/code], as the two methods are incompatible.
		</member>
		<member name="physics/common/physics_jitter_fix" type="float" setter="" getter="" default="0.5">
			Controls how much physics ticks are synchronized with real time. For 0 or less, the ticks are synchronized. Such values are recommended for network games, where clock synchronization matters. Higher values cause higher deviation of in-game clock and real clock, but allows smoothing out framerate jitters. The default value of 0.5 should be good enough for most; values above 2 could cause the game to react to dropped frames with a noticeable delay and are not recommended.
			[b]Note:[/b] Jitter fix is automatically disabled at runtime when [member physics/common/physics_interpolation] is enabled.
			[b]Note:[/b] When using a custom physics interpolation solution, the physics jitter fix should be disabled by setting [member physics/common/physics_jitter_fix] to [code]0.0[/code].
			[b]Note:[/b] This property is only read when the project starts. To change the physics jitter fix at runtime, set [member Engine.physics_jitter_fix] instead.
		</member>
		<member name="physics/common/physics_ticks_per_second" type="int" setter="" getter="" default="60">
			The number of fixed iterations per second. This controls how often physics simulation and [method Node._physics_process] methods are run. See also [member application/run/max_fps].
			[b]Note:[/b] This property is only read when the project starts. To change the physics FPS at runtime, set [member Engine.physics_ticks_per_second] instead.
			[b]Note:[/b] Only [member physics/common/max_physics_steps_per_frame] physics ticks may be simulated per rendered frame at most. If more physics ticks have to be simulated per rendered frame to keep up with rendering, the project will appear to slow down (even if [code]delta[/code] is used consistently in physics calculations). Therefore, it is recommended to also increase [member physics/common/max_physics_steps_per_frame] if increasing [member physics/common/physics_ticks_per_second] significantly above its default value.
		</member>
		<member name="physics/jolt_physics_3d/collisions/active_edge_threshold" type="float" setter="" getter="" default="0.872665">
			The maximum angle, in radians, between two adjacent triangles in a [ConcavePolygonShape3D] or [HeightMapShape3D] for which the edge between those triangles is considered inactive.
			Collisions against an inactive edge will have its normal overridden to instead be the surface normal of the triangle. This can help alleviate ghost collisions.
			[b]Note:[/b] Setting this too high can result in objects not depenetrating properly.
			[b]Note:[/b] This applies to all shape queries, as well as physics bodies within the simulation.
			[b]Note:[/b] This does not apply when enabling Jolt's enhanced internal edge removal, which supersedes this.
		</member>
		<member name="physics/jolt_physics_3d/collisions/collision_margin_fraction" type="float" setter="" getter="" default="0.08">
			The amount of collision margin to use for certain convex collision shapes, such as [BoxShape3D], [CylinderShape3D] and [ConvexPolygonShape3D], as a fraction of the shape's shortest axis, with [member Shape3D.margin] as the upper bound. This is mainly used to speed up collision detection with convex shapes.
			[b]Note:[/b] Collision margins in Jolt do not add any extra size to the shape. Instead the shape is first shrunk by the margin and then expanded by the same amount, resulting in a shape with rounded corners.
			[b]Note:[/b] Setting this value too close to [code]0.0[/code] may also negatively affect the accuracy of the collision detection with convex shapes.
		</member>
		<member name="physics/jolt_physics_3d/joints/world_node" type="int" setter="" getter="" default="0">
			Which of the two nodes bound by a joint should represent the world when one of the two is omitted, as either [member Joint3D.node_a] or [member Joint3D.node_b]. This can be thought of as having the omitted node be a [StaticBody3D] at the joint's position. Joint limits are more easily expressed when [member Joint3D.node_a] represents the world.
			[b]Note:[/b] In Godot Physics, only [member Joint3D.node_b] can represent the world.
		</member>
		<member name="physics/jolt_physics_3d/limits/max_angular_velocity" type="float" setter="" getter="" default="47.1239">
			The maximum angular velocity that a [RigidBody3D] can reach, in radians per second.
			This is mainly used as a fail-safe, to prevent the simulation from exploding, as fast-moving objects colliding with complex physics structures can otherwise cause them to go out of control. Fast-moving objects can also cause a lot of stress on the collision detection system, which can slow down the simulation considerably.
		</member>
		<member name="physics/jolt_physics_3d/limits/max_bodies" type="int" setter="" getter="" default="10240">
			The maximum number of [PhysicsBody3D] to support at the same time, awake or sleeping. When this limit is exceeded, an error is reported and anything past that point is undefined behavior.
			[b]Note:[/b] This limit also applies within the editor.
		</member>
		<member name="physics/jolt_physics_3d/limits/max_body_pairs" type="int" setter="" getter="" default="65536">
			The maximum number of body pairs to allow processing of. When this limit is exceeded, a warning is reported and collisions will randomly be ignored while bodies pass through each other.
		</member>
		<member name="physics/jolt_physics_3d/limits/max_contact_constraints" type="int" setter="" getter="" default="20480">
			The maximum number of contact constraints to allow processing of. When this limit is exceeded, a warning is reported and collisions will randomly be ignored while bodies pass through each other.
		</member>
		<member name="physics/jolt_physics_3d/limits/max_linear_velocity" type="float" setter="" getter="" default="500.0">
			The maximum linear velocity that a [RigidBody3D] can reach, in meters per second.
			This is mainly used as a fail-safe, to prevent the simulation from exploding, as fast-moving objects colliding with complex physics structures can otherwise cause them to go out of control. Fast-moving objects can also cause a lot of stress on the collision detection system, which can slow down the simulation considerably.
		</member>
		<member name="physics/jolt_physics_3d/limits/temporary_memory_buffer_size" type="int" setter="" getter="" default="32">
			The amount of memory to pre-allocate for the stack allocator used within Jolt, in MiB. This allocator is used within the physics step to store things that are only needed during it, like which bodies are in contact, how they form islands and the data needed to solve the contacts.
		</member>
		<member name="physics/jolt_physics_3d/limits/world_boundary_shape_size" type="float" setter="" getter="" default="2000.0">
			The size of [WorldBoundaryShape3D] boundaries, for all three dimensions. The plane is effectively centered within a box of this size, and anything outside of the box will not collide with it. This is necessary as [WorldBoundaryShape3D] is not unbounded when using Jolt, in order to prevent precision issues.
			[b]Note:[/b] Setting this value too high can make collision detection less accurate.
			[b]Note:[/b] Collisions against the effective edges of a [WorldBoundaryShape3D] will be inconsistent.
		</member>
		<member name="physics/jolt_physics_3d/motion_queries/recovery_amount" type="float" setter="" getter="" default="0.4">
			Fraction of the total penetration to depenetrate per iteration during motion queries.
			[b]Note:[/b] This affects methods [method CharacterBody3D.move_and_slide], [method PhysicsBody3D.move_and_collide], [method PhysicsBody3D.test_move] and [method PhysicsServer3D.body_test_motion].
		</member>
		<member name="physics/jolt_physics_3d/motion_queries/recovery_iterations" type="int" setter="" getter="" default="4">
			The number of iterations to run when depenetrating during motion queries.
			[b]Note:[/b] This affects methods [method CharacterBody3D.move_and_slide], [method PhysicsBody3D.move_and_collide], [method PhysicsBody3D.test_move] and [method PhysicsServer3D.body_test_motion].
		</member>
		<member name="physics/jolt_physics_3d/motion_queries/use_enhanced_internal_edge_removal" type="bool" setter="" getter="" default="true">
			If [code]true[/code], enables Jolt's enhanced internal edge removal during motion queries. This can help alleviate ghost collisions, but only with edges within a single body, meaning edges between separate bodies can still cause ghost collisions.
			[b]Note:[/b] This affects methods [method CharacterBody3D.move_and_slide], [method PhysicsBody3D.move_and_collide], [method PhysicsBody3D.test_move] and [method PhysicsServer3D.body_test_motion].
		</member>
		<member name="physics/jolt_physics_3d/queries/enable_ray_cast_face_index" type="bool" setter="" getter="" default="false">
			If [code]true[/code], populates the [code]face_index[/code] field in the results of [method PhysicsDirectSpaceState3D.intersect_ray], also accessed through [method RayCast3D.get_collision_face_index]. If [code]false[/code], the [code]face_index[/code] field will be left at its default value of [code]-1[/code].
			[b]Note:[/b] Enabling this setting will increase Jolt's memory usage for [ConcavePolygonShape3D] by around 25%.
		</member>
		<member name="physics/jolt_physics_3d/queries/use_enhanced_internal_edge_removal" type="bool" setter="" getter="" default="false">
			If [code]true[/code], enables Jolt's enhanced internal edge removal during shape queries. This can help alleviate ghost collisions when using shape queries for things like character movement, but only with edges within a single body, meaning edges between separate bodies can still cause ghost collisions.
			[b]Note:[/b] This affects methods [method PhysicsDirectSpaceState3D.cast_motion], [method PhysicsDirectSpaceState3D.collide_shape], [method PhysicsDirectSpaceState3D.get_rest_info] and [method PhysicsDirectSpaceState3D.intersect_shape].
			[b]Note:[/b] Enabling this setting can cause certain shapes to be culled from the results entirely, but you will get at least one intersection per body.
		</member>
		<member name="physics/jolt_physics_3d/simulation/allow_sleep" type="bool" setter="" getter="" default="true">
			If [code]true[/code], [RigidBody3D] nodes are allowed to go to sleep if their velocity is below the threshold defined in [member physics/jolt_physics_3d/simulation/sleep_velocity_threshold] for the duration set in [member physics/jolt_physics_3d/simulation/sleep_time_threshold]. This can improve physics simulation performance when there are non-moving [RigidBody3D] nodes, at the cost of some nodes possibly failing to wake up in certain scenarios. Consider disabling this temporarily to troubleshoot [RigidBody3D] nodes not moving when they should.
		</member>
		<member name="physics/jolt_physics_3d/simulation/areas_detect_static_bodies" type="bool" setter="" getter="" default="false">
			If [code]true[/code], [Area3D] nodes are able to detect overlaps with [StaticBody3D] nodes.
			[b]Note:[/b] Enabling this setting can come at a heavy CPU and memory cost if you allow many/large [Area3D] to overlap with complex static geometry, such as [ConcavePolygonShape3D] or [HeightMapShape3D]. It is strongly recommended that you set up your collision layers and masks in such a way that only a few small [Area3D] nodes can detect [StaticBody3D] nodes.
			[b]Note:[/b] This also applies to overlaps with a [RigidBody3D] frozen with [constant RigidBody3D.FREEZE_MODE_STATIC].
			[b]Note:[/b] This is not needed to detect overlaps with [AnimatableBody3D], as it is a kinematic body, despite inheriting from [StaticBody3D].
		</member>
		<member name="physics/jolt_physics_3d/simulation/baumgarte_stabilization_factor" type="float" setter="" getter="" default="0.2">
			How much of the position error of a [RigidBody3D] to fix during a physics step, where [code]0.0[/code] is none and [code]1.0[/code] is the full amount. This affects things like how quickly bodies depenetrate.
			[b]Note:[/b] Setting this value too high can make [RigidBody3D] nodes unstable.
		</member>
		<member name="physics/jolt_physics_3d/simulation/body_pair_contact_cache_angle_threshold" type="float" setter="" getter="" default="0.0349066">
			The maximum relative angle by which a body pair can move and still reuse the collision results from the previous physics step, in radians.
		</member>
		<member name="physics/jolt_physics_3d/simulation/body_pair_contact_cache_distance_threshold" type="float" setter="" getter="" default="0.001">
			The maximum relative distance by which a body pair can move and still reuse the collision results from the previous physics step, in meters.
		</member>
		<member name="physics/jolt_physics_3d/simulation/body_pair_contact_cache_enabled" type="bool" setter="" getter="" default="true">
			If [code]true[/code], enables the body pair contact cache, which removes the need for potentially expensive collision detection when the relative orientation between two bodies hasn't changed much.
		</member>
		<member name="physics/jolt_physics_3d/simulation/bounce_velocity_threshold" type="float" setter="" getter="" default="1.0">
			The minimum velocity needed before a collision can be bouncy, in meters per second.
		</member>
		<member name="physics/jolt_physics_3d/simulation/continuous_cd_max_penetration" type="float" setter="" getter="" default="0.25">
			Fraction of a body's inner radius that may penetrate another body while using continuous collision detection.
		</member>
		<member name="physics/jolt_physics_3d/simulation/continuous_cd_movement_threshold" type="float" setter="" getter="" default="0.75">
			Fraction of a body's inner radius that the body must move per step to make use of continuous collision detection.
		</member>
		<member name="physics/jolt_physics_3d/simulation/generate_all_kinematic_contacts" type="bool" setter="" getter="" default="false">
			If [code]true[/code], a [RigidBody3D] frozen with [constant RigidBody3D.FREEZE_MODE_KINEMATIC] is able to collide with other kinematic and static bodies, and therefore generate contacts for them.
			[b]Note:[/b] This setting can come at a heavy CPU and memory cost if you allow many/large frozen kinematic bodies with a non-zero [member RigidBody3D.max_contacts_reported] to overlap with complex static geometry, such as [ConcavePolygonShape3D] or [HeightMapShape3D].
		</member>
		<member name="physics/jolt_physics_3d/simulation/penetration_slop" type="float" setter="" getter="" default="0.02">
			How much bodies are allowed to penetrate each other, in meters.
		</member>
		<member name="physics/jolt_physics_3d/simulation/position_steps" type="int" setter="" getter="" default="2">
			Number of solver position iterations. The greater the number of iterations, the more accurate the simulation will be, at the cost of CPU performance.
		</member>
		<member name="physics/jolt_physics_3d/simulation/sleep_time_threshold" type="float" setter="" getter="" default="0.5">
			Time in seconds a [RigidBody3D] will spend below the sleep velocity threshold before going to sleep.
		</member>
		<member name="physics/jolt_physics_3d/simulation/sleep_velocity_threshold" type="float" setter="" getter="" default="0.03">
			The linear velocity of specific points on the bounding box of a [RigidBody3D], below which it can be put to sleep, in meters per second. These points help capture both the linear and angular motion of a [RigidBody3D].
		</member>
		<member name="physics/jolt_physics_3d/simulation/soft_body_point_radius" type="float" setter="" getter="" default="0.01">
			How big the points of a [SoftBody3D] are, in meters. A higher value can prevent behavior such as cloth laying perfectly flush against other surfaces and causing Z-fighting.
		</member>
		<member name="physics/jolt_physics_3d/simulation/speculative_contact_distance" type="float" setter="" getter="" default="0.02">
			Radius around physics bodies, inside which speculative contact points will be detected, in meters. This is mainly used to prevent tunneling/penetration for [RigidBody3D] nodes during simulation.
			[b]Note:[/b] Setting this too high may result in ghost collisions, as speculative contacts are based on the closest points during the collision detection step which may not be the actual closest points by the time the two bodies hit.
		</member>
		<member name="physics/jolt_physics_3d/simulation/use_enhanced_internal_edge_removal" type="bool" setter="" getter="" default="true">
			If [code]true[/code], enables Jolt's enhanced internal edge removal for [RigidBody3D]. This can help alleviate ghost collisions when, for example, a [RigidBody3D] collides with the edges of two perfectly joined [BoxShape3D]. The removal only applies to edges internal to a single body, meaning edges between separate bodies can still cause ghost collisions.
		</member>
		<member name="physics/jolt_physics_3d/simulation/velocity_steps" type="int" setter="" getter="" default="10">
			Number of solver velocity iterations. The greater the number of iterations, the more accurate the simulation will be, at the cost of CPU performance.
			[b]Note:[/b] This needs to be at least [code]2[/code] in order for friction to work, as friction is applied using the non-penetration impulse from the previous iteration.
		</member>
		<member name="rendering/2d/batching/item_buffer_size" type="int" setter="" getter="" default="16384">
			Maximum number of canvas item commands that can be batched into a single draw call.
		</member>
		<member name="rendering/2d/batching/uniform_set_cache_size" type="int" setter="" getter="" default="4096">
			Maximum number of uniform sets that will be cached by the 2D renderer when batching draw calls.
			[b]Note:[/b] A project that uses a large number of unique sprite textures per frame may benefit from increasing this value.
		</member>
		<member name="rendering/2d/sdf/oversize" type="int" setter="" getter="" default="1">
			Controls how much of the original viewport size should be covered by the 2D signed distance field. This SDF can be sampled in [CanvasItem] shaders and is used for [GPUParticles2D] collision. Higher values allow portions of occluders located outside the viewport to still be taken into account in the generated signed distance field, at the cost of performance. If you notice particles falling through [LightOccluder2D]s as the occluders leave the viewport, increase this setting.
			The percentage specified is added on each axis and on both sides. For example, with the default setting of 120%, the signed distance field will cover 20% of the viewport's size outside the viewport on each side (top, right, bottom, left).
			[b]Note:[/b] This property is only read when the project starts. To change the 2D SDF oversizing percentage at runtime, use [method RenderingServer.viewport_set_sdf_oversize_and_scale] instead.
		</member>
		<member name="rendering/2d/sdf/scale" type="int" setter="" getter="" default="1">
			The resolution scale to use for the 2D signed distance field. Higher values lead to a more precise and more stable signed distance field as the camera moves, at the cost of performance. The default value (50%) renders at half the resolution of the viewport size on each axis, which means the SDF is generated with 25% of the viewport's pixel count.
			[b]Note:[/b] This property is only read when the project starts. To change the 2D SDF resolution scale at runtime, use [method RenderingServer.viewport_set_sdf_oversize_and_scale] instead.
		</member>
		<member name="rendering/2d/shadow_atlas/size" type="int" setter="" getter="" default="2048">
			The size of the 2D shadow atlas in pixels. Higher values result in more precise [Light2D] shadows, at the cost of performance and video memory usage. The specified value is rounded up to the nearest power of 2.
			[b]Note:[/b] This property is only read when the project starts. To change the 2D shadow atlas size at runtime, use [method RenderingServer.canvas_set_shadow_texture_size] instead.
		</member>
		<member name="rendering/2d/snap/snap_2d_transforms_to_pixel" type="bool" setter="" getter="" default="false">
			If [code]true[/code], [CanvasItem] nodes will internally snap to full pixels. Useful for low-resolution pixel art games. Their position can still be sub-pixel, but the decimals will not have effect as the position is rounded. This can lead to a crisper appearance at the cost of less smooth movement, especially when [Camera2D] smoothing is enabled.
			[b]Note:[/b] This property is only read when the project starts. To toggle 2D transform snapping at runtime, use [method RenderingServer.viewport_set_snap_2d_transforms_to_pixel] on the root [Viewport] instead.
			[b]Note:[/b] [Control] nodes are snapped to the nearest pixel by default. This is controlled by [member gui/common/snap_controls_to_pixels].
			[b]Note:[/b] It is not recommended to use this setting together with [member rendering/2d/snap/snap_2d_vertices_to_pixel], as movement may appear even less smooth. Prefer only enabling this setting instead.
		</member>
		<member name="rendering/2d/snap/snap_2d_vertices_to_pixel" type="bool" setter="" getter="" default="false">
			If [code]true[/code], vertices of [CanvasItem] nodes will snap to full pixels. Useful for low-resolution pixel art games. Only affects the final vertex positions, not the transforms. This can lead to a crisper appearance at the cost of less smooth movement, especially when [Camera2D] smoothing is enabled.
			[b]Note:[/b] This property is only read when the project starts. To toggle 2D vertex snapping at runtime, use [method RenderingServer.viewport_set_snap_2d_vertices_to_pixel] on the root [Viewport] instead.
			[b]Note:[/b] [Control] nodes are snapped to the nearest pixel by default. This is controlled by [member gui/common/snap_controls_to_pixels].
			[b]Note:[/b] It is not recommended to use this setting together with [member rendering/2d/snap/snap_2d_transforms_to_pixel], as movement may appear even less smooth. Prefer only enabling that setting instead.
		</member>
		<member name="rendering/anti_aliasing/quality/msaa_2d" type="int" setter="" getter="" default="0">
			Sets the number of multisample antialiasing (MSAA) samples to use for 2D/Canvas rendering (as a power of two). MSAA is used to reduce aliasing around the edges of polygons. A higher MSAA value results in smoother edges but can be significantly slower on some hardware, especially integrated graphics due to their limited memory bandwidth. This has no effect on shader-induced aliasing or texture aliasing.
			[b]Note:[/b] MSAA is only supported in the Forward+ and Mobile rendering methods, not Compatibility.
			[b]Note:[/b] This property is only read when the project starts. To set the number of 2D MSAA samples at runtime, set [member Viewport.msaa_2d] or use [method RenderingServer.viewport_set_msaa_2d].
		</member>
		<member name="rendering/anti_aliasing/quality/msaa_3d" type="int" setter="" getter="" default="0">
			Sets the number of multisample antialiasing (MSAA) samples to use for 3D rendering (as a power of two). MSAA is used to reduce aliasing around the edges of polygons. A higher MSAA value results in smoother edges but can be significantly slower on some hardware, especially integrated graphics due to their limited memory bandwidth. See also [member rendering/scaling_3d/mode] for supersampling, which provides higher quality but is much more expensive. This has no effect on shader-induced aliasing or texture aliasing.
			[b]Note:[/b] This property is only read when the project starts. To set the number of 3D MSAA samples at runtime, set [member Viewport.msaa_3d] or use [method RenderingServer.viewport_set_msaa_3d].
		</member>
		<member name="rendering/anti_aliasing/quality/screen_space_aa" type="int" setter="" getter="" default="0">
			Sets the screen-space antialiasing mode for the default screen [Viewport]. Screen-space antialiasing works by selectively blurring edges in a post-process shader. It differs from MSAA which takes multiple coverage samples while rendering objects. Screen-space AA methods are typically faster than MSAA and will smooth out specular aliasing, but tend to make scenes appear blurry. The blurriness is partially counteracted by automatically using a negative mipmap LOD bias (see [member rendering/textures/default_filters/texture_mipmap_bias]).
			Another way to combat specular aliasing is to enable [member rendering/anti_aliasing/screen_space_roughness_limiter/enabled].
			[b]Note:[/b] Screen-space antialiasing is only supported in the Forward+ and Mobile rendering methods, not Compatibility.
			[b]Note:[/b] This property is only read when the project starts. To set the screen-space antialiasing mode at runtime, set [member Viewport.screen_space_aa] on the root [Viewport] instead, or use [method RenderingServer.viewport_set_screen_space_aa].
		</member>
		<member name="rendering/anti_aliasing/quality/use_debanding" type="bool" setter="" getter="" default="false">
			If [code]true[/code], uses a fast post-processing filter to make banding significantly less visible in 3D. 2D rendering is [i]not[/i] affected by debanding unless the [member Environment.background_mode] is [constant Environment.BG_CANVAS].
			In some cases, debanding may introduce a slightly noticeable dithering pattern. It's recommended to enable debanding only when actually needed since the dithering pattern will make lossless-compressed screenshots larger.
			[b]Note:[/b] This property is only read when the project starts. To set debanding at runtime, set [member Viewport.use_debanding] on the root [Viewport] instead, or use [method RenderingServer.viewport_set_use_debanding].
		</member>
		<member name="rendering/anti_aliasing/quality/use_taa" type="bool" setter="" getter="" default="false">
			Enables temporal antialiasing for the default screen [Viewport]. TAA works by jittering the camera and accumulating the images of the last rendered frames, motion vector rendering is used to account for camera and object motion. Enabling TAA can make the image blurrier, which is partially counteracted by automatically using a negative mipmap LOD bias (see [member rendering/textures/default_filters/texture_mipmap_bias]).
			[b]Note:[/b] The implementation is not complete yet. Some visual instances such as particles and skinned meshes may show ghosting artifacts in motion.
			[b]Note:[/b] TAA is only supported in the Forward+ rendering method, not Mobile or Compatibility.
			[b]Note:[/b] This property is only read when the project starts. To set TAA at runtime, set [member Viewport.use_taa] on the root [Viewport] instead, or use [method RenderingServer.viewport_set_use_taa].
		</member>
		<member name="rendering/anti_aliasing/screen_space_roughness_limiter/amount" type="float" setter="" getter="" default="0.25">
			[b]Note:[/b] This property is only read when the project starts. To control the screen-space roughness limiter at runtime, call [method RenderingServer.screen_space_roughness_limiter_set_active] instead.
		</member>
		<member name="rendering/anti_aliasing/screen_space_roughness_limiter/enabled" type="bool" setter="" getter="" default="true">
			If [code]true[/code], enables a spatial filter to limit roughness in areas with high-frequency detail. This can help reduce specular aliasing to an extent, though not as much as enabling [member rendering/anti_aliasing/quality/use_taa]. This filter has a small performance cost, so consider disabling it if it doesn't benefit your scene noticeably.
			[b]Note:[/b] The screen-space roughness limiter is only supported in the Forward+ and Mobile rendering methods, not Compatibility.
			[b]Note:[/b] This property is only read when the project starts. To control the screen-space roughness limiter at runtime, call [method RenderingServer.screen_space_roughness_limiter_set_active] instead.
		</member>
		<member name="rendering/anti_aliasing/screen_space_roughness_limiter/limit" type="float" setter="" getter="" default="0.18">
			[b]Note:[/b] This property is only read when the project starts. To control the screen-space roughness limiter at runtime, call [method RenderingServer.screen_space_roughness_limiter_set_active] instead.
		</member>
		<member name="rendering/camera/depth_of_field/depth_of_field_bokeh_quality" type="int" setter="" getter="" default="1">
			Sets the quality of the depth of field effect. Higher quality takes more samples, which is slower but looks smoother.
		</member>
		<member name="rendering/camera/depth_of_field/depth_of_field_bokeh_shape" type="int" setter="" getter="" default="1">
			Sets the depth of field shape. Can be Box, Hexagon, or Circle. Box is the fastest. Circle is the most realistic, but also the most expensive to compute.
		</member>
		<member name="rendering/camera/depth_of_field/depth_of_field_use_jitter" type="bool" setter="" getter="" default="false">
			If [code]true[/code], jitters DOF samples to make effect slightly blurrier and hide lines created from low sample rates. This can result in a slightly grainy appearance when used with a low number of samples.
		</member>
		<member name="rendering/driver/depth_prepass/disable_for_vendors" type="String" setter="" getter="" default="&quot;PowerVR,Mali,Adreno,Apple&quot;">
			Disables [member rendering/driver/depth_prepass/enable] conditionally for certain vendors. By default, disables the depth prepass for mobile devices as mobile devices do not benefit from the depth prepass due to their unique architecture.
		</member>
		<member name="rendering/driver/depth_prepass/enable" type="bool" setter="" getter="" default="true">
			If [code]true[/code], performs a previous depth pass before rendering 3D materials. This increases performance significantly in scenes with high overdraw, when complex materials and lighting are used. However, in scenes with few occluded surfaces, the depth prepass may reduce performance. If your game is viewed from a fixed angle that makes it easy to avoid overdraw (such as top-down or side-scrolling perspective), consider disabling the depth prepass to improve performance. This setting can be changed at run-time to optimize performance depending on the scene currently being viewed.
			[b]Note:[/b] Depth prepass is only supported when using the Forward+ or Compatibility rendering method. When using the Mobile rendering method, there is no depth prepass performed.
		</member>
		<member name="rendering/driver/threads/thread_model" type="int" setter="" getter="" default="1" experimental="This setting has several known bugs which can lead to crashing, especially when using particles or resizing the window. Not recommended for use in production at this stage.">
			The thread model to use for rendering. Rendering on a thread may improve performance, but synchronizing to the main thread can cause a bit more jitter.
		</member>
		<member name="rendering/environment/defaults/default_clear_color" type="Color" setter="" getter="" default="Color(0.128, 0.128, 0.128, 1)">
			Default background clear color. Overridable per [Viewport] using its [Environment]. See [member Environment.background_mode] and [member Environment.background_color] in particular. To change this default color programmatically, use [method RenderingServer.set_default_clear_color].
		</member>
		<member name="rendering/environment/defaults/default_environment" type="String" setter="" getter="" default="&quot;&quot;">
			[Environment] that will be used as a fallback environment in case a scene does not specify its own environment. The default environment is loaded in at scene load time regardless of whether you have set an environment or not. If you do not rely on the fallback environment, you do not need to set this property.
		</member>
		<member name="rendering/environment/glow/upscale_mode" type="int" setter="" getter="" default="1">
			Sets how the glow effect is upscaled before being copied onto the screen. Linear is faster, but looks blocky. Bicubic is slower but looks smooth.
			[b]Note:[/b] [member rendering/environment/glow/upscale_mode] is only effective when using the Forward+ or Mobile rendering methods, as Compatibility uses a different glow implementation.
		</member>
		<member name="rendering/environment/glow/upscale_mode.mobile" type="int" setter="" getter="" default="0">
			Lower-end override for [member rendering/environment/glow/upscale_mode] on mobile devices, due to performance concerns or driver support.
		</member>
		<member name="rendering/environment/screen_space_reflection/roughness_quality" type="int" setter="" getter="" default="1">
			Sets the quality for rough screen-space reflections. Turning off will make all screen space reflections sharp, while higher values make rough reflections look better.
		</member>
		<member name="rendering/environment/ssao/adaptive_target" type="float" setter="" getter="" default="0.5">
			Quality target to use when [member rendering/environment/ssao/quality] is set to [code]Ultra[/code]. A value of [code]0.0[/code] provides a quality and speed similar to [code]Medium[/code] while a value of [code]1.0[/code] provides much higher quality than any of the other settings at the cost of performance.
		</member>
		<member name="rendering/environment/ssao/blur_passes" type="int" setter="" getter="" default="2">
			Number of blur passes to use when computing screen-space ambient occlusion. A higher number will result in a smoother look, but will be slower to compute and will have less high-frequency detail.
		</member>
		<member name="rendering/environment/ssao/fadeout_from" type="float" setter="" getter="" default="50.0">
			Distance at which the screen-space ambient occlusion effect starts to fade out. Use this hide ambient occlusion from far away.
		</member>
		<member name="rendering/environment/ssao/fadeout_to" type="float" setter="" getter="" default="300.0">
			Distance at which the screen-space ambient occlusion is fully faded out. Use this hide ambient occlusion from far away.
		</member>
		<member name="rendering/environment/ssao/half_size" type="bool" setter="" getter="" default="true">
			If [code]true[/code], screen-space ambient occlusion will be rendered at half size and then upscaled before being added to the scene. This is significantly faster but may miss small details. If [code]false[/code], screen-space ambient occlusion will be rendered at full size.
		</member>
		<member name="rendering/environment/ssao/quality" type="int" setter="" getter="" default="2">
			Sets the quality of the screen-space ambient occlusion effect. Higher values take more samples and so will result in better quality, at the cost of performance. Setting to [code]Ultra[/code] will use the [member rendering/environment/ssao/adaptive_target] setting.
		</member>
		<member name="rendering/environment/ssil/adaptive_target" type="float" setter="" getter="" default="0.5">
			Quality target to use when [member rendering/environment/ssil/quality] is set to [code]Ultra[/code]. A value of [code]0.0[/code] provides a quality and speed similar to [code]Medium[/code] while a value of [code]1.0[/code] provides much higher quality than any of the other settings at the cost of performance. When using the adaptive target, the performance cost scales with the complexity of the scene.
		</member>
		<member name="rendering/environment/ssil/blur_passes" type="int" setter="" getter="" default="4">
			Number of blur passes to use when computing screen-space indirect lighting. A higher number will result in a smoother look, but will be slower to compute and will have less high-frequency detail.
		</member>
		<member name="rendering/environment/ssil/fadeout_from" type="float" setter="" getter="" default="50.0">
			Distance at which the screen-space indirect lighting effect starts to fade out. Use this to hide screen-space indirect lighting from far away.
		</member>
		<member name="rendering/environment/ssil/fadeout_to" type="float" setter="" getter="" default="300.0">
			Distance at which the screen-space indirect lighting is fully faded out. Use this to hide screen-space indirect lighting from far away.
		</member>
		<member name="rendering/environment/ssil/half_size" type="bool" setter="" getter="" default="true">
			If [code]true[/code], screen-space indirect lighting will be rendered at half size and then upscaled before being added to the scene. This is significantly faster but may miss small details and may result in some objects appearing to glow at their edges.
		</member>
		<member name="rendering/environment/ssil/quality" type="int" setter="" getter="" default="2">
			Sets the quality of the screen-space indirect lighting effect. Higher values take more samples and so will result in better quality, at the cost of performance. Setting to [code]Ultra[/code] will use the [member rendering/environment/ssil/adaptive_target] setting.
		</member>
		<member name="rendering/environment/subsurface_scattering/subsurface_scattering_depth_scale" type="float" setter="" getter="" default="0.01">
			Scales the depth over which the subsurface scattering effect is applied. A high value may allow light to scatter into a part of the mesh or another mesh that is close in screen space but far in depth. See also [member rendering/environment/subsurface_scattering/subsurface_scattering_scale].
			[b]Note:[/b] This property is only read when the project starts. To set the subsurface scattering depth scale at runtime, call [method RenderingServer.sub_surface_scattering_set_scale] instead.
		</member>
		<member name="rendering/environment/subsurface_scattering/subsurface_scattering_quality" type="int" setter="" getter="" default="1">
			Sets the quality of the subsurface scattering effect. Higher values are slower but look nicer. This affects the rendering of materials that have [member BaseMaterial3D.subsurf_scatter_enabled] set to [code]true[/code], along with [ShaderMaterial]s that set [code]SSS_STRENGTH[/code].
			[b]Note:[/b] This property is only read when the project starts. To set the subsurface scattering quality at runtime, call [method RenderingServer.sub_surface_scattering_set_quality] instead.
		</member>
		<member name="rendering/environment/subsurface_scattering/subsurface_scattering_scale" type="float" setter="" getter="" default="0.05">
			Scales the distance over which samples are taken for subsurface scattering effect. Changing this does not impact performance, but higher values will result in significant artifacts as the samples will become obviously spread out. A lower value results in a smaller spread of scattered light. See also [member rendering/environment/subsurface_scattering/subsurface_scattering_depth_scale].
			[b]Note:[/b] This property is only read when the project starts. To set the subsurface scattering scale at runtime, call [method RenderingServer.sub_surface_scattering_set_scale] instead.
		</member>
		<member name="rendering/environment/volumetric_fog/use_filter" type="int" setter="" getter="" default="1">
			Enables filtering of the volumetric fog effect prior to integration. This substantially blurs the fog which reduces fine details but also smooths out harsh edges and aliasing artifacts. Disable when more detail is required.
		</member>
		<member name="rendering/environment/volumetric_fog/volume_depth" type="int" setter="" getter="" default="64">
			Number of slices to use along the depth of the froxel buffer for volumetric fog. A lower number will be more efficient but may result in artifacts appearing during camera movement. See also [member Environment.volumetric_fog_length].
		</member>
		<member name="rendering/environment/volumetric_fog/volume_size" type="int" setter="" getter="" default="64">
			Base size used to determine size of froxel buffer in the camera X-axis and Y-axis. The final size is scaled by the aspect ratio of the screen, so actual values may differ from what is set. Set a larger size for more detailed fog, set a smaller size for better performance.
		</member>
		<member name="rendering/gl_compatibility/driver" type="String" setter="" getter="" default="&quot;opengl3&quot;">
			Sets the driver to be used by the renderer when using the Compatibility renderer. Editing this property has no effect in the default configuration, as first-party platforms each have platform-specific overrides. Use those overrides to configure the driver for each platform.
			This can be overridden using the [code]--rendering-driver &lt;driver&gt;[/code] command line argument.
			Supported values are:
			- [code]opengl3[/code], OpenGL 3.3 on desktop platforms, OpenGL ES 3.0 on mobile platforms, WebGL 2.0 on web.
			- [code]opengl3_angle[/code], OpenGL ES 3.0 using the ANGLE compatibility layer, supported on macOS (over native OpenGL) and Windows (over Direct3D 11).
			- [code]opengl3_es[/code], OpenGL ES 3.0 on Linux/BSD.
			[b]Note:[/b] The availability of these options depends on whether the engine was compiled with support for them (determined by SCons options [code]opengl3[/code] and [code]angle_libs[/code]).
			[b]Note:[/b] The actual rendering driver may be automatically changed by the engine as a result of a fallback, or a user-specified command line argument. To get the actual rendering driver that is used at runtime, use [method RenderingServer.get_current_rendering_driver_name] instead of reading this project setting's value.
		</member>
		<member name="rendering/gl_compatibility/driver.android" type="String" setter="" getter="" default="&quot;opengl3&quot;">
			Android override for [member rendering/gl_compatibility/driver].
			Only one option is supported:
			- [code]opengl3[/code], OpenGL ES 3.0 from native drivers.
		</member>
		<member name="rendering/gl_compatibility/driver.ios" type="String" setter="" getter="" default="&quot;opengl3&quot;">
			iOS override for [member rendering/gl_compatibility/driver].
			Only one option is supported:
			- [code]opengl3[/code], OpenGL ES 3.0 from native drivers.
		</member>
		<member name="rendering/gl_compatibility/driver.linuxbsd" type="String" setter="" getter="" default="&quot;opengl3&quot;">
			LinuxBSD override for [member rendering/gl_compatibility/driver].
			Two options are supported:
			- [code]opengl3[/code] (default), OpenGL 3.3 from native drivers.
			- [code]opengl3_es[/code], OpenGL ES 3.0 from native drivers. If [member rendering/gl_compatibility/fallback_to_gles] is enabled, this is used as a fallback if OpenGL 3.3 is not supported.
		</member>
		<member name="rendering/gl_compatibility/driver.macos" type="String" setter="" getter="" default="&quot;opengl3&quot;">
			macOS override for [member rendering/gl_compatibility/driver].
			Two options are supported:
			- [code]opengl3[/code] (default), OpenGL 3.3 from native drivers. If [member rendering/gl_compatibility/fallback_to_native] is enabled, this is used as a fallback if ANGLE is configured as the preferred driver but not supported.
			- [code]opengl3_angle[/code], OpenGL ES 3.0 using the ANGLE compatibility layer over native OpenGL drivers. If [member rendering/gl_compatibility/fallback_to_angle] is enabled, this is used as a fallback if OpenGL 3.3 is not supported.
		</member>
		<member name="rendering/gl_compatibility/driver.web" type="String" setter="" getter="" default="&quot;opengl3&quot;">
			Web override for [member rendering/gl_compatibility/driver].
			Only one option is supported:
			- [code]opengl3[/code], WebGL 2.0. The underlying native API depends on the target OS, browser, and browser configuration.
		</member>
		<member name="rendering/gl_compatibility/driver.windows" type="String" setter="" getter="" default="&quot;opengl3&quot;">
			Windows override for [member rendering/gl_compatibility/driver].
			Two options are supported:
			- [code]opengl3[/code] (default), OpenGL 3.3 from native drivers. If [member rendering/gl_compatibility/fallback_to_native] is enabled, this is used as a fallback if ANGLE is configured as the preferred driver but not supported.
			- [code]opengl3_angle[/code], OpenGL ES 3.0 using the ANGLE compatibility layer over native Direct3D 11 drivers. If [member rendering/gl_compatibility/fallback_to_angle] is enabled, this is used as a fallback if OpenGL 3.3 is not supported. By default, ANGLE is used as the default driver for some devices listed in [member rendering/gl_compatibility/force_angle_on_devices].
		</member>
		<member name="rendering/gl_compatibility/fallback_to_angle" type="bool" setter="" getter="" default="true">
			If [code]true[/code], the compatibility renderer will fall back to ANGLE if native OpenGL is not supported or the device is listed in [member rendering/gl_compatibility/force_angle_on_devices].
			[b]Note:[/b] This setting is implemented only on Windows.
		</member>
		<member name="rendering/gl_compatibility/fallback_to_gles" type="bool" setter="" getter="" default="true">
			If [code]true[/code], the compatibility renderer will fall back to OpenGLES if desktop OpenGL is not supported.
			[b]Note:[/b] This setting is implemented only on Linux/X11.
		</member>
		<member name="rendering/gl_compatibility/fallback_to_native" type="bool" setter="" getter="" default="true">
			If [code]true[/code], the compatibility renderer will fall back to native OpenGL if ANGLE is not supported, or ANGLE dynamic libraries aren't found.
			[b]Note:[/b] This setting is implemented on macOS and Windows.
		</member>
		<member name="rendering/gl_compatibility/force_angle_on_devices" type="Array" setter="" getter="">
			An [Array] of devices which should always use the ANGLE renderer.
			Each entry is a [Dictionary] with the following keys: [code]vendor[/code] and [code]name[/code]. [code]name[/code] can be set to [code]*[/code] to add all devices with the specified [code]vendor[/code].
			[b]Note:[/b] This setting is implemented only on Windows.
		</member>
		<member name="rendering/gl_compatibility/item_buffer_size" type="int" setter="" getter="" default="16384">
			Maximum number of canvas items commands that can be drawn in a single viewport update. If more render commands are issued they will be ignored. Decreasing this limit may improve performance on bandwidth limited devices. Increase this limit if you find that not all objects are being drawn in a frame.
		</member>
		<member name="rendering/gl_compatibility/nvidia_disable_threaded_optimization" type="bool" setter="" getter="" default="true">
			If [code]true[/code], disables the threaded optimization feature from the NVIDIA drivers, which are known to cause stuttering in most OpenGL applications.
			[b]Note:[/b] This setting only works on Windows, as threaded optimization is disabled by default on other platforms.
		</member>
		<member name="rendering/global_illumination/gi/use_half_resolution" type="bool" setter="" getter="" default="false">
			If [code]true[/code], renders [VoxelGI] and SDFGI ([member Environment.sdfgi_enabled]) buffers at halved resolution (e.g. 960×540 when the viewport size is 1920×1080). This improves performance significantly when VoxelGI or SDFGI is enabled, at the cost of artifacts that may be visible on polygon edges. The loss in quality becomes less noticeable as the viewport resolution increases. [LightmapGI] rendering is not affected by this setting.
			[b]Note:[/b] This property is only read when the project starts. To set half-resolution GI at run-time, call [method RenderingServer.gi_set_use_half_resolution] instead.
		</member>
		<member name="rendering/global_illumination/sdfgi/frames_to_converge" type="int" setter="" getter="" default="5">
			The number of frames to use for converging signed distance field global illumination. Higher values lead to a less noisy result, at the cost of taking a longer time to fully converge. This means the scene's global illumination will be too dark for a longer period of time, especially when the camera moves fast. The actual convergence speed depends on rendered framerate. For example, with the default setting of 30 frames, rendering at 60 FPS will make SDFGI fully converge after 0.5 seconds. See also [member rendering/global_illumination/sdfgi/frames_to_update_lights] and [member rendering/global_illumination/sdfgi/probe_ray_count].
			[b]Note:[/b] This property is only read when the project starts. To control SDFGI convergence speed at runtime, call [method RenderingServer.environment_set_sdfgi_frames_to_converge] instead.
		</member>
		<member name="rendering/global_illumination/sdfgi/frames_to_update_lights" type="int" setter="" getter="" default="2">
			The number of frames over which dynamic lights should be updated in signed distance field global illumination. Higher values take more time to update indirect lighting coming from dynamic lights, but result in better performance when many dynamic lights are present. See also [member rendering/global_illumination/sdfgi/frames_to_converge] and [member rendering/global_illumination/sdfgi/probe_ray_count].
			[b]Note:[/b] This only affects [Light3D] nodes whose [member Light3D.light_bake_mode] is [constant Light3D.BAKE_DYNAMIC] (which is the default). Consider making non-moving lights use the [constant Light3D.BAKE_STATIC] bake mode to improve performance.
			[b]Note:[/b] This property is only read when the project starts. To control SDFGI light update speed at runtime, call [method RenderingServer.environment_set_sdfgi_frames_to_update_light] instead.
		</member>
		<member name="rendering/global_illumination/sdfgi/probe_ray_count" type="int" setter="" getter="" default="1">
			The number of rays to throw per frame when computing signed distance field global illumination. Higher values lead to a less noisy result, at the cost of performance. See also [member rendering/global_illumination/sdfgi/frames_to_converge] and [member rendering/global_illumination/sdfgi/frames_to_update_lights].
			[b]Note:[/b] This property is only read when the project starts. To control SDFGI quality at runtime, call [method RenderingServer.environment_set_sdfgi_ray_count] instead.
		</member>
		<member name="rendering/global_illumination/voxel_gi/quality" type="int" setter="" getter="" default="0">
			The VoxelGI quality to use. High quality leads to more precise lighting and better reflections, but is slower to render. This setting does not affect the baked data and doesn't require baking the [VoxelGI] again to apply.
			[b]Note:[/b] This property is only read when the project starts. To control VoxelGI quality at runtime, call [method RenderingServer.voxel_gi_set_quality] instead.
		</member>
		<member name="rendering/lightmapping/bake_performance/max_rays_per_pass" type="int" setter="" getter="" default="4">
			The maximum number of rays that can be thrown per pass when baking lightmaps with [LightmapGI]. Depending on the scene, adjusting this value may result in higher GPU utilization when baking lightmaps, leading to faster bake times.
			[b]Note:[/b] Using a value that is too high for your system can cause crashes due to the GPU being unresponsive for long periods of time, and the graphics driver being reset by the OS.
		</member>
		<member name="rendering/lightmapping/bake_performance/max_rays_per_probe_pass" type="int" setter="" getter="" default="64">
			The maximum number of rays that can be thrown per pass when baking dynamic object lighting in [LightmapProbe]s with [LightmapGI]. Depending on the scene, adjusting this value may result in higher GPU utilization when baking lightmaps, leading to faster bake times.
			[b]Note:[/b] Using a value that is too high for your system can cause crashes due to the GPU being unresponsive for long periods of time, and the graphics driver being reset by the OS.
		</member>
		<member name="rendering/lightmapping/bake_performance/max_transparency_rays" type="int" setter="" getter="" default="8">
			The maximum number of retry rays that can be thrown per pass when hitting a transparent surface when baking lightmaps with [LightmapGI]. Depending on the scene, reducing this value may lead to faster bake times.
			[b]Note:[/b] Using a value that is too high for your system can cause crashes due to the GPU being unresponsive for long periods of time, and the graphics driver being reset by the OS.
		</member>
		<member name="rendering/lightmapping/bake_performance/region_size" type="int" setter="" getter="" default="512">
			The region size to use when baking lightmaps with [LightmapGI]. The specified value is rounded up to the nearest power of 2.
			[b]Note:[/b] Using a value that is too high for your system can cause crashes due to the GPU being unresponsive for long periods of time, and the graphics driver being reset by the OS.
		</member>
		<member name="rendering/lightmapping/bake_quality/high_quality_probe_ray_count" type="int" setter="" getter="" default="512">
			The number of rays to use for baking dynamic object lighting in [LightmapProbe]s when [member LightmapGI.quality] is [constant LightmapGI.BAKE_QUALITY_HIGH].
		</member>
		<member name="rendering/lightmapping/bake_quality/high_quality_ray_count" type="int" setter="" getter="" default="512">
			The number of rays to use for baking lightmaps with [LightmapGI] when [member LightmapGI.quality] is [constant LightmapGI.BAKE_QUALITY_HIGH].
		</member>
		<member name="rendering/lightmapping/bake_quality/low_quality_probe_ray_count" type="int" setter="" getter="" default="64">
			The number of rays to use for baking dynamic object lighting in [LightmapProbe]s when [member LightmapGI.quality] is [constant LightmapGI.BAKE_QUALITY_LOW].
		</member>
		<member name="rendering/lightmapping/bake_quality/low_quality_ray_count" type="int" setter="" getter="" default="32">
			The number of rays to use for baking lightmaps with [LightmapGI] when [member LightmapGI.quality] is [constant LightmapGI.BAKE_QUALITY_LOW].
		</member>
		<member name="rendering/lightmapping/bake_quality/medium_quality_probe_ray_count" type="int" setter="" getter="" default="256">
			The number of rays to use for baking dynamic object lighting in [LightmapProbe]s when [member LightmapGI.quality] is [constant LightmapGI.BAKE_QUALITY_MEDIUM].
		</member>
		<member name="rendering/lightmapping/bake_quality/medium_quality_ray_count" type="int" setter="" getter="" default="128">
			The number of rays to use for baking lightmaps with [LightmapGI] when [member LightmapGI.quality] is [constant LightmapGI.BAKE_QUALITY_MEDIUM].
		</member>
		<member name="rendering/lightmapping/bake_quality/ultra_quality_probe_ray_count" type="int" setter="" getter="" default="2048">
			The number of rays to use for baking dynamic object lighting in [LightmapProbe]s when [member LightmapGI.quality] is [constant LightmapGI.BAKE_QUALITY_ULTRA].
		</member>
		<member name="rendering/lightmapping/bake_quality/ultra_quality_ray_count" type="int" setter="" getter="" default="2048">
			The number of rays to use for baking lightmaps with [LightmapGI] when [member LightmapGI.quality] is [constant LightmapGI.BAKE_QUALITY_ULTRA].
		</member>
		<member name="rendering/lightmapping/denoising/denoiser" type="int" setter="" getter="" default="0">
			Denoiser tool used for denoising lightmaps.
			Using [url=https://www.openimagedenoise.org/]OpenImageDenoise[/url] (OIDN) requires configuring a path to an OIDN executable in the editor settings at [member EditorSettings.filesystem/tools/oidn/oidn_denoise_path]. OIDN can be downloaded from [url=https://www.openimagedenoise.org/downloads.html]OpenImageDenoise's downloads page[/url].
			OIDN will use GPU acceleration when available. Unlike JNLM which uses compute shaders for acceleration, OIDN uses vendor-specific acceleration methods. For GPU acceleration to be available, the following libraries must be installed on the system depending on your GPU:
			- NVIDIA GPUs: CUDA libraries
			- AMD GPUs: HIP libraries
			- Intel GPUs: SYCL libraries
			If no GPU acceleration is configured on the system, multi-threaded CPU-based denoising will be performed instead. This CPU-based denoising is significantly slower than the JNLM denoiser in most cases.
		</member>
		<member name="rendering/lightmapping/lightmap_gi/use_bicubic_filter" type="bool" setter="" getter="" default="true">
			If [code]true[/code], applies a bicubic filter during lightmap sampling. This makes lightmaps look much smoother, at a moderate performance cost.
			[b]Note:[/b] The bicubic filter exaggerates the 'bleeding' effect that occurs when a lightmap's resolution is low enough.
		</member>
		<member name="rendering/lightmapping/primitive_meshes/texel_size" type="float" setter="" getter="" default="0.2">
			The texel_size that is used to calculate the [member Mesh.lightmap_size_hint] on [PrimitiveMesh] resources if [member PrimitiveMesh.add_uv2] is enabled.
		</member>
		<member name="rendering/lightmapping/probe_capture/update_speed" type="float" setter="" getter="" default="15">
			The framerate-independent update speed when representing dynamic object lighting from [LightmapProbe]s. Higher values make dynamic object lighting update faster. Higher values can prevent fast-moving objects from having "outdated" indirect lighting displayed on them, at the cost of possible flickering when an object moves from a bright area to a shaded area.
		</member>
		<member name="rendering/lights_and_shadows/directional_shadow/16_bits" type="bool" setter="" getter="" default="true">
			Use 16 bits for the directional shadow depth map. Enabling this results in shadows having less precision and may result in shadow acne, but can lead to performance improvements on some devices.
		</member>
		<member name="rendering/lights_and_shadows/directional_shadow/size" type="int" setter="" getter="" default="4096">
			The directional shadow's size in pixels. Higher values will result in sharper shadows, at the cost of performance. The value is rounded up to the nearest power of 2.
		</member>
		<member name="rendering/lights_and_shadows/directional_shadow/size.mobile" type="int" setter="" getter="" default="2048">
			Lower-end override for [member rendering/lights_and_shadows/directional_shadow/size] on mobile devices, due to performance concerns or driver support.
		</member>
		<member name="rendering/lights_and_shadows/directional_shadow/soft_shadow_filter_quality" type="int" setter="" getter="" default="2">
			Quality setting for shadows cast by [DirectionalLight3D]s. Higher quality settings use more samples when reading from shadow maps and are thus slower. Low quality settings may result in shadows looking grainy.
			[b]Note:[/b] The Soft Very Low setting will automatically multiply [i]constant[/i] shadow blur by 0.75x to reduce the amount of noise visible. This automatic blur change only affects the constant blur factor defined in [member Light3D.shadow_blur], not the variable blur performed by [DirectionalLight3D]s' [member Light3D.light_angular_distance].
			[b]Note:[/b] The Soft High and Soft Ultra settings will automatically multiply [i]constant[/i] shadow blur by 1.5× and 2× respectively to make better use of the increased sample count. This increased blur also improves stability of dynamic object shadows.
		</member>
		<member name="rendering/lights_and_shadows/directional_shadow/soft_shadow_filter_quality.mobile" type="int" setter="" getter="" default="0">
			Lower-end override for [member rendering/lights_and_shadows/directional_shadow/soft_shadow_filter_quality] on mobile devices, due to performance concerns or driver support.
		</member>
		<member name="rendering/lights_and_shadows/positional_shadow/atlas_16_bits" type="bool" setter="" getter="" default="true">
			Use 16 bits for the omni/spot shadow depth map. Enabling this results in shadows having less precision and may result in shadow acne, but can lead to performance improvements on some devices.
		</member>
		<member name="rendering/lights_and_shadows/positional_shadow/atlas_quadrant_0_subdiv" type="int" setter="" getter="" default="2">
			Subdivision quadrant size for shadow mapping. See shadow mapping documentation.
		</member>
		<member name="rendering/lights_and_shadows/positional_shadow/atlas_quadrant_1_subdiv" type="int" setter="" getter="" default="2">
			Subdivision quadrant size for shadow mapping. See shadow mapping documentation.
		</member>
		<member name="rendering/lights_and_shadows/positional_shadow/atlas_quadrant_2_subdiv" type="int" setter="" getter="" default="3">
			Subdivision quadrant size for shadow mapping. See shadow mapping documentation.
		</member>
		<member name="rendering/lights_and_shadows/positional_shadow/atlas_quadrant_3_subdiv" type="int" setter="" getter="" default="4">
			Subdivision quadrant size for shadow mapping. See shadow mapping documentation.
		</member>
		<member name="rendering/lights_and_shadows/positional_shadow/atlas_size" type="int" setter="" getter="" default="4096">
			Size for shadow atlas (used for OmniLights and SpotLights). See documentation.
		</member>
		<member name="rendering/lights_and_shadows/positional_shadow/atlas_size.mobile" type="int" setter="" getter="" default="2048">
			Lower-end override for [member rendering/lights_and_shadows/positional_shadow/atlas_size] on mobile devices, due to performance concerns or driver support.
		</member>
		<member name="rendering/lights_and_shadows/positional_shadow/soft_shadow_filter_quality" type="int" setter="" getter="" default="2">
			Quality setting for shadows cast by [OmniLight3D]s and [SpotLight3D]s. Higher quality settings use more samples when reading from shadow maps and are thus slower. Low quality settings may result in shadows looking grainy.
			[b]Note:[/b] The Soft Very Low setting will automatically multiply [i]constant[/i] shadow blur by 0.75x to reduce the amount of noise visible. This automatic blur change only affects the constant blur factor defined in [member Light3D.shadow_blur], not the variable blur performed by [DirectionalLight3D]s' [member Light3D.light_angular_distance].
			[b]Note:[/b] The Soft High and Soft Ultra settings will automatically multiply shadow blur by 1.5× and 2× respectively to make better use of the increased sample count. This increased blur also improves stability of dynamic object shadows.
		</member>
		<member name="rendering/lights_and_shadows/positional_shadow/soft_shadow_filter_quality.mobile" type="int" setter="" getter="" default="0">
			Lower-end override for [member rendering/lights_and_shadows/positional_shadow/soft_shadow_filter_quality] on mobile devices, due to performance concerns or driver support.
		</member>
		<member name="rendering/lights_and_shadows/tighter_shadow_caster_culling" type="bool" setter="" getter="" default="true">
			If [code]true[/code], items that cannot cast shadows into the view frustum will not be rendered into shadow maps.
			This can increase performance.
		</member>
		<member name="rendering/lights_and_shadows/use_physical_light_units" type="bool" setter="" getter="" default="false">
			Enables the use of physically based units for light sources. Physically based units tend to be much larger than the arbitrary units used by Redot, but they can be used to match lighting within Redot to real-world lighting. Due to the large dynamic range of lighting conditions present in nature, Redot bakes exposure into the various lighting quantities before rendering. Most light sources bake exposure automatically at run time based on the active [CameraAttributes] resource, but [LightmapGI] and [VoxelGI] require a [CameraAttributes] resource to be set at bake time to reduce the dynamic range. At run time, Redot will automatically reconcile the baked exposure with the active exposure to ensure lighting remains consistent.
		</member>
		<member name="rendering/limits/cluster_builder/max_clustered_elements" type="float" setter="" getter="" default="512">
			The maximum number of clustered elements ([OmniLight3D] + [SpotLight3D] + [Decal] + [ReflectionProbe]) that can be rendered at once in the camera view. If there are more clustered elements present in the camera view, some of them will not be rendered (leading to pop-in during camera movement). Enabling distance fade on lights and decals ([member Light3D.distance_fade_enabled], [member Decal.distance_fade_enabled]) can help avoid reaching this limit.
			Decreasing this value may improve GPU performance on certain setups, even if the maximum number of clustered elements is never reached in the project.
			[b]Note:[/b] This setting is only effective when using the Forward+ rendering method, not Mobile and Compatibility.
		</member>
		<member name="rendering/limits/global_shader_variables/buffer_size" type="int" setter="" getter="" default="65536">
			The maximum number of uniforms that can be used by the global shader uniform buffer. Each item takes up one slot. In other words, a single uniform float and a uniform vec4 will take the same amount of space in the buffer.
			[b]Note:[/b] When using the Compatibility renderer, most mobile devices (and all web exports) will be limited to a maximum size of 1024 due to hardware constraints.
		</member>
		<member name="rendering/limits/opengl/max_lights_per_object" type="int" setter="" getter="" default="8">
			Max number of omnilights and spotlights renderable per object. At the default value of 8, this means that each surface can be affected by up to 8 omnilights and 8 spotlights. This is further limited by hardware support and [member rendering/limits/opengl/max_renderable_lights]. Setting this low will slightly reduce memory usage, may decrease shader compile times, and may result in faster rendering on low-end, mobile, or web devices.
			[b]Note:[/b] This setting is only effective when using the Compatibility rendering method, not Forward+ and Mobile.
		</member>
		<member name="rendering/limits/opengl/max_renderable_elements" type="int" setter="" getter="" default="65536">
			Max number of elements renderable in a frame. If more elements than this are visible per frame, they will not be drawn. Keep in mind elements refer to mesh surfaces and not meshes themselves. Setting this low will slightly reduce memory usage and may decrease shader compile times, particularly on web. For most uses, the default value is suitable, but consider lowering as much as possible on web export.
			[b]Note:[/b] This setting is only effective when using the Compatibility rendering method, not Forward+ and Mobile.
		</member>
		<member name="rendering/limits/opengl/max_renderable_lights" type="int" setter="" getter="" default="32">
			Max number of positional lights renderable in a frame. If more lights than this number are used, they will be ignored. Setting this low will slightly reduce memory usage and may decrease shader compile times, particularly on web. For most uses, the default value is suitable, but consider lowering as much as possible on web export.
			[b]Note:[/b] This setting is only effective when using the Compatibility rendering method, not Forward+ and Mobile.
		</member>
		<member name="rendering/limits/spatial_indexer/threaded_cull_minimum_instances" type="int" setter="" getter="" default="1000">
			The minimum number of instances that must be present in a scene to enable culling computations on multiple threads. If a scene has fewer instances than this number, culling is done on a single thread.
		</member>
		<member name="rendering/limits/spatial_indexer/update_iterations_per_frame" type="int" setter="" getter="" default="10">
		</member>
		<member name="rendering/limits/time/time_rollover_secs" type="float" setter="" getter="" default="3600">
			Maximum time (in seconds) before the [code]TIME[/code] shader built-in variable rolls over. The [code]TIME[/code] variable increments by [code]delta[/code] each frame, and when it exceeds this value, it rolls over to [code]0.0[/code]. Since large floating-point values are less precise than small floating-point values, this should be set as low as possible to maximize the precision of the [code]TIME[/code] built-in variable in shaders. This is especially important on mobile platforms where precision in shaders is significantly reduced. However, if this is set too low, shader animations may appear to restart from the beginning while the project is running.
			On desktop platforms, values below [code]4096[/code] are recommended, ideally below [code]2048[/code]. On mobile platforms, values below [code]64[/code] are recommended, ideally below [code]32[/code].
		</member>
		<member name="rendering/mesh_lod/lod_change/threshold_pixels" type="float" setter="" getter="" default="1.0">
			The automatic LOD bias to use for meshes rendered within the [ReflectionProbe]. Higher values will use less detailed versions of meshes that have LOD variations generated. If set to [code]0.0[/code], automatic LOD is disabled. Increase [member rendering/mesh_lod/lod_change/threshold_pixels] to improve performance at the cost of geometry detail.
			[b]Note:[/b] [member rendering/mesh_lod/lod_change/threshold_pixels] does not affect [GeometryInstance3D] visibility ranges (also known as "manual" LOD or hierarchical LOD).
			[b]Note:[/b] This property is only read when the project starts. To adjust the automatic LOD threshold at runtime, set [member Viewport.mesh_lod_threshold] on the root [Viewport].
		</member>
		<member name="rendering/occlusion_culling/bvh_build_quality" type="int" setter="" getter="" default="2">
			The [url=https://en.wikipedia.org/wiki/Bounding_volume_hierarchy]Bounding Volume Hierarchy[/url] quality to use when rendering the occlusion culling buffer. Higher values will result in more accurate occlusion culling, at the cost of higher CPU usage. See also [member rendering/occlusion_culling/occlusion_rays_per_thread].
			[b]Note:[/b] This property is only read when the project starts. To adjust the BVH build quality at runtime, use [method RenderingServer.viewport_set_occlusion_culling_build_quality].
		</member>
		<member name="rendering/occlusion_culling/jitter_projection" type="bool" setter="" getter="" default="true">
			If [code]true[/code], the projection used for rendering the occlusion buffer will be jittered. This can help prevent objects being incorrectly culled when visible through small gaps.
		</member>
		<member name="rendering/occlusion_culling/occlusion_rays_per_thread" type="int" setter="" getter="" default="512">
			The number of occlusion rays traced per CPU thread. Higher values will result in more accurate occlusion culling, at the cost of higher CPU usage. The occlusion culling buffer's pixel count is roughly equal to [code]occlusion_rays_per_thread * number_of_logical_cpu_cores[/code], so it will depend on the system's CPU. Therefore, CPUs with fewer cores will use a lower resolution to attempt keeping performance costs even across devices. See also [member rendering/occlusion_culling/bvh_build_quality].
			[b]Note:[/b] This property is only read when the project starts. To adjust the number of occlusion rays traced per thread at runtime, use [method RenderingServer.viewport_set_occlusion_rays_per_thread].
		</member>
		<member name="rendering/occlusion_culling/use_occlusion_culling" type="bool" setter="" getter="" default="false">
			If [code]true[/code], [OccluderInstance3D] nodes will be usable for occlusion culling in 3D in the root viewport. In custom viewports, [member Viewport.use_occlusion_culling] must be set to [code]true[/code] instead.
			[b]Note:[/b] Enabling occlusion culling has a cost on the CPU. Only enable occlusion culling if you actually plan to use it. Large open scenes with few or no objects blocking the view will generally not benefit much from occlusion culling. Large open scenes generally benefit more from mesh LOD and visibility ranges ([member GeometryInstance3D.visibility_range_begin] and [member GeometryInstance3D.visibility_range_end]) compared to occlusion culling.
			[b]Note:[/b] Due to memory constraints, occlusion culling is not supported by default in Web export templates. It can be enabled by compiling custom Web export templates with [code]module_raycast_enabled=yes[/code].
		</member>
		<member name="rendering/reflections/reflection_atlas/reflection_count" type="int" setter="" getter="" default="64">
			Number of cubemaps to store in the reflection atlas. The number of [ReflectionProbe]s in a scene will be limited by this amount. A higher number requires more VRAM.
		</member>
		<member name="rendering/reflections/reflection_atlas/reflection_size" type="int" setter="" getter="" default="256">
			Size of cubemap faces for [ReflectionProbe]s. A higher number requires more VRAM and may make reflection probe updating slower.
		</member>
		<member name="rendering/reflections/reflection_atlas/reflection_size.mobile" type="int" setter="" getter="" default="128">
			Lower-end override for [member rendering/reflections/reflection_atlas/reflection_size] on mobile devices, due to performance concerns or driver support.
		</member>
		<member name="rendering/reflections/sky_reflections/fast_filter_high_quality" type="bool" setter="" getter="" default="false">
			Use a higher quality variant of the fast filtering algorithm. Significantly slower than using default quality, but results in smoother reflections. Should only be used when the scene is especially detailed.
		</member>
		<member name="rendering/reflections/sky_reflections/ggx_samples" type="int" setter="" getter="" default="32">
			Sets the number of samples to take when using importance sampling for [Sky]s and [ReflectionProbe]s. A higher value will result in smoother, higher quality reflections, but increases time to calculate radiance maps. In general, fewer samples are needed for simpler, low dynamic range environments while more samples are needed for HDR environments and environments with a high level of detail.
		</member>
		<member name="rendering/reflections/sky_reflections/ggx_samples.mobile" type="int" setter="" getter="" default="16">
			Lower-end override for [member rendering/reflections/sky_reflections/ggx_samples] on mobile devices, due to performance concerns or driver support.
		</member>
		<member name="rendering/reflections/sky_reflections/roughness_layers" type="int" setter="" getter="" default="8">
			Limits the number of layers to use in radiance maps when using importance sampling. A lower number will be slightly faster and take up less VRAM.
		</member>
		<member name="rendering/reflections/sky_reflections/texture_array_reflections" type="bool" setter="" getter="" default="true">
			If [code]true[/code], uses texture arrays instead of mipmaps for reflection probes and panorama backgrounds (sky). This reduces jitter noise and upscaling artifacts on reflections, but is significantly slower to compute and uses [member rendering/reflections/sky_reflections/roughness_layers] times more memory.
			[b]Note:[/b] Texture array reflections are always disabled on macOS on Intel GPUs due to driver bugs.
		</member>
		<member name="rendering/reflections/sky_reflections/texture_array_reflections.mobile" type="bool" setter="" getter="" default="false">
			Lower-end override for [member rendering/reflections/sky_reflections/texture_array_reflections] on mobile devices, due to performance concerns or driver support.
		</member>
		<member name="rendering/renderer/rendering_method" type="String" setter="" getter="" default="&quot;forward_plus&quot;">
			Sets the renderer that will be used by the project. Options are:
			[b]forward_plus[/b] (Forward+): High-end renderer designed for desktop devices. Has a higher base overhead, but scales well with complex scenes. Not suitable for older devices or mobile.
			[b]mobile[/b] (Mobile): Modern renderer designed for mobile devices. Has a lower base overhead than Forward+, but does not scale as well to large scenes with many elements.
			[b]gl_compatibility[/b] (Compatibility): Low-end renderer designed for older devices. Based on the limitations of the OpenGL 3.3 / OpenGL ES 3.0 / WebGL 2 APIs.
			This can be overridden using the [code]--rendering-method &lt;method&gt;[/code] command line argument.
			[b]Note:[/b] The actual rendering method may be automatically changed by the engine as a result of a fallback, or a user-specified command line argument. To get the actual rendering method that is used at runtime, use [method RenderingServer.get_current_rendering_method] instead of reading this project setting's value.
		</member>
		<member name="rendering/renderer/rendering_method.mobile" type="String" setter="" getter="" default="&quot;mobile&quot;">
			Override for [member rendering/renderer/rendering_method] on mobile devices.
		</member>
		<member name="rendering/renderer/rendering_method.web" type="String" setter="" getter="" default="&quot;gl_compatibility&quot;">
			Override for [member rendering/renderer/rendering_method] on web.
		</member>
		<member name="rendering/rendering_device/d3d12/agility_sdk_version" type="int" setter="" getter="" default="613">
			Version code of the [url=https://devblogs.microsoft.com/directx/directx12agility/]Direct3D 12 Agility SDK[/url] to use ([code]D3D12SDKVersion[/code]). This must match the [i]minor[/i] version that is installed next to the editor binary and in the export templates directory for the current editor version. For example, if you have [code]1.613.3[/code] installed, you need to input [code]613[/code] here.
		</member>
		<member name="rendering/rendering_device/d3d12/max_misc_descriptors_per_frame" type="int" setter="" getter="" default="512">
			The number of entries in the miscellaneous descriptors heap the Direct3D 12 rendering driver uses each frame, used for various operations like clearing a texture.
			Depending on the complexity of scenes, this value may be lowered or may need to be raised.
		</member>
		<member name="rendering/rendering_device/d3d12/max_resource_descriptors_per_frame" type="int" setter="" getter="" default="16384">
			The number of entries in the resource descriptors heap the Direct3D 12 rendering driver uses each frame, used for most rendering operations.
			Depending on the complexity of scenes, this value may be lowered or may need to be raised.
		</member>
		<member name="rendering/rendering_device/d3d12/max_sampler_descriptors_per_frame" type="int" setter="" getter="" default="1024">
			The number of entries in the sampler descriptors heap the Direct3D 12 rendering driver uses each frame, used for most rendering operations.
			Depending on the complexity of scenes, this value may be lowered or may need to be raised.
		</member>
		<member name="rendering/rendering_device/driver" type="String" setter="" getter="" default="&quot;vulkan&quot;">
			Sets the driver to be used by the renderer when using a RenderingDevice-based renderer like the Forward+ or Mobile renderers. Editing this property has no effect in the default configuration, as first-party platforms each have platform-specific overrides. Use those overrides to configure the driver for each platform.
			This can be overridden using the [code]--rendering-driver &lt;driver&gt;[/code] command line argument.
			Supported values are:
			- [code]metal[/code], Metal (supported on Apple Silicon Macs and iOS).
			- [code]vulkan[/code], Vulkan (supported on all desktop and mobile platforms).
			- [code]d3d12[/code], Direct3D 12 (supported on Windows).
			[b]Note:[/b] The availability of these options depends on whether the engine was compiled with support for them (determined by SCons options [code]vulkan[/code], [code]metal[/code], and [code]d3d12[/code]).
			[b]Note:[/b] If a given platform has no registered drivers, it can fall back to the Compatibility renderer (OpenGL 3) if [member rendering/rendering_device/fallback_to_opengl3] is enabled. This fallback happens automatically for the Web platform regardless of that property.
			[b]Note:[/b] The actual rendering driver may be automatically changed by the engine as a result of a fallback, or a user-specified command line argument. To get the actual rendering driver that is used at runtime, use [method RenderingServer.get_current_rendering_driver_name] instead of reading this project setting's value.
		</member>
		<member name="rendering/rendering_device/driver.android" type="String" setter="" getter="" default="&quot;vulkan&quot;">
			Android override for [member rendering/rendering_device/driver].
			Only one option is supported:
			- [code]vulkan[/code], Vulkan from native drivers.
			[b]Note:[/b] If Vulkan was disabled at compile time, there is no alternative RenderingDevice driver.
		</member>
		<member name="rendering/rendering_device/driver.ios" type="String" setter="" getter="" default="&quot;metal&quot;">
			iOS override for [member rendering/rendering_device/driver].
			Two options are supported:
			- [code]metal[/code] (default), Metal from native drivers.
			- [code]vulkan[/code], Vulkan over Metal via MoltenVK.
		</member>
		<member name="rendering/rendering_device/driver.linuxbsd" type="String" setter="" getter="" default="&quot;vulkan&quot;">
			LinuxBSD override for [member rendering/rendering_device/driver].
			Only one option is supported:
			- [code]vulkan[/code], Vulkan from native drivers.
			[b]Note:[/b] If Vulkan was disabled at compile time, there is no alternative RenderingDevice driver.
		</member>
		<member name="rendering/rendering_device/driver.macos" type="String" setter="" getter="" default="&quot;metal&quot;">
			macOS override for [member rendering/rendering_device/driver].
			Two options are supported:
			- [code]metal[/code] (default), Metal from native drivers, only supported on Apple Silicon Macs. On Intel Macs, it will automatically fall back to [code]vulkan[/code] as Metal support is not implemented.
			- [code]vulkan[/code], Vulkan over Metal via MoltenVK, supported on both Apple Silicon and Intel Macs.
		</member>
		<member name="rendering/rendering_device/driver.windows" type="String" setter="" getter="" default="&quot;vulkan&quot;">
			Windows override for [member rendering/rendering_device/driver].
			Two options are supported:
			- [code]vulkan[/code] (default), Vulkan from native drivers. If [member rendering/rendering_device/fallback_to_vulkan] is enabled, this is used as a fallback if Direct3D 12 is not supported.
			- [code]d3d12[/code], Direct3D 12 from native drivers. If [member rendering/rendering_device/fallback_to_d3d12] is enabled, this is used as a fallback if Vulkan is not supported.
		</member>
		<member name="rendering/rendering_device/fallback_to_d3d12" type="bool" setter="" getter="" default="true">
			If [code]true[/code], the forward renderer will fall back to Direct3D 12 if Vulkan is not supported. The fallback is always attempted regardless of this setting if Vulkan driver support was disabled at compile time.
			[b]Note:[/b] This setting is implemented only on Windows.
		</member>
		<member name="rendering/rendering_device/fallback_to_opengl3" type="bool" setter="" getter="" default="true">
			If [code]true[/code], the forward renderer will fall back to OpenGL 3 if Direct3D 12, Metal, and Vulkan are not supported.
			[b]Note:[/b] This setting is implemented on Windows, Android, macOS, iOS, and Linux/X11.
		</member>
		<member name="rendering/rendering_device/fallback_to_vulkan" type="bool" setter="" getter="" default="true">
			If [code]true[/code], the forward renderer will fall back to Vulkan if Direct3D 12 (on Windows) or Metal (on macOS x86_64) are not supported. The fallback is always attempted regardless of this setting if Direct3D 12 (Windows) or Metal (macOS) driver support was disabled at compile time.
			[b]Note:[/b] This setting is implemented on Windows and macOS.
		</member>
		<member name="rendering/rendering_device/pipeline_cache/enable" type="bool" setter="" getter="" default="true">
			Enable the pipeline cache that is saved to disk if the graphics API supports it.
			[b]Note:[/b] This property is unable to control the pipeline caching the GPU driver itself does. Only turn this off along with deleting the contents of the driver's cache if you wish to simulate the experience a user will get when starting the game for the first time.
		</member>
		<member name="rendering/rendering_device/pipeline_cache/save_chunk_size_mb" type="float" setter="" getter="" default="3.0">
			Determines at which interval pipeline cache is saved to disk. The lower the value, the more often it is saved.
		</member>
		<member name="rendering/rendering_device/staging_buffer/block_size_kb" type="int" setter="" getter="" default="256">
			The size of a block allocated in the staging buffers. Staging buffers are the intermediate resources the engine uses to upload or download data to the GPU. This setting determines the max amount of data that can be transferred in a copy operation. Increasing this will result in faster data transfers at the cost of extra memory.
			[b]Note:[/b] This property is only read when the project starts. There is currently no way to change this value at run-time.
		</member>
		<member name="rendering/rendering_device/staging_buffer/max_size_mb" type="int" setter="" getter="" default="128">
			The maximum amount of memory allowed to be used by staging buffers. If the amount of data being uploaded or downloaded exceeds this amount, the GPU will stall and wait for previous frames to finish.
			[b]Note:[/b] This property is only read when the project starts. There is currently no way to change this value at run-time.
		</member>
		<member name="rendering/rendering_device/staging_buffer/texture_download_region_size_px" type="int" setter="" getter="" default="64">
			The region size in pixels used to download texture data from the GPU when using methods like [method RenderingDevice.texture_get_data_async].
			[b]Note:[/b] This property's upper limit is controlled by [member rendering/rendering_device/staging_buffer/block_size_kb] and whether it's possible to allocate a single block of texture data with this region size in the format that is requested.
			[b]Note:[/b] This property is only read when the project starts. There is currently no way to change this value at run-time.
		</member>
		<member name="rendering/rendering_device/staging_buffer/texture_upload_region_size_px" type="int" setter="" getter="" default="64">
			The region size in pixels used to upload texture data from the GPU when using methods like [method RenderingDevice.texture_update].
			[b]Note:[/b] This property's upper limit is controlled by [member rendering/rendering_device/staging_buffer/block_size_kb] and whether it's possible to allocate a single block of texture data with this region size in the format that is requested.
			[b]Note:[/b] This property is only read when the project starts. There is currently no way to change this value at run-time.
		</member>
		<member name="rendering/rendering_device/vsync/frame_queue_size" type="int" setter="" getter="" default="2">
			The number of frames to track on the CPU side before stalling to wait for the GPU.
			Try the [url=https://darksylinc.github.io/vsync_simulator/]V-Sync Simulator[/url], an interactive interface that simulates presentation to better understand how it is affected by different variables under various conditions.
			[b]Note:[/b] This property is only read when the project starts. There is currently no way to change this value at run-time.
		</member>
		<member name="rendering/rendering_device/vsync/swapchain_image_count" type="int" setter="" getter="" default="3">
			The number of images the swapchain will consist of (back buffers + front buffer).
			[code]2[/code] corresponds to double-buffering and [code]3[/code] to triple-buffering.
			Double-buffering may give you the lowest lag/latency but if V-Sync is on and the system can't render at 60 fps, the framerate will go down in multiples of it (e.g. 30 fps, 15, 7.5, etc.). Triple buffering gives you higher framerate (specially if the system can't reach a constant 60 fps) at the cost of up to 1 frame of latency, with [constant DisplayServer.VSYNC_ENABLED] (FIFO).
			Use double-buffering with [constant DisplayServer.VSYNC_ENABLED]. Triple-buffering is a must if you plan on using [constant DisplayServer.VSYNC_MAILBOX] mode.
			Try the [url=https://darksylinc.github.io/vsync_simulator/]V-Sync Simulator[/url], an interactive interface that simulates presentation to better understand how it is affected by different variables under various conditions.
			[b]Note:[/b] This property is only read when the project starts. There is currently no way to change this value at run-time.
			[b]Note:[/b] Some platforms may restrict the actual value.
		</member>
		<member name="rendering/rendering_device/vulkan/max_descriptors_per_pool" type="int" setter="" getter="" default="64">
			The number of descriptors per pool. Redot's Vulkan backend uses linear pools for descriptors that will be created and destroyed within a single frame. Instead of destroying every single descriptor every frame, they all can be destroyed at once by resetting the pool they belong to.
			A larger number is more efficient up to a limit, after that it will only waste RAM (maximum efficiency is achieved when there is no more than 1 pool per frame). A small number could end up with one pool per descriptor, which negatively impacts performance.
			[b]Note:[/b] Changing this property requires a restart to take effect.
		</member>
		<member name="rendering/scaling_3d/fsr_sharpness" type="float" setter="" getter="" default="0.2">
			Determines how sharp the upscaled image will be when using the FSR upscaling mode. Sharpness halves with every whole number. Values go from 0.0 (sharpest) to 2.0. Values above 2.0 won't make a visible difference.
		</member>
		<member name="rendering/scaling_3d/mode" type="int" setter="" getter="" default="0">
			Sets the scaling 3D mode. Bilinear scaling renders at different resolution to either undersample or supersample the viewport. FidelityFX Super Resolution 1.0, abbreviated to FSR, is an upscaling technology that produces high quality images at fast framerates by using a spatially-aware upscaling algorithm. FSR is slightly more expensive than bilinear, but it produces significantly higher image quality. On particularly low-end GPUs, the added cost of FSR may not be worth it (compared to using bilinear scaling with a slightly higher resolution scale to match performance).
			[b]Note:[/b] FSR is only effective when using the Forward+ rendering method, not Mobile or Compatibility. If using an incompatible rendering method, FSR will fall back to bilinear scaling.
		</member>
		<member name="rendering/scaling_3d/mode.ios" type="int" setter="" getter="">
			iOS override for [member rendering/scaling_3d/mode]. This allows selecting the MetalFX spatial and MetalFX temporal scaling modes, which are exclusive to platforms where the Metal rendering driver is used.
		</member>
		<member name="rendering/scaling_3d/mode.macos" type="int" setter="" getter="">
			macOS override for [member rendering/scaling_3d/mode]. This allows selecting the MetalFX spatial and MetalFX temporal scaling modes, which are exclusive to platforms where the Metal rendering driver is used.
		</member>
		<member name="rendering/scaling_3d/scale" type="float" setter="" getter="" default="1.0">
			Scales the 3D render buffer based on the viewport size uses an image filter specified in [member rendering/scaling_3d/mode] to scale the output image to the full viewport size. Values lower than [code]1.0[/code] can be used to speed up 3D rendering at the cost of quality (undersampling). Values greater than [code]1.0[/code] are only valid for bilinear mode and can be used to improve 3D rendering quality at a high performance cost (supersampling). See also [member rendering/anti_aliasing/quality/msaa_3d] for multi-sample antialiasing, which is significantly cheaper but only smooths the edges of polygons.
		</member>
		<member name="rendering/shader_compiler/shader_cache/compress" type="bool" setter="" getter="" default="true">
		</member>
		<member name="rendering/shader_compiler/shader_cache/enabled" type="bool" setter="" getter="" default="true">
			Enable the shader cache, which stores compiled shaders to disk to prevent stuttering from shader compilation the next time the shader is needed.
		</member>
		<member name="rendering/shader_compiler/shader_cache/strip_debug" type="bool" setter="" getter="" default="false">
		</member>
		<member name="rendering/shader_compiler/shader_cache/strip_debug.release" type="bool" setter="" getter="" default="true">
		</member>
		<member name="rendering/shader_compiler/shader_cache/use_zstd_compression" type="bool" setter="" getter="" default="true">
		</member>
		<member name="rendering/shading/overrides/force_lambert_over_burley" type="bool" setter="" getter="" default="false">
			If [code]true[/code], uses faster but lower-quality Lambert material lighting model instead of Burley.
		</member>
		<member name="rendering/shading/overrides/force_lambert_over_burley.mobile" type="bool" setter="" getter="" default="true">
			Lower-end override for [member rendering/shading/overrides/force_lambert_over_burley] on mobile devices, due to performance concerns or driver support.
		</member>
		<member name="rendering/shading/overrides/force_vertex_shading" type="bool" setter="" getter="" default="false">
			If [code]true[/code], forces vertex shading for all rendering. This can increase performance a lot, but also reduces quality immensely. Can be used to optimize performance on low-end mobile devices.
		</member>
		<member name="rendering/textures/canvas_textures/default_texture_filter" type="int" setter="" getter="" default="1">
			The default texture filtering mode to use for [CanvasItem]s built-in texture. In shaders, this texture is accessed as [code]TEXTURE[/code].
			[b]Note:[/b] For pixel art aesthetics, see also [member rendering/2d/snap/snap_2d_vertices_to_pixel] and [member rendering/2d/snap/snap_2d_transforms_to_pixel].
		</member>
		<member name="rendering/textures/canvas_textures/default_texture_repeat" type="int" setter="" getter="" default="0">
			The default texture repeating mode to use for [CanvasItem]s built-in texture. In shaders, this texture is accessed as [code]TEXTURE[/code].
		</member>
		<member name="rendering/textures/decals/filter" type="int" setter="" getter="" default="3">
			The filtering quality to use for [Decal] nodes. When using one of the anisotropic filtering modes, the anisotropic filtering level is controlled by [member rendering/textures/default_filters/anisotropic_filtering_level].
		</member>
		<member name="rendering/textures/default_filters/anisotropic_filtering_level" type="int" setter="" getter="" default="2">
			Sets the maximum number of samples to take when using anisotropic filtering on textures (as a power of two). A higher sample count will result in sharper textures at oblique angles, but is more expensive to compute. A value of [code]0[/code] forcibly disables anisotropic filtering, even on materials where it is enabled.
			The anisotropic filtering level also affects decals and light projectors if they are configured to use anisotropic filtering. See [member rendering/textures/decals/filter] and [member rendering/textures/light_projectors/filter].
			[b]Note:[/b] In 3D, for this setting to have an effect, set [member BaseMaterial3D.texture_filter] to [constant BaseMaterial3D.TEXTURE_FILTER_LINEAR_WITH_MIPMAPS_ANISOTROPIC] or [constant BaseMaterial3D.TEXTURE_FILTER_NEAREST_WITH_MIPMAPS_ANISOTROPIC] on materials.
			[b]Note:[/b] In 2D, for this setting to have an effect, set [member CanvasItem.texture_filter] to [constant CanvasItem.TEXTURE_FILTER_LINEAR_WITH_MIPMAPS_ANISOTROPIC] or [constant CanvasItem.TEXTURE_FILTER_NEAREST_WITH_MIPMAPS_ANISOTROPIC] on the [CanvasItem] node displaying the texture (or in [CanvasTexture]). However, anisotropic filtering is rarely useful in 2D, so only enable it for textures in 2D if it makes a meaningful visual difference.
			[b]Note:[/b] This property is only read when the project starts. To change the anisotropic filtering level at runtime, set [member Viewport.anisotropic_filtering_level] on the root [Viewport] instead.
		</member>
		<member name="rendering/textures/default_filters/texture_mipmap_bias" type="float" setter="" getter="" default="0.0">
			Affects the final texture sharpness by reading from a lower or higher mipmap (also called "texture LOD bias"). Negative values make mipmapped textures sharper but grainier when viewed at a distance, while positive values make mipmapped textures blurrier (even when up close).
			Enabling temporal antialiasing ([member rendering/anti_aliasing/quality/use_taa]) will automatically apply a [code]-0.5[/code] offset to this value, while enabling FXAA ([member rendering/anti_aliasing/quality/screen_space_aa]) will automatically apply a [code]-0.25[/code] offset to this value. If both TAA and FXAA are enabled at the same time, an offset of [code]-0.75[/code] is applied to this value.
			[b]Note:[/b] If [member rendering/scaling_3d/scale] is lower than [code]1.0[/code] (exclusive), [member rendering/textures/default_filters/texture_mipmap_bias] is used to adjust the automatic mipmap bias which is calculated internally based on the scale factor. The formula for this is [code]log2(scaling_3d_scale) + mipmap_bias[/code].
		</member>
		<member name="rendering/textures/default_filters/use_nearest_mipmap_filter" type="bool" setter="" getter="" default="false">
			If [code]true[/code], uses nearest-neighbor mipmap filtering when using mipmaps (also called "bilinear filtering"), which will result in visible seams appearing between mipmap stages. This may increase performance in mobile as less memory bandwidth is used. If [code]false[/code], linear mipmap filtering (also called "trilinear filtering") is used.
			[b]Note:[/b] This property is only read when the project starts. There is currently no way to change this setting at run-time.
		</member>
		<member name="rendering/textures/light_projectors/filter" type="int" setter="" getter="" default="3">
			The filtering quality to use for [OmniLight3D] and [SpotLight3D] projectors. When using one of the anisotropic filtering modes, the anisotropic filtering level is controlled by [member rendering/textures/default_filters/anisotropic_filtering_level].
		</member>
		<member name="rendering/textures/lossless_compression/force_png" type="bool" setter="" getter="" default="false">
			If [code]true[/code], the texture importer will import lossless textures using the PNG format. Otherwise, it will default to using WebP.
		</member>
		<member name="rendering/textures/vram_compression/cache_gpu_compressor" type="bool" setter="" getter="" default="true">
			If [code]true[/code], the GPU texture compressor will cache the local RenderingDevice and its resources (shaders and pipelines), allowing for faster subsequent imports at a memory cost.
		</member>
		<member name="rendering/textures/vram_compression/compress_with_gpu" type="bool" setter="" getter="" default="true">
			If [code]true[/code], the texture importer will utilize the GPU for compressing textures, improving the import time of large images.
			[b]Note:[/b] This only functions on a device which supports either Vulkan, Direct3D 12, or Metal as a rendering driver.
			[b]Note:[/b] Currently this only affects certain compressed formats (BC1, BC3, BC4, BC5, and BC6), all of which are exclusive to desktop platforms and consoles.
		</member>
		<member name="rendering/textures/vram_compression/import_etc2_astc" type="bool" setter="" getter="" default="false">
			If [code]true[/code], the texture importer will import VRAM-compressed textures using the Ericsson Texture Compression 2 algorithm for lower quality textures and normal maps and Adaptable Scalable Texture Compression algorithm for high quality textures (in 4×4 block size).
			[b]Note:[/b] This setting is an override. The texture importer will always import the format the host platform needs, even if this is set to [code]false[/code].
			[b]Note:[/b] Changing this setting does [i]not[/i] impact textures that were already imported before. To make this setting apply to textures that were already imported, exit the editor, remove the [code].godot/imported/[/code] folder located inside the project folder then restart the editor (see [member application/config/use_hidden_project_data_directory]).
		</member>
		<member name="rendering/textures/vram_compression/import_s3tc_bptc" type="bool" setter="" getter="" default="false">
			If [code]true[/code], the texture importer will import VRAM-compressed textures using the S3 Texture Compression algorithm (DXT1-5) for lower quality textures and the BPTC algorithm (BC6H and BC7) for high quality textures. This algorithm is only supported on PC desktop platforms and consoles.
			[b]Note:[/b] This setting is an override. The texture importer will always import the format the host platform needs, even if this is set to [code]false[/code].
			[b]Note:[/b] Changing this setting does [i]not[/i] impact textures that were already imported before. To make this setting apply to textures that were already imported, exit the editor, remove the [code].godot/imported/[/code] folder located inside the project folder then restart the editor (see [member application/config/use_hidden_project_data_directory]).
		</member>
		<member name="rendering/textures/webp_compression/compression_method" type="int" setter="" getter="" default="2">
			The default compression method for WebP. Affects both lossy and lossless WebP. A higher value results in smaller files at the cost of compression speed. Decompression speed is mostly unaffected by the compression method. Supported values are 0 to 6. Note that compression methods above 4 are very slow and offer very little savings.
		</member>
		<member name="rendering/textures/webp_compression/lossless_compression_factor" type="float" setter="" getter="" default="25">
			The default compression factor for lossless WebP. Decompression speed is mostly unaffected by the compression factor. Supported values are 0 to 100.
		</member>
		<member name="rendering/viewport/hdr_2d" type="bool" setter="" getter="" default="false">
			If [code]true[/code], enables [member Viewport.use_hdr_2d] on the root viewport. 2D rendering will use an high dynamic range (HDR) format framebuffer matching the bit depth of the 3D framebuffer. When using the Forward+ renderer this will be an [code]RGBA16[/code] framebuffer, while when using the Mobile renderer it will be an [code]RGB10_A2[/code] framebuffer. Additionally, 2D rendering will take place in linear color space and will be converted to sRGB space immediately before blitting to the screen. Practically speaking, this means that the end result of the Viewport will not be clamped into the [code]0-1[/code] range and can be used in 3D rendering without color space adjustments. This allows 2D rendering to take advantage of effects requiring high dynamic range (e.g. 2D glow) as well as substantially improves the appearance of effects requiring highly detailed gradients.
			[b]Note:[/b] This setting will have no effect when using the Compatibility renderer, which always renders in low dynamic range for performance reasons.
			[b]Note:[/b] This property is only read when the project starts. To toggle HDR 2D at runtime, set [member Viewport.use_hdr_2d] on the root [Viewport].
		</member>
		<member name="rendering/viewport/transparent_background" type="bool" setter="" getter="" default="false">
			If [code]true[/code], enables [member Viewport.transparent_bg] on the root viewport. This allows per-pixel transparency to be effective after also enabling [member display/window/size/transparent] and [member display/window/per_pixel_transparency/allowed].
		</member>
		<member name="rendering/vrs/mode" type="int" setter="" getter="" default="0">
			Set the default Variable Rate Shading (VRS) mode for the main viewport. See [member Viewport.vrs_mode] to change this at runtime, and [enum Viewport.VRSMode] for possible values.
		</member>
		<member name="rendering/vrs/texture" type="String" setter="" getter="" default="&quot;&quot;">
			If [member rendering/vrs/mode] is set to [b]Texture[/b], this is the path to default texture loaded as the VRS image.
			The texture [i]must[/i] use a lossless compression format so that colors can be matched precisely. The following VRS densities are mapped to various colors, with brighter colors representing a lower level of shading precision:
			[codeblock lang=text]
			- 1×1 = rgb(0, 0, 0)     - #000000
			- 1×2 = rgb(0, 85, 0)    - #005500
			- 2×1 = rgb(85, 0, 0)    - #550000
			- 2×2 = rgb(85, 85, 0)   - #555500
			- 2×4 = rgb(85, 170, 0)  - #55aa00
			- 4×2 = rgb(170, 85, 0)  - #aa5500
			- 4×4 = rgb(170, 170, 0) - #aaaa00
			- 4×8 = rgb(170, 255, 0) - #aaff00 - Not supported on most hardware
			- 8×4 = rgb(255, 170, 0) - #ffaa00 - Not supported on most hardware
			- 8×8 = rgb(255, 255, 0) - #ffff00 - Not supported on most hardware
			[/codeblock]
		</member>
		<member name="threading/worker_pool/low_priority_thread_ratio" type="float" setter="" getter="" default="0.3">
			The ratio of [WorkerThreadPool]'s threads that will be reserved for low-priority tasks. For example, if 10 threads are available and this value is set to [code]0.3[/code], 3 of the worker threads will be reserved for low-priority tasks. The actual value won't exceed the number of CPU cores minus one, and if possible, at least one worker thread will be dedicated to low-priority tasks.
		</member>
		<member name="threading/worker_pool/max_threads" type="int" setter="" getter="" default="-1">
			Maximum number of threads to be used by [WorkerThreadPool]. Value of [code]-1[/code] means [code]1[/code] on Web, or a number of [i]logical[/i] CPU cores available on other platforms (see [method OS.get_processor_count]).
		</member>
		<member name="xr/openxr/binding_modifiers/analog_threshold" type="bool" setter="" getter="" default="false">
			If [code]true[/code], enables the analog threshold binding modifier if supported by the XR runtime.
		</member>
		<member name="xr/openxr/binding_modifiers/dpad_binding" type="bool" setter="" getter="" default="false">
			If [code]true[/code], enables the D-pad binding modifier if supported by the XR runtime.
		</member>
		<member name="xr/openxr/default_action_map" type="String" setter="" getter="" default="&quot;res://openxr_action_map.tres&quot;">
			Action map configuration to load by default.
		</member>
		<member name="xr/openxr/enabled" type="bool" setter="" getter="" default="false">
			If [code]true[/code], Redot will setup and initialize OpenXR on startup.
		</member>
		<member name="xr/openxr/environment_blend_mode" type="int" setter="" getter="" default="&quot;0&quot;">
			Specify how OpenXR should blend in the environment. This is specific to certain AR and passthrough devices where camera images are blended in by the XR compositor.
		</member>
		<member name="xr/openxr/extensions/debug_message_types" type="int" setter="" getter="" default="&quot;15&quot;">
			Specifies the message types for which we request debug messages. Requires [member xr/openxr/extensions/debug_utils] to be set and the extension to be supported by the XR runtime.
		</member>
		<member name="xr/openxr/extensions/debug_utils" type="int" setter="" getter="" default="&quot;0&quot;">
			Enables debug utilities on XR runtimes that supports the debug utils extension. Sets the maximum severity being reported (0 = disabled, 1 = error, 2 = warning, 3 = info, 4 = verbose).
		</member>
		<member name="xr/openxr/extensions/eye_gaze_interaction" type="bool" setter="" getter="" default="false">
			Specify whether to enable eye tracking for this project. Depending on the platform, additional export configuration may be needed.
		</member>
		<member name="xr/openxr/extensions/hand_interaction_profile" type="bool" setter="" getter="" default="false">
			If [code]true[/code] the hand interaction profile extension will be activated if supported by the platform.
		</member>
		<member name="xr/openxr/extensions/hand_tracking" type="bool" setter="" getter="" default="false">
			If [code]true[/code], the hand tracking extension is enabled if available.
			[b]Note:[/b] By default hand tracking will only work for data sources chosen by the XR runtime. For SteamVR this is the controller inferred data source, for most other runtimes this is the unobstructed data source. There is no way to query this. If a runtime supports the OpenXR data source extension you can use the [member xr/openxr/extensions/hand_tracking_controller_data_source] and/or [member xr/openxr/extensions/hand_tracking_unobstructed_data_source] to indicate you wish to enable these data sources. If neither is selected the data source extension is not enabled and the XR runtimes default behavior persists.
		</member>
		<member name="xr/openxr/extensions/hand_tracking_controller_data_source" type="bool" setter="" getter="" default="false">
			If [code]true[/code], support for the controller inferred data source is requested. If supported, you will receive hand tracking data even if the user has a controller in hand, with finger positions automatically inferred from controller input and/or sensors.
			[b]Note:[/b] This requires the OpenXR data source extension and controller inferred handtracking to be supported by the XR runtime. If not supported this setting will be ignored. [member xr/openxr/extensions/hand_tracking] must be enabled for this setting to be used.
		</member>
		<member name="xr/openxr/extensions/hand_tracking_unobstructed_data_source" type="bool" setter="" getter="" default="false">
			If [code]true[/code], support for the unobstructed data source is requested. If supported, you will receive hand tracking data based on the actual finger positions of the user often determined by optical tracking.
			[b]Note:[/b] This requires the OpenXR data source extension and unobstructed handtracking to be supported by the XR runtime. If not supported this setting will be ignored. [member xr/openxr/extensions/hand_tracking] must be enabled for this setting to be used.
		</member>
		<member name="xr/openxr/form_factor" type="int" setter="" getter="" default="&quot;0&quot;">
			Specify whether OpenXR should be configured for an HMD or a hand held device.
		</member>
		<member name="xr/openxr/foveation_dynamic" type="bool" setter="" getter="" default="false">
			If [code]true[/code] and foveation is supported, will automatically adjust foveation level based on framerate up to the level set on [member xr/openxr/foveation_level].
		</member>
		<member name="xr/openxr/foveation_level" type="int" setter="" getter="" default="&quot;0&quot;">
			Applied foveation level if supported: 0 = off, 1 = low, 2 = medium, 3 = high.
			[b]Note:[/b] On platforms other than Android, if [member rendering/anti_aliasing/quality/msaa_3d] is enabled, this feature will be disabled.
		</member>
		<member name="xr/openxr/reference_space" type="int" setter="" getter="" default="&quot;1&quot;">
			Specify the default reference space.
		</member>
		<member name="xr/openxr/startup_alert" type="bool" setter="" getter="" default="true">
			If [code]true[/code], Redot will display an alert modal when OpenXR initialization fails on startup.
		</member>
		<member name="xr/openxr/submit_depth_buffer" type="bool" setter="" getter="" default="false">
			If [code]true[/code], OpenXR will manage the depth buffer and use the depth buffer for advanced reprojection provided this is supported by the XR runtime. Note that some rendering features in Redot can't be used with this feature.
		</member>
		<member name="xr/openxr/view_configuration" type="int" setter="" getter="" default="&quot;1&quot;">
			Specify the view configuration with which to configure OpenXR setting up either Mono or Stereo rendering.
		</member>
		<member name="xr/shaders/enabled" type="bool" setter="" getter="" default="false">
			If [code]true[/code], Redot will compile shaders required for XR.
		</member>
	</members>
	<signals>
		<signal name="settings_changed">
			<description>
				Emitted when any setting is changed, up to once per process frame.
			</description>
		</signal>
	</signals>
</class>
