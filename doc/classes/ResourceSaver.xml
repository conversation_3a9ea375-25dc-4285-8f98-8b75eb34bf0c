<?xml version="1.0" encoding="UTF-8" ?>
<class name="ResourceSaver" inherits="Object" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A singleton for saving [Resource]s to the filesystem.
	</brief_description>
	<description>
		A singleton for saving resource types to the filesystem.
		It uses the many [ResourceFormatSaver] classes registered in the engine (either built-in or from a plugin) to save resource data to text-based (e.g. [code].tres[/code] or [code].tscn[/code]) or binary files (e.g. [code].res[/code] or [code].scn[/code]).
	</description>
	<tutorials>
	</tutorials>
	<methods>
		<method name="add_resource_format_saver">
			<return type="void" />
			<param index="0" name="format_saver" type="ResourceFormatSaver" />
			<param index="1" name="at_front" type="bool" default="false" />
			<description>
				Registers a new [ResourceFormatSaver]. The ResourceSaver will use the ResourceFormatSaver as described in [method save].
				This method is performed implicitly for ResourceFormatSavers written in GDScript (see [ResourceFormatSaver] for more information).
			</description>
		</method>
		<method name="get_recognized_extensions">
			<return type="PackedStringArray" />
			<param index="0" name="type" type="Resource" />
			<description>
				Returns the list of extensions available for saving a resource of a given type.
			</description>
		</method>
		<method name="get_resource_id_for_path">
			<return type="int" />
			<param index="0" name="path" type="String" />
			<param index="1" name="generate" type="bool" default="false" />
			<description>
				Returns the resource ID for the given path. If [param generate] is [code]true[/code], a new resource ID will be generated if one for the path is not found. If [param generate] is [code]false[/code] and the path is not found, [constant ResourceUID.INVALID_ID] is returned.
			</description>
		</method>
		<method name="remove_resource_format_saver">
			<return type="void" />
			<param index="0" name="format_saver" type="ResourceFormatSaver" />
			<description>
				Unregisters the given [ResourceFormatSaver].
			</description>
		</method>
		<method name="save">
			<return type="int" enum="Error" />
			<param index="0" name="resource" type="Resource" />
			<param index="1" name="path" type="String" default="&quot;&quot;" />
			<param index="2" name="flags" type="int" enum="ResourceSaver.SaverFlags" is_bitfield="true" default="0" />
			<description>
				Saves a resource to disk to the given path, using a [ResourceFormatSaver] that recognizes the resource object. If [param path] is empty, [ResourceSaver] will try to use [member Resource.resource_path].
				The [param flags] bitmask can be specified to customize the save behavior using [enum SaverFlags] flags.
				Returns [constant OK] on success.
				[b]Note:[/b] When the project is running, any generated UID associated with the resource will not be saved as the required code is only executed in editor mode.
			</description>
		</method>
		<method name="set_uid">
			<return type="int" enum="Error" />
			<param index="0" name="resource" type="String" />
			<param index="1" name="uid" type="int" />
			<description>
				Sets the UID of the given [param resource] path to [param uid]. You can generate a new UID using [method ResourceUID.create_id].
				Since resources will normally get a UID automatically, this method is only useful in very specific cases.
			</description>
		</method>
	</methods>
	<constants>
		<constant name="FLAG_NONE" value="0" enum="SaverFlags" is_bitfield="true">
			No resource saving option.
		</constant>
		<constant name="FLAG_RELATIVE_PATHS" value="1" enum="SaverFlags" is_bitfield="true">
			Save the resource with a path relative to the scene which uses it.
		</constant>
		<constant name="FLAG_BUNDLE_RESOURCES" value="2" enum="SaverFlags" is_bitfield="true">
			Bundles external resources.
		</constant>
		<constant name="FLAG_CHANGE_PATH" value="4" enum="SaverFlags" is_bitfield="true">
			Changes the [member Resource.resource_path] of the saved resource to match its new location.
		</constant>
		<constant name="FLAG_OMIT_EDITOR_PROPERTIES" value="8" enum="SaverFlags" is_bitfield="true">
			Do not save editor-specific metadata (identified by their [code]__editor[/code] prefix).
		</constant>
		<constant name="FLAG_SAVE_BIG_ENDIAN" value="16" enum="SaverFlags" is_bitfield="true">
			Save as big endian (see [member FileAccess.big_endian]).
		</constant>
		<constant name="FLAG_COMPRESS" value="32" enum="SaverFlags" is_bitfield="true">
			Compress the resource on save using [constant FileAccess.COMPRESSION_ZSTD]. Only available for binary resource types.
		</constant>
		<constant name="FLAG_REPLACE_SUBRESOURCE_PATHS" value="64" enum="SaverFlags" is_bitfield="true">
			Take over the paths of the saved subresources (see [method Resource.take_over_path]).
		</constant>
	</constants>
</class>
