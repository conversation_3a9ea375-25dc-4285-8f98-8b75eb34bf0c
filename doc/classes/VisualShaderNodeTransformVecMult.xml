<?xml version="1.0" encoding="UTF-8" ?>
<class name="VisualShaderNodeTransformVecMult" inherits="VisualShaderNode" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Multiplies a [Transform3D] and a [Vector3] within the visual shader graph.
	</brief_description>
	<description>
		A multiplication operation on a transform (4×4 matrix) and a vector, with support for different multiplication operators.
	</description>
	<tutorials>
	</tutorials>
	<members>
		<member name="operator" type="int" setter="set_operator" getter="get_operator" enum="VisualShaderNodeTransformVecMult.Operator" default="0">
			The multiplication type to be performed. See [enum Operator] for options.
		</member>
	</members>
	<constants>
		<constant name="OP_AxB" value="0" enum="Operator">
			Multiplies transform [code]a[/code] by the vector [code]b[/code].
		</constant>
		<constant name="OP_BxA" value="1" enum="Operator">
			Multiplies vector [code]b[/code] by the transform [code]a[/code].
		</constant>
		<constant name="OP_3x3_AxB" value="2" enum="Operator">
			Multiplies transform [code]a[/code] by the vector [code]b[/code], skipping the last row and column of the transform.
		</constant>
		<constant name="OP_3x3_BxA" value="3" enum="Operator">
			Multiplies vector [code]b[/code] by the transform [code]a[/code], skipping the last row and column of the transform.
		</constant>
		<constant name="OP_MAX" value="4" enum="Operator">
			Represents the size of the [enum Operator] enum.
		</constant>
	</constants>
</class>
