<?xml version="1.0" encoding="UTF-8" ?>
<class name="ResourceImporterShaderFile" inherits="ResourceImporter" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Imports native GLSL shaders (not Godot Shaders) as a [RDShaderFile].
	</brief_description>
	<description>
		This imports native GLSL shaders as [RDShaderFile] resources, for use with low-level [RenderingDevice] operations. This importer does [i]not[/i] handle [code].gdshader[/code] files.
	</description>
	<tutorials>
	</tutorials>
</class>
