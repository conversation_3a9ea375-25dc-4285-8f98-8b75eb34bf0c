<?xml version="1.0" encoding="UTF-8" ?>
<class name="VisualShaderNodeComment" inherits="VisualShaderNodeFrame" deprecated="This class has no function anymore and only exists for compatibility." xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Only exists for compatibility. Use [VisualShaderNodeFrame] as a replacement.
	</brief_description>
	<description>
		This node was replaced by [VisualShaderNodeFrame] and only exists to preserve compatibility. In the [VisualShader] editor it behaves exactly like [VisualShaderNodeFrame].
	</description>
	<tutorials>
	</tutorials>
	<members>
		<member name="description" type="String" setter="set_description" getter="get_description" default="&quot;&quot;">
			This property only exists to preserve data authored in earlier versions of Redot. It has currently no function.
		</member>
	</members>
</class>
