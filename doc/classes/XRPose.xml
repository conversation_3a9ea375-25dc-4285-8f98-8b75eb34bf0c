<?xml version="1.0" encoding="UTF-8" ?>
<class name="XRPose" inherits="RefCounted" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		This object contains all data related to a pose on a tracked object.
	</brief_description>
	<description>
		XR runtimes often identify multiple locations on devices such as controllers that are spatially tracked.
		Orientation, location, linear velocity and angular velocity are all provided for each pose by the XR runtime. This object contains this state of a pose.
	</description>
	<tutorials>
		<link title="XR documentation index">$DOCS_URL/tutorials/xr/index.html</link>
	</tutorials>
	<methods>
		<method name="get_adjusted_transform" qualifiers="const">
			<return type="Transform3D" />
			<description>
				Returns the [member transform] with world scale and our reference frame applied. This is the transform used to position [XRNode3D] objects.
			</description>
		</method>
	</methods>
	<members>
		<member name="angular_velocity" type="Vector3" setter="set_angular_velocity" getter="get_angular_velocity" default="Vector3(0, 0, 0)">
			The angular velocity for this pose.
		</member>
		<member name="has_tracking_data" type="bool" setter="set_has_tracking_data" getter="get_has_tracking_data" default="false">
			If [code]true[/code] our tracking data is up to date. If [code]false[/code] we're no longer receiving new tracking data and our state is whatever that last valid state was.
		</member>
		<member name="linear_velocity" type="Vector3" setter="set_linear_velocity" getter="get_linear_velocity" default="Vector3(0, 0, 0)">
			The linear velocity of this pose.
		</member>
		<member name="name" type="StringName" setter="set_name" getter="get_name" default="&amp;&quot;&quot;">
			The name of this pose. Usually, this name is derived from an action map set up by the user. Redot also suggests some pose names that [XRInterface] objects are expected to implement:
			- [code]root[/code] is the root location, often used for tracked objects that do not have further nodes.
			- [code]aim[/code] is the tip of a controller with its orientation pointing outwards, often used for raycasts.
			- [code]grip[/code] is the location where the user grips the controller.
			- [code]skeleton[/code] is the root location for a hand mesh, when using hand tracking and an animated skeleton is supplied by the XR runtime.
		</member>
		<member name="tracking_confidence" type="int" setter="set_tracking_confidence" getter="get_tracking_confidence" enum="XRPose.TrackingConfidence" default="0">
			The tracking confidence for this pose, provides insight on how accurate the spatial positioning of this record is.
		</member>
		<member name="transform" type="Transform3D" setter="set_transform" getter="get_transform" default="Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0)">
			The transform containing the original and transform as reported by the XR runtime.
		</member>
	</members>
	<constants>
		<constant name="XR_TRACKING_CONFIDENCE_NONE" value="0" enum="TrackingConfidence">
			No tracking information is available for this pose.
		</constant>
		<constant name="XR_TRACKING_CONFIDENCE_LOW" value="1" enum="TrackingConfidence">
			Tracking information may be inaccurate or estimated. For example, with inside out tracking this would indicate a controller may be (partially) obscured.
		</constant>
		<constant name="XR_TRACKING_CONFIDENCE_HIGH" value="2" enum="TrackingConfidence">
			Tracking information is considered accurate and up to date.
		</constant>
	</constants>
</class>
