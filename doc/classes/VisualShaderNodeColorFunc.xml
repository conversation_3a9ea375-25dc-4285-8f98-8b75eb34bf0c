<?xml version="1.0" encoding="UTF-8" ?>
<class name="VisualShaderNodeColorFunc" inherits="VisualShaderNode" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A [Color] function to be used within the visual shader graph.
	</brief_description>
	<description>
		Accept a [Color] to the input port and transform it according to [member function].
	</description>
	<tutorials>
	</tutorials>
	<members>
		<member name="function" type="int" setter="set_function" getter="get_function" enum="VisualShaderNodeColorFunc.Function" default="0">
			A function to be applied to the input color. See [enum Function] for options.
		</member>
	</members>
	<constants>
		<constant name="FUNC_GRAYSCALE" value="0" enum="Function">
			Converts the color to grayscale using the following formula:
			[codeblock]
			vec3 c = input;
			float max1 = max(c.r, c.g);
			float max2 = max(max1, c.b);
			float max3 = max(max1, max2);
			return vec3(max3, max3, max3);
			[/codeblock]
		</constant>
		<constant name="FUNC_HSV2RGB" value="1" enum="Function">
			Converts HSV vector to RGB equivalent.
		</constant>
		<constant name="FUNC_RGB2HSV" value="2" enum="Function">
			Converts RGB vector to HSV equivalent.
		</constant>
		<constant name="FUNC_SEPIA" value="3" enum="Function">
			Applies sepia tone effect using the following formula:
			[codeblock]
			vec3 c = input;
			float r = (c.r * 0.393) + (c.g * 0.769) + (c.b * 0.189);
			float g = (c.r * 0.349) + (c.g * 0.686) + (c.b * 0.168);
			float b = (c.r * 0.272) + (c.g * 0.534) + (c.b * 0.131);
			return vec3(r, g, b);
			[/codeblock]
		</constant>
		<constant name="FUNC_LINEAR_TO_SRGB" value="4" enum="Function">
			Converts color from linear color space to sRGB color space using the following formula:
			[codeblock]
			vec3 c = clamp(c, vec3(0.0), vec3(1.0));
			const vec3 a = vec3(0.055f);
			return mix((vec3(1.0f) + a) * pow(c.rgb, vec3(1.0f / 2.4f)) - a, 12.92f * c.rgb, lessThan(c.rgb, vec3(0.0031308f)));
			[/codeblock]
			The Compatibility renderer uses a simpler formula:
			[codeblock]
			vec3 c = input;
			return max(vec3(1.055) * pow(c, vec3(0.416666667)) - vec3(0.055), vec3(0.0));
			[/codeblock]
		</constant>
		<constant name="FUNC_SRGB_TO_LINEAR" value="5" enum="Function">
			Converts color from sRGB color space to linear color space using the following formula:
			[codeblock]
			vec3 c = input;
			return mix(pow((c.rgb + vec3(0.055)) * (1.0 / (1.0 + 0.055)), vec3(2.4)), c.rgb * (1.0 / 12.92), lessThan(c.rgb, vec3(0.04045)));
			[/codeblock]
			The Compatibility renderer uses a simpler formula:
			[codeblock]
			vec3 c = input;
			return c * (c * (c * 0.305306011 + 0.682171111) + 0.012522878);
			[/codeblock]
		</constant>
		<constant name="FUNC_MAX" value="6" enum="Function">
			Represents the size of the [enum Function] enum.
		</constant>
	</constants>
</class>
