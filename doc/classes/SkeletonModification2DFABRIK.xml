<?xml version="1.0" encoding="UTF-8" ?>
<class name="SkeletonModification2DFABRIK" inherits="SkeletonModification2D" experimental="" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A modification that uses FABRIK to manipulate a series of [Bone2D] nodes to reach a target.
	</brief_description>
	<description>
		This [SkeletonModification2D] uses an algorithm called Forward And Backward Reaching Inverse Kinematics, or FABRIK, to rotate a bone chain so that it reaches a target.
		FABRIK works by knowing the positions and lengths of a series of bones, typically called a "bone chain". It first starts by running a forward pass, which places the final bone at the target's position. Then all other bones are moved towards the tip bone, so they stay at the defined bone length away. Then a backwards pass is performed, where the root/first bone in the FABRIK chain is placed back at the origin. Then all other bones are moved so they stay at the defined bone length away. This positions the bone chain so that it reaches the target when possible, but all of the bones stay the correct length away from each other.
		Because of how FABRIK works, it often gives more natural results than those seen in [SkeletonModification2DCCDIK]. FABRIK also supports angle constraints, which are fully taken into account when solving.
		[b]Note:[/b] The FABRIK modifier has [code]fabrik_joints[/code], which are the data objects that hold the data for each joint in the FABRIK chain. This is different from [Bone2D] nodes! FABRIK joints hold the data needed for each [Bone2D] in the bone chain used by FABRIK.
		To help control how the FABRIK joints move, a magnet vector can be passed, which can nudge the bones in a certain direction prior to solving, giving a level of control over the final result.
	</description>
	<tutorials>
	</tutorials>
	<methods>
		<method name="get_fabrik_joint_bone2d_node" qualifiers="const">
			<return type="NodePath" />
			<param index="0" name="joint_idx" type="int" />
			<description>
				Returns the [Bone2D] node assigned to the FABRIK joint at [param joint_idx].
			</description>
		</method>
		<method name="get_fabrik_joint_bone_index" qualifiers="const">
			<return type="int" />
			<param index="0" name="joint_idx" type="int" />
			<description>
				Returns the index of the [Bone2D] node assigned to the FABRIK joint at [param joint_idx].
			</description>
		</method>
		<method name="get_fabrik_joint_magnet_position" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="joint_idx" type="int" />
			<description>
				Returns the magnet position vector for the joint at [param joint_idx].
			</description>
		</method>
		<method name="get_fabrik_joint_use_target_rotation" qualifiers="const">
			<return type="bool" />
			<param index="0" name="joint_idx" type="int" />
			<description>
				Returns whether the joint is using the target's rotation rather than allowing FABRIK to rotate the joint. This option only applies to the tip/final joint in the chain.
			</description>
		</method>
		<method name="set_fabrik_joint_bone2d_node">
			<return type="void" />
			<param index="0" name="joint_idx" type="int" />
			<param index="1" name="bone2d_nodepath" type="NodePath" />
			<description>
				Sets the [Bone2D] node assigned to the FABRIK joint at [param joint_idx].
			</description>
		</method>
		<method name="set_fabrik_joint_bone_index">
			<return type="void" />
			<param index="0" name="joint_idx" type="int" />
			<param index="1" name="bone_idx" type="int" />
			<description>
				Sets the bone index, [param bone_idx], of the FABRIK joint at [param joint_idx]. When possible, this will also update the [code]bone2d_node[/code] of the FABRIK joint based on data provided by the linked skeleton.
			</description>
		</method>
		<method name="set_fabrik_joint_magnet_position">
			<return type="void" />
			<param index="0" name="joint_idx" type="int" />
			<param index="1" name="magnet_position" type="Vector2" />
			<description>
				Sets the magnet position vector for the joint at [param joint_idx].
			</description>
		</method>
		<method name="set_fabrik_joint_use_target_rotation">
			<return type="void" />
			<param index="0" name="joint_idx" type="int" />
			<param index="1" name="use_target_rotation" type="bool" />
			<description>
				Sets whether the joint at [param joint_idx] will use the target node's rotation rather than letting FABRIK rotate the node.
				[b]Note:[/b] This option only works for the tip/final joint in the chain. For all other nodes, this option will be ignored.
			</description>
		</method>
	</methods>
	<members>
		<member name="fabrik_data_chain_length" type="int" setter="set_fabrik_data_chain_length" getter="get_fabrik_data_chain_length" default="0">
			The number of FABRIK joints in the FABRIK modification.
		</member>
		<member name="target_nodepath" type="NodePath" setter="set_target_node" getter="get_target_node" default="NodePath(&quot;&quot;)">
			The NodePath to the node that is the target for the FABRIK modification. This node is what the FABRIK chain will attempt to rotate the bone chain to.
		</member>
	</members>
</class>
