<?xml version="1.0" encoding="UTF-8" ?>
<class name="TileMap" inherits="Node2D" deprecated="Use multiple [TileMapLayer] nodes instead. To convert a TileMap to a set of TileMapLayer nodes, open the TileMap bottom panel with the node selected, click the toolbox icon in the top-right corner and choose &apos;Extract TileMap layers as individual TileMapLayer nodes&apos;." keywords="gridmap" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Node for 2D tile-based maps.
	</brief_description>
	<description>
		Node for 2D tile-based maps. Tilemaps use a [TileSet] which contain a list of tiles which are used to create grid-based maps. A TileMap may have several layers, layouting tiles on top of each other.
		For performance reasons, all TileMap updates are batched at the end of a frame. Notably, this means that scene tiles from a [TileSetScenesCollectionSource] may be initialized after their parent. This is only queued when inside the scene tree.
		To force an update earlier on, call [method update_internals].
		[b]Note:[/b] For performance and compatibility reasons, the coordinates serialized by [TileMap] are limited to 16-bit signed integers, i.e. the range for X and Y coordinates is from [code]-32768[/code] to [code]32767[/code]. When saving tile data, tiles outside this range are wrapped.
	</description>
	<tutorials>
		<link title="Using Tilemaps">$DOCS_URL/tutorials/2d/using_tilemaps.html</link>
		<link title="2D Platformer Demo">https://godotengine.org/asset-library/asset/2727</link>
		<link title="2D Isometric Demo">https://godotengine.org/asset-library/asset/2718</link>
		<link title="2D Hexagonal Demo">https://godotengine.org/asset-library/asset/2717</link>
		<link title="2D Grid-based Navigation with AStarGrid2D Demo">https://godotengine.org/asset-library/asset/2723</link>
		<link title="2D Role Playing Game (RPG) Demo">https://godotengine.org/asset-library/asset/2729</link>
		<link title="2D Kinematic Character Demo">https://godotengine.org/asset-library/asset/2719</link>
		<link title="2D Dynamic TileMap Layers Demo">https://godotengine.org/asset-library/asset/2713</link>
	</tutorials>
	<methods>
		<method name="_tile_data_runtime_update" qualifiers="virtual">
			<return type="void" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="coords" type="Vector2i" />
			<param index="2" name="tile_data" type="TileData" />
			<description>
				Called with a TileData object about to be used internally by the TileMap, allowing its modification at runtime.
				This method is only called if [method _use_tile_data_runtime_update] is implemented and returns [code]true[/code] for the given tile [param coords] and [param layer].
				[b]Warning:[/b] The [param tile_data] object's sub-resources are the same as the one in the TileSet. Modifying them might impact the whole TileSet. Instead, make sure to duplicate those resources.
				[b]Note:[/b] If the properties of [param tile_data] object should change over time, use [method notify_runtime_tile_data_update] to notify the TileMap it needs an update.
			</description>
		</method>
		<method name="_use_tile_data_runtime_update" qualifiers="virtual">
			<return type="bool" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="coords" type="Vector2i" />
			<description>
				Should return [code]true[/code] if the tile at coordinates [param coords] on layer [param layer] requires a runtime update.
				[b]Warning:[/b] Make sure this function only return [code]true[/code] when needed. Any tile processed at runtime without a need for it will imply a significant performance penalty.
				[b]Note:[/b] If the result of this function should changed, use [method notify_runtime_tile_data_update] to notify the TileMap it needs an update.
			</description>
		</method>
		<method name="add_layer">
			<return type="void" />
			<param index="0" name="to_position" type="int" />
			<description>
				Adds a layer at the given position [param to_position] in the array. If [param to_position] is negative, the position is counted from the end, with [code]-1[/code] adding the layer at the end of the array.
			</description>
		</method>
		<method name="clear">
			<return type="void" />
			<description>
				Clears all cells.
			</description>
		</method>
		<method name="clear_layer">
			<return type="void" />
			<param index="0" name="layer" type="int" />
			<description>
				Clears all cells on the given layer.
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="erase_cell">
			<return type="void" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="coords" type="Vector2i" />
			<description>
				Erases the cell on layer [param layer] at coordinates [param coords].
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="fix_invalid_tiles">
			<return type="void" />
			<description>
				Clears cells that do not exist in the tileset.
			</description>
		</method>
		<method name="force_update" deprecated="Use [method notify_runtime_tile_data_update] and/or [method update_internals] instead.">
			<return type="void" />
			<param index="0" name="layer" type="int" default="-1" />
			<description>
				Forces the TileMap and the layer [param layer] to update.
			</description>
		</method>
		<method name="get_cell_alternative_tile" qualifiers="const">
			<return type="int" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="coords" type="Vector2i" />
			<param index="2" name="use_proxies" type="bool" default="false" />
			<description>
				Returns the tile alternative ID of the cell on layer [param layer] at [param coords].
				If [param use_proxies] is [code]false[/code], ignores the [TileSet]'s tile proxies, returning the raw alternative identifier. See [method TileSet.map_tile_proxy].
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="get_cell_atlas_coords" qualifiers="const">
			<return type="Vector2i" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="coords" type="Vector2i" />
			<param index="2" name="use_proxies" type="bool" default="false" />
			<description>
				Returns the tile atlas coordinates ID of the cell on layer [param layer] at coordinates [param coords]. Returns [code]Vector2i(-1, -1)[/code] if the cell does not exist.
				If [param use_proxies] is [code]false[/code], ignores the [TileSet]'s tile proxies, returning the raw atlas coordinate identifier. See [method TileSet.map_tile_proxy].
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="get_cell_source_id" qualifiers="const">
			<return type="int" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="coords" type="Vector2i" />
			<param index="2" name="use_proxies" type="bool" default="false" />
			<description>
				Returns the tile source ID of the cell on layer [param layer] at coordinates [param coords]. Returns [code]-1[/code] if the cell does not exist.
				If [param use_proxies] is [code]false[/code], ignores the [TileSet]'s tile proxies, returning the raw source identifier. See [method TileSet.map_tile_proxy].
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="get_cell_tile_data" qualifiers="const">
			<return type="TileData" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="coords" type="Vector2i" />
			<param index="2" name="use_proxies" type="bool" default="false" />
			<description>
				Returns the [TileData] object associated with the given cell, or [code]null[/code] if the cell does not exist or is not a [TileSetAtlasSource].
				If [param layer] is negative, the layers are accessed from the last one.
				[codeblock]
				func get_clicked_tile_power():
				    var clicked_cell = tile_map.local_to_map(tile_map.get_local_mouse_position())
				    var data = tile_map.get_cell_tile_data(0, clicked_cell)
				    if data:
				        return data.get_custom_data("power")
				    else:
				        return 0
				[/codeblock]
				If [param use_proxies] is [code]false[/code], ignores the [TileSet]'s tile proxies. See [method TileSet.map_tile_proxy].
			</description>
		</method>
		<method name="get_coords_for_body_rid">
			<return type="Vector2i" />
			<param index="0" name="body" type="RID" />
			<description>
				Returns the coordinates of the tile for given physics body RID. Such RID can be retrieved from [method KinematicCollision2D.get_collider_rid], when colliding with a tile.
			</description>
		</method>
		<method name="get_layer_for_body_rid">
			<return type="int" />
			<param index="0" name="body" type="RID" />
			<description>
				Returns the tilemap layer of the tile for given physics body RID. Such RID can be retrieved from [method KinematicCollision2D.get_collider_rid], when colliding with a tile.
			</description>
		</method>
		<method name="get_layer_modulate" qualifiers="const">
			<return type="Color" />
			<param index="0" name="layer" type="int" />
			<description>
				Returns a TileMap layer's modulate.
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="get_layer_name" qualifiers="const">
			<return type="String" />
			<param index="0" name="layer" type="int" />
			<description>
				Returns a TileMap layer's name.
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="get_layer_navigation_map" qualifiers="const">
			<return type="RID" />
			<param index="0" name="layer" type="int" />
			<description>
				Returns the [RID] of the [NavigationServer2D] navigation map assigned to the specified TileMap layer [param layer].
				By default the TileMap uses the default [World2D] navigation map for the first TileMap layer. For each additional TileMap layer a new navigation map is created for the additional layer.
				In order to make [NavigationAgent2D] switch between TileMap layer navigation maps use [method NavigationAgent2D.set_navigation_map] with the navigation map received from [method get_layer_navigation_map].
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="get_layer_y_sort_origin" qualifiers="const">
			<return type="int" />
			<param index="0" name="layer" type="int" />
			<description>
				Returns a TileMap layer's Y sort origin.
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="get_layer_z_index" qualifiers="const">
			<return type="int" />
			<param index="0" name="layer" type="int" />
			<description>
				Returns a TileMap layer's Z-index value.
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="get_layers_count" qualifiers="const">
			<return type="int" />
			<description>
				Returns the number of layers in the TileMap.
			</description>
		</method>
		<method name="get_navigation_map" qualifiers="const" deprecated="Use [method get_layer_navigation_map] instead.">
			<return type="RID" />
			<param index="0" name="layer" type="int" />
			<description>
				Returns the [RID] of the [NavigationServer2D] navigation map assigned to the specified TileMap layer [param layer].
			</description>
		</method>
		<method name="get_neighbor_cell" qualifiers="const">
			<return type="Vector2i" />
			<param index="0" name="coords" type="Vector2i" />
			<param index="1" name="neighbor" type="int" enum="TileSet.CellNeighbor" />
			<description>
				Returns the neighboring cell to the one at coordinates [param coords], identified by the [param neighbor] direction. This method takes into account the different layouts a TileMap can take.
			</description>
		</method>
		<method name="get_pattern">
			<return type="TileMapPattern" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="coords_array" type="Vector2i[]" />
			<description>
				Creates a new [TileMapPattern] from the given layer and set of cells.
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="get_surrounding_cells">
			<return type="Vector2i[]" />
			<param index="0" name="coords" type="Vector2i" />
			<description>
				Returns the list of all neighbourings cells to the one at [param coords].
			</description>
		</method>
		<method name="get_used_cells" qualifiers="const">
			<return type="Vector2i[]" />
			<param index="0" name="layer" type="int" />
			<description>
				Returns a [Vector2i] array with the positions of all cells containing a tile in the given layer. A cell is considered empty if its source identifier equals -1, its atlas coordinates identifiers is [code]Vector2(-1, -1)[/code] and its alternative identifier is -1.
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="get_used_cells_by_id" qualifiers="const">
			<return type="Vector2i[]" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="source_id" type="int" default="-1" />
			<param index="2" name="atlas_coords" type="Vector2i" default="Vector2i(-1, -1)" />
			<param index="3" name="alternative_tile" type="int" default="-1" />
			<description>
				Returns a [Vector2i] array with the positions of all cells containing a tile in the given layer. Tiles may be filtered according to their source ([param source_id]), their atlas coordinates ([param atlas_coords]) or alternative id ([param alternative_tile]).
				If a parameter has its value set to the default one, this parameter is not used to filter a cell. Thus, if all parameters have their respective default value, this method returns the same result as [method get_used_cells].
				A cell is considered empty if its source identifier equals -1, its atlas coordinates identifiers is [code]Vector2(-1, -1)[/code] and its alternative identifier is -1.
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="get_used_rect" qualifiers="const">
			<return type="Rect2i" />
			<description>
				Returns a rectangle enclosing the used (non-empty) tiles of the map, including all layers.
			</description>
		</method>
		<method name="is_cell_flipped_h" qualifiers="const">
			<return type="bool" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="coords" type="Vector2i" />
			<param index="2" name="use_proxies" type="bool" default="false" />
			<description>
				Returns [code]true[/code] if the cell on layer [param layer] at coordinates [param coords] is flipped horizontally. The result is valid only for atlas sources.
			</description>
		</method>
		<method name="is_cell_flipped_v" qualifiers="const">
			<return type="bool" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="coords" type="Vector2i" />
			<param index="2" name="use_proxies" type="bool" default="false" />
			<description>
				Returns [code]true[/code] if the cell on layer [param layer] at coordinates [param coords] is flipped vertically. The result is valid only for atlas sources.
			</description>
		</method>
		<method name="is_cell_transposed" qualifiers="const">
			<return type="bool" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="coords" type="Vector2i" />
			<param index="2" name="use_proxies" type="bool" default="false" />
			<description>
				Returns [code]true[/code] if the cell on layer [param layer] at coordinates [param coords] is transposed. The result is valid only for atlas sources.
			</description>
		</method>
		<method name="is_layer_enabled" qualifiers="const">
			<return type="bool" />
			<param index="0" name="layer" type="int" />
			<description>
				Returns if a layer is enabled.
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="is_layer_navigation_enabled" qualifiers="const">
			<return type="bool" />
			<param index="0" name="layer" type="int" />
			<description>
				Returns if a layer's built-in navigation regions generation is enabled.
			</description>
		</method>
		<method name="is_layer_y_sort_enabled" qualifiers="const">
			<return type="bool" />
			<param index="0" name="layer" type="int" />
			<description>
				Returns if a layer Y-sorts its tiles.
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="local_to_map" qualifiers="const">
			<return type="Vector2i" />
			<param index="0" name="local_position" type="Vector2" />
			<description>
				Returns the map coordinates of the cell containing the given [param local_position]. If [param local_position] is in global coordinates, consider using [method Node2D.to_local] before passing it to this method. See also [method map_to_local].
			</description>
		</method>
		<method name="map_pattern">
			<return type="Vector2i" />
			<param index="0" name="position_in_tilemap" type="Vector2i" />
			<param index="1" name="coords_in_pattern" type="Vector2i" />
			<param index="2" name="pattern" type="TileMapPattern" />
			<description>
				Returns for the given coordinate [param coords_in_pattern] in a [TileMapPattern] the corresponding cell coordinates if the pattern was pasted at the [param position_in_tilemap] coordinates (see [method set_pattern]). This mapping is required as in half-offset tile shapes, the mapping might not work by calculating [code]position_in_tile_map + coords_in_pattern[/code].
			</description>
		</method>
		<method name="map_to_local" qualifiers="const">
			<return type="Vector2" />
			<param index="0" name="map_position" type="Vector2i" />
			<description>
				Returns the centered position of a cell in the TileMap's local coordinate space. To convert the returned value into global coordinates, use [method Node2D.to_global]. See also [method local_to_map].
				[b]Note:[/b] This may not correspond to the visual position of the tile, i.e. it ignores the [member TileData.texture_origin] property of individual tiles.
			</description>
		</method>
		<method name="move_layer">
			<return type="void" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="to_position" type="int" />
			<description>
				Moves the layer at index [param layer] to the given position [param to_position] in the array.
			</description>
		</method>
		<method name="notify_runtime_tile_data_update">
			<return type="void" />
			<param index="0" name="layer" type="int" default="-1" />
			<description>
				Notifies the TileMap node that calls to [method _use_tile_data_runtime_update] or [method _tile_data_runtime_update] will lead to different results. This will thus trigger a TileMap update.
				If [param layer] is provided, only notifies changes for the given layer. Providing the [param layer] argument (when applicable) is usually preferred for performance reasons.
				[b]Warning:[/b] Updating the TileMap is computationally expensive and may impact performance. Try to limit the number of calls to this function to avoid unnecessary update.
				[b]Note:[/b] This does not trigger a direct update of the TileMap, the update will be done at the end of the frame as usual (unless you call [method update_internals]).
			</description>
		</method>
		<method name="remove_layer">
			<return type="void" />
			<param index="0" name="layer" type="int" />
			<description>
				Removes the layer at index [param layer].
			</description>
		</method>
		<method name="set_cell">
			<return type="void" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="coords" type="Vector2i" />
			<param index="2" name="source_id" type="int" default="-1" />
			<param index="3" name="atlas_coords" type="Vector2i" default="Vector2i(-1, -1)" />
			<param index="4" name="alternative_tile" type="int" default="0" />
			<description>
				Sets the tile identifiers for the cell on layer [param layer] at coordinates [param coords]. Each tile of the [TileSet] is identified using three parts:
				- The source identifier [param source_id] identifies a [TileSetSource] identifier. See [method TileSet.set_source_id],
				- The atlas coordinates identifier [param atlas_coords] identifies a tile coordinates in the atlas (if the source is a [TileSetAtlasSource]). For [TileSetScenesCollectionSource] it should always be [code]Vector2i(0, 0)[/code]),
				- The alternative tile identifier [param alternative_tile] identifies a tile alternative in the atlas (if the source is a [TileSetAtlasSource]), and the scene for a [TileSetScenesCollectionSource].
				If [param source_id] is set to [code]-1[/code], [param atlas_coords] to [code]Vector2i(-1, -1)[/code] or [param alternative_tile] to [code]-1[/code], the cell will be erased. An erased cell gets [b]all[/b] its identifiers automatically set to their respective invalid values, namely [code]-1[/code], [code]Vector2i(-1, -1)[/code] and [code]-1[/code].
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="set_cells_terrain_connect">
			<return type="void" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="cells" type="Vector2i[]" />
			<param index="2" name="terrain_set" type="int" />
			<param index="3" name="terrain" type="int" />
			<param index="4" name="ignore_empty_terrains" type="bool" default="true" />
			<description>
				Update all the cells in the [param cells] coordinates array so that they use the given [param terrain] for the given [param terrain_set]. If an updated cell has the same terrain as one of its neighboring cells, this function tries to join the two. This function might update neighboring tiles if needed to create correct terrain transitions.
				If [param ignore_empty_terrains] is [code]true[/code], empty terrains will be ignored when trying to find the best fitting tile for the given terrain constraints.
				If [param layer] is negative, the layers are accessed from the last one.
				[b]Note:[/b] To work correctly, this method requires the TileMap's TileSet to have terrains set up with all required terrain combinations. Otherwise, it may produce unexpected results.
			</description>
		</method>
		<method name="set_cells_terrain_path">
			<return type="void" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="path" type="Vector2i[]" />
			<param index="2" name="terrain_set" type="int" />
			<param index="3" name="terrain" type="int" />
			<param index="4" name="ignore_empty_terrains" type="bool" default="true" />
			<description>
				Update all the cells in the [param path] coordinates array so that they use the given [param terrain] for the given [param terrain_set]. The function will also connect two successive cell in the path with the same terrain. This function might update neighboring tiles if needed to create correct terrain transitions.
				If [param ignore_empty_terrains] is [code]true[/code], empty terrains will be ignored when trying to find the best fitting tile for the given terrain constraints.
				If [param layer] is negative, the layers are accessed from the last one.
				[b]Note:[/b] To work correctly, this method requires the TileMap's TileSet to have terrains set up with all required terrain combinations. Otherwise, it may produce unexpected results.
			</description>
		</method>
		<method name="set_layer_enabled">
			<return type="void" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="enabled" type="bool" />
			<description>
				Enables or disables the layer [param layer]. A disabled layer is not processed at all (no rendering, no physics, etc.).
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="set_layer_modulate">
			<return type="void" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="modulate" type="Color" />
			<description>
				Sets a layer's color. It will be multiplied by tile's color and TileMap's modulate.
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="set_layer_name">
			<return type="void" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="name" type="String" />
			<description>
				Sets a layer's name. This is mostly useful in the editor.
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="set_layer_navigation_enabled">
			<return type="void" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="enabled" type="bool" />
			<description>
				Enables or disables a layer's built-in navigation regions generation. Disable this if you need to bake navigation regions from a TileMap using a [NavigationRegion2D] node.
			</description>
		</method>
		<method name="set_layer_navigation_map">
			<return type="void" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="map" type="RID" />
			<description>
				Assigns [param map] as a [NavigationServer2D] navigation map for the specified TileMap layer [param layer].
				By default the TileMap uses the default [World2D] navigation map for the first TileMap layer. For each additional TileMap layer a new navigation map is created for the additional layer.
				In order to make [NavigationAgent2D] switch between TileMap layer navigation maps use [method NavigationAgent2D.set_navigation_map] with the navigation map received from [method get_layer_navigation_map].
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="set_layer_y_sort_enabled">
			<return type="void" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="y_sort_enabled" type="bool" />
			<description>
				Enables or disables a layer's Y-sorting. If a layer is Y-sorted, the layer will behave as a CanvasItem node where each of its tile gets Y-sorted.
				Y-sorted layers should usually be on different Z-index values than not Y-sorted layers, otherwise, each of those layer will be Y-sorted as whole with the Y-sorted one. This is usually an undesired behavior.
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="set_layer_y_sort_origin">
			<return type="void" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="y_sort_origin" type="int" />
			<description>
				Sets a layer's Y-sort origin value. This Y-sort origin value is added to each tile's Y-sort origin value.
				This allows, for example, to fake a different height level on each layer. This can be useful for top-down view games.
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="set_layer_z_index">
			<return type="void" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="z_index" type="int" />
			<description>
				Sets a layers Z-index value. This Z-index is added to each tile's Z-index value.
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="set_navigation_map" deprecated="Use [method set_layer_navigation_map] instead.">
			<return type="void" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="map" type="RID" />
			<description>
				Assigns [param map] as a [NavigationServer2D] navigation map for the specified TileMap layer [param layer].
			</description>
		</method>
		<method name="set_pattern">
			<return type="void" />
			<param index="0" name="layer" type="int" />
			<param index="1" name="position" type="Vector2i" />
			<param index="2" name="pattern" type="TileMapPattern" />
			<description>
				Paste the given [TileMapPattern] at the given [param position] and [param layer] in the tile map.
				If [param layer] is negative, the layers are accessed from the last one.
			</description>
		</method>
		<method name="update_internals">
			<return type="void" />
			<description>
				Triggers a direct update of the TileMap. Usually, calling this function is not needed, as TileMap node updates automatically when one of its properties or cells is modified.
				However, for performance reasons, those updates are batched and delayed to the end of the frame. Calling this function will force the TileMap to update right away instead.
				[b]Warning:[/b] Updating the TileMap is computationally expensive and may impact performance. Try to limit the number of updates and how many tiles they impact.
			</description>
		</method>
	</methods>
	<members>
		<member name="collision_animatable" type="bool" setter="set_collision_animatable" getter="is_collision_animatable" default="false">
			If enabled, the TileMap will see its collisions synced to the physics tick and change its collision type from static to kinematic. This is required to create TileMap-based moving platform.
			[b]Note:[/b] Enabling [member collision_animatable] may have a small performance impact, only do it if the TileMap is moving and has colliding tiles.
		</member>
		<member name="collision_visibility_mode" type="int" setter="set_collision_visibility_mode" getter="get_collision_visibility_mode" enum="TileMap.VisibilityMode" default="0">
			Show or hide the TileMap's collision shapes. If set to [constant VISIBILITY_MODE_DEFAULT], this depends on the show collision debug settings.
		</member>
		<member name="navigation_visibility_mode" type="int" setter="set_navigation_visibility_mode" getter="get_navigation_visibility_mode" enum="TileMap.VisibilityMode" default="0">
			Show or hide the TileMap's navigation meshes. If set to [constant VISIBILITY_MODE_DEFAULT], this depends on the show navigation debug settings.
		</member>
		<member name="rendering_quadrant_size" type="int" setter="set_rendering_quadrant_size" getter="get_rendering_quadrant_size" default="16">
			The TileMap's quadrant size. A quadrant is a group of tiles to be drawn together on a single canvas item, for optimization purposes. [member rendering_quadrant_size] defines the length of a square's side, in the map's coordinate system, that forms the quadrant. Thus, the default quadrant size groups together [code]16 * 16 = 256[/code] tiles.
			The quadrant size does not apply on Y-sorted layers, as tiles are grouped by Y position instead in that case.
			[b]Note:[/b] As quadrants are created according to the map's coordinate system, the quadrant's "square shape" might not look like square in the TileMap's local coordinate system.
		</member>
		<member name="tile_set" type="TileSet" setter="set_tileset" getter="get_tileset">
			The [TileSet] used by this [TileMap]. The textures, collisions, and additional behavior of all available tiles are stored here.
		</member>
	</members>
	<signals>
		<signal name="changed">
			<description>
				Emitted when the [TileSet] of this TileMap changes.
			</description>
		</signal>
	</signals>
	<constants>
		<constant name="VISIBILITY_MODE_DEFAULT" value="0" enum="VisibilityMode">
			Use the debug settings to determine visibility.
		</constant>
		<constant name="VISIBILITY_MODE_FORCE_HIDE" value="2" enum="VisibilityMode">
			Always hide.
		</constant>
		<constant name="VISIBILITY_MODE_FORCE_SHOW" value="1" enum="VisibilityMode">
			Always show.
		</constant>
	</constants>
</class>
