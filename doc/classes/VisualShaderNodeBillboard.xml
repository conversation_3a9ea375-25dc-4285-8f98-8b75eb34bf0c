<?xml version="1.0" encoding="UTF-8" ?>
<class name="VisualShaderNodeBillboard" inherits="VisualShaderNode" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A node that controls how the object faces the camera to be used within the visual shader graph.
	</brief_description>
	<description>
		The output port of this node needs to be connected to [code]Model View Matrix[/code] port of [VisualShaderNodeOutput].
	</description>
	<tutorials>
	</tutorials>
	<members>
		<member name="billboard_type" type="int" setter="set_billboard_type" getter="get_billboard_type" enum="VisualShaderNodeBillboard.BillboardType" default="1">
			Controls how the object faces the camera. See [enum BillboardType].
		</member>
		<member name="keep_scale" type="bool" setter="set_keep_scale_enabled" getter="is_keep_scale_enabled" default="false">
			If [code]true[/code], the shader will keep the scale set for the mesh. Otherwise, the scale is lost when billboarding.
		</member>
	</members>
	<constants>
		<constant name="BILLBOARD_TYPE_DISABLED" value="0" enum="BillboardType">
			Billboarding is disabled and the node does nothing.
		</constant>
		<constant name="BILLBOARD_TYPE_ENABLED" value="1" enum="BillboardType">
			A standard billboarding algorithm is enabled.
		</constant>
		<constant name="BILLBOARD_TYPE_FIXED_Y" value="2" enum="BillboardType">
			A billboarding algorithm to rotate around Y-axis is enabled.
		</constant>
		<constant name="BILLBOARD_TYPE_PARTICLES" value="3" enum="BillboardType">
			A billboarding algorithm designed to use on particles is enabled.
		</constant>
		<constant name="BILLBOARD_TYPE_MAX" value="4" enum="BillboardType">
			Represents the size of the [enum BillboardType] enum.
		</constant>
	</constants>
</class>
