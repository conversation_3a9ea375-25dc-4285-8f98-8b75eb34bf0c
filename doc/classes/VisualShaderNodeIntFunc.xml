<?xml version="1.0" encoding="UTF-8" ?>
<class name="VisualShaderNodeIntFunc" inherits="VisualShaderNode" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A scalar integer function to be used within the visual shader graph.
	</brief_description>
	<description>
		Accept an integer scalar ([code]x[/code]) to the input port and transform it according to [member function].
	</description>
	<tutorials>
	</tutorials>
	<members>
		<member name="function" type="int" setter="set_function" getter="get_function" enum="VisualShaderNodeIntFunc.Function" default="2">
			A function to be applied to the scalar. See [enum Function] for options.
		</member>
	</members>
	<constants>
		<constant name="FUNC_ABS" value="0" enum="Function">
			Returns the absolute value of the parameter. Translates to [code]abs(x)[/code] in the Godot Shader Language.
		</constant>
		<constant name="FUNC_NEGATE" value="1" enum="Function">
			Negates the [code]x[/code] using [code]-(x)[/code].
		</constant>
		<constant name="FUNC_SIGN" value="2" enum="Function">
			Extracts the sign of the parameter. Translates to [code]sign(x)[/code] in the Godot Shader Language.
		</constant>
		<constant name="FUNC_BITWISE_NOT" value="3" enum="Function">
			Returns the result of bitwise [code]NOT[/code] operation on the integer. Translates to [code]~a[/code] in the Godot Shader Language.
		</constant>
		<constant name="FUNC_MAX" value="4" enum="Function">
			Represents the size of the [enum Function] enum.
		</constant>
	</constants>
</class>
