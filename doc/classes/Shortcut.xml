<?xml version="1.0" encoding="UTF-8" ?>
<class name="Shortcut" inherits="Resource" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		A shortcut for binding input.
	</brief_description>
	<description>
		Shortcuts are commonly used for interacting with a [Control] element from an [InputEvent] (also known as hotkeys).
		One shortcut can contain multiple [InputEvent]s, allowing the possibility of triggering one action with multiple different inputs.
	</description>
	<tutorials>
	</tutorials>
	<methods>
		<method name="get_as_text" qualifiers="const">
			<return type="String" />
			<description>
				Returns the shortcut's first valid [InputEvent] as a [String].
			</description>
		</method>
		<method name="has_valid_event" qualifiers="const">
			<return type="bool" />
			<description>
				Returns whether [member events] contains an [InputEvent] which is valid.
			</description>
		</method>
		<method name="matches_event" qualifiers="const">
			<return type="bool" />
			<param index="0" name="event" type="InputEvent" />
			<description>
				Returns whether any [InputEvent] in [member events] equals [param event]. This uses [method InputEvent.is_match] to compare events.
			</description>
		</method>
	</methods>
	<members>
		<member name="events" type="Array" setter="set_events" getter="get_events" default="[]">
			The shortcut's [InputEvent] array.
			Generally the [InputEvent] used is an [InputEventKey], though it can be any [InputEvent], including an [InputEventAction].
		</member>
	</members>
</class>
