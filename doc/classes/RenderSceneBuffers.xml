<?xml version="1.0" encoding="UTF-8" ?>
<class name="RenderSceneBuffers" inherits="RefCounted" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../class.xsd">
	<brief_description>
		Abstract scene buffers object, created for each viewport for which 3D rendering is done.
	</brief_description>
	<description>
		Abstract scene buffers object, created for each viewport for which 3D rendering is done. It manages any additional buffers used during rendering and will discard buffers when the viewport is resized.
		[b]Note:[/b] This is an internal rendering server object, do not instantiate this from script.
	</description>
	<tutorials>
	</tutorials>
	<methods>
		<method name="configure">
			<return type="void" />
			<param index="0" name="config" type="RenderSceneBuffersConfiguration" />
			<description>
				This method is called by the rendering server when the associated viewports configuration is changed. It will discard the old buffers and recreate the internal buffers used.
			</description>
		</method>
	</methods>
</class>
