load("//bazel:rex_engine.bzl", "rex_engine_library")

package(default_visibility = ["//visibility:public"])

# Editor icons
genrule(
    name = "editor_icons_gen",
    srcs = glob(["icons/*.svg"]),
    outs = ["editor_icons.gen.h"],
    cmd = "echo '/* Generated editor icons header */' > $@",
)

# Editor translations
genrule(
    name = "editor_translations_gen",
    srcs = glob(["translations/*.po"]),
    outs = ["editor_translations.gen.h"],
    cmd = "echo '/* Generated editor translations header */' > $@",
)

# Editor themes
genrule(
    name = "editor_themes_gen",
    srcs = glob(["themes/*.tres"]),
    outs = ["editor_themes.gen.h"],
    cmd = "echo '/* Generated editor themes header */' > $@",
)

# Animation editor
rex_engine_library(
    name = "animation",
    srcs = select({
        "//bazel:editor": glob(["animation/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:editor": glob(["animation/*.h"]),
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        "//scene:scene",
    ],
)

# Code editor
rex_engine_library(
    name = "code_editor",
    srcs = select({
        "//bazel:editor": glob(["code_editor/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:editor": glob(["code_editor/*.h"]),
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        "//scene:scene",
    ],
)

# Debugger
rex_engine_library(
    name = "debugger",
    srcs = select({
        "//bazel:editor": glob(["debugger/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:editor": glob(["debugger/*.h"]),
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        "//scene:scene",
    ],
)

# Export system
rex_engine_library(
    name = "export",
    srcs = select({
        "//bazel:editor": glob(["export/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:editor": glob(["export/*.h"]),
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        "//scene:scene",
    ],
)

# File system dock
rex_engine_library(
    name = "filesystem_dock",
    srcs = select({
        "//bazel:editor": glob(["filesystem_dock/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:editor": glob(["filesystem_dock/*.h"]),
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        "//scene:scene",
    ],
)

# History dock
rex_engine_library(
    name = "history_dock",
    srcs = select({
        "//bazel:editor": glob(["history_dock/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:editor": glob(["history_dock/*.h"]),
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        "//scene:scene",
    ],
)

# Import system
rex_engine_library(
    name = "import",
    srcs = select({
        "//bazel:editor": glob(["import/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:editor": glob(["import/*.h"]),
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        "//scene:scene",
    ],
)

# Inspector dock
rex_engine_library(
    name = "inspector",
    srcs = select({
        "//bazel:editor": glob(["inspector/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:editor": glob(["inspector/*.h"]),
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        "//scene:scene",
    ],
)

# Plugins system
rex_engine_library(
    name = "plugins",
    srcs = select({
        "//bazel:editor": glob(["plugins/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:editor": glob(["plugins/*.h"]),
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        "//scene:scene",
    ],
)

# Project manager
rex_engine_library(
    name = "project_manager",
    srcs = select({
        "//bazel:editor": glob(["project_manager/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:editor": glob(["project_manager/*.h"]),
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        "//scene:scene",
    ],
)

# Scene dock
rex_engine_library(
    name = "scene_tree_dock",
    srcs = select({
        "//bazel:editor": glob(["scene_tree_dock/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:editor": glob(["scene_tree_dock/*.h"]),
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
        "//scene:scene",
    ],
)

# Basic editor library (minimal functionality)
rex_engine_library(
    name = "editor",
    srcs = select({
        "//bazel:editor": glob(["*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:editor": glob(["*.h"]) + [
            ":editor_icons_gen",
            ":editor_translations_gen",
            ":editor_themes_gen",
        ],
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:editor": ["TOOLS_ENABLED"],
        "//conditions:default": ["TOOLS_DISABLED"],
    }),
    deps = [
        "//core:core",
        "//scene:scene",
        "//servers:servers",
    ],
)

# Full editor library (with all components)
rex_engine_library(
    name = "editor_full",
    deps = select({
        "//bazel:editor": [
            ":editor",
            ":animation",
            ":code_editor",
            ":debugger",
            ":export",
            ":filesystem_dock",
            ":history_dock",
            ":import",
            ":inspector",
            ":plugins",
            ":project_manager",
            ":scene_tree_dock",
        ],
        "//conditions:default": [":editor"],
    }),
)
