load("//bazel:rex_engine.bzl", "rex_engine_library")

package(default_visibility = ["//visibility:public"])

# X11 support
rex_engine_library(
    name = "x11",
    srcs = select({
        "//bazel:use_x11": glob(["x11/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:use_x11": glob(["x11/*.h"]),
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:use_x11": ["X11_ENABLED"],
        "//conditions:default": [],
    }),
    linkopts = select({
        "//bazel:use_x11": [
            "-lX11",
            "-lXcursor",
            "-lXinerama",
            "-lXext",
            "-lXrandr",
            "-lXrender",
            "-lXi",
            "-lXfixes",
        ],
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
    ],
)

# Wayland support
rex_engine_library(
    name = "wayland",
    srcs = select({
        "//bazel:use_wayland": glob(["wayland/*.cpp"]),
        "//conditions:default": [],
    }),
    hdrs = select({
        "//bazel:use_wayland": glob(["wayland/*.h"]),
        "//conditions:default": [],
    }),
    defines = select({
        "//bazel:use_wayland": ["WAYLAND_ENABLED"],
        "//conditions:default": [],
    }),
    linkopts = select({
        "//bazel:use_wayland": [
            "-lwayland-client",
            "-lwayland-cursor",
            "-lwayland-egl",
            "-lxkbcommon",
            "-ldecor-0",
        ],
        "//conditions:default": [],
    }),
    deps = [
        "//core:core",
    ],
)

# Main Linux/BSD platform library
rex_engine_library(
    name = "linuxbsd_platform",
    srcs = [
        "crash_handler_linuxbsd.cpp",
        "os_linuxbsd.cpp",
        "joypad_linux.cpp",
        "freedesktop_portal_desktop.cpp",
        "freedesktop_screensaver.cpp",
        "freedesktop_at_spi_monitor.cpp",
    ] + select({
        "//bazel:use_sowrap": ["xkbcommon-so_wrap.c"],
        "//conditions:default": [],
    }) + select({
        "//bazel:speechd": ["tts_linux.cpp"],
        "//conditions:default": [],
    }) + select({
        "//bazel:speechd": select({
            "//bazel:use_sowrap": ["speechd-so_wrap.c"],
            "//conditions:default": [],
        }),
        "//conditions:default": [],
    }) + select({
        "//bazel:fontconfig": select({
            "//bazel:use_sowrap": ["fontconfig-so_wrap.c"],
            "//conditions:default": [],
        }),
        "//conditions:default": [],
    }) + select({
        "//bazel:udev": select({
            "//bazel:use_sowrap": ["libudev-so_wrap.c"],
            "//conditions:default": [],
        }),
        "//conditions:default": [],
    }) + select({
        "//bazel:dbus": select({
            "//bazel:use_sowrap": ["dbus-so_wrap.c"],
            "//conditions:default": [],
        }),
        "//conditions:default": [],
    }),
    hdrs = glob(["*.h"]),
    defines = [
        "UNIX_ENABLED",
        "LINUXBSD_ENABLED",
    ] + select({
        "//bazel:speechd": ["SPEECHD_ENABLED"],
        "//conditions:default": [],
    }) + select({
        "//bazel:fontconfig": ["FONTCONFIG_ENABLED"],
        "//conditions:default": [],
    }) + select({
        "//bazel:udev": ["UDEV_ENABLED"],
        "//conditions:default": [],
    }) + select({
        "//bazel:dbus": ["DBUS_ENABLED"],
        "//conditions:default": [],
    }),
    linkopts = [
        "-lpthread",
        "-ldl",
        "-lrt",
    ] + select({
        "//bazel:use_pulseaudio": ["-lpulse", "-lpulse-simple"],
        "//conditions:default": [],
    }) + select({
        "//bazel:use_alsa": ["-lasound"],
        "//conditions:default": [],
    }) + select({
        "//bazel:speechd": ["-lspeechd"],
        "//conditions:default": [],
    }) + select({
        "//bazel:fontconfig": ["-lfontconfig"],
        "//conditions:default": [],
    }) + select({
        "//bazel:udev": ["-ludev"],
        "//conditions:default": [],
    }) + select({
        "//bazel:dbus": ["-ldbus-1"],
        "//conditions:default": [],
    }),
    target_compatible_with = [
        "@platforms//os:linux",
    ],
    deps = [
        "//core:core",
        "//servers:servers",
        "//drivers:drivers",
    ] + select({
        "//bazel:use_x11": [":x11"],
        "//conditions:default": [],
    }) + select({
        "//bazel:use_wayland": [":wayland"],
        "//conditions:default": [],
    }),
)
