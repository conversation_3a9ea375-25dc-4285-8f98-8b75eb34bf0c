load("//bazel:rex_engine.bzl", "rex_engine_library")

package(default_visibility = ["//visibility:public"])

# macOS platform library
rex_engine_library(
    name = "macos_platform",
    srcs = [
        "crash_handler_macos.mm",
        "os_macos.mm",
        "joypad_macos.cpp",
        "display_server_macos.mm",
        "gl_manager_macos_legacy.mm",
        "gl_manager_macos.mm",
        "key_mapping_macos.mm",
        "dir_access_macos.mm",
        "file_access_macos.mm",
        "macos_terminal_logger.mm",
        "tts_macos.mm",
        "vulkan_context_macos.mm",
    ] + select({
        "//bazel:use_metal": [
            "rendering_context_driver_metal_macos.mm",
        ],
        "//conditions:default": [],
    }),
    hdrs = glob(["*.h"]),
    defines = [
        "MACOS_ENABLED",
        "UNIX_ENABLED",
        "COREAUDIO_ENABLED",
        "COREMIDI_ENABLED",
    ] + select({
        "//bazel:use_metal": ["METAL_ENABLED"],
        "//conditions:default": [],
    }),
    copts = [
        "-mmacosx-version-min=10.14",
        "-std=gnu++17",
        "-fobjc-arc",
        "-Wno-deprecated-declarations",
    ],
    linkopts = [
        "-mmacosx-version-min=10.14",
        "-framework", "Cocoa",
        "-framework", "Carbon",
        "-framework", "AudioUnit",
        "-framework", "CoreAudio",
        "-framework", "CoreMIDI",
        "-framework", "IOKit",
        "-framework", "ForceFeedback",
        "-framework", "CoreVideo",
        "-framework", "AVFoundation",
        "-framework", "CoreMedia",
        "-framework", "QuartzCore",
        "-framework", "Security",
        "-framework", "SystemConfiguration",
        "-framework", "OpenGL",
    ] + select({
        "//bazel:use_metal": [
            "-framework", "Metal",
            "-framework", "MetalKit",
        ],
        "//conditions:default": [],
    }),
    target_compatible_with = [
        "@platforms//os:macos",
    ],
    deps = [
        "//core:core",
        "//servers:servers", 
        "//drivers:drivers",
    ],
)
