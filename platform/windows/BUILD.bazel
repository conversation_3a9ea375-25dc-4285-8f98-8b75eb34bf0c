load("//bazel:rex_engine.bzl", "rex_engine_library")

package(default_visibility = ["//visibility:public"])

# Windows platform library
rex_engine_library(
    name = "windows_platform",
    srcs = [
        "crash_handler_windows.cpp",
        "os_windows.cpp",
        "joypad_windows.cpp",
        "windows_terminal_logger.cpp",
        "key_mapping_windows.cpp",
        "display_server_windows.cpp",
        "gl_manager_windows.cpp",
        "vulkan_context_win.cpp",
        "tts_windows.cpp",
    ] + select({
        "//bazel:use_d3d12": [
            "rendering_context_driver_d3d12_windows.cpp",
        ],
        "//conditions:default": [],
    }),
    hdrs = glob(["*.h"]),
    defines = [
        "WINDOWS_ENABLED",
        "WASAPI_ENABLED",
        "WINMIDI_ENABLED",
        "TYPED_METHOD_BIND",
        "WIN32_LEAN_AND_MEAN",
        "NOMINMAX",
    ] + select({
        "//bazel:use_d3d12": ["D3D12_ENABLED"],
        "//conditions:default": [],
    }),
    copts = [
        "/std:c++17",
        "/permissive-",
        "/W3",
        "/wd4244",  # Conversion warnings
        "/wd4267",  # Size_t conversion warnings
        "/wd4305",  # Truncation warnings
        "/wd4996",  # Deprecated function warnings
    ],
    linkopts = [
        "/SUBSYSTEM:CONSOLE",
        "kernel32.lib",
        "ole32.lib",
        "oleaut32.lib",
        "user32.lib",
        "gdi32.lib",
        "IPHLPAPI.lib",
        "Shlwapi.lib",
        "wsock32.lib",
        "Ws2_32.lib",
        "shell32.lib",
        "advapi32.lib",
        "dinput8.lib",
        "dxguid.lib",
        "imm32.lib",
        "bcrypt.lib",
        "Avrt.lib",
        "dwmapi.lib",
        "dwrite.lib",
        "wbemuuid.lib",
    ] + select({
        "//bazel:use_d3d12": [
            "d3d12.lib",
            "dxgi.lib",
            "dxguid.lib",
        ],
        "//conditions:default": [],
    }),
    target_compatible_with = [
        "@platforms//os:windows",
    ],
    deps = [
        "//core:core",
        "//servers:servers",
        "//drivers:drivers",
    ] + select({
        "//bazel:use_d3d12": ["//thirdparty/d3d12ma:d3d12ma"],
        "//conditions:default": [],
    }),
)
